name: Deployment

on:
  push:
    branches: ["main"]  # branchname

jobs:  
  use-reusable-workflow:
    uses: navya-app/shared-cicd-pipeline/.github/workflows/deployment.yml@main  # Reference to the reusable workflow
    with:
      service_name: ondc-ref-app-webapp
      branch_name: "main"
      environment: "preprod"
      API_BASE_URL: "https://preprod.nirmitbap.ondc.org"
    secrets:
      PAT_TOKEN: ${{ secrets.PAT_TOKEN }}
      GCLOUD_SERVICE_KEY: ${{ secrets.GCLOUD_SERVICE_KEY }}
      ENCRYPTION_KEY: ${{ secrets.ENCRYPTION_KEY }}
      MMI_CLIENT_SECRET: ${{ secrets.MMI_CLIENT_SECRET }}
      MMI_API_KEY: ${{ secrets.MMI_API_KEY }}
      
     
