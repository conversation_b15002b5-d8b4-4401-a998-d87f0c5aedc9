{"name": "digihaat-web", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.4.4", "@mui/material": "^6.4.10", "@mui/styles": "^6.4.4", "@reduxjs/toolkit": "^2.5.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "ajv": "^8.17.1", "axios": "^1.8.1", "crypto-js": "^4.2.0", "env-cmd": "^10.1.0", "event-source-polyfill": "^1.0.31", "formik": "^2.4.6", "framer-motion": "^12.9.2", "geolib": "^3.3.4", "i18next": "^23.10.0", "install": "^0.13.0", "lottie": "^0.0.1", "lottie-react": "^2.4.1", "mappls-web-maps": "^3.5.8", "moment": "^2.30.1", "moment-duration-format": "^2.3.2", "npm": "^11.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.4.0", "react-lottie": "^1.2.10", "react-pdf": "^9.2.1", "react-phone-input-2": "^2.15.1", "react-qr-reader": "^3.0.0-beta-1", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-scripts": "5.0.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "react-uuid": "^2.0.0", "slick-carousel": "^1.8.1", "uuid": "^11.1.0", "web-vitals": "^2.1.4", "yup": "^1.6.1"}, "scripts": {"start": "env-cmd -f .env.preprod react-scripts start", "build": "react-scripts build", "start:preprod": "react-scripts start", "build:preprod": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/event-source-polyfill": "^1.0.5", "@types/react": "^19.1.0", "@types/react-dom": "^19.0.4", "@types/react-lottie": "^1.2.10", "@types/react-slick": "^0.23.13", "@types/uuid": "^10.0.0", "typescript": "^5.8.2"}}