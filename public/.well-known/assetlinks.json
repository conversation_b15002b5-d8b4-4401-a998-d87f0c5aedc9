[{"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "org.ondc.nirmitbap", "sha256_cert_fingerprints": ["FA:C6:17:45:DC:09:03:78:6F:B9:ED:E6:2A:96:2B:39:9F:73:48:F0:BB:6F:89:9B:83:32:66:75:91:03:3B:9C", "6E:49:CB:EA:56:11:74:35:64:61:AA:07:BD:35:67:84:DC:6A:88:77:62:D3:D5:21:00:1F:72:74:58:60:73:86", "15:73:3F:DE:4B:B1:57:88:35:BE:F8:1D:11:B3:E8:6D:C6:77:31:63:05:99:66:8A:B8:42:1E:0B:35:04:AA:A3", "90:3A:57:45:29:30:FB:86:D4:88:B5:9C:D4:F8:40:FD:10:0C:D9:A6:EA:11:52:FA:9E:4F:42:7E:9F:75:85:01"]}}, {"relation": ["delegate_permission/common.handle_all_urls"], "target": {"namespace": "android_app", "package_name": "org.ondc.nirmitbap.preprod", "sha256_cert_fingerprints": ["FA:C6:17:45:DC:09:03:78:6F:B9:ED:E6:2A:96:2B:39:9F:73:48:F0:BB:6F:89:9B:83:32:66:75:91:03:3B:9C", "6E:49:CB:EA:56:11:74:35:64:61:AA:07:BD:35:67:84:DC:6A:88:77:62:D3:D5:21:00:1F:72:74:58:60:73:86", "15:73:3F:DE:4B:B1:57:88:35:BE:F8:1D:11:B3:E8:6D:C6:77:31:63:05:99:66:8A:B8:42:1E:0B:35:04:AA:A3", "40:95:12:7B:AE:0B:46:EA:30:45:33:5E:62:57:DB:DF:E0:C0:99:F0:F6:26:C8:28:6D:80:7D:38:0D:8C:7F:14"]}}]