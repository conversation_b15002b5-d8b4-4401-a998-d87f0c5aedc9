# Stage 1: Build the React app
FROM node:18-alpine AS builder

# Set build arguments
ARG REACT_APP_API_BASE_URL
ARG REACT_APP_ULCA_BASE_URL
ARG REACT_APP_ULCA_API_KEY
ARG REACT_APP_ULCA_USER_ID
ARG REACT_APP_PIPELINE_ID
ARG REACT_APP_ENCRYPTION_KEY
ARG REACT_APP_GOOGLE_CLIENT_ID
ARG REACT_APP_MMI_CLIENT_SECRET
ARG REACT_APP_MMI_API_KEY
ARG REACT_APP_HELP_EMAIL
ARG REACT_APP_ENV
ARG REACT_APP_GLOBAL_SUGGESTION
ARG REACT_APP_FACEBOOK_APP_ID

# Convert arguments to environment variables
ENV REACT_APP_API_BASE_URL=${REACT_APP_API_BASE_URL}
ENV REACT_APP_ULCA_BASE_URL=${REACT_APP_ULCA_BASE_URL}
ENV REACT_APP_ULCA_API_KEY=${REACT_APP_ULCA_API_KEY}
ENV REACT_APP_ULCA_USER_ID=${REACT_APP_ULCA_USER_ID}
ENV REACT_APP_PIPELINE_ID=${REACT_APP_PIPELINE_ID}
ENV REACT_APP_ENCRYPTION_KEY=${REACT_APP_ENCRYPTION_KEY}
ENV REACT_APP_GOOGLE_CLIENT_ID=${REACT_APP_GOOGLE_CLIENT_ID}
ENV REACT_APP_MMI_CLIENT_SECRET=${REACT_APP_MMI_CLIENT_SECRET}
ENV REACT_APP_MMI_API_KEY=${REACT_APP_MMI_API_KEY}
ENV REACT_APP_HELP_EMAIL=${REACT_APP_HELP_EMAIL}
ENV REACT_APP_ENV=${REACT_APP_ENV}
ENV REACT_APP_GLOBAL_SUGGESTION=${REACT_APP_GLOBAL_SUGGESTION}
ENV REACT_APP_FACEBOOK_APP_ID=${REACT_APP_FACEBOOK_APP_ID}

# Print environment variables for verification
RUN echo "ENV values:" && \
    echo "REACT_APP_API_BASE_URL=$REACT_APP_API_BASE_URL" && \
    echo "REACT_APP_ENCRYPTION_KEY=$REACT_APP_ENCRYPTION_KEY" && \
    echo "REACT_APP_MMI_CLIENT_SECRET=$REACT_APP_MMI_CLIENT_SECRET" && \
    echo "REACT_APP_MMI_API_KEY=$REACT_APP_MMI_API_KEY" && \
    echo "REACT_APP_HELP_EMAIL=$REACT_APP_HELP_EMAIL" && \
    echo "REACT_APP_ENV=$REACT_APP_ENV"

WORKDIR /app
COPY . .

# Install dependencies and build the app
RUN npm install --legacy-peer-deps && npm run-script build

# Stage 2: Serve with Nginx
FROM nginx:alpine

# Copy the built app from the builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Ensure the .well-known directory exists
RUN mkdir -p /usr/share/nginx/html/.well-known

# Expose port 80 (default for nginx)
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
