declare module "@mui/material/styles" {
  interface Palette {
    neutral: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
    };

    neutral500: string;
    neutral400: string;
    neutral300: string;
    neutral200: string;
    neutral100: string;
    neutral50: string;

    primary300: string;
    primary200: string;
    primary50: string;

    success600: string;
    success400: string;
    success50: string;

    warning600: string;
    warning400: string;
    warning50: string;

    error600: string;
    error400: string;
    error50: string;

    black: string;
    white: string;
    disabled: string;

    surfaceDisabled: string;
    onSurfaceDisabled: string;
  }

  interface PaletteOptions {
    neutral?: {
      main: string;
      light: string;
      dark: string;
      contrastText: string;
    };

    neutral500?: string;
    neutral400?: string;
    neutral300?: string;
    neutral200?: string;
    neutral100?: string;
    neutral50?: string;

    primary300?: string;
    primary200?: string;
    primary50?: string;

    success600?: string;
    success400?: string;
    success50?: string;

    warning600?: string;
    warning400?: string;
    warning50?: string;

    error600?: string;
    error400?: string;
    error50?: string;

    black?: string;
    white?: string;
    disabled?: string;

    surfaceDisabled?: string;
    onSurfaceDisabled?: string;
  }
}

declare module "@mui/material" {
  interface Theme {
    palette: Palette;
  }

  interface ThemeOptions {
    palette?: PaletteOptions;
  }
}
