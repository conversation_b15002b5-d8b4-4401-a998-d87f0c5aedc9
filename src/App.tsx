import React from 'react';
import {Provider as StoreProvider} from 'react-redux';
import {ThemeProvider} from '@mui/material/styles';

import store from './toolkit/store';
import AppNavigation from './navigation/AppNavigation';
import {theme} from "./utils/theme";
import './i18n';
import {ToastProvider} from './hooks/toastProvider';
import './App.css';

const App = () => {
  return (
    <StoreProvider store={store}>
      <ThemeProvider theme={theme}>
        <ToastProvider>
          <AppNavigation/>
        </ToastProvider>
      </ThemeProvider>
    </StoreProvider>
  );
};

export default App;
