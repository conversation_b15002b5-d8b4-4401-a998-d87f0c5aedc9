{"Home": {"Home": "Home", "Featured Categories": "Featured Categories", "View All": "View All", "All": "All", "Subcategories": "Subcategories", "Shop by Category": "Shop by Category", "Stores Near Me": "Stores Near Me", "Deliver to": "Deliver to", "Search": "Search...", "Try Saying Something, We are Listening": "Try Saying Something, We are Listening", "Search Product List": {"No products available": "No products available", "No products available message": "Sorry, we couldn’t find any products for you at the moment"}, "Search Provider List": {"No providers available": "No Stores Available", "No providers available message": "Sorry, we couldn’t find any stores near you at the moment"}}, "WishList": {"Wishlist": "Wishlist", "Wishlisted": "Wishlisted", "Delete Item": "Delete Item", "Your Wishlist is empty": "Your Wishlist is empty", "Save your favorites in your wishlist. Review them anytime and easily add them to your cart.": "Save your favorites in your wishlist. Review them anytime and easily add them to your cart.", "Remove Wishlist": "Remove <PERSON>list", "Are you sure you would like to remove the store from wishlist?": "Are you sure you would like to remove the store from wishlist?", "No": "No", "Yes": "Yes", "Are you sure you want to remove this item from wishlist?": "Are you sure you want to remove this item from wishlist?"}, "Cart": {"Store Cart": "Store Cart", "Remove from Cart": "Remove from Cart", "Are you sure you want to remove this item from your cart?": "Are you sure you want to remove this item from your cart?", "Cart": "<PERSON><PERSON>", "Whitelisted Member": "Whitelisted Member", "Only whitelisted member can place order and checkout in the app": "Only whitelisted member can place order and checkout in the app", "Okay": "Okay", "Your Carts": "All Carts ({{count}})", "My Cart": "My Cart", "Items in cart": "Items in cart ({{count}})", "Total": "Total", "Total Amount": "Total: {{total}}", "View Cart": "View Cart", "Almost There": "Almost There", "Update your profile quickly to place order": "Update your profile quickly to place order", "Update Your Profile": "Update Your Profile", "Login account quickly to place order": "Login account quickly to place order", "Proceed with Login": "Proceed with <PERSON><PERSON>", "Customization for": "Customization for", "Maximum allowed quantity is": "Maximum allowed quantity is {{count}}", "Add More Items": "Add More Items", "Missing something?": "Missing something?", "Customise": "Customise", "Delivery Address": "Delivery Address", "Change": "Change", "Select Address": "Select Address", "View Delivery Options": "Proceed", "item": "item", "items": "items", "Add to cart": "Add to cart", "Product Details": "Product Details", "Select any": "Select any", "options": "options", "Veg": "Veg", "Non Veg": "Non-Veg", "Egg": "Egg", "Required": "Required", "Maximum allowed quantity": "Maximum allowed quantity is {{count}}", "All Options": "All\nOptions", "Edit": "Edit", "Qty": "Qty", "Add new customization": "Add new customization", "Remove Cart": "Remove <PERSON>", "OnSelectWarnings": {"30011": {"title": "Delivery Partners Are Not Available", "message": "We apologise for the inconvenience. There are currently no delivery partners available for this store", "firstButtonText": "Shop With Another Store", "secondButtonText": ""}, "40006": {"title": "Delivery Partners Are Not Available", "message": "We apologise for the inconvenience. There are currently no delivery partners available for this store", "firstButtonText": "Shop With Another Store", "secondButtonText": ""}, "40013": {"title": "Oops! You've reached the maximum order quantity for this store", "message": "Complete your order by removing items from this cart and placing the rest in a separate order", "firstButtonText": "Proceed With Quantity Update", "secondButtonText": "Back To Cart"}, "40009": {"title": "Oops! You've reached the maximum order quantity for this store", "message": "Complete your order by removing items from this cart and placing the rest in a separate order", "firstButtonText": "Back To Cart", "secondButtonText": ""}, "40004": {"title": "Payment Method Not Supported", "message": "The selected payment method is not available for this store. Please select a different payment method to complete your purchase", "firstButtonText": "Retry Payment", "secondButtonText": "Back To Cart"}, "40012": {"title": "Minimum Order Quantity Required", "message": "Almost there! Add a few more items to meet minimum order quantity", "firstButtonText": "Proceed With Quantity Update", "secondButtonText": "Add More Item"}, "30023": {"title": "Add a Little More to Your Cart", "message": "The total cart value is below the minimum required for this store. Please add more items to complete your order", "firstButtonText": "Proceed With Quantity Update", "secondButtonText": "Add More Item"}, "30004": {"title": "Item Not Found", "message": "Looks like the item is no longer available. Please try another store/product", "firstButtonText": "Explore The Store", "secondButtonText": "Back To Cart"}, "40002": {"title": "Item Quantity Unavailable", "message": "Some items are out of stock. Please reduce the quantity or choose another item", "firstButtonText": "Proceed Without Product", "secondButtonText": "Explore The Store"}, "30016": {"title": "Invalid Signature", "message": "Oops! Something went wrong. Please try another store or product", "firstButtonText": "Explore The Store", "secondButtonText": "Back To Home"}, "40000": {"title": "Business Error", "message": "Oops! Something went wrong. Please try another store or product", "firstButtonText": "Explore The Store", "secondButtonText": "Back To Home"}, "30022": {"title": "Request Timed Out", "message": "Oops! Something went wrong. Please try another store or product", "firstButtonText": "Explore The Store", "secondButtonText": "Back To Home"}, "30008": {"title": "Location Serviceability Error", "message": "Pickup not available for this location. Please try a different store", "firstButtonText": "Update Address", "secondButtonText": "Back To Home"}, "30017": {"title": "Merchant Unavailable", "message": "This store isn't delivering right now. Please try another one.", "firstButtonText": "Explore Other Stores", "secondButtonText": "Back To Home"}, "30021": {"title": "Merchant Inactive", "message": "This store isn't delivering right now. Please try another one.", "firstButtonText": "Explore Other Stores", "secondButtonText": "Back To Home"}, "30010": {"title": "Location Serviceability Error", "message": "This store is not serviceable at your location.", "firstButtonText": "Update Address", "secondButtonText": "Back To Home"}, "30009": {"title": "Location Serviceability Error", "message": "This store is not serviceable at your location.", "firstButtonText": "Update Address", "secondButtonText": "Back To Home"}, "10002": {"title": "Location Serviceability Error", "message": "This store is not serviceable at your location.", "firstButtonText": "Update Address", "secondButtonText": "Back To Home"}, "31002": {"title": "Order Validation Failure", "message": "Oops! Something went wrong. Please try another store or item.", "firstButtonText": "Explore The Stores", "secondButtonText": "Back To Home"}, "30001": {"title": "Provider Not Found", "message": "Oops! Something went wrong. Please try another store or item.", "firstButtonText": "Explore The Stores", "secondButtonText": "Back To Home"}, "30000": {"title": "Invalid Request", "message": "Oops! Something went wrong. Please try another store or item.", "firstButtonText": "Explore The Stores", "secondButtonText": "Back To Home"}, "31001": {"title": "Internal Error", "message": "Oops! Something went wrong. Please try another store or item.", "firstButtonText": "Explore The Stores", "secondButtonText": "Back To Home"}, "Price Update": {"title": "Price Update", "message": "An update from the seller that there is a change in the price of these products.", "firstButtonText": "Proceed With Quantity Update", "secondButtonText": "Back To Cart"}, "Store Not Available At Your Location": {"title": "Store Not Available At Your Location", "message": "The store is not available at your location. Please check other sellers or update your address.", "firstButtonText": "Back To Home", "secondButtonText": ""}, "Store Is Not Available Currently": {"title": "Store Is Not Available Currently", "message": "This store is not available at this moment. Meanwhile, you can explore other stores nearby!", "firstButtonText": "Back To Home", "secondButtonText": ""}}, "Are you sure you would like to remove the cart?": "Are you sure you would like to remove the cart?", "Outlet Details": {"Get Direction": "Get Direction", "Call Now": "Call Now", "Open now": "Open now"}, "FBProduct": {"Add": "Add", "Out of stock": "Out of stock", "Item is unavailable": "Item is unavailable", "Customizable": "Customizable"}, "Filter List": {"Filters": "Filters", "Clear all": "Clear all", "Close": "Close", "Apply": "Apply", "Draft": "Verifying Payment", "Failed": "Failed"}, "Payment": {"Select Payment Option": "Select Payment Option", "Cash on delivery": "Cash on delivery", "Prepaid": "Prepaid", "Proceed to Buy": "Proceed to Pay"}, "Reference": {"reference app": "Reference app", "Reference App Message": "This is a White Labelled Reference App. Orders cannot be placed", "Refer manual": "Refer to the user manual for more details", "ok": "Okay"}}, "Coupon": {"Invalid code": "Oops! The code you entered is invalid. Please double-check and try again!", "Limit exhausted": "Uh-oh! Looks like you've hit the usage limit! Take a breather and try again later, or give our support team a shout. They're great at unlocking things!", "Criteria not met": "Oops! It seems this coupon code is not valid. Please check and try again!", "Apply": "Apply", "Save upto": "Save upto ₹{{amount}} with this coupon code", "Percent message": "Get {{percent}}% Upto Rs.{{maximum}} Off", "Amount message": "Flat Rs.{{amount}} Off", "Percent delivery message": "Get {{percent}}% on delivery value with this coupon code", "Amount delivery message": "Get flat {{amount}} on delivery value with this coupon code", "Please enter valid coupon code": "Please enter valid coupon code", "Apply Coupon": "Apply Coupon", "Tap to Apply": "Tap to Apply", "Hide Details": "Hide Details", "View Details": "View Details", "Select": "Select", "Applicable on minimum order value": "Applicable on minimum order value of ₹{{total}}", "Applicable maximum times in a day": "Applicable maximum {{time}} times in a {{frequency}}", "Maximum discount": "Maximum discount of ₹{{amount}}", "Coupon valid till": "Coupon is valid till {{date}}", "Add items worth more to unlock": "Add items worth ₹{{amount}} more to unlock", "Applied": "Applied", "with this coupon code": "with this coupon code", "Continue with Savings": "Continue with Savings", "remove": "Remove", "with": "with", "View all coupons": "View all coupons", "You saved": "You saved", "Free delivery": "Free delivery", "Coupon Applied": "{{coupon}} Coupon Applied", "Promo code is invalid. Please try another one": "Promo code {{offerId}} is invalid. Please try another one", "Login to use coupon": "Login to use coupon", "View More": "View More", "Copy": "Copy", "Copied": "<PERSON>pied", "All Offers": "All Offers", "Applied coupon not available": "Sorry, this offer isn’t applicable right now. Check for other offers!"}, "Cart Items": {"Only Available Of Total": "Only {{quantity}} available instead of {{total}}. Update the quantity or switch to another provider.", "You are ordering from different store. Please check your order again": "You are ordering from different store. Please check your order again.", "You are ordering from different category. Please check your order again": "You are ordering from different category. Please check your order again."}, "Profile": {"Add Your Name": "Add Your Name", "Update Your Name": "Update Your Name", "Name is required": "Name is required", "Full Name": "Full Name", "Name can only contain letters and spaces": "Name can only contain letters and spaces", "Please enter valid email address": "Please enter valid email address", "Update": "Update", "Add": "Add", "Profile": "Profile", "My Profile": "My Profile", "Order History": "Order History", "Complaints": "<PERSON><PERSON><PERSON><PERSON>", "Language": "Language", "About": "About", "App version": "App version", "Open Source Libraries": "Open Source Libraries", "Logout": "Logout", "Login": "Sign In", "No data found": "No data found", "Are you sure you want to logout?": "Are you sure you want to logout", "Cancel": "Cancel", "Get Status": "Get Status", "Track": "Track Order", "Order placed on": "Order placed on", "Shipment Details": "Shipment Details", "Payment Methods": "Payment Methods", "Shipping Address": "Shipping Address", "Share Your Experience": "Share Your Experience", "Raise Issue": "Raise Issue", "Cancellable": "Cancellable", "Non-cancellable": "Non-cancellable", "Returnable": "Returnable", "Non-returnable": "Non-returnable", "Order status updated successfully": "Order status updated successfully", "Name": "Name", "Email": "Email", "Mobile": "Mobile", "Verified": "Verified", "Account Settings": "Account <PERSON><PERSON>", "In order to delete your account please send an <NAME_EMAIL> with your account details": "In order to delete your account please send an <NAME_EMAIL> with your account details", "Delete Account": "Delete Account", "I am using different account": "I am using different account", "This app is not working properly": "This app is not working properly", "Other": "Other", "Account Deletion": "Account Deletion", "You have requested the deletion of your account": "You have requested the deletion of your account", "Account Deletion Description": "Please note that the account will get deleted. By proceeding with this request, you understand and acknowledge that information associated with your account cannot be retrieved by you once it has been deleted. Please refer to our privacy policy for more information on data deletion.", "Delete my Account": "Delete my Account", "Back to Profile": "Back to Profile", "Okay": "Okay", "Verify": "Verify", "Verify Email": "<PERSON><PERSON><PERSON>", "Email send": "Email has been sent to your email address. Please click on the link to verify your account", "Refresh": "Refresh", "Support Email ID": "Support Email ID", "Legal and Policies": "Legal and Policies", "Privacy Policy": "Privacy Policy", "Terms & Condition": "Terms & Condition"}, "Choose Language": {"Hindi": "हिंदी", "English": "English", "Marathi": "मराठी", "Tamil": "தமிழ்", "Bengali": "বাংলা", "Choose Language": "Choose Language", "Select a language": "Select a language"}, "Find Location": {"Finding Your Location": "Finding Your Location", "Location Permission Denied": "Location Permission Denied", "Location Permission Help Message": "Getting your precise location would help setting the precise delivery location.", "Location Permission Denied Message": "You have denied the location permission. Please enable it from the ‘Settings’ section of your phone.", "Location Disabled": "Your location permissions are disabled. Please enable it to proceed to address addition", "Cancel": "Cancel", "Setting": "Go to Settings", "Enable": "Enable", "Ok": "Ok", "Fetching Details": "Fetching Details", "Please give us a few seconds to grab your details": "Please give us a few seconds to grab your details", "Something wrong at end": "Something is wrong on our end", "Something wrong message": "While we are working on the issue, please try adding your address later again"}, "Address List": {"Delivery Address": "Delivery Address", "Add Address": "Add Address", "Add new address": "Add new address", "Select a Delivery Address": "Select a Delivery Address", "Saved Addresses": "Saved Addresses", "No Saved Address": "No Saved Address", "No address available": "No address available"}, "Select Location": {"Uh-oh! Cannot proceed without your location": "Uh-oh! Cannot proceed without your location", "Please select delivery location to continue": "Please select delivery location to continue", "Please select an address to continue": "Please select an address to continue", "Select Delivery Location": "Select Delivery Location", "Select Address": "Select Address"}, "Address Form": {"Add Address": "Add Address", "Update Delivery Address": "Update Delivery Address", "Name": "Receiver Name", "Email": "Email Address", "Mobile Number": "Receiver Number", "Building": "Building", "Street": "Street", "Pin Code": "Pin Code", "City": "City", "State": "State", "Address Type": "Address Type", "Save": "Save", "Line 1": "Flat/ House no/ Floor/ Building", "Line 1 placeholder": "Flat/ House no/ Floor/ Building", "Line 2": "Nearby landmark (optional)", "Line 2 placeholder": "Nearby landmark (optional)", "Street placeholder": "my street name", "Please select a valid location": "Please enter the location manually by clicking the search button", "Your delivery address has been added successfully": "Your delivery address has been added successfully", "Your delivery address has been updated successfully": "Your delivery address has been updated successfully"}, "Categories": {"Fashion": "Fashion", "Grocery": "Grocery", "Electronics": "Electronics", "Food & Beverage": "Food & Beverage", "Home & Decor": "Home & Decor", "Health & Wellness": "Health & Wellness", "Beauty & Personal Care": "Beauty & Personal Care", "Agriculture": "Agriculture"}, "Stores Near me": {"Stores Near me": "Stores Near me"}, "Stores Info": {"Legal Name": "Legal Name", "Seller Network Partner": "Seller Network Partner", "GST Number": "GST Number", "FSSAI Lic No": "FSSAI Lic No", "Mappls": "Mappls", "Maps": "Maps", "Photo": "Photo"}, "Empty Cart": {"Your Cart is Empty": "Your Cart is Empty", "It seems you haven’t added any products in your cart": "It seems you haven’t added any products in your cart"}, "Page": {", Go To Cart": ", Go To Cart", "Item Added": "<PERSON>em Added", "Items Added": "Items Added"}, "Fulfillment": {"price": "Price", "with": "with", "Total": "Total", "Delivery": "Delivery", "Address": "Address", "Receiver": "Receiver", "Details": "Details", "Total items": "Total items", "Order Details": "Order Details", "You saved on delivery": "You saved ₹{{amount}} on delivery", "saved on the total": "saved on the total", "Discount": "Discount", "Method": "Method", "Choose delivery/pickup": "Choose delivery/pickup", "Proceed to Pay": "Proceed to Pay", "Item Total": "Item Total", "Items Total": "Items Total", "Delivery Method": "Delivery Method", "To Pay": "To Pay", "Qty": "Qty", "Items": "Items", "Item": "<PERSON><PERSON>", "Please try ordering from another store or try again later": "Please try ordering from another store or try again later", "Something went wrong. Please try another store or product": "Something went wrong. Please try another store or product", "Explore Other Stores": "Explore Other Stores", "Fulfilment no": "{{current}} of {{total}}", "Delivered Today by": "Delivery Today by {{time}}", "Delivered In": "Delivery in", "Delivered by": "Delivery by", "Self pickup by": "Self-Pickup by {{time}}", "minutes": "minutes", "Seller Fee": "<PERSON><PERSON>", "Delivery Fee": "Delivery Fee", "Platform Fee": "Platform Fee", "Taxes": "Taxes", "Convenience Fee": "Convenience Fee", "Service Charge": "Service Charge", "Packaging Charges": "Packaging Charges", "Seller Discount": "<PERSON><PERSON> Discount", "Saved": "Saved", "DigiHaat Discount": "DigiHaat Discount", "Base Fee": "Base Fee", "SNP Delivery Fees Discount": "SNP Delivery Fees Discount", "Item Taxes": "Item Taxes", "Platform Fees Tax": "Platform Fees Tax", "Save": "Save", "Order Total": "Order Total", "Errors": {"30011": "Delivery Partners are not available at the moment. Please try again later!", "30017": "The store is not delivering at the moment, please try another store.", "30004": "Something went wrong. Please try another store/product.", "40002": "Something went wrong. Please try another store/product.", "30010": "This store is not serviceable at your location", "30016": "Something went wrong. Please try another store/product.", "30009": "This store is not serviceable at your location", "30021": "The store is not delivering at the moment, please try another store.", "30000": "We are unable to confirm the order at this moment", "31001": "We are unable to confirm the order at this moment", "40009": "Maximum order quantity in the cart exceeded. ", "30018": "Something went wrong. Please try another store/product.", "40000": "Something went wrong. Please try another store/product.", "30023": "Please add more items to place the order", "50001": "Cancellation is not allowed for this order", "30019": "We are unable to confirm the order at this moment", "30022": "Something went wrong. Please try another store/product.", "65001": "We are unable to confirm the order at this moment", "40008": "Something went wrong. Please try another store/product.", "50002": "We are unable to confirm the order at this moment", "40012": "Please add more items to meet the minimum order quantity.", "30008": "Store is currently not accepting any orders. Please try again later!", "10002": "This store is not serviceable at your location", "40006": "Delivery Partners are not available at the moment. Please try again later!", "31002": "We are not able to move forward with this order, please try another store or item!", "30001": "We are not able to move forward with this order, please try another store or item!", "40004": "Payment method not supported. Please use a different one."}}, "Domains": {"All": "All", "Featured Categories": "Featured Categories", "Categories": "Categories", "Fashion": "Fashion", "Grocery": "Grocery", "Electronics": "Electronics", "Food & Beverage": "Food", "Home & Decor": "Home & Decor", "Health & Wellness": "Health & Wellness", "Beauty & Personal Care": "Beauty", "Agriculture": "Agriculture", "Appliances": "Appliances"}, "Featured Categories": {"All": "All", "Featured Categories": "Featured Categories", "Categories": "Categories", "Fashion": "Fashion", "Grocery": "Grocery", "Electronics": "Electronics", "Food & Beverage": "Food & Beverage", "F&B": "Food & Beverage", "Home & Decor": "Home & Decor", "Health & Wellness": "Health & Wellness", "Beauty & Personal Care": "Beauty & Personal Care", "BPC": "Beauty & Personal Care", "Agriculture": "Agriculture", "Appliances": "Appliances"}, "Product SubCategories": {"Search": "Search", "Sort": "Sort", "Filter": "Filter", "Shop by Category": "Find Stores by category", "Shop By Category": "Find Stores by category", "Fruits and Vegetables": "Fruits and Vegetables", "Masala & Seasoning": "Masala & Seasoning", "Oil & Ghee": "Oil & Ghee", "Eggs, Meat & Fish": "Eggs, Meat & Fish", "Cleaning & Household": "Cleaning & Household", "Bakery, Cakes & Dairy": "Bakery, Cakes & Dairy", "Pet Care": "Pet Care", "Dairy and Cheese": "Dairy and Cheese", "Snacks, Dry Fruits, Nuts": "Snacks, Dry Fruits, Nuts", "Pasta, Soup and Noodles": "Pasta, Soup and Noodles", "Cereals and Breakfast": "Cereals and Breakfast", "Sauces, Spreads and Dips": "Sauces, Spreads and Dips", "Chocolates and Biscuits": "Chocolates and Biscuits", "Cooking and Baking Needs": "Cooking and Baking Needs", "Tinned and Processed Food": "Tinned and Processed Food", "Atta, Flours and Sooji": "Atta, Flours and Sooji", "Rice and Rice Products": "Rice and Rice Products", "Dals and Pulses": "Dals and Pulses", "Salt, Sugar and Jaggery": "Salt, Sugar and Jaggery", "Energy and Soft Drinks": "Energy and Soft Drinks", "Water": "Water", "Tea and Coffee": "Tea and Coffee", "Fruit Juices and Fruit Drinks": "Fruit Juices and Fruit Drinks", "Snacks and Namkeen": "Snacks and Namkeen", "Ready to Cook and Eat": "Ready to Cook and Eat", "Pickles and Chutney": "Pickles and Chutney", "Indian Sweets": "Indian Sweets", "Frozen Vegetables": "Frozen Vegetables", "Frozen Snacks": "Frozen Snacks", "Gift Voucher": "Gift Voucher", "Air Purifier": "Air Purifier", "Dehumidifier": "Dehumidifier", "Humidifier": "Humidifier", "Air Cleaner Accessories": "Air Cleaner Accessories", "Air Conditioner": "Air Conditioner", "Air Conditioner Accessories": "Air Conditioner Accessories", "Air Cooler": "Air Cooler", "Trimmer": "Trimmer", "Shaver": "<PERSON><PERSON><PERSON>", "Epilator": "Epilator", "Hair Straightener": "Hair Straightener", "Hair Dryer": "Hair Dryer", "Hair Curler": "<PERSON>urler", "Hair Crimper": "Hair Crimper", "Electric Brush": "Electric Brush", "Electric Iron": "Electric Iron", "Electric Sewing Machine": "Electric Sewing Machine", "Water Heater": "Water Heater", "Heater Cables": "Heater Cables", "Air Heater": "Air Heater", "Coffee Maker": "Coffee Maker", "Beverage Maker": "Beverage Maker", "Roti Maker": "Roti <PERSON>", "Induction Cooktop": "Induction Cooktop", "Sandwich Maker": "Sandwich Maker", "Electric Cooker": "Electric Cooker", "Electric Kettle": "Electric Kettle", "Microwave Oven": "Microwave Oven", "OTG": "OTG", "Toaster": "Toaster", "Electric Air Fryer": "Electric Air Fryer", "Cooking Appliance Accessories ": "Cooking Appliance Accessories ", "Coffee Grinder": "Coffee Grinder", "Food Processor": "Food Processor", "Pasta Maker": "Pasta Maker", "Food Processor Accessories": "Food Processor Accessories", "Blender": "<PERSON><PERSON>der", "Juicer": "Juicer", "Mixer Grinder": "Mixer <PERSON>", "Wet Grinder": "Wet Grinder", "Dishwasher": "Dishwasher", "Dishwasher Accessories": "Dishwasher Accessories", "Electric Chimney": "Electric Chimney", "Kitchen Accessories": "Kitchen Accessories", "Freezer": "<PERSON><PERSON>", "Refrigerator": "Refrigerator", "Refrigerator Accessories": "Refrigerator Accessories", "Vacuum Cleaner": "Vacuum Cleaner", "Vacuum Cleaner Parts and Accessories": "Vacuum Cleaner Parts and Accessories", "Washing Machine": "Washing Machine", "Washing Machine Accessories": "Washing Machine Accessories", "Water Purifier": "Water Purifier", "Water Cooler": "Water Cooler", "Water Dispenser": "Water Dispenser", "Water Purifier Service Kit": "Water Purifier Service Kit", "Water Purifier Filter": "Water Purifier Filter", "Water Purifier Candle": "Water Purifier Candle", "Water Purifier Pipe": "Water Purifier Pipe", "Water Purifier Accessories": "Water Purifier Accessories", "Water Cooler Accessories": "Water Cooler Accessories", "Inverter": "Inverter", "Inverter Batteries": "Inverter Batteries", "Battery tray": "Battery tray", "Voltage Stabilizer": "Voltage Stabilizer", "Fragrance": "Fragrance", "Bath Soaps and Gels": "Bath Soaps and Gels", "Hair Oils, Care, and Styling": "Hair Oils, Care, and Styling", "Shampoos and Conditioners": "Shampoos and Conditioners", "Shaving and Grooming": "Shaving and Grooming", "Beard Care and Tools": "Beard Care and Tools", "Grooming Tools and Accessories": "Grooming Tools and Accessories", "Makeup - Nail Care": "Makeup - Nail <PERSON>", "Makeup - Eyes": "Makeup - Eyes", "Makeup - Face": "Makeup - Face", "Makeup - Lips": "Makeup - Lips", "Makeup - Body": "Makeup - Body", "Makeup - Remover": "Makeup - Remover", "Makeup - Sets and Kits": "Makeup - Sets and Kits", "Makeup - Tools and Brushes": "Makeup - Tools and Brushes", "Makeup - Kits and Combos": "Makeup - Kits and Combos", "Skin Care - Face Cleansers": "Skin Care - Face Cleansers", "Skin Care - Hand and Feet": "Skin Care - Hand and Feet", "Body Care - Cleansers": "Body Care - Cleansers", "Body Care - Moisturizers": "Body Care - Moisturizers", "Body Care - Loofah and Other Tools": "Body Care - Loofah and Other Tools", "Body Care - Bath Salt and Additives": "Body Care - Bath Salt and Additives", "Hair Care - Shampoo, Oils, Conditioners": "Hair Care - Shampoo, Oils, Conditioners", "Skin Care - Lotions, Moisturisers, and Creams": "Skin Care - Lotions, Moisturisers, and Creams", "Skin Care - Oils and Serums": "Skin Care - Oils and Serums", "Shirts": "Shirts", "T Shirts": "T Shirts", "Sweatshirts": "Sweatshirts", "Kurtas & Kurta Sets": "Kurtas & Kurta Sets", "Jackets & Coats": "Jackets & Coats", "Sweaters": "Sweaters", "Suits": "Suits", "Sherwanis": "<PERSON><PERSON><PERSON><PERSON>", "Track Shirts": "Track Shirts", "Track Suits": "Track Suits", "Unstitched Fabrics": "Unstitched Fabrics", "Dresses": "Dresses", "Tops": "Tops", "Trousers": "Trousers", "Capris": "<PERSON><PERSON>", "Coordinates": "Coordinates", "Playsuits": "Playsuits", "Jumpsuits": "Jumpsuits", "Shrugs & Blouses": "Shrugs & Blouses", "Blazers & Waistcoats": "Blazers & Waistcoats", "Tights, Leggings & Jeggings": "Tights, Leggings & Jeggings", "Track Pants": "Track Pants", "Jeans": "<PERSON><PERSON>", "Shorts": "Shorts", "Joggers": "<PERSON><PERSON><PERSON>", "Dhotis & Dhoti Pants": "Dhotis & Dhoti Pants", "Churidars": "Churidars", "Salwars": "<PERSON><PERSON><PERSON>", "Dungarees & Jumpsuits": "Dungarees & Jumpsuits", "Skirts": "Skirts", "Clothing Sets": "Clothing Sets", "Belts": "Belts", "Caps & Hats": "Caps & Hats", "Kurtis, Tunics": "Kurtis, Tunics", "Sarees": "Sa<PERSON>s", "Ethnic Wear": "Ethnic Wear", "Palazzos": "Palazzos", "Dress Materials": "Dress <PERSON>", "Lehenga Cholis": "Le<PERSON><PERSON>", "Dupattas & Shawls": "Dupattas & Shawls", "Burqas & Hijabs": "Burqas & Hijabs", "Blouses": "Blouses", "Blouse Pieces": "Blouse Pieces", "Briefs": "Briefs", "Boxers": "Boxers", "Vests": "Vests", "Robes": "Robes", "Night Suits": "Night Suits", "Thermal Wear": "Thermal Wear", "Swim Bottoms": "Swim Bottoms", "Swimwear": "Swimwear", "Bra": "Bra", "Shapewear": "Shapewear", "Sleepwear & Loungewear": "Sleepwear & Loungewear", "Camisoles": "Camisoles", "Lingerie Sets & Accessories": "Lingerie Sets & Accessories", "Bath Robes": "Bath Robes", "Towels": "<PERSON><PERSON><PERSON>", "Pyjamas": "P<PERSON><PERSON><PERSON>", "Party Wear": "Party Wear", "Innerwear & Sleepwear": "Innerwear & Sleepwear", "Nightwear & Loungewear": "Nightwear & Loungewear", "Watches": "Watches", "Gloves": "Gloves", "Socks": "Socks", "Stockings": "Stockings", "Laces": "Laces", "Soles & Charms": "Soles & Charms", "Shoe Racks & Organisers": "Shoe Racks & Organisers", "Shoe Care - Accessories": "Shoe Care - Accessories", "Flip-Flops & Flats": "Flip-Flops & Flats", "Sandals & Floaters": "Sandals & Floaters", "Backpacks": "Backpacks", "Handbags": "Handbags", "Trolley, Luggage & Suitcases": "Trolley, Luggage & Suitcases", "Formal Shoes": "Formal Shoes", "Casual Shoes": "Casual Shoes", "Sports Shoes": "Sports Shoes", "Outdoor Shoes": "Outdoor Shoes", "Work & Safety Shoes": "Work & Safety Shoes", "Ethnic Shoes": "Ethnic Shoes", "Boots": "Boots", "Heels": "<PERSON><PERSON>", "Contact Lenses": "Contact Lenses", "Eye Glasses": "Eye Glasses", "Eye Glass Frames": "Eye Glass Frames", "Sunglasses": "Sunglasses", "Contact Lens Cases": "Contact Lens Cases", "Contact Lens Solutions": "Contact Lens Solutions", "Contact Lens Tweezers": "Contact Lens Tweezers", "Eyeglasses Pouches & Cases": "Eyeglasses Pouches & Cases", "Microfiber Wipes": "Microfiber Wipes", "Eyewear Slings": "Eyewear Slings", "Bracelets": "Bracelets", "Chains": "Chains", "Mangalsutra": "Mangalsutra", "Anklets": "Anklets", "Bangles & Bracelets": "Bangles & Bracelets", "Necklaces": "Necklaces", "Earrings": "Earrings", "Jewellery Sets": "Jewellery Sets", "Nosepins & Noserings": "Nosepins & Noserings", "Pendants": "Pendants", "Rings": "Rings", "Toe Rings": "<PERSON><PERSON>", "Gold Coins": "Gold Coins", "Brooch": "<PERSON>roo<PERSON>", "Home Decor": "Home Decor", "Furniture": "Furniture", "Home Furnishing - Bedding and Linen": "Home Furnishing - Bedding and Linen", "Cleaning Supplies": "Cleaning Supplies", "Electricals": "Electricals", "Bathroom and Kitchen fixtures": "Bathroom and Kitchen fixtures", "Garden & Outdoor": "Garden & Outdoor", "Sports and Fitness Equipment": "Sports and Fitness Equipment", "Cookware": "Cookware", "Serveware": "Serveware", "Kitchen Storage and Containers": "Kitchen Storage and Containers", "Kitchen Tools": "Kitchen Tools", "Closet/Laundry/Shoe Organization": "Closet/Laundry/Shoe Organization", "Toys and Games": "Toys and Games", "Stationery": "Stationery", "Mobile Phone": "Mobile Phone", "Smart Watch": "Smart Watch", "Headset": "Headset", "Laptop": "Laptop", "Desktop": "Desktop", "Tablet": "Tablet", "Keyboard": "Keyboard", "Monitor": "Monitor", "Mouse": "Mouse", "Power Bank": "Power Bank", "Earphone": "Earphone", "True Wireless Stereo (TWS)": "True Wireless Stereo (TWS)", "Adapter": "Adapter", "Cable": "Cable", "Extension Cord": "Extension Cord", "Audio Accessories": "Audio Accessories", "Home Audio": "Home Audio", "Microphone": "Microphone", "Speaker": "Speaker", "Vehicle Audio": "Vehicle Audio", "Camcorder": "Camcorder", "Camera": "Camera", "Camera Bag": "Camera Bag", "Batteries": "Batteries", "Charger": "Charger", "Camera Lens": "Camera Lens", "Photo Printer": "Photo Printer", "Tripod": "<PERSON><PERSON>", "Camera Accessories": "Camera Accessories", "UPS": "UPS", "Networking Device": "Networking Device", "Printer": "Printer", "Printer Accessories": "Printer Accessories", "Storage Drive": "Storage Drive", "Pen Drive": "Pen Drive", "Memory Card": "Memory Card", "Computer Component": "Computer Component", "Cooling Pad": "Cooling Pad", "Docking Station": "Docking Station", "Keyboard Guard": "Keyboard Guard", "Laptop Skin": "Laptop Skin", "Laptop Stand": "Laptop Stand", "Mousepad": "Mousepad", "Laptop Bag": "Laptop Bag", "Screen Protector": "Screen Protector", "Computer Accessories": "Computer Accessories", "Computer Software": "Computer Software", "Ebook Reader": "Ebook Reader", "Tablet Accessories ": "Tablet Accessories ", "Gaming Controller": "Gaming Controller", "Gaming Chair": "Gaming Chair", "Gaming Accessories": "Gaming Accessories", "Gaming Console": "Gaming Console", "Video Games": "Video Games", "Mobile Cover": "Mobile Cover", "Mobile Mount": "Mobile Mount", "Mobile Screen Guard": "Mobile Screen Guard", "Selfie Stick": "<PERSON><PERSON>", "Mobile Skin Sticker": "Mobile Skin Sticker", "Biometrics": "Biometrics", "Home Alarm": "Home Alarm", "Home Automation": "Home Automation", "Smart Switch": "Smart Switch", "Smart Lighting": "Smart Lighting", "Home Safe": "Home Safe", "Intercom": "Intercom", "Sensor": "Sensor", "Smart TV": "Smart TV", "Standard TV": "Standard TV", "TV Mount": "TV Mount", "Remote": "Remote", "Streaming Device": "Streaming Device", "TV Accessories": "TV Accessories", "Virtual Reality Headset": "Virtual Reality Headset", "3D Glasses": "3D Glasses", "3D Modulator": "3D Modulator", "Projector": "Projector", "Projector Screen": "Projector Screen", "Projector Mount": "Projector Mount", "Projector Accessories": "Projector Accessories", "TV Part": "TV Part", "TV Remote": "TV Remote", "Set Top Box": "Set Top Box", "TV Stand": "TV Stand", "Video Player": "Video Player", "Digital Photo Frame": "Digital Photo Frame", "Home Theatre Projector": "Home Theatre Projector", "Video Player Accessories": "Video Player Accessories", "Smart Band": "Smart Band", "Smart Glasses": "Smart Glasses", "Watch Strap Band": "Watch Strap Band", "Wearable Accessories": "Wearable Accessories", "Pain Relief": "Pain Relief", "Nutrition and Fitness Supplements": "Nutrition and Fitness Supplements", "Speciality Care": "Speciality Care", "Covid Essentials": "Covid <PERSON>s", "Diabetes Control": "Diabetes Control", "Healthcare & Fitness Devices": "Healthcare & Fitness Devices", "Ayurvedic": "Ayurvedic", "Homeopathy": "Homeopathy", "Unani and Siddha": "<PERSON><PERSON> and <PERSON><PERSON>", "Elder Care": "Elder Care", "Baby Care": "Baby Care", "Orthopaedic Care": "Orthopaedic Care", "Mobility Aids": "Mobility Aids", "Medicated Hair Care": "Medicated Hair Care", "Medicated Skin Care": "Medicated Skin Care", "Speciality Face Cleansers": "Speciality Face Cleansers", "Gastric Care": "Gastric Care", "ENT Care": "ENT Care", "Eye Care": "Eye Care", "Cold and Cough": "Cold and Cough", "Sexual Wellness": "Sexual Wellness", "Feminine Care": "Feminine Care", "Maternity Care": "Maternity Care", "Nursing and Feeding": "Nursing and Feeding", "Hand Wash": "<PERSON>", "Sanitizers": "Sanitizers", "Baby Care - Wipes and Buds": "Baby Care - Wipes and Buds", "Baby Care - Rash Creams": "Baby Care - Rash Creams", "Baby Care - Diapers and Accessories": "Baby Care - Diapers and Accessories", "Health and Safety": "Health and Safety", "Oral Care": "Oral Care", "Contraceptives": "Contraceptives", "Breathe Easy": "Breathe Easy", "Health Foods and Drinks": "Health Foods and Drinks", "Wound Care and Dressings": "Wound Care and Dressings", "Surgicals": "Surgicals", "Mental Wellness": "Mental Wellness", "Seed": "Seed", "Pesticide": "Pesticide", "Fertilizer": "Fertilizer", "Organic Crop Protection": "Organic Crop Protection", "Organic Crop Nutrition": "Organic Crop Nutrition", "Tools and Machinery": "Tools and Machinery", "Cattle Feed": "Cattle Feed"}, "SubCategories": {"Shop By Category": "Find Stores by category", "Fruits and Vegetables": "Fruits and Vegetables", "Masala & Seasoning": "Masala & Seasoning", "Oil & Ghee": "Oil & Ghee", "Gourmet & World Foods": "Gourmet & World Foods", "Foodgrains": "Foodgrains", "Eggs, Meat & Fish": "Eggs, Meat & Fish", "Cleaning & Household": "Cleaning & Household", "Beverages": "Beverages"}, "Store": {"km": "{{distance}} km", "day": "{{time}} day", "days": "{{time}} days", "more than 7 days": "7-10 Days", "hour": "{{time}} hr", "hours": "{{time}} hrs", "minute": "{{time}} min", "minutes": "{{time}} mins", "Opens at": "Opens: {{time}}", "Opens": "Opens: {{time}}", "Outlets": "Outlets", "Menu": "<PERSON><PERSON>"}, "Network Banner": {"You are offline, please verify your internet connection": "You are offline, please verify your internet connection"}, "Variations": {"Available Options": "Select {{groupName}}", "Size Guide": "Size Chart"}, "Complaint": {"Issue Id": "Issue Id", "Issue Raised On": "Issue Raised On", "View Summary": "View Summary", "Complaint raised successfully": "<PERSON><PERSON><PERSON><PERSON> raised successfully"}, "Complaint Details": {"Complaint Details": "<PERSON><PERSON><PERSON><PERSON>", "Updated by": "Updated by", "Issue Id": "Issue Id", "Level": "Level", "Issue": "Issue", "Order Id": "Order Id", "Issue Raised On": "Issue Raised On", "QTY": "QTY", "Expected Response Time": "Expected Response Time", "Expected Resolution Time": "Expected Resolution Time", "Respondent Details": "Respondent Details", "Phone": "Phone", "Email": "Email", "Complaint status updated successfully": "Complaint status updated successfully", "Something went wrong, issue status cannot be fetched": "Something went wrong!, issue status cannot be fetched", "Close": "Close", "Escalate": "Escalate", "Opened on": "Opened on", "Closed on": "Closed on"}, "Close Form": {"Close": "Close", "Choose Rating": "<PERSON><PERSON>ing"}, "Escalate Form": {"Escalate": "Escalate"}, "Cancel Order": {"Cancel Order": "Cancel Order", "Cancel": "Cancel", "Complete order": "Complete order", "Select reason": "Select reason", "Are You sure you would like to cancel this order?": "Are You sure you would like to cancel this order?", "Something went wrong, product status cannot be updated": "Something went wrong, product status cannot be updated", "Yes": "Yes", "No": "No"}, "Payment Methods": {"Cash On Delivery": "Cash On Delivery", "Prepaid": "Prepaid", "Billed Address": "Billed Address", "Customer Details": "Customer Details", "Order Number": "Order Number", "Customer Name": "Customer Name", "Phone Number": "Phone Number"}, "Return Items": {"Return Items": "Return Items", "Confirm": "Confirm", "Are you sure you would like to return the items?": "Are you sure you would like to return the items?", "Something went wrong, please try again": "Something went wrong, please try again", "Something went wrong, product status cannot be updated": "Something went wrong, product status cannot be updated", "Cannot proceed with you request now. Please try again": "Cannot proceed with you request now. Please try again"}, "Return Item": {"Return Item": "Return Item", "Select reason": "Select reason", "Upload Images": "Upload Images", "Browse": "Browse"}, "Item Details": {"Items will be delivered by": "Items will be delivered by", "Return Details": "Return Details", "Return initiated on": "Return initiated on", "Cancel Details": "Cancel Details", "Cancelled on": "Cancelled on"}, "Return Reason": {"Buyer does not want product any more": "Buyer does not want product any more", "Product available at lower than order price": "Product available at lower than order price", "Product damaged or not in usable state": "Product damaged or not in usable state", "Product is of incorrect quantity or size": "Product is of incorrect quantity or size", "Product delivered is different from what was shown and ordered": "Product delivered is different from what was shown and ordered"}, "Cancellation Reason": {"Price of one or more items have changed due to which buyer was asked to make additional payment": "Price of one or more items have changed due to which buyer was asked to make additional payment", "Product available at lower than order price": "Product available at lower than order price", "Order not received as per buyer app TAT SLA": "Order not received as per buyer app TAT SLA", "Wrong product delivered": "Wrong product delivered", "Buyer wants to modify address / other order details": "Buyer wants to modify address / other order details", "One or more items in the Order not available": "One or more items in the Order not available", "Merchant rejected the order": "Merchant rejected the order", "Buyer not found or cannot be contacted": "Buyer not found or cannot be contacted", "Buyer does not want product any more": "Buyer does not want product any more", "Buyer refused to accept delivery": "Buyer refused to accept delivery", "Address not found": "Address not found", "Buyer not available at location": "Buyer not available at location", "Delivery pin code not serviceable": "Delivery pin code not serviceable", "Pickup pin code not serviceable": "Pickup pin code not serviceable"}, "Non cancelled order": {"Order completed on": "Order completed on"}, "Order Meta": {"Order Details": "Order Details", "Order Number": "Order Number", "Payment mode": "Payment mode", "Date": "Date", "Phone Number": "Phone Number", "Delivery Address": "Delivery Address", "Refund Initiated": "Refund Initiated"}, "Product Summary": {"Order Summary": "Order Summary", "Your Items": "Your Items", "Order Total": "Order Total", "Add Item Total": "Add Item Total - {{total}}", "Update Item Total": "Update Item Total - {{total}}", "Item added to cart successfully": "Item added to cart successfully.", "Item quantity updated in your cart": "Item quantity updated in your cart", "The maximum available quantity for item is already in your cart": "The maximum available quantity for item is already in your cart"}, "Provider Details": {"Order Cancelled": "Order Cancelled", "Payment failed": "Payment failed", "Download Invoice": "Download Invoice", "Go Home": "Go Home", "Incorrect specifications or malformed request": "Issue in link -  contact seller or report at - {{email}}", "This store does not service your location": "This seller is not currently available. Please check later", "This store/seller type is not supported by DigiHaat Application, explore other buyer apps.": "This store/seller type is not supported by DigiHaat Application, explore other buyer apps."}, "Raise Issue": {"Issues": "Issues", "Issue": "Issue", "Raise an Issue": "Raise an Issue", "Choose items that had a problem": "Choose items that had a problem", "Images (Maximum 4)": "Images (Maximum 4)", "Browse": "Browse", "Confirm": "Confirm", "Cancel": "Cancel", "File size exceeds 2MB. Please select a smaller file": "File size exceeds 2MB. Please select a smaller file", "Please attach image": "Please select at least 1 image", "Maximum 4 images allowed": "Maximum 4 images allowed", "For any issues, kindly reach out to us at": "For any issues, kindly reach out to us at"}, "Return Details": {"Return Details": "Return Details", "Items Returned": "{{count}} Item(s) Returned", "Return On": "Returned On"}, "Return Summary": {"Items": "Items", "Qty": "Qty", "Reason": "Reason", "Click to View Images": "Click to View Images", "Order Total": "Order Total"}, "Shipment Details": {"Shipment Details": "Shipment Details", "Items Arriving": "{{count}} Item(s) Arriving", "Items Delivered": "{{count}} Item(s) Delivered", "Arriving On": "Arriving On", "Delivered On": "Delivered On"}, "Orders": {"Orders": "Orders", "No data found": "No data found", "Search": "Search", "Filter": "Filter", "Filter List": {"Filters": "Filters", "Clear all": "Clear all", "Close": "Close", "Apply": "Apply", "Order Type": "Order Type", "Ongoing": "Ongoing", "Completed": "Completed", "Cancelled": "Cancelled", "Processing": "Processing", "Draft": "Verifying Payment", "Failed": "Failed"}, "Price (Low to High)": "Price (Low to High)", "Price (High to Low)": "Price (High to Low)", "Relevance": "Relevance", "Cannot get status for this product": "Cannot get status for this product", "Tracking information is not provided by the provider": "Tracking information is not provided by the provider", "Processing": "Processing", "Processing Payment Message": "We're verifying your payment. Hang tight! We'll update you once it's confirmed."}, "Global": {"Details not available": "Store Details not available", "Cannot fetch details for this product. Please try again": "Cannot fetch details for this product. Please try again", "Please select payment": "Please select payment", "Please select address": "Please select address", "Cannot fetch details for some product, those products will be ignored": "Cannot fetch details for some product, those products will be ignored", "Session Expired": "Session Expired", "Session expired, please login again to continue": "Oops! Your session timed out. Please log in again to continue.", "Logout": "Logout", "Version mismatch": "Version mismatch", "Please login to proceed with this request": "Please login to proceed with this request", "Please upgrade your application to the latest version": "New version available! Update now for new features and improvements", "Internet connection not available. Please check internet connection": "It looks like you're offline. Please check your internet and try again.", "Something went wrong, please try again after some time": "Uh-oh! We're having trouble processing your request. Please try again.", "Unable to fetch details. Please try again": "Uh-oh! The request took longer than expected. Please try again.", "Some orders are not initialized": "Some orders are not initialized", "Available Cities": "Available Cities", "The app is only for Delhi city": "The app is only for Delhi city", "Open with": "Open with", "Please log in to your account and set your preferences": "Please log in to your account and set your preferences", "Please set your preferences": "Please set your preferences", "and": " and ", "Read Less": "Read Less", "Read More": "Read More", "Sorry its not you its us": "Sorry! it’s not you its us", "Something went wrong! Please refresh the page and try again": "Something went wrong! Please refresh the page and try again", "A Fresh Look, Just for You": "A Fresh Look, Just for You !", "App logo change message": "Update now for a sleek new app icon. The app will close after updating. Relaunch to continue.", "Keep The Old Look": "Keep The Old Look", "Update": "Update"}, "Seller QR": {"ONDC QR se Bharat Khulega": "ONDC QR se Bharat Khulega"}, "Fulfilment Status": {"Return_Initiated": "Return Initiated", "Return_Approved": "Return Approved", "Return_Picked": "Return Picked", "Return_Delivered": "Return Delivered", "Return_Pick_Failed": "Return Pick Failed", "Return_Failed": "Return Failed", "Return_Rejected": "Return Rejected", "Pending": "Pending", "pending": "Pending", "Packed": "Packed", "Liquidated": "Liquidated", "Agent-assigned": "Agent assigned", "Out-for-pickup": "Out for pickup", "Pickup-failed": "Pickup failed", "Order-picked-up": "Order picked up", "In-transit": "In transit", "At-pickup": "At pickup", "At-delivery": "At delivery", "At-destination-hub": "At destination hub", "Out-for-delivery": "Out for delivery", "Delivery-failed": "Delivery failed", "Order-delivered": "Order delivered", "Cancelled": "Cancelled", "RTO-Initiated": "RTO Initiated", "RTO-Delivered": "RTO Delivered", "RTO-Disposed": "RTO Disposed", "on time": "on {{time}}"}, "Search": {"Products": "Products", "Stores": "Stores"}, "Customisation": {"Select any and upto": "Select any {{minQuantity}} and upto {{maxQuantity}} options", "Select any": "Select any {{minQuantity}} options", "Select upto": "Select upto {{maxQuantity}} options"}, "Order Details": {"OTP": "OTP", "Payment Refund": "Payment Refund", "We could not place your order": "We could not place your order. Don't worry! If your account has been debited for this transaction, it will be refunded in 4-5 business days", "Your order has been cancelled": "Your order has been cancelled. Don't worry! Your amount will be refunded in 4-5 business days", "Refund initiated message": "We've processed your refund to your original payment method. It may take up to 5-7 business days to reflect in your account", "Order Confirmation is Pending": "Order Confirmation is Pending", "Please wait, we are confirming your order": "Please wait, we are confirming your order with the seller as soon as possible", "Items Total": "Items Total", "Still facing Issue": "Still facing Issue?", "For further assistance, please drop an email to": "For further assistance, please drop an email to", "Order placed on": "Order placed on {{date}}", "Order completed on": "Order completed on {{date}}", "Order Shipped": "Order Shipped", "Order Updated": "Order Updated", "Order Delivered": "Order Delivered", "Order Active": "Order Active", "Order Completed": "Order Completed", "Order Returned": "Order Returned", "Order Accepted": "Order Accepted", "Order Cancelled": "Order Cancelled", "Order Created": "Order Created", "Order In-progress": "Order In-progress", "Order Draft": "Verifying Payment"}, "Payment": {"Order Confirmed": "Yay! Order Confirmed", "Your order has been placed successfully": "Your order has been placed successfully.", "Review Order": "Review Order", "Hold On": "Hold On", "We’re placing your order": "Sit back and relax—we're placing your order now!", "Heading to Cart": "Hold Tight – Your Cart is Heading to the Checkout!"}, "Product": {"discount": "{{discount}}% off"}, "QR Code": {"Camera Permission": "Camera Permission", "Getting your camera access would help capture the codes": "Getting your camera access would help capture the codes.", "You have denied the camera permission": "You have denied the camera permission. Please enable it from the ‘Settings’ section of your phone."}, "Filters": {"brand": "brand", "gender": "gender", "size": "size", "pricerange": "Price range"}, "Grocery": {"Quick Delivery": "Quick Delivery", "At your Doorstep in 30 minutes": "At your Doorstep in 30 minutes", "Grocery & Snacks": "Grocery & Snacks", "Beauty & Personal Care": "Beauty & Personal Care", "Household Essentials": "Household Essentials", "Health & Wellness": "Health & Wellness", "Delivery": "Delivery", "Mins": "<PERSON>s"}, "Update": {"Update Title": "Update Now for a Better DigiHaat Experience!", "Update Message": "Don’t miss out on the best of DigiHaat! \nUpdate your app now to continue enjoying all the benefits.\n", "Update Now": "Update Now"}}