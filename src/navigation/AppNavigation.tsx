import Box from "@mui/material/Box"
import Container from "@mui/material/Container"
import { useEffect } from "react"
import { Route, BrowserRouter as Router, Routes } from "react-router-dom"
import Login from "../modules/authentication/modules/login/Login"
import Splash from "../modules/authentication/modules/splash/Splash"
import BrandDetails from "../modules/main/components/provider/BrandDetails"
import StoreInfo from "../modules/main/components/provider/components/StoreInfo"
import InvalidBrandDetails from "../modules/main/components/provider/InvalidBrandDetails"
import About from "../modules/main/modules/about/About"
import Dashboard from "../modules/main/modules/dashboard/Dashboard"
import SearchProducts from "../modules/main/modules/globalSearch/SearchProducts"

import PrivateRoute from "../components/privateRoute/PrivateRoute"
import PublicRoute from "../components/publicRoute/PublicRoute"
import VerifyMobileNumber from "../modules/authentication/modules/login/VerifyMobileNumber"
import FulfillmentErrorProvider from "../modules/main/components/providers/FulfillmentErrorProvider"
import AddDefaultAddress from "../modules/main/modules/address/AddDefaultAddress"
import AddressSheet from "../modules/main/modules/address/AddressSheet"
import CheckPermissions from "../modules/main/modules/address/CheckPermissions"
import FindLocation from "../modules/main/modules/address/FindLocation"
import SelectLocation from "../modules/main/modules/address/SelectLocation"
import UpdateAddress from "../modules/main/modules/address/UpdateAddress"
import Cart from "../modules/main/modules/cart/all/Cart"
import CouponList from "../modules/main/modules/cart/provider/CouponList"
import Fulfillment from "../modules/main/modules/cart/provider/Fulfillment"
import OrderConfirmed from "../modules/main/modules/cart/provider/OrderConfirmed"
import OrderProcessing from "../modules/main/modules/cart/provider/OrderProcessing"
import ProviderCart from "../modules/main/modules/cart/provider/ProviderCart"
import CategoryPage from "../modules/main/modules/category/Category"
import ComplaintDetails from "../modules/main/modules/complaint/details/ComplaintDetails"
import Complaints from "../modules/main/modules/complaint/list/Complaints"
import LegalPolicies from "../modules/main/modules/Legal/LegalPolicies"
import OpenSourceLibraries from "../modules/main/modules/Legal/OpenSourceLibraries"
import CancelOrder from "../modules/main/modules/order/details/CancelOrder"
import OrderDetails from "../modules/main/modules/order/details/OrderDetails"
import OrderProductDetails from "../modules/main/modules/order/details/OrderProductDetails"
import OrderReturnDetails from "../modules/main/modules/order/details/OrderReturnDetails"
import PaymentMethods from "../modules/main/modules/order/details/PaymentMethods"
import DynamicPage from "../modules/main/modules/pages/DynamicPage"
import StoresNearMe from "../modules/main/modules/pages/StoresNearMe"
import SubCategories from "../modules/main/modules/pages/SubCategories"
import TagSubCategoryDetails from "../modules/main/modules/pages/TagSubCategoryDetails"
import ProductDetails from "../modules/main/modules/product/details/ProductDetails"
import AccountDeletion from "../modules/main/modules/profile/account/AccountDeletion"
import AccountSetting from "../modules/main/modules/profile/account/AccountSetting"
import DeleteAccount from "../modules/main/modules/profile/account/DeleteAccount"
import SellerQRCodeWeb from "../modules/main/modules/qr/SellerQrCode"
import SubCategoryDetails from "../modules/main/modules/subCategory/SubCategoryDetails"
import WishList from "../modules/main/modules/wishlist/WishList"
import PrivacyPolicy from "../modules/static/privacyPolicy/PrivacyPolicy"
import TermsCondition from "../modules/static/termsCondition/TermsCondition"
import HyperLocalLanding from "../modules/main/modules/pages/HyperLocalLanding"

/**
 * Component for stack navigation
 * @returns {JSX.Element}
 * @constructor
 */
const AppNavigation = () => {
  useEffect(() => {
    const loadRazorpayScript = () => {
      const script = document.createElement("script")
      script.src = "https://checkout.razorpay.com/v1/checkout.js"
      script.async = true
      document.body.appendChild(script)
    }
    loadRazorpayScript()
  }, [])

  return (
    <Container
      maxWidth={false}
      sx={{
        display: "flex",
        justifyContent: "center",
        padding: "0px !important",
      }}
    >
      <Box
        sx={{
          maxWidth: 480,
          width: "100%",
          background: "#fff",
          height: "100dvh",
        }}
      >
        <Router>
          <FulfillmentErrorProvider>
            <Routes>
              <Route
                path="/"
                element={
                  <PublicRoute>
                    <Splash />
                  </PublicRoute>
                }
              />
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <Login />
                  </PublicRoute>
                }
              />
              <Route path="/privacy-policy" element={<PrivacyPolicy />} />
              <Route path="/terms-condition" element={<TermsCondition />} />
              <Route
                path="/verify-mobile-number"
                element={
                  <PublicRoute>
                    <VerifyMobileNumber />
                  </PublicRoute>
                }
              />
              <Route
                path="/dashboard"
                element={
                  <PrivateRoute>
                    <Dashboard />
                  </PrivateRoute>
                }
              />
              <Route
                path="/category"
                element={
                  <PrivateRoute>
                    <CategoryPage />
                  </PrivateRoute>
                }
              />
              <Route
                path="/about"
                element={
                  <PrivateRoute>
                    <About />
                  </PrivateRoute>
                }
              />
              <Route
                path="/search"
                element={
                  <PrivateRoute>
                    <SearchProducts />
                  </PrivateRoute>
                }
              />
              <Route
                path="/product"
                element={
                  <PrivateRoute>
                    <ProductDetails />
                  </PrivateRoute>
                }
              />
              <Route
                path="/store"
                element={
                  <PrivateRoute>
                    <BrandDetails />
                  </PrivateRoute>
                }
              />
              <Route
                path="/store-info"
                element={
                  <PrivateRoute>
                    <StoreInfo />
                  </PrivateRoute>
                }
              />
              <Route
                path="/invalid-store-details"
                element={
                  <PrivateRoute>
                    <InvalidBrandDetails />
                  </PrivateRoute>
                }
              />
              <Route
                path="/sub-category"
                element={
                  <PrivateRoute>
                    <SubCategoryDetails />
                  </PrivateRoute>
                }
              />
              <Route
                path="/order"
                element={
                  <PrivateRoute>
                    <OrderDetails />
                  </PrivateRoute>
                }
              />

              <Route
                path="/cart"
                element={
                  <PrivateRoute>
                    <Cart />
                  </PrivateRoute>
                }
              />
              <Route
                path="/cart/:cartLocationId"
                element={
                  <PrivateRoute>
                    <ProviderCart />
                  </PrivateRoute>
                }
              />
              <Route
                path="/fulfillment"
                element={
                  <PrivateRoute>
                    <Fulfillment />
                  </PrivateRoute>
                }
              />
              <Route
                path="/order-processing"
                element={
                  <PrivateRoute>
                    <OrderProcessing />
                  </PrivateRoute>
                }
              />
              <Route
                path="/order-confirmed"
                element={
                  <PrivateRoute>
                    <OrderConfirmed />
                  </PrivateRoute>
                }
              />
              <Route
                path="/coupons"
                element={
                  <PrivateRoute>
                    <CouponList />
                  </PrivateRoute>
                }
              />
              <Route
                path="/wishlist"
                element={
                  <PrivateRoute>
                    <WishList />
                  </PrivateRoute>
                }
              />
              <Route
                path="/cancel-order"
                element={
                  <PrivateRoute>
                    <CancelOrder
                      route={{
                        params: {
                          domain: "",
                          bppId: "",
                          bppUrl: "",
                          transactionId: "",
                          orderId: "",
                        },
                      }}
                    />
                  </PrivateRoute>
                }
              />
              <Route
                path="/order-product-details/:fulfillmentId"
                element={
                  <PrivateRoute>
                    <OrderProductDetails />
                  </PrivateRoute>
                }
              />
              {/* <Route path="/return-item" element={<PrivateRoute><ReturnItem /></PrivateRoute>} />  */}
              <Route
                path="/payment-methods"
                element={
                  <PrivateRoute>
                    <PaymentMethods />
                  </PrivateRoute>
                }
              />
              <Route
                path="/check-permissions"
                element={
                  <PrivateRoute>
                    <CheckPermissions />
                  </PrivateRoute>
                }
              />
              <Route
                path="/find-location"
                element={
                  <PrivateRoute>
                    <FindLocation />
                  </PrivateRoute>
                }
              />
              <Route
                path="/select-location"
                element={
                  <PrivateRoute>
                    <SelectLocation />
                  </PrivateRoute>
                }
              />
              <Route
                path="/address-sheet"
                element={
                  <PrivateRoute>
                    <AddressSheet />
                  </PrivateRoute>
                }
              />
              <Route
                path="/account-setting"
                element={
                  <PrivateRoute>
                    <AccountSetting />
                  </PrivateRoute>
                }
              />
              <Route
                path="/delete-account"
                element={
                  <PrivateRoute>
                    <DeleteAccount />
                  </PrivateRoute>
                }
              />
              <Route
                path="/account-deletion"
                element={
                  <PrivateRoute>
                    <AccountDeletion />
                  </PrivateRoute>
                }
              />
              <Route
                path="/add-default-address"
                element={
                  <PrivateRoute>
                    <AddDefaultAddress />
                  </PrivateRoute>
                }
              />
              <Route
                path="/update-address"
                element={
                  <PrivateRoute>
                    <UpdateAddress />
                  </PrivateRoute>
                }
              />
              <Route
                path="/tag-subcategory-details"
                element={
                  <PrivateRoute>
                    <TagSubCategoryDetails />
                  </PrivateRoute>
                }
              />
              <Route
                path="/hyper-local-landing"
                element={
                  <PrivateRoute>
                    <HyperLocalLanding />
                  </PrivateRoute>
                }
              />
              <Route
                path="/stores-near-me"
                element={
                  <PrivateRoute>
                    <StoresNearMe />
                  </PrivateRoute>
                }
              />
              <Route
                path="/sub-categories"
                element={
                  <PrivateRoute>
                    <SubCategories />
                  </PrivateRoute>
                }
              />
              <Route
                path="/complaints"
                element={
                  <PrivateRoute>
                    <Complaints />
                  </PrivateRoute>
                }
              />
              <Route
                path="/complaint-details"
                element={
                  <PrivateRoute>
                    <ComplaintDetails />
                  </PrivateRoute>
                }
              />
              <Route
                path="/order-return-details/:fulfillmentId"
                element={
                  <PrivateRoute>
                    <OrderReturnDetails />
                  </PrivateRoute>
                }
              />
              <Route
                path="/page"
                element={
                  <PrivateRoute>
                    <DynamicPage />
                  </PrivateRoute>
                }
              />
              <Route
                path="/SellerQRCode"
                element={
                  <PrivateRoute>
                    <SellerQRCodeWeb />
                  </PrivateRoute>
                }
              />
              <Route
                path="/LegalPolicies"
                element={
                  <PrivateRoute>
                    <LegalPolicies />
                  </PrivateRoute>
                }
              />
              <Route
                path="/OpenSourceLibraries"
                element={
                  <PrivateRoute>
                    <OpenSourceLibraries />
                  </PrivateRoute>
                }
              />
            </Routes>
          </FulfillmentErrorProvider>
        </Router>
      </Box>
    </Container>
  )
}

export default AppNavigation
