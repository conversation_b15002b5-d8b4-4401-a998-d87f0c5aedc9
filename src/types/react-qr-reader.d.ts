declare module 'react-qr-reader' {
  import * as React from 'react';

  export interface QrReaderProps {
    delay?: number | false;
    onError?: (error: any) => void;
    onScan?: (data: string | null) => void;
    style?: React.CSSProperties;
    className?: string;
    facingMode?: 'user' | 'environment';
    legacyMode?: boolean;
    resolution?: number;
    constraints?: MediaTrackConstraints;
  }

  const QrReader: React.FC<QrReaderProps>;
  export default QrReader;
}
