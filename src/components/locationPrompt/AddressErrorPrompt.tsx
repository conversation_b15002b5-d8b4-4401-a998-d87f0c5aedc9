import React from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import LocationOnIcon from "@mui/icons-material/LocationOn";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {setAddress} from "../../toolkit/reducer/address";
import "./AddressErrorPrompt.css";

interface AddressErrorPromptProps {
  hideLocationFailureVisible: () => void;
}

const AddressErrorPrompt: React.FC<AddressErrorPromptProps> = ({
                                                                 hideLocationFailureVisible,
                                                               }) => {
  const dispatch = useDispatch();
  const {t} = useTranslation();

  const navigateToDashboard = async () => {
    const address = {
      areaCode: "110049",
      lat: 28.55370299999999,
      lng: 77.21492799999999,
    };
    dispatch(setAddress({address}));
    hideLocationFailureVisible();
    window.location.href = "/dashboard"; // Simulating navigation reset
  };

  return (
    <Modal open={true} onClose={() => {
    }}>
      <Box className="modal">
        <Box className="imageContainer">
          <LocationOnIcon className="icon"/>
        </Box>
        <Typography variant="headlineSmall" className="text">
          {t("Find Location.Something wrong at end")}
        </Typography>
        <Typography variant="labelSmall" className="text">
          {t("Find Location.Something wrong message")}
        </Typography>
        <Button onClick={navigateToDashboard} className="button" variant="contained">
          {t("Find Location.Ok")}
        </Button>
      </Box>
    </Modal>
  );
};

export default AddressErrorPrompt;
