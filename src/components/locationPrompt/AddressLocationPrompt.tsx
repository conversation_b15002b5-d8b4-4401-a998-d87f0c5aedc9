import React from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import CloseIcon from "@mui/icons-material/Close";
import {useTranslation} from "react-i18next";
import "./AddressLocationPrompt.css";

interface AddressLocationPromptProps {
  setupDefaultAddress: () => void;
  hidePermissionModal: () => void;
  setNavigatedToSettings: () => void;
}

const AddressLocationPrompt: React.FC<AddressLocationPromptProps> = ({
                                                                       setupDefaultAddress,
                                                                       hidePermissionModal,
                                                                       setNavigatedToSettings,
                                                                     }) => {
  const {t} = useTranslation();

  const navigateToSettings = () => {
    setNavigatedToSettings();
    window.open("app-settings:", "_blank"); // Simulating settings navigation
  };

  return (
    <Modal open={true} onClose={hidePermissionModal}>
      <Box className="modal">
        <Box className="modalHeader">
          <Typography variant="headlineSmall" noWrap>
            {t("Find Location.Location Permission Denied")}
          </Typography>
          <CloseIcon className="closeIcon" onClick={hidePermissionModal}/>
        </Box>
        <Typography variant="labelSmall" className="message">
          {t("Find Location.Location Disabled")}
        </Typography>
        <Box className="modalFooter">
          <Button className="button" onClick={setupDefaultAddress} variant="outlined">
            {t("Find Location.Cancel")}
          </Button>
          <Button className="button setting" onClick={navigateToSettings} variant="contained">
            {t("Find Location.Enable")}
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default AddressLocationPrompt;
