.modal {
    background-color: white;
    margin: 16px;
    border-radius: 16px;
    padding: 16px;
}

.modalHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.message {
    margin-top: 8px;
    color: #666;
    text-align: center;
}

.modalFooter {
    margin-top: 28px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15px;
}

.button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    border: 1px solid #008ECC;
    height: 44px;
}

.cancelLabel {
    color: #008ECC;
}

.setting {
    background-color: #008ECC;
    color: #ffffff;
}

.settingLabel {
    color: white;
}

.modalIconContainer {
    align-items: center;
    display: flex;
    flex-direction: column;
}

.imageContainer {
    width: 150px;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #eef3f8;
    border-radius: 75px;
    margin-bottom: 24px;
}

.closeContainer {
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;
}
