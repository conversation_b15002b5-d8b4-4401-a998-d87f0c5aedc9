import React, {forwardRef, useImperativeHandle, useRef, useState} from 'react';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import CloseIcon from '@mui/icons-material/Close';
import {useTranslation} from 'react-i18next';
import {useNavigate} from 'react-router-dom';
import useLocationPermission from '../../modules/main/hooks/useLocationPermission';
import {emptyAlertCallback} from '../../utils/utils';
import styles from "./styles.module.css"; // Import CSS Module

interface Props {
  updateNavigatingToSettings: () => void;
}

const LocationPrompt = forwardRef(({updateNavigatingToSettings}: Props, ref) => {
  const navigate = useNavigate();
  const {checkLocationPermissionAvailable, requestLocationPermission} = useLocationPermission();
  const {t} = useTranslation();
  const [permissionModalVisible, setPermissionModalVisible] = useState(false);
  const [permissionCount, setPermissionCount] = useState(0);
  const locationFailedCount = useRef(0);
  const checkingPermission = useRef(false);

  const navigateToFindLocation = () => {
    navigate('/find-location', {replace: true});
  };

  const navigateToSelectLocation = () => {
    hidePermissionModal();
    navigate('/select-location', {replace: true});
  };

  const promptLocationModal = () => {
    setPermissionModalVisible(true);
  };

  const hidePermissionModal = () => {
    setPermissionModalVisible(false);
  };

  const askPermissionAgain = () => {
    hidePermissionModal();
    locationFailedCount.current += 1;
    setPermissionCount(locationFailedCount.current);
    checkLocationPermission().then(emptyAlertCallback).catch(emptyAlertCallback);
  };

  const navigateToSettings = async () => {
    locationFailedCount.current += 1;
    setPermissionCount(locationFailedCount.current);
    hidePermissionModal();
    updateNavigatingToSettings();
    window.open('chrome://settings/content/location', '_blank');
  };

  const checkLocationPermission = async () => {
    if (!checkingPermission.current) {
      checkingPermission.current = true;
      const permissionStatus = await checkLocationPermissionAvailable();
      if (permissionStatus !== 'granted') {
        const locationPermission = await requestLocationPermission();
        if (locationPermission === 'granted') {
          checkingPermission.current = false;
          navigateToFindLocation();
        } else {
          checkingPermission.current = false;
          if (locationFailedCount.current > 1) {
            navigateToFindLocation();
          } else {
            promptLocationModal();
          }
        }
      } else {
        checkingPermission.current = false;
        navigateToFindLocation();
      }
    }
  };

  useImperativeHandle(ref, () => ({
    checkPermissions: () => {
      checkLocationPermission().then(emptyAlertCallback).catch(emptyAlertCallback);
    },
  }));

  return (
    <Modal open={permissionModalVisible} onClose={hidePermissionModal}>
      <Box className={styles.modal}>
        <Box className={styles.closeContainer}>
          <IconButton onClick={hidePermissionModal}>
            <CloseIcon style={{color: '#999'}}/>
          </IconButton>
        </Box>

        <Box className={styles.modalIconContainer}>
          <Box className={styles.imageContainer}>
            <LocationOnIcon style={{fontSize: 64, color: '#008ECC'}}/>
          </Box>
          <Typography variant="headlineSmall" className={styles.modalHeader}>
            {t('Find Location.Location Permission Denied')}
          </Typography>
          <Typography variant="labelSmall" className={styles.message}>
            {t('Find Location.Location Disabled')}
          </Typography>
        </Box>

        <Box className={styles.modalFooter}>
          <Button className={styles.button} onClick={navigateToFindLocation}>
            {t('Find Location.Cancel')}
          </Button>
          {permissionCount === 0 ? (
            <Button className={`${styles.button} ${styles.setting}`} onClick={askPermissionAgain}>
              {t('Find Location.Enable')}
            </Button>
          ) : (
            <Button className={`${styles.button} ${styles.setting}`} onClick={navigateToSettings}>
              {t('Find Location.Setting')}
            </Button>
          )}
        </Box>
      </Box>
    </Modal>
  );
});

export default LocationPrompt;
