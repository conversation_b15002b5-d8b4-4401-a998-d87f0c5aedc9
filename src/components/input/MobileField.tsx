import React from 'react';
import Box from '@mui/material/Box';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";

/**
 * Component to render generic input field
 * @param inputLabel
 * @param props
 * @returns {JSX.Element}
 * @constructor
 */
const MobileField: React.FC<any> = ({inputLabel, ...props}) => {
  const styles = useStyles();

  return (
    <Box>
      <Typography variant="bodySmall" className={styles.inputLabel}>
        {inputLabel}
        {props.required && <span className={styles.required}>*</span>}
      </Typography>
      <Box sx={{mt: 0.5}}>
        <TextField
          {...props}
          variant="outlined"
          type="tel"
          size="small"
          className={styles.inputText}
          inputProps={{maxLength: 10}}
          slotProps={{
            input: {
              startAdornment: <InputAdornment position="start">
                <Typography variant="bodySmall" sx={{color: '#1A1A1A'}}>+91 |</Typography>
              </InputAdornment>,
            },
          }}
          helperText={props.helperText}
        />
      </Box>
    </Box>
  );
};

const useStyles = makeStyles((theme: any) => ({
  inputLabel: {
    color: theme.palette.neutral400,
  },
  required: {
    color: theme.palette.error.main,
  },
  inputText: {
    backgroundColor: 'transparent',
    width: '100%',
  },
}));

export default MobileField;
