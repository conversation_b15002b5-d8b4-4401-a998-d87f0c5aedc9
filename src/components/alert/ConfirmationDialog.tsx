import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import {theme} from "../../utils/theme";

interface ConfirmationDialogProps {
  open: boolean;
  title: string;
  message: string;
  positiveText: string;
  negativeText: string;
  positiveAction: () => void;
  negativeAction: () => void;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
                                                                 open,
                                                                 title,
                                                                 message,
                                                                 positiveText,
                                                                 negativeText,
                                                                 positiveAction,
                                                                 negativeAction,
                                                               }) => {
  return (
    <Dialog
      open={open}
      onClose={() => {
      }}
      disableEscapeKeyDown
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
    >
      <DialogTitle id="alert-dialog-title">{title}</DialogTitle>
      <DialogContent>
        <DialogContentText sx={{color: theme.palette.neutral.main}} id="alert-dialog-description">
          {message}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={negativeAction} color="primary">
          {negativeText}
        </Button>
        <Button onClick={positiveAction} color="primary" autoFocus>
          {positiveText}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmationDialog;
