import React, {useEffect, useState} from 'react';
import {Navigate} from 'react-router-dom';
import {useDispatch} from 'react-redux';
import {useSelector} from "react-redux";
import {getMultipleData} from "../../utils/storage";
import {getStoredData} from "../../utils/storage";
import useProcessAuthPayload from "../../hooks/useProcessAuthPayload";
import {setAddress} from "../../toolkit/reducer/address";
import PageLoader from "../../modules/main/modules/dashboard/components/pageLoader/PageLoader";

const PrivateRoute = ({children}: { children: React.ReactNode }) => {
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const dispatch = useDispatch();
  const processAuthPayload = useProcessAuthPayload();
  const token = useSelector((state: any) => state.auth.token);

  useEffect(() => {
    const initializeAuth = () => {
      const data = getMultipleData([
        'token',
        'uid',
        'emailId',
        'name',
        'transaction_id',
        'language',
        'phoneNumber',
        'isGuest',
        'isWhiteListed',
      ]);

      if (data[0][1] !== null) {
        processAuthPayload(data);
        const addressString = getStoredData('address');
        if (addressString) {
          const address = JSON.parse(addressString);
          dispatch(setAddress(address));
        }
      } else {
        setIsAuthenticated(false);
        setLoading(false);
      }
    };

    initializeAuth();
  }, [dispatch]);

  useEffect(() => {
    if (token) {
      setIsAuthenticated(true);
      setLoading(false);
    }
  }, [token]);

  if (loading) {
    return <PageLoader/>;
  }

  return isAuthenticated ? children : <Navigate to="/login" replace/>;
};

export default PrivateRoute;
