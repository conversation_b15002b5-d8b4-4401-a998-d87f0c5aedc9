import {configureStore} from '@reduxjs/toolkit';
import auth from './reducer/auth';
import address from './reducer/address';
import cart from './reducer/cart';
import wishlist from './reducer/wishlist';
import categories from './reducer/categories';
import complaint from './reducer/complaint';
import device from './reducer/device';
import gesture from './reducer/gesture';
import stores from './reducer/stores';
import order from './reducer/order';
import product from './reducer/product';
import widgets from './reducer/widgets';
import loginConfirmation from './reducer/loginConfirmation';
import masthead from './reducer/masthead';
import pages from './reducer/pages';
import homeDomain from './reducer/homeDomain';

const store = configureStore({
  reducer: {
    auth,
    address,
    cart,
    categories,
    complaint,
    device,
    gesture,
    homeDomain,
    order,
    product,
    stores,
    widgets,
    wishlist,
    loginConfirmation,
    masthead,
    pages,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
      ignoredPaths: ['navigation.params.handleDeepLink'],
    }),
});

export default store;
