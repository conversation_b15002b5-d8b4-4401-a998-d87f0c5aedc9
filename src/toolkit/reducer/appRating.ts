import {createSlice, PayloadAction} from '@reduxjs/toolkit';

interface AppRatingState {
  apprating: boolean; // Whether user is eligible for rating
  orderData: any | null; // Order data related to the rating (only for eligibility)
  message: string; // Rating prompt message
  orderId: string; // ID of the order to rate (only for eligibility)
  mongoId: string; // MongoDB ID of the order (only for eligibility)
}

const initialState: AppRatingState = {
  apprating: false,
  orderData: null,
  message: '',
  orderId: '',
  mongoId: '',
};

const appRatingSlice = createSlice({
  name: 'appRating',
  initialState,
  reducers: {
    setAppRatingEligibility: (state, action: PayloadAction<any>) => {
      state.apprating = action.payload.apprating === true;
      state.orderData = action.payload.orders || null;
      state.message = action.payload.message || '';
      state.orderId = action.payload.order_id || '';
      state.mongoId = action.payload.mongo_id || '';
    },
    resetAppRatingEligibility: state => {
      return initialState;
    },
    // Keep this for compatibility, but we don't use it for submission anymore
    setRatingOrderIds: (
      state,
      action: PayloadAction<{
        orderId?: string;
        mongoId?: string;
        orderData?: any;
      }>,
    ) => {
      if (action.payload.orderId) state.orderId = action.payload.orderId;
      if (action.payload.mongoId) state.mongoId = action.payload.mongoId;
      if (action.payload.orderData) state.orderData = action.payload.orderData;
    },
  },
});

// Export actions
export const {
  setAppRatingEligibility,
  resetAppRatingEligibility,
  setRatingOrderIds,
} = appRatingSlice.actions;

// Define selectors to access specific parts of the state
export const getAppRating = (state: any) => state.appRating.apprating;
export const getOrderData = (state: any) => state.appRating.orderData;
export const getMessage = (state: any) => state.appRating.message;
export const getOrderId = (state: any) => state.appRating.orderId;
export const getMongoId = (state: any) => state.appRating.mongoId;

// Export reducer
export default appRatingSlice.reducer;
