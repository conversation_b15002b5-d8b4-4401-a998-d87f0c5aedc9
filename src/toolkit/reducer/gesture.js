import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  gestureEnabled: false,
};

const gestureSlice = createSlice({
  name: 'gesture',
  initialState,
  reducers: {
    updateGesture(state, action) {
      state.gestureEnabled = action.payload;
    },
  },
  extraReducers: builder => {
  },
});

export const {updateGesture} = gestureSlice.actions;
export default gestureSlice.reducer;
