import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  widgetSections: null,
  pageWiseWidgets: {},
};

const Coupons = createSlice({
  name: 'widgets',
  initialState,
  reducers: {
    updatePageWiseWidgets(state, action) {
      state.pageWiseWidgets = action.payload;
    },
    updateWidgetSections(state, action) {
      state.widgetSections = action.payload;
    },
    updateWidgetForPage(state, action) {
      state.pageWiseWidgets[action.payload.page] = action.payload.widgets;
    },
  },
  extraReducers: builder => {
  },
});

export const {
  updateWidgetSections,
  updatePageWiseWidgets,
  updateWidgetForPage,
} = Coupons.actions;
export default Coupons.reducer;
