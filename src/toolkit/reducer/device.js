import {createSlice} from '@reduxjs/toolkit';
import {APP_VERSION} from "../../utils/constants";

const initialState = {
  deviceManufacturer: 'Unknown',
  deviceModel: navigator.userAgent,
  OSVersion: navigator.platform,
  deviceName: navigator.userAgent,
  appVersion: APP_VERSION,
  deviceIdentity: navigator.userAgent,
};

const deviceSlice = createSlice({
  name: 'device',
  initialState,
  reducers: {
    setDeviceDetails(state, action) {
      const {
        appVersion,
        deviceManufacturer,
        deviceModel,
        OSVersion,
        deviceName,
        deviceIdentity,
      } = action.payload;
      state.appVersion = appVersion;
      state.deviceManufacturer = deviceManufacturer;
      state.deviceModel = deviceModel;
      state.OSVersion = OSVersion;
      state.deviceName = deviceName;
      state.deviceIdentity = deviceIdentity;
    },
  },
});

export const {setDeviceDetails} = deviceSlice.actions;
export default deviceSlice.reducer;
