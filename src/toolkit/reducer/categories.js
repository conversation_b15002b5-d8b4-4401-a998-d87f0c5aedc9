import {createSlice} from '@reduxjs/toolkit';
import {DOMAINS} from '../../utils/domains';
import AllIcon from '../../assets/dashboard/domains/all.svg';

const initialState = {
  domainImages: {},
  domains: [
    {
      id: '1',
      name: 'All',
      shortName: 'All',
      Icon: AllIcon,
      routeName: 'All',
      domain: 'All',
    },
  ].concat(DOMAINS),
};

const categoriesSlice = createSlice({
  name: 'categories',
  initialState,
  reducers: {
    updateImages(state, action) {
      state.domainImages = action.payload;
    },
  },
  extraReducers: builder => {
  },
});

export const {updateImages} = categoriesSlice.actions;
export default categoriesSlice.reducer;
