import {createSlice} from '@reduxjs/toolkit';
import {setStoredData} from '../../utils/storage';

const initialState = {
  address: null,
  addressListPresent: true,
};


const addressSlice = createSlice({
  name: 'address',
  initialState,
  reducers: {
    setAddress(state, action) {
      state.address = action.payload;
    },
    updateExistingAddress(state, action) {
      if (state.address.id === action.payload.id) {
        state.address = action.payload.address;
        setStoredData('address', JSON.stringify(action.payload.address));
      }
    },
    setAddressListAvailable(state, action) {
      state.addressListPresent = action.payload;
    },
    clearAddress(state) {
      Object.assign(state, initialState);
    },
  },
});

export const {
  setAddress,
  setAddressListAvailable,
  updateExistingAddress,
  clearAddress,
} = addressSlice.actions;
export default addressSlice.reducer;
