import { createSlice } from "@reduxjs/toolkit"

const initialState = {
  title: null,
  logoImage: null,
  loaderJson: null,
  headerBackgroundColor: null,
  domainListBackgroundColor: null,
  mastheadMedia: null,
  mastheadMediaType: null,
  startDate: null,
  endDate: null,
  status: null,
}

const mastheadSlice = createSlice({
  name: "masthead",
  initialState,
  reducers: {
    setMasthead(state, action) {
      state[action.payload.page] = action.payload.data
    },
    clearMasthead(state, action) {
      state = initialState
    },
  },
})

export const { setMasthead, clearMasthead } = mastheadSlice.actions
export default mastheadSlice.reducer
