import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  cartItems: [],
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    updateCartItems(state, action) {
      state.cartItems = action.payload;
    },
    clearSpecificCart(state, action) {
      state.cartItems = state.cartItems.filter(
        item => item._id !== action.payload,
      );
    },
    removeStoreFromCart(state, action) {
      const items = [...state.cartItems];
      state.cartItems = items.filter(item => item._id !== action.payload);
    },
    removeProductFromCart(state, action) {
      const items = [...state.cartItems];
      state.cartItems = items.filter(item => {
        if (item._id === action.payload.cartId) {
          item.items = item.items.filter(product => {
            return product._id !== action.payload.productId;
          });
        }
        return item.items.length > 0;
      });
    },
    updateCartItemQuantity(state, action) {
      const newCartItems = [...state.cartItems];
      const providerCart = newCartItems?.find(
        cart => cart.location_id === action.payload.locationId,
      );
      const providerItem = providerCart?.items?.find(
        item => item._id === action.payload.itemId,
      );
      providerItem.item.quantity.count = action.payload.quantity;
      state.cartItems = newCartItems;
    },
    clearCart(state) {
      return initialState;
    },
    clearData(state) {
      return initialState;
    },
  },
});

export const {
  updateCartItems,
  updateCartItemQuantity,
  clearCart,
  clearData,
  clearSpecificCart,
  removeStoreFromCart,
  removeProductFromCart,
} = cartSlice.actions;
export default cartSlice.reducer;
