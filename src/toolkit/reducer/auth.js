import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  token: null,
  uid: null,
  emailId: null,
  phoneNumber: null,
  name: "name",
  photoURL: null,
  language: 'en',
  isLoading: true,
  transaction_id: null,
  isWhiteListed: false,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoginDetails(state, action) {
      state.token = action.payload.token;
      state.emailId = action.payload.emailId;
      state.uid = action.payload.uid;
      state.name = action.payload.name;
      state.phoneNumber = action.payload.phoneNumber;
      state.photoURL = action.payload.photoURL;
      state.transaction_id = action.payload.transaction_id;
      state.isWhiteListed = action.payload.isWhiteListed;
    },
    setToken(state, action) {
      state.token = action.payload;
    },
    setTransactionId(state, action) {
      state.transaction_id = action.payload;
    },
    updateLanguage(state, action) {
      state.language = action.payload;
    },
    logoutUser(state) {
      Object.assign(state, initialState);
    },
    saveUser(state, action) {
      return action.payload;
    },
    setName(state, action) {
      state.name = action.payload;
    },
    setProfile(state, action) {
      state.name = action.payload.name;
      state.emailId = action.payload.email;
    },
    updateIsWhiteListed(state, action) {
      state.isWhiteListed = action.payload;
    },
  },
});

export const {
  setLoginDetails,
  setToken,
  setTransactionId,
  updateLanguage,
  logoutUser,
  saveUser,
  setName,
  setProfile,
  updateIsWhiteListed,
} = authSlice.actions;
export default authSlice.reducer;
