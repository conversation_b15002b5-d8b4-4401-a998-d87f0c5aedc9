import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  wishlistItems: [],
};

const wishlistSlice = createSlice({
  name: 'wishlist',
  initialState,
  reducers: {
    updateWishlistItems(state, action) {
      state.wishlistItems = action.payload;
    },
    removeStoreFromWishlist(state, action) {
      const items = [...state.wishlistItems];
      state.wishlistItems = items.filter(item => item._id !== action.payload);
    },
    removeProductFromWishlist(state, action) {
      const items = [...state.wishlistItems];
      state.wishlistItems = items.filter(item => {
        if (item._id === action.payload.wishlistId) {
          item.items = item.items.filter(product => {
            return product._id !== action.payload.productId;
          });
        }
        return item.items.length > 0;
      });
    },
    clearWishlist(state) {
      return initialState;
    },
  },
});

export const {
  updateWishlistItems,
  removeStoreFromWishlist,
  removeProductFromWishlist,
  clearWishlist,
} = wishlistSlice.actions;
export default wishlistSlice.reducer;
