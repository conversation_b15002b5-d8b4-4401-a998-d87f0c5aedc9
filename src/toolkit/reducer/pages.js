import {createSlice} from '@reduxjs/toolkit';

const initialState = {
  pages: {},
};

const pagesSlice = createSlice({
  name: 'pages',
  initialState,
  reducers: {
    setPage(state, action) {
      const pages = {...state.pages};
      const {pageName, page} = action.payload;
      pages[pageName] = page;
      state.pages = pages;
    },
  },
});

export const {setPage} = pagesSlice.actions;
export default pagesSlice.reducer;
