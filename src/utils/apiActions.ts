export const API_BASE_URL: string = "https://prod.nirmitbap.ondc.org" // process.env.REACT_APP_API_BASE_URL ?? ""
export const MAPPLS_BASE_URL: string =
  "https://apis.mappls.com/advancedmaps/v1/"

export const DELIVERY_ADDRESS: string = "/clientApis/v1/delivery_address"
export const VERIFY_OTP: string = "/clientApis/v2/auth/verifyOTP"
export const SEND_OTP: string = "/clientApis/v2/auth/sendOTP"
export const RESEND_OTP: string = "/clientApis/v2/auth/resendOTP"

export const DOMAIN_IMAGES: string = "/clientApis/v2/domainImage"
export const CATEGORIES: string = "/clientApis/v2/categories"

export const MASTHEAD: string = "/clientApis/v2/masthead"

export const SERVICEABLE_LOCATIONS: string =
  "/clientApis/v2/servieablelocations"
export const UPDATE_DELIVERY_ADDRESS: string =
  "/clientApis/v1/update_delivery_address/"
export const MAP_ACCESS_TOKEN: string = "/clientApis/v2/map/accesstoken"
export const PROVIDER: string = "/clientApis/v2/provider-details"
export const STORE_DETAILS: string = "/clientApis/v2/location-details"
export const CUSTOM_MENU: string = "/clientApis/v2/custom-menus"
export const PRODUCT_ATTRIBUTES: string = "/clientApis/v2/attributes"
export const PRODUCT_ATTRIBUTE_VALUES: string = "/clientApis/v2/attributeValues"
export const PRODUCT_SEARCH: string = "/clientApis/v2/search"
export const ITEMS: string = "/clientApis/v2/items"
export const ITEM_DETAILS: string = "/clientApis/v2/item-details"
export const ORDERS: string = "/clientApis/v2/orders"
export const ORDER_STATUS: string = "/clientApis/v2/order_status"
export const TRACK_ORDER: string = "/clientApis/v2/track"
export const ON_TRACK: string = "/clientApis/v2/on_track"
export const CART: string = "/clientApis/v2/cart"
export const WISHLIST: string = "/clientApis/v2/wishlist"
export const PAGE: string = "/clientApis/v2/pages"
export const PAGE_WIDGETS: string = "/clientApis/v2/page-widgets"
export const COUPONS: string = "/clientApis/v2/coupons"
export const GET_ISSUES: string = "/issueApis/v1/getIssues"
export const GET_ISSUE: string = "/issueApis/v1/issue"
export const ON_SELECT: string = "/clientApis/v2/on_select?messageIds="

export const SELECT: string = "/clientApis/v2/select"
export const VERIFY_PAYMENT: string = "/clientApis/v2/razorpay/verify/process"
export const EVENTS: string = "/clientApis/events/v2?messageId="
export const ISSUE: string = "/issueApis/events?messageId="
export const ON_ISSUE: string = "/issueApis/v1/on_issue?messageId="
export const INITIALIZE_ORDER: string = "/clientApis/v2/initialize_order"
export const ON_INITIALIZE: string =
  "/clientApis/v2/on_initialize_order?messageIds="

export const CANCEL_ORDER: string = "/clientApis/v2/cancel_order"
export const ON_CANCEL: string = "/clientApis/v2/on_cancel_order"
export const RETURN_ORDER: string = "/clientApis/v2/update"
export const ON_UPDATE: string = "/clientApis/v2/on_update?messageId="

export const GET_SIGN_URL: string = "/clientApis/v2/getSignUrlForUpload"

export const RAISE_ISSUE: string = "/issueApis/v1/issue"

export const ISSUE_STATUS: string = "/issueApis/v1/issue_status"
export const ON_ISSUE_STATUS: string =
  "/issueApis/v1/on_issue_status?messageId="

export const ORDER_EVENT: string = "/clientApis/events/v2?messageId="

export const RAZORPAY_KEYS: string = "/clientApis/v2/razorpay/razorPay/keys"
export const CREATE_PAYMENT: string = "/clientApis/v2/razorpay/"

export const GLOBAL_SEARCH_STORES: string =
  "/clientApis/v2/search/global/providers"
export const GLOBAL_SEARCH_ITEMS: string = "/clientApis/v2/search/global/items"
export const MERGE_ACCOUNTS: string = "/clientApis/v2/cart/user/merge"
export const UPDATE_PROFILE: string = "/clientApis/v2/user/updateProfile"
export const APPLY_COUPON: string = "/clientApis/v2/coupons/apply"

export const APP_RATING_ELIGIBILITY = "/clientApis/v2/apprating/eligibility"
export const POST_APP_RATING = "/clientApis/v2/apprating/"
export const GET_APP_RATING = "/clientApis/v2/apprating/"
export const APP_RATING_DISMISS = "/clientApis/v2/apprating/dismissed/"
