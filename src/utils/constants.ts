export const BRAND_PRODUCTS_LIMIT: number = 10

export const APP_STORE_URL: string =
  "https://apps.apple.com/in/app/digihaat/id6736940618"
export const PLAY_STORE_URL: string =
  "https://play.google.com/store/apps/details?id=org.ondc.nirmitbap"

export const CURRENCY_SYMBOLS: any = {
  INR: "₹",
}
export const FEEDBACK_FORM = {
  NON_COMPLETED_ORDER: "https://forms.gle/4HDN3K7ipitpt6cw8",
  COMPLETED_ORDER: "https://forms.gle/FJxR55kjAhq1cfSUA",
}

export const CAROUSEL_TIME: number = 5000

export const PRICE_RANGE: [number, number] = [0, 10000]

export const FB_DOMAIN: string = "ONDC:RET11"
export const GROCERY_DOMAIN: string = "ONDC:RET10"
export const FASHION_DOMAIN: string = "ONDC:RET12"

export const CANCEL_ORDER_STATUS: string = "Cancelled"
export const FAILED_ORDER_STATUS: string = "Failed"
export const PROCESSING_ORDER_STATUS: string = "Processing"
export const DRAFT_ORDER_STATUS: string = "Draft"

export const NEAREST_DISTANCE: number = 5000

export const SSE_TIMEOUT: number = 2 * 60 * 1000
export const CART_LOADER_TIMER = 2000

export const ORDER_PAYMENT_METHODS: any = {
  COD: "cash_on_delivery",
  PREPAID: "prepaid",
}
export const FOOD_TYPE: string[] = ["veg", "non_veg", "egg"]
export const BUYER_CANCELLATION_REASONS = [
  {
    key: "001",
    value:
      "Price of one or more items have changed due to which buyer was asked to make additional payment",
    isApplicableForCancellation: false,
  },
  {
    key: "003",
    value: "Product available at lower than order price",
    isApplicableForCancellation: false,
  },
  {
    key: "006",
    value: "Order not received as per buyer app TAT SLA",
    isApplicableForCancellation: false,
  },
  {
    key: "010",
    value: "Buyer wants to modify address / other order details",
    isApplicableForCancellation: false,
  },
]

export const CANCELLATION_REASONS = [
  {
    key: "001",
    value:
      "Price of one or more items have changed due to which buyer was asked to make additional payment",
    isApplicableForCancellation: false,
  },
  {
    key: "003",
    value: "Product available at lower than order price",
    isApplicableForCancellation: false,
  },
  {
    key: "006",
    value: "Order not received as per buyer app TAT SLA",
    isApplicableForCancellation: false,
  },
  {
    key: "009",
    value: "Wrong product delivered",
    isApplicableForCancellation: false,
  },
  {
    key: "010",
    value: "Buyer wants to modify address / other order details",
    isApplicableForCancellation: false,
  },
]

export const RETURN_REASONS = [
  {
    key: "001",
    value: "Buyer does not want product any more",
    isApplicableForNonReturnable: false,
  },
  {
    key: "002",
    value: "Product available at lower than order price",
    isApplicableForNonReturnable: false,
  },
  {
    key: "003",
    value: "Product damaged or not in usable state",
    isApplicableForNonReturnable: true,
  },
  {
    key: "004",
    value: "Product is of incorrect quantity or size",
    isApplicableForNonReturnable: true,
  },
  {
    key: "005",
    value: "Product delivered is different from what was shown and ordered",
    isApplicableForNonReturnable: true,
  },
]

export const CANCELLATION_REASONS_SELLER = [
  {
    key: "002",
    value: "One or more items in the Order not available",
    isApplicableForCancellation: true,
  },
  {
    key: "005",
    value: "Merchant rejected the order",
    isApplicableForCancellation: false,
  },
  {
    key: "011",
    value: "Buyer not found or cannot be contacted",
    isApplicableForCancellation: false,
  },
  {
    key: "012",
    value: "Buyer does not want product any more",
    isApplicableForCancellation: true,
  },
  {
    key: "013",
    value: "Buyer refused to accept delivery",
    isApplicableForCancellation: false,
  },
  {
    key: "014",
    value: "Address not found",
    isApplicableForCancellation: false,
  },
  {
    key: "015",
    value: "Buyer not available at location",
    isApplicableForCancellation: false,
  },
  {
    key: "018",
    value: "Delivery pin code not serviceable",
    isApplicableForCancellation: false,
  },
  {
    key: "019",
    value: "Pickup pin code not serviceable",
    isApplicableForCancellation: false,
  },
]

export const MOBILE_PAGES = {
  GROCERY: "GROCERY",
  FOOD: "FOOD",
  ELECTRONICS: "ELECTRONICS",
  BEAUTY: "BEAUTY",
  ALL: "ALL",
}

export const WIDGET_CTA = {
  SUBCATEGORY_PAGE: "SUBCATEGORY_PAGE",
  ITEM_SEARCH: "ITEM_SEARCH",
  GLOBAL_SEARCH: "GLOBAL_SEARCH",
  STORE_PAGE: "STORE_PAGE",
  CATEGORY_PAGE: "CATEGORY_PAGE",
  BANNER_PAGE: "BANNER_PAGE",
  PAGE: "PAGE",
  ITEM: "ITEM",
  PRODUCT: "PRODUCT",
  CUSTOM_LINK: "CUSTOM_LINK",
  HYPERLOCAL_LANDING: "HYPERLOCAL_LANDING",
}

export const TERMS_CONDITION: string = "https://digihaat.in/terms-condition"

export const APPS_FLYER_EVENTS = {
  APP_LAUNCHER: "app_launch",
  SIGN_UP: "af_complete_registration",
  LOGIN: "af_login",
  LAND_ON_MERCHANT_PAGE: "land_on_merchant_page",
  ITEM_ADDED_TO_CART: "af_add_to_cart",
  ADD_ADDRESS: "add_address_click",
  ADDRESS_ADDED: "address_added",
  LAND_ON_CHECKOUT_PAGE: "af_initiated_checkout",
  LAND_ON_PG_PAGE: "land_on_pg_page",
  TRANSACTION_COMPLETED: "af_purchase",
}

export const PRIVACY_POLICY = "https://digihaat.in/privacy-policy"

export const APP_VERSION = "1.1.56"

export const OTP_RETRY_COUNTER = 60
