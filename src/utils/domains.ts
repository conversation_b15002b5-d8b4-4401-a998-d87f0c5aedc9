import Grocery from '../assets/dashboard/domains/grocery.svg';
import Electronics from '../assets/dashboard/domains/electronics.svg';
import Food from '../assets/dashboard/domains/food.svg';
import Beauty from '../assets/dashboard/domains/beauty.svg';

export interface Domain {
  id: string;
  name: string;
  shortName: string;
  Icon: any;
  routeName: string;
  domain: string;
}

export const DOMAINS: Domain[] = [
  {
    id: '4',
    name: 'Food',
    shortName: 'F&B',
    Icon: Food,
    routeName: 'F&B',
    domain: 'ONDC:RET11',
  },
  {
    id: '2',
    name: 'Grocery',
    shortName: 'Grocery',
    Icon: Grocery,
    routeName: 'Grocery',
    domain: 'ONDC:RET10',
  },
  {
    id: '7',
    name: 'Beauty',
    shortName: 'BPC',
    Icon: Beauty,
    routeName: 'BPC',
    domain: 'ONDC:RET13',
  },
  {
    id: '3',
    name: 'Electronics',
    shortName: 'Electronics',
    Icon: Electronics,
    routeName: 'Electronics',
    domain: 'ONDC:RET14',
  },
];
