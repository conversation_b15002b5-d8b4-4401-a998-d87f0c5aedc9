import {decryptData, encryptData} from './utils';

export const removeData = (key: string) => {
  localStorage.removeItem(key);
};

export const getStoredData = (key: string) => {
  const data = localStorage.getItem(key);
  return data ? decryptData(data) : data;
};

export const setStoredData = (key: string, value: any) => {
  localStorage.setItem(key, encryptData(value));
};

export const clearAll = () => {
  localStorage.clear();
};

export const saveMultipleData = (data: any[]) => {
  try {
    data.forEach(([key, value]) => {
      localStorage.setItem(key, encryptData(value));
    });
    return true;
  } catch (error) {
    throw error;
  }
};

export const getMultipleData = (keys: string[]) => {
  try {
    return keys.map((key) => {
      const value = localStorage.getItem(key);
      return [key, value ? decryptData(value) : value];
    });
  } catch (error) {
    throw error;
  }
};
