import moment from "moment"
// @ts-ignore
import CryptoJ<PERSON> from "crypto-js"
import { CATEGORIES, DEEP_LINK_DOMAINS } from "./categories"
import { COLOR_CODE_TO_NAME } from "./colorCodes"
import { MOBILE_PAGES } from "./constants"

export interface TimeRange {
  start: number
  end: number
}

export const skeletonList: any[] = [
  {
    isSkeleton: true,
    _id: "9b1deb4d-3b7d-4bad-9bdd-2b0d7b4dcb6d",
    id: "9b1deb4d-3b7d-4bad-9bdd-2b0d7b4dcb6d",
  },
  {
    isSkeleton: true,
    _id: "9b1deb4d-3b7d-4bad-9bed-2b0d7b4dcb6d",
    id: "9b1deb4d-3b7d-4bad-9bed-2b0d7b4dcb6d",
  },
  {
    isSkeleton: true,
    _id: "9b1deb4d-3b7d-4bad-9bfd-2b0d7b4dcb6d",
    id: "9b1deb4d-3b7d-4bad-9bfd-2b0d7b4dcb6d",
  },
  {
    isSkeleton: true,
    _id: "9b1deb4d-3b7d-4bad-9bid-2b0d7b4dcb6d",
    id: "9b1deb4d-3b7d-4bad-9bid-2b0d7b4dcb6d",
  },
]

/**
 * Common function used to assign ids to flat list item
 * @param item
 */
export const keyExtractor = (item: any) => item?._id

export const createCustomizationAndGroupMapping = (customizations: any[]) => {
  let newCustomizationGroupMappings: any = {}
  let customizationToGroupMap: any = {}
  customizations?.forEach((customization: any) => {
    const groupId = customization.parent
    const childId = customization.id

    customizationToGroupMap = {
      ...customizationToGroupMap,
      [customization.id]: customization.childs ?? [],
    }

    if (!newCustomizationGroupMappings[groupId]) {
      newCustomizationGroupMappings[groupId] = new Set()
    }
    newCustomizationGroupMappings[groupId].add(childId)
  })

  return customizationToGroupMap
}

export const getDefaultTrackedKeys = (data: any) => {
  const nonEmptyKeys = Object.keys(data).filter((key) => data[key].length > 0)
  let result = [...nonEmptyKeys]
  nonEmptyKeys.forEach((key) => {
    result = [...result, ...data[key]]
  })
  return new Set(result)
}

export const constructQuoteObject = (cartItems: any[]) => {
  const map = new Map()
  cartItems.forEach((item: any) => {
    let bpp_uri = item?.product?.context?.bpp_uri
    if (bpp_uri) {
      item.bpp_uri = bpp_uri
    }

    const provider_id = item?.provider?.id
    if (map.get(provider_id)) {
      return map.set(provider_id, [...map.get(provider_id), item])
    }
    return map.set(provider_id, [item])
  })
  return Array.from(map.values())
}

export const getPriceWithCustomisations = (cartItem: any) => {
  let basePrice = cartItem.item.product.price.value
  let price = 0
  cartItem?.item?.customisations?.map(
    (customisation: any) => (price += customisation.item_details.price.value)
  )

  return basePrice + price
}

export const removeNullValues = (object: any) => {
  Object.entries(object).forEach(([k, v]: any) => {
    if (v && typeof v === "object") {
      removeNullValues(v)
    }
    if (
      (v && typeof v === "object" && !Object.keys(v).length) ||
      v === null ||
      v === undefined ||
      v.length === 0
    ) {
      if (Array.isArray(object)) {
        object.splice(k, 1)
      } else if (!(v instanceof Date)) {
        delete object[k]
      }
    }
  })
  return object
}

const getSingleCustomization = (
  groupId: any,
  customizationState: any,
  selectedCustomizationIds: any[]
) => {
  let group = customizationState[groupId]
  if (!group) {
    return selectedCustomizationIds
  }

  group.selected.map((selected: any) =>
    selectedCustomizationIds.push(selected.id)
  )
  group?.childs?.map((child: any) => {
    selectedCustomizationIds = getSingleCustomization(
      child,
      customizationState,
      selectedCustomizationIds
    )
  })
  return selectedCustomizationIds
}

export const getCustomizations = async (
  product: any,
  customizationState: any
) => {
  const { customisation_items } = product
  const customizations: any[] = []

  const firstGroupId = customizationState.firstGroup?.id
  if (!firstGroupId) {
    return
  }

  let selectedCustomizationIds: any[] = []
  customizationState?.parentCustomizations?.forEach((one: string) => {
    const ids = getSingleCustomization(one, customizationState, [])
    selectedCustomizationIds = selectedCustomizationIds.concat(ids)
  })

  for (const cId of selectedCustomizationIds) {
    let customizationItem = customisation_items.find(
      (item: any) => item.local_id === cId
    )
    if (customizationItem) {
      customizationItem = {
        ...customizationItem,
        quantity: {
          count: 1,
        },
      }
      customizations.push(customizationItem)
    }
  }
  return customizations
}

export const isItemCustomization = (tags: any[]) => {
  let isCustomization = false
  tags?.forEach((tag: any) => {
    if (tag.code === "type") {
      tag.list.forEach((listOption: any) => {
        if (
          listOption.code === "type" &&
          listOption.value === "customization"
        ) {
          isCustomization = true
          return true
        }
      })
    }
  })
  return isCustomization
}

const parseDuration = (duration: number) => {
  return moment.duration(duration).asMilliseconds()
}

export const compareDateWithDuration = (duration: any, dateStr: string) => {
  const currentDate = new Date()
  const providedDate = new Date(dateStr)
  // Parse the duration
  const durationInMilliseconds = parseDuration(duration)
  // Add the duration to the provided date
  const newDate = new Date(providedDate.getTime() + durationInMilliseconds)
  // Compare the new date with the current date
  return currentDate.getTime() > newDate.getTime()
}

export const getFilterCategory = (tags: any) => {
  let category = ""

  const selectedTag = tags?.find((tag: any) => tag.code === "veg_nonveg")
  const veg = selectedTag?.list?.find(
    (one: any) => one.code === "veg" && one.value.toLowerCase() === "yes"
  )
  if (veg) {
    category = "veg"
  } else {
    const nonVeg = selectedTag?.list?.find(
      (one: any) => one.code === "non_veg" && one.value.toLowerCase() === "yes"
    )
    if (nonVeg) {
      category = "nonveg"
    } else {
      const egg = selectedTag?.list?.find(
        (one: any) => one.code === "egg" && one.value.toLowerCase() === "yes"
      )
      if (egg) {
        category = "egg"
      }
    }
  }

  return category
}

export const getUrlParams = (url: string) => {
  const urlParams: any = {}
  const params = url.split("?")
  if (params.length > 1) {
    const variables = params[1].split("&")
    variables.forEach((one: any) => {
      const fields = one.split("=")
      if (fields.length > 0) {
        urlParams[fields[0]] = fields[1]
      }
    })
  }
  return urlParams
}

// Encryption function
export const encryptData = (data: any) => {
  // Convert data to string
  const dataString = JSON.stringify(data)
  // Encrypt data using AES encryption algorithm with the provided key
  return CryptoJS.AES.encrypt(
    dataString,
    process.env.REACT_APP_ENCRYPTION_KEY
  ).toString()
}

// Decryption function
export const decryptData = (encryptedData: any) => {
  // Decrypt data using AES decryption algorithm with the provided key
  const decryptedData = CryptoJS.AES.decrypt(
    encryptedData,
    process.env.REACT_APP_ENCRYPTION_KEY
  ).toString(CryptoJS.enc.Utf8)

  // Parse decrypted data back to JSON
  return JSON.parse(decryptedData)
}

export const getLocale = (code: string) => {
  switch (code) {
    case "en":
      return "en-US"

    case "hi":
      return "hi-IN"

    case "mr":
      return "mr-IN"

    case "ta":
      return "ta-IN"

    case "bn":
      return "bn-IN"
  }
}

export const convertHexToName = (hex: any) => {
  const hexLowerCase = hex.toLowerCase()
  return COLOR_CODE_TO_NAME?.hasOwnProperty(hexLowerCase)
    ? COLOR_CODE_TO_NAME[hexLowerCase]
    : hex
}

/**
 * Function to get current time in HHMM format
 * @returns number (e.g., 1345 for 13:45)
 */
const getCurrentTimeAsNumber = (): number => {
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  return hours * 100 + minutes // Converts to HHMM format
}

/**
 * Function to check if current time falls within the range for the current day
 * @param timeRanges - The predefined time range object
 * @returns boolean
 */
export const isCurrentTimeInRange = (
  timeRanges: Record<string, TimeRange[]>
): boolean => {
  if (!timeRanges) {
    return false
  }
  let currentDay = new Date().getDay()
  if (currentDay === 0) {
    currentDay = 7
  }
  const currentTime = getCurrentTimeAsNumber()

  const ranges = timeRanges[currentDay.toString()] || []

  return ranges.some(({ start, end }) => {
    if (start === 0 && end === 0) {
      return false
    } // Special case: start 0 and end 0 means closed
    return currentTime >= start && currentTime <= end
  })
}
export const formatNumber = (num: any) => (num < 10 ? "0" + num : String(num))

/**
 * Convert time from HHMM (e.g., 2359) to 12-hour format with AM/PM
 * @param time - Time in HHMM format
 * @returns string in 12-hour format with AM/PM
 */
export const formatTime12Hour = (time: number): string => {
  const hours24 = Math.floor(time / 100)
  const minutes = time % 100
  const period = hours24 >= 12 ? "pm" : "am"
  const hours12 = hours24 % 12 || 12 // Convert 0 or 24 to 12
  return `${hours12}:${minutes.toString().padStart(2, "0")} ${period}`
}

/**
 * Find the next start time from the current time
 */
export const getNextStartTime = (
  timeRanges: Record<string, TimeRange[]>
): string => {
  if (!timeRanges || Object.keys(timeRanges).length === 0) {
    return ""
  }

  const currentDay = new Date().getDay() || 7 // Adjust Sunday (0) to 7
  const currentMinutes = new Date().getHours() * 60 + new Date().getMinutes()

  // Iterate over the next 7 days, starting from today
  for (let dayOffset = 0; dayOffset < 7; dayOffset++) {
    const dayIndex = ((currentDay + dayOffset - 1) % 7) + 1 // Wrap around days (1-7)
    const ranges = timeRanges[dayIndex.toString()] || []

    for (const range of ranges) {
      if (range.start === 0 && range.end === 0) {
        continue // Skip closed ranges
      }

      const endMinutes = Math.floor(range.end / 100) * 60 + (range.end % 100)

      // If today, ensure the end time is in the future
      if (dayOffset === 0 && endMinutes <= currentMinutes) {
        continue
      }

      return formatTime12Hour(range.start)
    }
  }

  return "" // Return an empty string if no valid start time is found
}

export const getStoreTimeForDay = (
  timeRanges: Record<string, TimeRange[]>
): TimeRange[] => {
  if (!timeRanges || Object.keys(timeRanges).length === 0) {
    return []
  }
  const currentDay = new Date().getDay() || 7 // Sunday as 7
  return timeRanges[currentDay.toString()] || []
}

export const getFulfilmentContact = (fulfilmentList: any[], type: string) => {
  const fulfilment = fulfilmentList.find((one) => one.type === type)
  return fulfilment?.contact?.phone ?? ""
}

export const isValidQRURL = (urlParams: any) => {
  return (
    urlParams.hasOwnProperty("context.action") &&
    urlParams.hasOwnProperty("context.bpp_id") &&
    urlParams.hasOwnProperty("context.domain") &&
    urlParams.hasOwnProperty("message.intent.provider.id") &&
    urlParams["context.action"] === "search"
  )
}

export const isValidNewUrlSchema = (urlParams: any) => {
  return (
    urlParams.hasOwnProperty("message.tags.descriptor.name") ||
    (urlParams.hasOwnProperty("context.bpp_id") &&
      urlParams.hasOwnProperty("message.intent.provider.id"))
  )
}

export const isDomainSupported = (domain: string) => {
  return CATEGORIES.findIndex((one: any) => one.domain === domain) > -1
}

export const isNewDeepLinkSupported = (domain: string) => {
  return DEEP_LINK_DOMAINS.findIndex((one: any) => one === domain) > -1
}

export const areCustomisationsSame = (
  existingIds: any[],
  currentIds: any[]
) => {
  if (existingIds.length !== currentIds.length) {
    return false
  }

  existingIds.sort()
  currentIds.sort()

  for (let i = 0; i < existingIds.length; i++) {
    if (existingIds[i] !== currentIds[i]) {
      return false
    }
  }

  return true
}

export const formatCustomizationGroups = (groups: any) => {
  const formattedGroups = groups?.map((group: any) => {
    let minConfig, maxConfig, inputTypeConfig, seqConfig

    group?.tags?.forEach((tag: any) => {
      if (tag.code === "config") {
        tag.list.forEach((one: any) => {
          if (one.code === "min") {
            minConfig = one.value
          }
          if (one.code === "max") {
            maxConfig = one.value
          }
          if (one.code === "input") {
            inputTypeConfig = one.value
          }
          if (one.code === "seq") {
            seqConfig = one.value
          }
        })
      }
    })

    const customization: any = {
      id: group.local_id,
      name: group.descriptor.name,
      inputType: inputTypeConfig,
      minQuantity: Number(minConfig),
      maxQuantity: Number(maxConfig),
      seq: Number(seqConfig),
    }

    if (inputTypeConfig === "input") {
      customization.special_instructions = ""
    }

    return customization
  })
  return formattedGroups.sort((a: any, b: any) => a.seq - b.seq)
}

export const formatCustomizations = (items: any) => {
  return items?.map((customization: any) => {
    let parent = null
    let isDefault = false
    let childs: any[] = []
    let vegNonVegTag: any = null

    customization?.item_details?.tags?.forEach((tag: any) => {
      if (tag.code === "parent") {
        tag.list.forEach((one: any) => {
          if (one.code === "default") {
            isDefault = one.value.toLowerCase() === "yes"
          } else if (one.code === "id") {
            parent = one.value
          }
        })
      } else if (tag.code === "child") {
        tag.list.forEach((item: any) => {
          childs.push(item.value)
        })
      } else if (tag.code === "veg_nonveg") {
        vegNonVegTag = tag
      }
    })

    const vegNonVegTagCodes = vegNonVegTag?.list
      ?.filter((item: any) => item.value === "yes")
      ?.map((item: any) => item.code)

    return {
      id: customization.item_details.id,
      name: customization.item_details.descriptor.name,
      price: customization.item_details.price.value,
      inStock: customization.item_details.quantity.available.count > 0,
      parent,
      childs: childs?.length > 0 ? childs : null,
      isDefault: isDefault,
      vegNonVeg: vegNonVegTagCodes ? vegNonVegTagCodes[0] : "",
    }
  })
}

export const getAddressString = (address: any) => {
  return [
    address?.name,
    address?.street,
    address?.locality,
    address?.city,
    address?.state && `${address?.state} - ${address?.area_code}`,
    address?.country,
  ]
    .filter((value) => value) // Filters out null, undefined, or empty string values
    .join(", ")
}

export const isJSON = (str: string) => {
  try {
    const parsed = JSON.parse(str)
    // Check if the parsed result is an object or an array, since these are valid JSON types
    return typeof parsed === "object" && parsed !== null
  } catch (e) {
    return false // Not a valid JSON string
  }
}

export const checkIfFloat = (number: number) => {
  return Number.isInteger(number) ? number.toString() : number.toFixed(2)
}

export const emptyAlertCallback = () => {}

export const errorCallback = (error: any) => {
  // crashlytics().recordError(error);
}

export const formatToIndianCurrency = (number: string) => {
  const numberString = number.toString()
  const afterPoint = numberString.includes(".")
    ? numberString.split(".")[1]
    : ""
  let beforePoint = numberString.split(".")[0]

  let lastThreeDigits = beforePoint.slice(-3)
  const otherDigits = beforePoint.slice(0, -3)

  if (otherDigits !== "") {
    lastThreeDigits = "," + lastThreeDigits
  }

  const formattedNumber =
    otherDigits.replace(/\B(?=(\d{2})+(?!\d))/g, ",") + lastThreeDigits

  return formattedNumber + (afterPoint ? "." + afterPoint : "")
}

export const cleanPhoneNumber = (phoneNumber: string) => {
  return phoneNumber.replace("+91", "").replace(/[^\d]/g, "")
}

export const segregateByTitleType = (data: any[]) => {
  return data.reduce((acc, item) => {
    const { title_type } = item
    if (!acc[title_type]) {
      acc[title_type] = []
    }
    acc[title_type].push(item)
    return acc
  }, {})
}

export const getSubdomain = (url: string, schema: string = "beckn") => {
  try {
    const parts = url?.split("?")
    if (!parts) {
      return null
    }
    const domain = parts[0].replace(`${schema}://`, "")
    const subDomains = domain.split(".")
    if (!subDomains || subDomains.length < 2) {
      return null
    } else {
      return subDomains[0]
    }
  } catch (error) {
    return null
  }
}

export const extractUrlPage = (url: string) => {
  return url.split("?")[0].split("/").pop()
}
export function localSearch(
  dataArray: any[],
  searchText: string,
  searchableDataArray?: any[]
) {
  let text = searchText.replace(/[^\w\s]/gi, "")
  let regexp = new RegExp(text, "gi")
  let tempData: any[] = []

  if (
    searchableDataArray &&
    searchableDataArray.length !== 0 &&
    searchableDataArray !== null &&
    searchableDataArray !== undefined
  ) {
    searchableDataArray.forEach((searchableValue) => {
      dataArray.forEach((dataValue) => {
        if (String(dataValue[searchableValue]).match(regexp)) {
          const isAvailable = tempData.some(
            (childValue) => dataValue === childValue
          )
          if (!isAvailable) {
            tempData.push(dataValue)
          }
        }
      })
    })
  } else {
    tempData = dataArray.filter((e) =>
      Object.values(e).join("").toLowerCase().match(regexp)
    )
  }

  return tempData
}
export const getFirstMaxItems = (list: any[], maxItems: number) =>
  list.length > maxItems ? list.slice(0, maxItems) : list

export const getCurrentDate = () => new Date().toISOString()

export const validateCityAndPincode = (data: any) => {
  if (!data) {
    return false
  }
  const { city, pincode, village } = data

  // Check if city and pincode are not null, are strings, and have length > 1
  const isCityValid = city && typeof city === "string" && city.length > 1
  const isVillageValid =
    village && typeof village === "string" && village.length > 1
  const isPincodeValid =
    pincode && typeof pincode === "string" && pincode.length > 1

  return (isCityValid || isVillageValid) && isPincodeValid
}

export const validateLatLong = (data: any) => {
  if (!data) {
    return false
  }
  const { lat, lng } = data

  return lat != null && lat !== "" && lng != null && lng !== ""
}

export const formatAddress = (address: any) => {
  if (!address) return ""

  const { door, building, street, locality, city, areaCode, state, country } =
    address || {}

  const addressParts = []

  if (door) addressParts.push(door)
  if (building) addressParts.push(building)
  if (street) addressParts.push(street)
  if (locality) addressParts.push(locality)

  if (city || areaCode) {
    const cityPart = [city, areaCode ? `- ${areaCode}` : ""]
      .filter(Boolean)
      .join(" ")
    addressParts.push(cityPart.trim())
  }

  if (state) addressParts.push(state)
  if (country) addressParts.push(country)

  return addressParts.join(", ")
}

export const sortSectionWidgets = (list: any[]) => {
  return list.sort((a, b) => {
    const seqA = a.sequence
    const seqB = b.sequence

    const isNullish = (val: any) => val === null || val === undefined

    if (isNullish(seqA) && isNullish(seqB)) {
      return 0
    }
    if (isNullish(seqA)) {
      return 1
    }
    if (isNullish(seqB)) {
      return -1
    }

    // If sequences are numbers
    if (typeof seqA === "number" && typeof seqB === "number") {
      return seqA - seqB
    }

    // If sequences are strings
    return String(seqA).localeCompare(String(seqB))
  })
}

type BrandOutletResult = {
  bppId: string
  domain: string
  providerId: string
  locationId?: string
}

export const parseBrandOutletId = (
  brandOutletId: string
): BrandOutletResult | null => {
  const [bppId, rest] = brandOutletId.split("_ONDC:RET")

  if (!rest) return null

  const match = rest.match(/^(\d+)_([^_]+)(?:_(.+))?$/)

  if (!match) return null

  const domain = `ONDC:RET${match[1]}`
  const providerId = match[2]
  const locationId = match[3] // might be undefined

  return { bppId, domain, providerId, ...(locationId && { locationId }) }
}

export const distributeProviderId = (input: string) => {
  const match = input.match(/^(.*)_(ONDC:RET\d+)_([^_]+)$/)

  if (match) {
    return { bppId: match[1], domain: match[2], providerId: match[3] }
  } else {
    return { bppId: null, domain: null, providerId: null }
  }
}

export const getMobilePageFromDomain = (domain: string): string => {
  switch (domain) {
    case "ONDC:RET11":
      return MOBILE_PAGES.FOOD
    case "ONDC:RET10":
      return MOBILE_PAGES.GROCERY
    case "ONDC:RET13":
      return MOBILE_PAGES.BEAUTY
    case "ONDC:RET14":
      return MOBILE_PAGES.ELECTRONICS
    default:
      return MOBILE_PAGES.ALL
  }
}
