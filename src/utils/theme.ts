import {createTheme} from "@mui/material/styles";
import React from "react";

declare module "@mui/material/styles" {
  interface TypographyVariants {
    headlineLarge: React.CSSProperties;
    headlineMedium: React.CSSProperties;
    headlineSmall: React.CSSProperties;

    titleLarge: React.CSSProperties;
    titleMedium: React.CSSProperties;
    titleSmall: React.CSSProperties;

    body3: React.CSSProperties;
    labelLarge: React.CSSProperties;
    labelMedium: React.CSSProperties;
    labelSmall: React.CSSProperties;

    bodyLarge: React.CSSProperties;
    bodyMedium: React.CSSProperties;
    bodySmall: React.CSSProperties;
  }

  interface TypographyVariantsOptions {
    headlineLarge?: React.CSSProperties;
    headlineMedium?: React.CSSProperties;
    headlineSmall?: React.CSSProperties;

    titleLarge?: React.CSSProperties;
    titleMedium?: React.CSSProperties;
    titleSmall?: React.CSSProperties;

    body3?: React.CSSProperties;

    labelLarge?: React.CSSProperties;
    labelMedium?: React.CSSProperties;
    labelSmall?: React.CSSProperties;

    bodyLarge?: React.CSSProperties;
    bodyMedium?: React.CSSProperties;
    bodySmall?: React.CSSProperties;
  }
}

declare module "@mui/material/Typography" {
  interface TypographyPropsVariantOverrides {
    headlineLarge: true;
    headlineMedium: true;
    headlineSmall: true;

    titleLarge: true;
    titleMedium: true;
    titleSmall: true;

    body3: true;

    labelLarge: true;
    labelMedium: true;
    labelSmall: true;

    bodyLarge: true;
    bodyMedium: true;
    bodySmall: true;
  }
}

// Define the theme options
const themeOptions = {
  components: {
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          height: 48,
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        input: {
          fontWeight: 400,
          fontFamily: 'Inter-Regular, sans-serif',
          color: '#1A1A1A',
          '::placeholder': {
            fontWeight: 400,
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          fontWeight: '600',
        },
      },
    },
    MuiInputAdornment: {
      styleOverrides: {
        root: {
          fontWeight: 400,
          color: '#1A1A1A',
          fontFamily: 'Inter-Regular, sans-serif',
          opacity: 1,
        },
      },
    },
  },
  palette: {
    primary: {
      main: '#008ECC',
      light: '#06477D',
      dark: '#023259',
      contrastText: '#fff',
    },
    success: {
      main: '#419E6A',
      light: '#A5E1BF',
      dark: '#00642C',
      contrastText: '#fff',
    },
    warning: {
      main: '#F9C51C',
      light: '#FCF17E',
      dark: '#C89A04',
      contrastText: '#fff',
    },
    error: {
      main: '#D83232',
      light: '#FC9595',
      dark: '#9E2626',
      contrastText: '#fff',
    },
    neutral: {
      main: '#1A1A1A',
      light: '#686868',
      dark: '#B5B5B5',
      contrastText: '#fff',
    },
    background: {
      default: '#FAFAFA',
      paper: '#fff',
    },
    text: {
      primary: '#1D1D1D',
      secondary: '#B5B5B5',
      disabled: '#686868',
    },
    neutral500: '#1D1D1D',
    neutral400: '#1A1A1A',
    neutral300: '#686868',
    neutral200: '#B5B5B5',
    neutral100: '#E8E8E8',
    neutral50: '#FAFAFA',

    primary300: '#023259',
    primary200: '#06477D',
    primary50: '#ECF3F8',

    success600: '#419E6A',
    success400: '#A5E1BF',
    success50: '#E8FCF1',

    warning600: '#F9C51C',
    warning400: '#FCF17E',
    warning50: '#FEFCE4',

    error600: '#D83232',
    error400: '#FC9595',
    error50: '#FFEBEB',

    black: '#000',
    white: '#fff',
    disabled: '#686868',

    surfaceDisabled: '#E8E8E8',
    onSurfaceDisabled: '#B5B5B5',
  },
  typography: {
    h1: {
      fontWeight: 700,
      fontSize: 32,
      lineHeight: '44px',
      fontFamily: 'Inter-Bold, sans-serif',
    },
    h2: {
      fontWeight: 700,
      fontSize: 24,
      lineHeight: '36px',
      fontFamily: 'Inter-Bold, sans-serif',
    },
    h3: {
      fontWeight: 700,
      fontSize: 18,
      lineHeight: '28px',
      fontFamily: 'Inter-Bold, sans-serif',
    },
    body1: {
      fontWeight: 600,
      fontSize: 14,
      lineHeight: '22px',
      fontFamily: 'Inter-Medium, sans-serif',
    },
    body2: {
      fontWeight: 500,
      fontSize: 14,
      lineHeight: '22px',
      fontFamily: 'Inter-Medium, sans-serif',
    },
    body3: {
      fontWeight: 400,
      fontSize: 14,
      lineHeight: '18px',
      fontFamily: 'Inter-Regular, sans-serif',
    },
    headlineLarge: {
      fontWeight: 700,
      fontSize: 32,
      lineHeight: '44px',
      fontFamily: 'Inter-Bold, sans-serif',
    },
    headlineMedium: {
      fontWeight: 700,
      fontSize: 24,
      lineHeight: '36px',
      fontFamily: 'Inter-Bold, sans-serif',
    },
    headlineSmall: {
      fontWeight: 700,
      fontSize: 18,
      lineHeight: '28px',
      fontFamily: 'Inter-Bold, sans-serif',
    },

    titleLarge: {
      fontWeight: 600,
      fontSize: 16,
      lineHeight: '24px',
      fontFamily: 'Inter-Medium, sans-serif',
    },
    titleMedium: {
      fontWeight: 500,
      fontSize: 16,
      lineHeight: '24px',
      fontFamily: 'Inter-Medium, sans-serif',
    },
    titleSmall: {
      fontWeight: 400,
      fontSize: 16,
      lineHeight: '24px',
      fontFamily: 'Inter-Regular, sans-serif',
    },

    labelLarge: {
      fontWeight: 600,
      fontSize: 11,
      lineHeight: '18px',
      fontFamily: 'Inter-Medium, sans-serif',
    },
    labelMedium: {
      fontWeight: 500,
      fontSize: 11,
      lineHeight: '18px',
      fontFamily: 'Inter-Medium, sans-serif',
    },
    labelSmall: {
      fontWeight: 400,
      fontSize: 11,
      lineHeight: '18px',
      fontFamily: 'Inter-Regular, sans-serif',
    },

    bodyLarge: {
      fontWeight: 600,
      fontSize: 14,
      lineHeight: '22px',
      fontFamily: 'Inter-Medium, sans-serif',
    },
    bodyMedium: {
      fontWeight: 500,
      fontSize: 14,
      lineHeight: '22px',
      fontFamily: 'Inter-Medium, sans-serif',
    },
    bodySmall: {
      fontWeight: 400,
      fontSize: 14,
      lineHeight: '18px',
      fontFamily: 'Inter-Regular, sans-serif',
    },

    button: {
      textTransform: 'capitalize'
    }
  },
};

export const theme = createTheme(themeOptions);
