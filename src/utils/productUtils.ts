export const mergeAttributesUrls = (attributes: any) => {
  let url: string = '';
  Object.keys(attributes).forEach(key => {
    const value = attributes[key];
    if (!value.skipAttribute) {
      url += `&product_attr_${key}=${value
        .map((one: string) => encodeURIComponent(one))
        .join(',')}`;
    } else {
      url += `&minPrice=${value.values[0]}&maxPrice=${value.values[1]}`;
    }
  });

  return url;
};
