import {useTranslation} from 'react-i18next';

const useGetFilterAttribute = () => {
  const {t} = useTranslation();

  const translateFilterAttribute = (attribute: string) => {
    const name = t(`Filters.${attribute.toLowerCase()}`);
    if (name.includes('Filters.')) {
      return attribute;
    } else {
      return name;
    }
  };

  return {translateFilterAttribute};
};

export default useGetFilterAttribute;
