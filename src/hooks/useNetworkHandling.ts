import axios from 'axios';
import {useSelector} from 'react-redux';

const useNetworkHandling = () => {
  const {token, language} = useSelector((state: any) => state.auth);
  const {
    appVersion,
    deviceManufacturer,
    deviceModel,
    OSVersion,
    deviceName,
    deviceIdentity,
  } = useSelector((state: any) => state.device);

  const getAuthConfig = (cancelToken: AbortController | null = null) => {
    const config: any = {
      headers: {
        Authorization: `Bearer ${token}`,
        targetlanguage: language,
        appVersion,
        deviceManufacturer,
        deviceModel,
        OSVersion,
        deviceName,
        deviceIdentity,
        web: true,
      },
    };

    if (cancelToken) {
      config.signal = cancelToken.signal; // ✅ Correct way to handle request cancellation
    }
    return config;
  };


  const getConfig = (cancelToken = null) => {
    const language = localStorage.getItem("language") || "en";
    const config: any = {
      headers: {
        targetlanguage: language,
        appVersion,
        deviceManufacturer,
        deviceModel,
        OSVersion,
        deviceName,
        deviceIdentity,
        web: true,
      },
    };
    if (cancelToken) {
      process.env.REACT_APP_cancelToken = cancelToken;
    }
    return config;
  };

  const getAuthConfigForToken = (accessToken: string, cancelToken = null) => {
    const config: any = {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        targetlanguage: language,
        appVersion,
        deviceManufacturer,
        deviceModel,
        OSVersion,
        deviceName,
        deviceIdentity,
        web: true,
      },
    };
    if (cancelToken) {
      process.env.REACT_APP_cancelToken = cancelToken;
    }
    return config;
  };

  const postData = async (url: string, params: any, cancelToken: any) => {
    try {
      const config = getConfig(cancelToken);
      return await axios.post(encodeURI(url), params, config);
    } catch (e: any) {
      // // crashlytics().recordError(e);
      throw e;
    }
  };

  const postDataWithAuth = async (
    url: string,
    params: any,
    cancelToken: any,
  ) => {
    try {
      const config = getAuthConfig(cancelToken);
      return await axios.post(encodeURI(url), params, config);
    } catch (e: any) {
      // crashlytics().recordError(e);
      throw e;
    }
  };

  const putDataWithAuth = async (
    url: string,
    params: any,
    cancelToken: any,
  ) => {
    try {
      const config = getAuthConfig(cancelToken);
      return await axios.put(encodeURI(url), params, config);
    } catch (e: any) {
      // crashlytics().recordError(e);
      throw e;
    }
  };

  const getDataWithWithoutEncode = async (url: string, cancelToken: any) => {
    try {
      const config = getAuthConfig(cancelToken);
      return await axios.get(url, config);
    } catch (e: any) {
      // crashlytics().recordError(e);
      throw e;
    }
  };

  const getDataWithToken = async (
    url: string,
    accessToken: string,
    cancelToken: any,
  ) => {
    try {
      const config = getAuthConfigForToken(accessToken, cancelToken);
      return await axios.get(encodeURI(url), config);
    } catch (e: any) {
      // crashlytics().recordError(e);
      throw e;
    }
  };

  const postDataWithToken = async (
    url: string,
    params: any,
    accessToken: string,
    cancelToken: any,
  ) => {
    try {
      const config = getAuthConfigForToken(accessToken, cancelToken);
      return await axios.post(encodeURI(url), params, config);
    } catch (e: any) {
      // crashlytics().recordError(e);
      throw e;
    }
  };

  const getData = async (url: string, cancelToken: any) => {
    try {
      const config = getConfig(cancelToken);
      return await axios.get(encodeURI(url), config);
    } catch (e: any) {
      // crashlytics().recordError(e);
      throw e;
    }
  };

  const getDataWithAuth = async (url: string, cancelToken: any) => {
    try {
      const config = getAuthConfig(cancelToken);
      return await axios.get(encodeURI(url), config);
    } catch (e: any) {
      // crashlytics().recordError(e);
      throw e;
    }
  };

  const deleteDataWithAuth = async (url: string, cancelToken: any) => {
    try {
      const config = getAuthConfig(cancelToken);
      return await axios.delete(encodeURI(url), config);
    } catch (e: any) {
      // crashlytics().recordError(e);
      throw e;
    }
  };

  return {
    getData,
    getDataWithAuth,
    getDataWithToken,
    postDataWithToken,
    postData,
    postDataWithAuth,
    getDataWithWithoutEncode,
    putDataWithAuth,
    deleteDataWithAuth,
  };
};

export default useNetworkHandling;
