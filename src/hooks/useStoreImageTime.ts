import {useMemo} from 'react';
import useMinutesToString from '../modules/main/hooks/useMinutesToString';

const useStoreImageTime = (outlet: any) => {
  const {convertMinutesToHumanReadable} = useMinutesToString();

  return useMemo(() => {
    let source = null;
    let time = {type: 'minutes', time: 0};

    if (outlet) {
      if (outlet?.provider_descriptor?.symbol) {
        source = {uri: outlet.provider_descriptor.symbol};
      } else if (
        outlet?.provider_descriptor?.images &&
        outlet.provider_descriptor.images.length > 0
      ) {
        source = {uri: outlet.provider_descriptor.images[0]};
      }

      if (outlet?.minDaysWithTTS) {
        time = convertMinutesToHumanReadable(
          Number(outlet.minDaysWithTTS / 60),
        );
      }
    }

    return {timeToShip: time, imageSource: source};
  }, [outlet]);
};

export default useStoreImageTime;
