import {useNavigate} from 'react-router-dom';
import {useDispatch} from 'react-redux';
import {useTranslation} from 'react-i18next';
import {alertWithOneButton} from '../utils/alerts';
import {clearAll} from '../utils/storage';
import {logoutUser} from '../toolkit/reducer/auth';
import {clearAddress} from '../toolkit/reducer/address';
import {clearCart} from '../toolkit/reducer/cart';
import {clearComplaint} from '../toolkit/reducer/complaint';
import {clearOrder} from '../toolkit/reducer/order';
import {clearStoresList} from '../toolkit/reducer/stores';
import {useToast} from "./toastProvider";

let sessionExpiredMessageShown = false;

const useNetworkErrorHandling = () => {
  const {showToast} = useToast();
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const clearDataAndLogout = () => {
    clearAll();
    dispatch(logoutUser());
    dispatch(clearAddress());
    dispatch(clearCart());
    dispatch(clearComplaint());
    dispatch(clearOrder());
    dispatch(clearStoresList());

    navigate('/login', {replace: true});
  };

  const handleApiError = (error, setError = null) => {
    if (error.code === 'ERR_CANCELED') {
      return;
    }

    if (error.response) {
      handleResponseError(error.response, setError);
    } else if (error.request) {
      handleNetworkError(setError);
    } else {
      handleUnknownError(setError);
    }
  };

  const handleResponseError = (response, setError) => {
    switch (response.status) {
      case 401:
        handleSessionExpired();
        break;
      case 403:
        showToast(
          t('Global.Please login to proceed with this request'), 'error'
        );
        break;
      case 426:
        handleVersionMismatch();
        break;
      default:
        handleOtherResponseErrors(response, setError);
        break;
    }
  };

  const handleSessionExpired = () => {
    if (!sessionExpiredMessageShown) {
      sessionExpiredMessageShown = true;
      alertWithOneButton(
        t('Global.Session Expired'),
        t('Global.Session expired, please login again to continue'),
        t('Global.Logout'),
        () => {
          sessionExpiredMessageShown = false;
          clearDataAndLogout();
        },
      );
    }
  };

  const handleVersionMismatch = () => {
  };

  const handleOtherResponseErrors = (response, setError) => {
    if (setError) {
      setError(response.data);
    } else {
      showToast(
        t('Global.Something went wrong, please try again after some time'), 'error'
      );
    }
  };

  const handleNetworkError = setError => {
    const message = t(
      'Global.Internet connection not available. Please check internet connection',
    );
    setError ? setError(message) : showToast(message, 'error');
  };

  const handleUnknownError = setError => {
    const message = t(
      'Global.Something went wrong, please try again after some time',
    );
    setError ? setError(message) : showToast(message, 'error');
  };

  return {handleApiError};
};

export default useNetworkErrorHandling;
