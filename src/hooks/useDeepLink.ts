import axios from "axios"
import { useCallback, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import useNetworkHandling from "../hooks/useNetworkHandling"
import { PAGE, SERVICEABLE_LOCATIONS } from "../utils/apiActions"
import { FB_DOMAIN } from "../utils/constants"
import { getStoredData } from "../utils/storage"
import {
  extractUrlPage,
  getSubdomain,
  getUrlParams,
  isDomainSupported,
  isNewDeepLinkSupported,
  isValidNewUrlSchema,
  isValidQRURL,
  parseBrandOutletId,
} from "../utils/utils"

// Provide the context
const useDeepLink = () => {
  const HELP_EMAIL = process.env.REACT_APP_HELP_EMAIL
  const API_BASE_URL = process.env.REACT_APP_API_BASE_URL

  const { getDataWithToken } = useNetworkHandling()
  const { t } = useTranslation()
  const [linkModalVisible, setLinkModalVisible] = useState<boolean>(false)
  const [linkMessage, setLinkMessage] = useState<string>("")
  const currentNavigation = useRef<any>(null)

  const showLinkMessage = (message: string) => {
    setLinkMessage(message)
    setLinkModalVisible(true)
  }

  const navigateToStorePage = (
    brandId: string,
    locationId: string | undefined,
    userToken: string,
    userAddress: any,
    navigation: any,
    item: any = null
  ) => {
    const pageParams: any = { brandId }
    if (locationId) {
      pageParams.outletId = `${brandId}_${locationId}`
      // navigation.navigate('BrandDetails', pageParams);
      const result = parseBrandOutletId(pageParams.outletId)

      if (item) {
        if (item.params.locationId) {
          navigation(
            `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${result?.locationId}`
          )
        } else {
          navigation(
            `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}`
          )
        }
      } else {
        navigation(`/store?provider_id=${brandId}&locationId=${locationId}`)
      }
    } else {
      const source = axios.CancelToken.source()
      const { lat, lng, areaCode } = userAddress?.address
      const params = new URLSearchParams()
      params.append("latitude", lat)
      params.append("longitude", lng)
      params.append("pincode", areaCode)
      getDataWithToken(
        `${API_BASE_URL}${SERVICEABLE_LOCATIONS}?${params.toString()}&providerId=${brandId}`,
        userToken,
        source.token
      )
        .then((locationDetails: any) => {
          if (locationDetails?.data?.data?.length > 0) {
            pageParams.outletId = locationDetails.data.data[0].id
            const result = parseBrandOutletId(pageParams.outletId)

            if (item) {
              if (item.params.locationId) {
                navigation(
                  `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${result?.locationId}`
                )
              } else {
                navigation(
                  `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}`
                )
              }
            } else {
              navigation(`/store?provider_id=${brandId}`)
            }
          } else {
            currentNavigation.current = navigation
            showLinkMessage(
              t("Provider Details.This store does not service your location")
            )
            currentNavigation.current = navigation
          }
        })
        .catch(() => {
          currentNavigation.current = navigation
          navigationToDashboard()
        })
    }
  }

  const navigateToProductPage = (
    domain: string,
    brandId: string,
    productId: string,
    userToken: string,
    userAddress: any,
    navigation: any,
    item: any = null
  ) => {
    const pageParams: any = { brandId }
    const source = axios.CancelToken.source()
    const { lat, lng, areaCode } = userAddress?.address
    const params = new URLSearchParams()
    params.append("latitude", lat)
    params.append("longitude", lng)
    params.append("pincode", areaCode)
    getDataWithToken(
      `${API_BASE_URL}${SERVICEABLE_LOCATIONS}?${params.toString()}&providerId=${brandId}`,
      userToken,
      source.token
    )
      .then((locationDetails: any) => {
        if (locationDetails?.data?.data?.length > 0) {
          pageParams.outletId = locationDetails.data.data[0].id
          pageParams.productId = productId
          pageParams.showProductDetails = true
          const result = parseBrandOutletId(pageParams.outletId)
          if (item) {
            if (item.params.locationId) {
              navigation(
                `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${result?.locationId}`
              )
            } else {
              navigation(
                `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}`
              )
            }
          } else {
            navigation(`/store?provider_id=${brandId}`)
          }
          if (domain !== FB_DOMAIN) {
            if (item) {
              const [bppId, providerId, itemId] = item.params.providerId
                ? item.params.providerId.split("_")
                : [null, null, null]
              navigation(
                `/product?domain=${domain}&provider_id=${providerId}&bpp_id=${bppId}&item_id=${itemId}`
              )
            } else {
              navigation(
                `/product?domain=${domain}&provider_id=${brandId}&item_id=${productId}`
              )
            }
          }
        } else {
          currentNavigation.current = navigation
          showLinkMessage(
            t("Provider Details.This store does not service your location")
          )
          currentNavigation.current = navigation
        }
      })
      .catch(() => {
        currentNavigation.current = navigation
        navigationToDashboard()
      })
  }

  const navigateToDynamicPage = (
    pageName: string,
    userToken: string,
    navigation: any,
    item: any = null
  ) => {
    const source = axios.CancelToken.source()
    getDataWithToken(
      `${API_BASE_URL}${PAGE}?name=${pageName}`,
      userToken,
      source.token
    )
      .then(({ data }) => {
        if (data?.pages?.length > 0) {
          // navigation.navigate('DynamicPage', {
          //   tag: data.pages[0].name,
          // });
          navigation(`/page?tag=${data.pages[0].name}`)
        } else {
          currentNavigation.current = navigation
          navigationToDashboard()
        }
      })
      .catch(() => {
        currentNavigation.current = navigation
        navigationToDashboard()
      })
  }

  const navigationToDashboard = () => {
    hideLinkModal()
    if (currentNavigation.current) {
      currentNavigation.current.reset({
        index: 0,
        routes: [{ name: "Dashboard" }],
      })
    }
  }

  const navigateToSearchPage = (
    navigation: any,
    query = "",
    tagName = "",
    domain = "",
    subCategory = "",
    bppId = "",
    minPrice = "",
    maxPrice = "",
    city = "",
    hideStores = false,
    itemIds = "",
    providerIds = "",
    item = null
  ) => {
    // navigation.navigate('SearchProducts', {
    //   query,
    //   tagId: tagName,
    //   categoryDomain: domain,
    //   subCategory: subCategory,
    //   bppId,
    //   minPrice,
    //   maxPrice,
    //   city,
    //   hideStores,
    //   itemIds,
    //   providerIds,
    // });
    navigation(
      `/search?query=${query}&tagId=${tagName}&categoryDomain=${domain}&subCategory=${subCategory}&bppId=${bppId}&minPrice=${minPrice}&maxPrice=${maxPrice}&city=${city}&hideStores=${hideStores}&itemIds=${itemIds}&providerIds=${providerIds}`
    )
  }

  const navigateToCategoryPage = (
    navigation: any,
    domain: string,
    item: any = null
  ) => {
    // navigation.navigate('Dashboard', {
    //   domain,
    // });
    const tab = getStoredData("home_page_tab") ?? "All"
    navigation(`/dashboard?tab=${tab}`)
  }

  const navigateToDashboardPage = (navigation: any, item: any = null) => {
    // navigation.navigate('Dashboard');
    navigation(`/dashboard`)
  }

  const handleMarketingLinks = (
    url: string,
    userToken: string,
    userAddress: any,
    navigation: any,
    item: any = null
  ) => {
    const page = extractUrlPage(url)
    const urlParams = getUrlParams(url)
    switch (page) {
      case "store":
        const brandId = `${urlParams.bpp_id}_${urlParams.domain}_${urlParams.provider_id}`
        navigateToStorePage(
          brandId,
          urlParams.location_id,
          userToken,
          userAddress,
          navigation,
          item
        )
        break

      case "product":
        const productBrandId = `${urlParams.bpp_id}_${urlParams.domain}_${urlParams.provider_id}`
        const productId = `${urlParams.bpp_id}_${urlParams.domain}_${urlParams.provider_id}_${urlParams.item_id}`
        navigateToProductPage(
          urlParams.domain,
          productBrandId,
          productId,
          userToken,
          userAddress,
          navigation,
          item
        )
        break

      case "search":
        navigateToSearchPage(
          navigation,
          urlParams?.query,
          urlParams?.tag,
          urlParams?.domain,
          urlParams?.sub_category,
          urlParams?.bpp_id || urlParams?.bpp_ids,
          urlParams?.minPrice,
          urlParams?.maxPrice,
          urlParams?.city,
          urlParams?.hide_stores,
          urlParams?.item_id,
          urlParams?.provider_id,
          item
        )
        break

      case "page":
        navigateToDynamicPage(urlParams?.tag, userToken, navigation, item)
        break

      case "category":
        navigateToCategoryPage(navigation, urlParams?.domain, item)
        break

      default:
        navigateToDashboardPage(navigation, item)
    }
  }

  const handleNewLinks = (
    subDomain: string,
    url: string,
    userToken: string,
    userAddress: any,
    navigation: any,
    item: any = null
  ) => {
    const urlParams = getUrlParams(url)
    if (isValidNewUrlSchema(urlParams)) {
      if (subDomain !== "*" && isNewDeepLinkSupported(subDomain)) {
        if (urlParams.hasOwnProperty("message.tags.descriptor.name")) {
          navigateToDynamicPage(
            urlParams["message.tags.descriptor.name"],
            userToken,
            navigation,
            item
          )
        } else {
          const brandId = `${urlParams["context.bpp_id"]}_${subDomain}_${urlParams["message.intent.provider.id"]}`
          navigateToStorePage(
            brandId,
            urlParams["message.intent.provider.locations.0.id"],
            userToken,
            userAddress,
            navigation,
            item
          )
        }
      } else if (urlParams.hasOwnProperty("message.tags.descriptor.name")) {
        navigateToDynamicPage(
          urlParams["message.tags.descriptor.name"],
          userToken,
          navigation,
          item
        )
      } else {
        currentNavigation.current = navigation
        showLinkMessage(
          t(
            "Provider Details.This store/seller type is not supported by DigiHaat Application, explore other buyer apps."
          )
        )
      }
    } else {
      currentNavigation.current = navigation
      showLinkMessage(
        t("Provider Details.Incorrect specifications or malformed request", {
          email: HELP_EMAIL,
        })
      )
    }
  }

  const handleOldLinks = (
    url: string,
    userToken: string,
    userAddress: any,
    navigation: any,
    item: any = null
  ) => {
    const urlParams = getUrlParams(url)
    if (isValidQRURL(urlParams)) {
      if (isDomainSupported(urlParams["context.domain"])) {
        const brandId = `${urlParams["context.bpp_id"]}_${urlParams["context.domain"]}_${urlParams["message.intent.provider.id"]}`
        navigateToStorePage(
          brandId,
          urlParams["message.intent.provider.locations.0.id"],
          userToken,
          userAddress,
          navigation,
          item
        )
      } else {
        currentNavigation.current = navigation
        showLinkMessage(
          t(
            "Provider Details.This store/seller type is not supported by DigiHaat Application, explore other buyer apps."
          )
        )
      }
    } else {
      currentNavigation.current = navigation
      showLinkMessage(
        t("Provider Details.Incorrect specifications or malformed request", {
          email: HELP_EMAIL,
        })
      )
    }
  }

  const handleDeepLink = (
    url: string,
    userToken: string,
    userAddress: any,
    navigation: any,
    item: any = null
  ) => {
    if (url.startsWith("beckn")) {
      const subDomain = getSubdomain(url)
      if (subDomain) {
        handleNewLinks(subDomain, url, userToken, userAddress, navigation, item)
      } else {
        handleOldLinks(url, userToken, userAddress, navigation, item)
      }
    } else if (url.startsWith("https")) {
      handleMarketingLinks(url, userToken, userAddress, navigation, item)
    } else {
      currentNavigation.current = navigation
      showLinkMessage(
        t("Provider Details.Incorrect specifications or malformed request", {
          email: HELP_EMAIL,
        })
      )
    }
  }

  const hideLinkModal = useCallback(() => {
    setLinkModalVisible(false)
  }, [])

  return {
    linkModalVisible,
    linkMessage,
    handleDeepLink,
    hideLinkModal,
    showLinkMessage,
    navigationToDashboard,
  }
}

export default useDeepLink
