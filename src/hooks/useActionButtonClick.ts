import {useEffect, useRef} from 'react';

const useActionButtonClick = () => {
  const clickAllowed = useRef<boolean>(true);
  const timeout = useRef<any>(null);

  const onButtonPressAction = (callback: any) => {
    if (clickAllowed.current) {
      callback();
      clickAllowed.current = false;
      timeout.current = setTimeout(() => {
        clickAllowed.current = true;
      }, 2000);
    }
  };

  useEffect(() => {
    return () => {
      if (timeout.current) {
        clearTimeout(timeout.current);
        timeout.current = null;
      }
    };
  }, []);

  return onButtonPressAction;
};

export default useActionButtonClick;
