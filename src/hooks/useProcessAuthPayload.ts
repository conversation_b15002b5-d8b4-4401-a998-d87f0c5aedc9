import {saveUser, setToken} from "../toolkit/reducer/auth";
import {useDispatch} from "react-redux";

const useProcessAuthPayload = () => {
  const dispatch = useDispatch();

  return (data: any) => {
    const payload: any = {};
    data.forEach((item: any) => {
      try {
        payload[item[0]] = JSON.parse(item[1]);
      } catch (error) {
        payload[item[0]] = item[1];
      }
    });
    dispatch(saveUser(payload));
    dispatch(setToken(payload.token));
    return payload;
  }
};

export default useProcessAuthPayload;
