import React from 'react';
import {useTranslation} from 'react-i18next';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import RefreshImage from '../../../../assets/refresh.svg';
import {makeStyles} from "@mui/styles";

const GlobalError = ({onRefreshPress}: { onRefreshPress: () => void }) => {
  const styles = useStyles();
  const {t} = useTranslation();

  return (
    <Box className={styles.container}>
      <Box className={styles.subContainer}>
        <img alt="Refresh" src={RefreshImage} width={128} height={128}/>
        <Typography variant="titleSmall" className={styles.title}>
          {t('Global.Sorry its not you its us')}
        </Typography>
        <Typography variant="bodySmall" className={styles.message}>
          {t(
            'Global.Something went wrong! Please refresh the page and try again',
          )}
        </Typography>
        <Button variant="contained" className={styles.button} onClick={onRefreshPress}>
          <Typography variant="bodySmall" className={styles.buttonText}>
            {t('Profile.Refresh')}
          </Typography>
        </Button>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
    height: 'calc(100dvh - 54px)',
  },
  subContainer: {
    width: 260,
    alignItems: 'center',
    justifyContent: 'center',
    display: 'flex',
    flexDirection: 'column',
  },
  title: {
    color: theme.palette.neutral400,
    marginTop: 16,
    marginBottom: 8,
  },
  message: {
    color: theme.palette.neutral400,
    textAlign: 'center',
  },
  button: {
    borderRadius: 8,
    backgroundColor: theme.palette.primary,
    paddingVertical: 13,
    width: 156,
    marginTop: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: theme.palette.white,
  },
}));

export default GlobalError;
