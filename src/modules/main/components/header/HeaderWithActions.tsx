import ArrowBackIcon from "@mui/icons-material/ArrowBack"
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorderOutlined"
import SearchIcon from "@mui/icons-material/SearchOutlined"
import ShoppingCartOutlinedIcon from "@mui/icons-material/ShoppingCartOutlined"
import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useCallback, useEffect, useMemo, useRef } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"

import axios from "axios"
import useNetworkHandling from "../../../../hooks/useNetworkHandling"
import { updateCartItems } from "../../../../toolkit/reducer/cart"
import { API_BASE_URL, CART } from "../../../../utils/apiActions"
import { theme } from "../../../../utils/theme"
import useBackHandler from "../../hooks/useBackHandler"
const CancelToken = axios.CancelToken
interface Page {
  label: string
  search?: boolean
  wishlist?: boolean
  cart?: boolean
}

const HeaderWithActions: React.FC<Page> = ({
  label,
  search,
  wishlist,
  cart,
}) => {
  const styles = useStyles()
  const { goBack } = useBackHandler()
  const { cartItems } = useSelector((state: any) => state.cart)
  const { uid } = useSelector((state: any) => state.auth)
  const navigation = useNavigate()
  const source = useRef<any>(null)
  const { getDataWithAuth } = useNetworkHandling()
  const dispatch = useDispatch()

  useEffect(() => {
    getCartItems()
  }, [uid])

  const getCartItems = async () => {
    try {
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/all`,
        source.current.token
      )
      dispatch(updateCartItems(data))
    } catch (error: any) {
      // crashlytics().recordError(error);
      return []
    }
  }

  const openSearch = useCallback(() => {
    navigation("/search")
  }, [navigation])

  const openWishlist = useCallback(() => {
    navigation("/wishList")
  }, [navigation])

  const openCart = useCallback(() => {
    navigation("/cart")
  }, [navigation])

  const itemsCount = useMemo(() => {
    return cartItems.length
  }, [cartItems])

  return (
    <Box className={styles.header}>
      <Box className={styles.headerTitle}>
        <IconButton onClick={goBack}>
          <ArrowBackIcon sx={{ fontSize: 24 }} />
        </IconButton>
        <Typography variant="titleLarge" className={styles.pageTitle}>
          {label}
        </Typography>
      </Box>
      <Box className={styles.actionContainer}>
        {search && (
          <IconButton onClick={openSearch}>
            <SearchIcon sx={{ fontSize: 24 }} />
          </IconButton>
        )}
        {wishlist && (
          <IconButton onClick={openWishlist}>
            <FavoriteBorderIcon sx={{ fontSize: 24 }} />
          </IconButton>
        )}
        {cart && (
          <Box>
            <IconButton onClick={openCart}>
              <ShoppingCartOutlinedIcon
                sx={{ fontSize: 24, color: theme.palette.neutral.main }}
              />
            </IconButton>
            {itemsCount > 0 && (
              <Box className={styles.badge}>
                <Typography variant="labelLarge" className={styles.badgeText}>
                  {itemsCount}
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Box>
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  header: {
    display: "flex",
    height: 48,
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 14,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: theme.palette.white,
    gap: 20,
    flex: 1,
  },
  headerTitle: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
    flexGrow: 1,
    display: "flex",
    overflow: "hidden",
  },
  pageTitle: {
    color: theme.palette.neutral400,
    flexGrow: 1,
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  actionContainer: {
    display: "flex",
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
    justifyContent: "flex-end",
  },
  badge: {
    display: "flex",
    position: "absolute",
    width: 20,
    height: 20,
    backgroundColor: theme.palette.primary200,
    alignItems: "center",
    justifyContent: "center",
    marginTop: -44,
    marginLeft: 20,
    borderRadius: 34,
  },
  badgeText: {
    color: theme.palette.white,
    fontSize: 8,
  },
}))

export default HeaderWithActions
