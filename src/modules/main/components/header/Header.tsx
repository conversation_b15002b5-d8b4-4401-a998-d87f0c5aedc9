import React from 'react';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import {makeStyles} from "@mui/styles";
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';

import useBackHandler from '../../hooks/useBackHandler';
import {theme} from "../../../../utils/theme";

interface Page {
  label: string;
  search?: boolean;
  wishlist?: boolean;
  cart?: boolean;
}

const Header: React.FC<Page> = ({label}) => {
  const styles = useStyles();
  const {goBack} = useBackHandler();

  return (
    <Box className={styles.header}>
      <Box className={styles.headerTitle}>
        <IconButton size="small" onClick={goBack}>
          <ArrowBackIcon sx={{fontSize: 24, color: theme.palette.neutral.main}}/>
        </IconButton>
        <Typography variant="titleLarge" className={styles.pageTitle}>
          {label}
        </Typography>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles((theme) => ({
  header: {
    display: 'flex',
    padding: '14px 14px 0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 20,
  },
  headerTitle: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
    fontWeight: 'bold'
  },
  pageTitle: {
    flex: 1,
    fontWeight: 'bold'
  },
}));

export default Header;
