import ArrowBackIcon from "@mui/icons-material/ArrowBackOutlined"
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorderOutlined"
import SearchIcon from "@mui/icons-material/SearchOutlined"
import ShoppingCartOutlinedIcon from "@mui/icons-material/ShoppingCartOutlined"
import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useCallback, useEffect, useMemo, useRef } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"

import axios from "axios"
import useNetworkHandling from "../../../../hooks/useNetworkHandling"
import { updateCartItems } from "../../../../toolkit/reducer/cart"
import { API_BASE_URL, CART } from "../../../../utils/apiActions"
import { theme } from "../../../../utils/theme"
import useBackHandler from "../../hooks/useBackHandler"

interface Page {
  label: string
  tagId: string
  categoryDomain: string
  subCategory: string
}
const CancelToken = axios.CancelToken
const HeaderWithSearchParams: React.FC<Page> = ({
  label,
  tagId,
  categoryDomain,
  subCategory,
}) => {
  const styles = useStyles()
  const { goBack } = useBackHandler()
  const { cartItems } = useSelector((state: any) => state.cart)
  const { uid } = useSelector((state: any) => state.auth)
  const navigation = useNavigate()

  const source = useRef<any>(null)
  const { getDataWithAuth } = useNetworkHandling()
  const dispatch = useDispatch()

  useEffect(() => {
    getCartItems()
  }, [uid])

  const getCartItems = async () => {
    try {
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/all`,
        source.current.token
      )
      dispatch(updateCartItems(data))
    } catch (error: any) {
      // crashlytics().recordError(error);
      return []
    }
  }

  const openSearch = useCallback(() => {
    const searchParams = new URLSearchParams({
      tag: tagId || "",
      domain: categoryDomain || "",
      subCategory: subCategory || "",
    }).toString()

    navigation(`/search?${searchParams}`)
  }, [navigation, tagId, categoryDomain, subCategory])

  const openWishlist = useCallback(() => {
    navigation("/wishList")
  }, [navigation])

  const openCart = useCallback(() => {
    navigation("/cart")
  }, [navigation])

  const itemsCount = useMemo(() => {
    return cartItems.length
  }, [cartItems])

  return (
    <Box className={styles.header}>
      <Box className={styles.headerTitle}>
        <IconButton onClick={goBack}>
          <ArrowBackIcon sx={{ fontSize: 24 }} />
        </IconButton>
        <Typography variant="titleLarge" className={styles.pageTitle}>
          {label}
        </Typography>
      </Box>

      <Box className={styles.actionContainer}>
        <IconButton onClick={openSearch}>
          <SearchIcon sx={{ fontSize: 24 }} />
        </IconButton>

        <IconButton onClick={openWishlist}>
          <FavoriteBorderIcon sx={{ fontSize: 24 }} />
        </IconButton>
        <Box>
          <IconButton onClick={openCart}>
            <ShoppingCartOutlinedIcon
              sx={{ fontSize: 24, color: theme.palette.neutral.main }}
            />
          </IconButton>
          {itemsCount > 0 && (
            <Box className={styles.badge}>
              <Typography variant="labelLarge" className={styles.badgeText}>
                {itemsCount}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  )
}

const useStyles = makeStyles((theme) => ({
  header: {
    display: "flex",
    height: 48,
    paddingRight: 16,
    paddingLeft: 16,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 10,
    gap: 20,
  },
  headerTitle: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
    flex: 1,
  },
  pageTitle: {
    fontWeight: "bold",
    whiteSpace: "nowrap",
    overflow: "hidden",
    textOverflow: "ellipsis",
    display: "block",
    width: "123px",
  },
  actionContainer: {
    display: "flex",
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
    justifyContent: "flex-end",
  },
  badge: {
    display: "flex",
    position: "absolute",
    width: 20,
    height: 20,
    backgroundColor: theme.palette.primary200,
    alignItems: "center",
    justifyContent: "center",
    marginTop: -44,
    marginLeft: 20,
    borderRadius: 34,
  },
  badgeText: {
    color: theme.palette.white,
    fontSize: 8,
  },
}))

export default HeaderWithSearchParams
