import React from "react";
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';
import {makeStyles} from "@mui/styles";

const ProductSkeleton: React.FC = () => {
  const classes = useStyles();

  return (
    <Box className={classes.container}>
      <Skeleton variant="rectangular" className={classes.image}/>
      <Skeleton variant="text" className={classes.title}/>
      <Skeleton variant="text" className={classes.brand}/>
    </Box>
  );
};

const useStyles = makeStyles({
  container: {
    borderRadius: 12,
    padding: "0 8px",
    flex: 1,
    marginBottom: 20,
  },
  image: {
    height: 180,
    width: "100%",
    marginBottom: 10,
  },
  title: {
    height: 32,
    width: "100%",
    marginBottom: 8,
  },
  brand: {
    height: 12,
    width: "100%",
    marginBottom: 8,
  },
});

export default ProductSkeleton;
