import React from 'react';
import Box from '@mui/material/Box';
import {makeStyles} from '@mui/styles';

interface PrimarySafeAreaPage {
  children: React.ReactNode;
}

const PrimarySafeAreaPage: React.FC<PrimarySafeAreaPage> = ({children}) => {
  const styles = useStyles();

  return (
    <Box className={styles.container}>
      <Box className={styles.topSafeArea}/>
      <Box className={styles.bottomSafeArea}>{children}</Box>
    </Box>
  );
};
const useStyles = makeStyles<any>((theme) => ({
  container: {flex: 1},
  topSafeArea: {
    flex: 0,
    backgroundColor: theme.palette.primary,
  },
  bottomSafeArea: {
    flex: 1,
    backgroundColor: theme.palette.white,
  },
}));

export default PrimarySafeAreaPage;
