import React, { useCallback, useMemo } from "react"
import Skeleton from "@mui/material/Skeleton"
import { useNavigate } from "react-router-dom"
import Provider from "./Provider"
import { skeletonList } from "../../../../../../utils/utils"
import SectionHeader from "../SectionHeader"
import Box from "@mui/material/Box"
import { makeStyles } from "@mui/styles"

const ItemSeparatorComponent = () => {
  const styles = useProviderStyles()
  return <Box className={styles.separator} />
}

const Providers = ({
  sectionsRequested,
  sectionId,
  tagName,
  imageHeight,
  imageWidth,
  tts,
  serviceability,
  showViewAll,
  title,
  alignTitle,
  maxItems,
  numberOfColumns,
  numberOfRows,
  providers,
  domain,
  sectionStyle,
}: {
  sectionsRequested: boolean
  sectionId: string
  tagName: string
  imageHeight: string
  imageWidth: string
  maxItems: number
  tts: any
  serviceability: any
  numberOfColumns: number
  numberOfRows: number
  title: string
  alignTitle: string
  showViewAll: boolean
  providers: any[]
  domain: string
  sectionStyle: any
}) => {
  const navigate = useNavigate()
  const styles = useProviderStyles()

  const navigateToAll = () => {
    const queryParams = new URLSearchParams({
      tagName,
      tts,
      serviceability,
      title,
      domain,
    }).toString()
    navigate(`/Stores?${queryParams}`)
  }

  const providerRows = useMemo(() => {
    const rows = []
    const columns = numberOfColumns > 0 ? numberOfColumns : 4
    for (let i = 0; i < providers.length; i += columns) {
      rows.push(providers.slice(i, i + columns))
      if (rows.length === numberOfRows) {
        break
      }
    }
    return rows
  }, [providers, numberOfColumns, numberOfRows])

  const imageStyle = useMemo(
    () => ({
      height: Number(imageHeight),
      width: Number(imageWidth),
    }),
    [imageWidth, imageHeight]
  )

  const renderSkeleton = useCallback(
    () => (
      <Skeleton
        variant="rectangular"
        width={imageStyle.width}
        height={imageStyle.height}
      />
    ),
    [imageStyle]
  )

  if (sectionsRequested) {
    return (
      <Box
        className={styles.skeletonContainer}
        display="flex"
        flexDirection="row"
        overflow="auto"
      >
        {skeletonList.map((item) => (
          <Box key={item.id} className={styles.skeletonItem}>
            {renderSkeleton()}
          </Box>
        ))}
      </Box>
    )
  }

  if (providers?.length === 0) {
    return <></>
  }

  if (maxItems <= 4) {
    return (
      <Box className={styles.sectionContainer} sx={sectionStyle}>
        <SectionHeader
          title={title}
          alignTitle={alignTitle}
          showViewAll={showViewAll}
          navigateToAll={navigateToAll}
        />
        <Box
          display="flex"
          flexDirection="row"
          gap={2}
          className={styles.horizontalRow}
        >
          {providers.map((provider) => (
            <Box key={provider.id} className={styles.providerContainer}>
              <Provider imageStyle={imageStyle} store={provider} />
            </Box>
          ))}
        </Box>
      </Box>
    )
  }

  return (
    <Box className={styles.sectionContainer}>
      <SectionHeader
        title={title}
        alignTitle={alignTitle}
        showViewAll={showViewAll}
        navigateToAll={navigateToAll}
      />
      <Box
        display="flex"
        flexDirection="row"
        className={styles.scrollContainer}
      >
        {providerRows.map((row, index) => (
          <Box key={`${sectionId}${index}`} className={styles.row}>
            {row.map((provider, providerIndex) => (
              <Box
                key={provider.id}
                className={`${styles.item} ${
                  providerIndex === 0 ? styles.isFirst : ""
                }`}
              >
                <Provider imageStyle={imageStyle} store={provider} />
              </Box>
            ))}
          </Box>
        ))}
      </Box>
    </Box>
  )
}

const useProviderStyles = makeStyles<any>((theme) => ({
  sectionContainer: {
    padding: "12px 0",
    marginBottom: 16,
  },
  separator: {
    width: 12,
  },
  skeletonContainer: {
    display: "flex",
    flexDirection: "row",
    gap: 12,
    overflowX: "auto",
    paddingLeft: 16,
  },
  skeletonItem: {
    flexShrink: 0,
  },
  providerContainer: {
    flex: 1,
  },
  horizontalRow: {
    display: "flex !important",
    alignItems: "flex-start !important",
    gap: 12,
    paddingRight: 16,
    paddingLeft: 16,
  },
  scrollContainer: {
    display: "flex",
    flexDirection: "row",
    overflowX: "auto",
    gap: 12,
    paddingBottom: 8,
  },
  row: {
    display: "flex",
    flexDirection: "row",
    marginBottom: 20,
  },
  isFirst: {
    paddingLeft: 16,
  },
  item: {
    marginRight: 12,
  },
}))

export default Providers
