import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useCallback, useMemo, useState } from "react"
import { useNavigate } from "react-router-dom"
import NoImageAvailable from "../../../../../../assets/no_store.png"
import StoreIcon from "../../../../../../assets/no_store_icon.svg"
import {distributeProviderId} from "../../../../../../utils/utils";

interface ProviderImage {
  source: any
  imageStyle: { width: number; height: number }
}

const ProviderImage: React.FC<ProviderImage> = ({ source, imageStyle }) => {
  const [imageSource, setImageSource] = useState(source)
  const [imageLoadFailed, setImageLoadFailed] = useState<boolean>(false)
  const styles = useProviderStyles()

  const onError = () => {
    setImageLoadFailed(true)
    setImageSource(NoImageAvailable)
  }

  if (source) {
    return (
      <img
        src={imageLoadFailed ? NoImageAvailable : imageSource.uri}
        alt="image"
        style={{
          ...imageStyle,
          objectFit: imageLoadFailed ? "cover" : "contain",
        }}
        onError={onError}
      />
    )
  } else {
    return (
      <Box sx={{ ...imageStyle }} className={styles.brandImageEmpty}>
        <img src={StoreIcon} alt="Store Icon" width={48} height={48} />
      </Box>
    )
  }
}

const Provider = ({
  store,
  imageStyle,
}: {
  store: any
  imageStyle: { width: number; height: number }
}) => {
  const navigate = useNavigate()
  const styles = useProviderStyles()

  const navigateToDetails = useCallback(() => {
    const {bppId, domain, providerId} = distributeProviderId(store.id);
    navigate(
      `/store?domain=${domain}&provider_id=${providerId}&bpp_id=${bppId}`
    )
  }, [store, navigate]);

  const imageSource = useMemo(() => {
    let source = null
    if (store?.descriptor?.symbol) {
      source = { uri: store?.descriptor?.symbol }
    } else if (store?.descriptor?.images?.length > 0) {
      source = { uri: store?.descriptor?.images[0] }
    }
    return source
  }, [store])

  return (
    <Button className={styles.buttonContainer} onClick={navigateToDetails}>
      <ProviderImage source={imageSource} imageStyle={imageStyle} />
      <Typography
        variant="labelLarge"
        sx={{ width: imageStyle.width }}
        className={styles.name}
      >
        {store?.descriptor?.name}
      </Typography>
    </Button>
  )
}

const useProviderStyles = makeStyles<any>((theme) => ({
  brandImageEmpty: {
    backgroundColor: theme.palette.neutral200,
  },
  name: {
    color: theme.palette.neutral400,
    textAlign: "center",
    marginTop: 4,
  },
  buttonContainer: {
    flexDirection: "column",
  },
}))

export default Provider
