import React, { useCallback, useMemo } from "react"
import Skeleton from "@mui/material/Skeleton"
import { useNavigate } from "react-router-dom"
import Store from "./Store"
import { skeletonList } from "../../../../../../utils/utils"
import SectionHeader from "../SectionHeader"
import Box from "@mui/material/Box"
import { makeStyles } from "@mui/styles"

const ItemSeparatorComponent = () => {
  const styles = useStoreStyles()
  return <Box className={styles.separator} />
}

const Stores = ({
  sectionsRequested,
  sectionId,
  tagName,
  imageHeight,
  imageWidth,
  tts,
  serviceability,
  showViewAll,
  title,
  alignTitle,
  maxItems,
  numberOfColumns,
  numberOfRows,
  stores,
  domain,
  sectionStyle,
}: {
  sectionsRequested: boolean
  sectionId: string
  tagName: string
  imageHeight: string
  imageWidth: string
  maxItems: number
  tts: any
  serviceability: any
  numberOfColumns: number
  numberOfRows: number
  title: string
  alignTitle: string
  showViewAll: boolean
  stores: any[]
  domain: string
  sectionStyle: any
}) => {
  const navigate = useNavigate()
  const styles = useStoreStyles()

  const navigateToAll = () => {
    navigate("/stores-near-me", {
      state: {
        tagName,
        tts,
        serviceability,
        title,
        domain,
      },
    })
  }

  const storeRows = useMemo(() => {
    const rows = []
    const columns = numberOfColumns > 0 ? numberOfColumns : 4
    for (let i = 0; i < stores.length; i += columns) {
      rows.push(stores.slice(i, i + columns))
      if (rows.length === numberOfRows) {
        break
      }
    }
    return rows
  }, [stores, numberOfColumns, numberOfRows])

  const imageStyle = useMemo(
    () => ({
      height: Number(imageHeight),
      width: Number(imageWidth),
    }),
    [imageWidth, imageHeight]
  )

  const renderSkeleton = useCallback(
    () => (
      <Skeleton
        variant="rectangular"
        width={imageStyle.width}
        height={imageStyle.height}
      />
    ),
    [imageStyle]
  )

  if (sectionsRequested) {
    return (
      <Box
        className={styles.skeletonContainer}
        display="flex"
        flexDirection="row"
        overflow="auto"
      >
        {skeletonList.map((item) => (
          <Box key={item.id} className={styles.skeletonItem}>
            {renderSkeleton()}
          </Box>
        ))}
      </Box>
    )
  }

  if (stores?.length === 0) {
    return null
  }

  if (maxItems <= 4) {
    return (
      <Box className={styles.sectionContainer} sx={sectionStyle}>
        <SectionHeader
          title={title}
          alignTitle={alignTitle}
          showViewAll={showViewAll}
          navigateToAll={navigateToAll}
        />
        <Box
          display="flex"
          flexDirection="row"
          gap={2}
          className={styles.horizontalRow}
        >
          {stores.map((store) => (
            <Box key={store.id} className={styles.providerContainer}>
              <Store imageStyle={imageStyle} store={store} />
            </Box>
          ))}
        </Box>
      </Box>
    )
  }

  return (
    <Box className={styles.sectionContainer}>
      <SectionHeader
        title={title}
        alignTitle={alignTitle}
        showViewAll={showViewAll}
        navigateToAll={navigateToAll}
      />
      <Box
        display="flex"
        flexDirection="row"
        overflow="auto"
        className={styles.scrollContainer}
      >
        {storeRows.map((row, index) => (
          <Box key={`${sectionId}${index}`} className={styles.row}>
            {row.map((store, storeIndex) => (
              <Box
                key={store.id}
                className={`${styles.item} ${
                  storeIndex === 0 ? styles.isFirst : ""
                }`}
              >
                <Store imageStyle={imageStyle} store={store} />
              </Box>
            ))}
          </Box>
        ))}
      </Box>
    </Box>
  )
}

const useStoreStyles = makeStyles<any>((theme) => ({
  sectionContainer: {
    padding: "12px 0",
    marginBottom: 16,
  },
  separator: {
    width: 12,
  },
  skeletonContainer: {
    display: "flex",
    flexDirection: "row",
    gap: 12,
    overflowX: "auto",
    paddingLeft: 16,
  },
  skeletonItem: {
    flexShrink: 0,
  },
  providerContainer: {
    flex: 1,
  },
  horizontalRow: {
    display: "flex !important",
    alignItems: "flex-start !important",
    gap: 12,
    paddingRight: 16,
    paddingLeft: 16,
  },
  scrollContainer: {
    display: "flex",
    flexDirection: "row",
    overflowX: "auto",
    gap: 12,
    paddingBottom: 8,
  },
  row: {
    display: "flex",
    flexDirection: "row",
    marginBottom: 20,
  },
  isFirst: {
    paddingLeft: 16,
  },
  item: {
    marginRight: 12,
  },
}))

export default Stores
