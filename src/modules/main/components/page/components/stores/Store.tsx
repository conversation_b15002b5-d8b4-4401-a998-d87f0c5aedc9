import React, { useCallback, useMemo, useState } from "react"
import { useNavigate } from "react-router-dom"

import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import NoImageAvailable from "../../../../../../assets/no_store.png"
import StoreIcon from "../../../../../../assets/no_store_icon.svg"
import useMinutesToString from "../../../../hooks/useMinutesToString"
import useStoreImageTime from "../../../../../../hooks/useStoreImageTime"
import { WatchLater } from "@mui/icons-material"
import { useTheme } from "@mui/material/styles"
interface StoreImage {
  source: any
  imageStyle: { width: number; height: number }
}

const StoreImage: React.FC<StoreImage> = ({ source, imageStyle }) => {
  const [imageSource, setImageSource] = useState(source)
  const [imageLoadFailed, setImageLoadFailed] = useState<boolean>(false)

  const styles = useStoreStyles()

  const onError = () => {
    setImageLoadFailed(true)
    setImageSource(NoImageAvailable)
  }

  return source ? (
    <img
      src={imageLoadFailed ? NoImageAvailable : imageSource.uri}
      alt="image"
      style={{
        ...imageStyle,
        objectFit: imageLoadFailed ? "cover" : "contain",
      }}
      onError={onError}
    />
  ) : (
    <Box className={styles.brandImageEmpty} sx={{ ...imageStyle }}>
      <img src={StoreIcon} alt="Store Icon" width={48} height={48} />
    </Box>
  )
}

const Store = ({
  store,
  imageStyle,
}: {
  store: any
  imageStyle: { width: number; height: number }
}) => {
  const navigate = useNavigate()
  const styles = useStoreStyles()
  const theme = useTheme()
  const { timeToShip, imageSource } = useStoreImageTime(store)
  const { translateMinutesToHumanReadable } = useMinutesToString()
  const navigateToDetails = useCallback(() => {
    const brandOutletId = store.id
    const [bppId, domain, providerId, location] = brandOutletId
      ? brandOutletId.split("_")
      : [null, null, null, null]
    navigate(
      `/store?domain=${domain}&provider_id=${providerId}&bpp_id=${bppId}&locationId=${location}`
    )
  }, [store, navigate])

  return (
    <Box onClick={navigateToDetails} className={styles.container}>
      <StoreImage source={imageSource} imageStyle={imageStyle} />
      <Typography
        variant="labelLarge"
        className={styles.name}
        sx={{
          width: imageStyle.width,
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        {store?.provider_descriptor?.name &&
        store.provider_descriptor.name.length > 10
          ? `${store.provider_descriptor.name.substring(0, 10)}...`
          : store?.provider_descriptor?.name}
      </Typography>
      <br />
      <Box
        sx={{
          flexDirection: "row",
          display: "flex",
          alignItems: "center",
          gap: 0.5,
        }}
      >
        <WatchLater
          sx={{ color: `${theme.palette.neutral200} !important`, fontSize: 16 }}
        />
        <Typography variant="labelLarge" className={styles.eta}>
          {translateMinutesToHumanReadable(timeToShip.type, timeToShip.time)}
        </Typography>
      </Box>
    </Box>
  )
}

const useStoreStyles = makeStyles<any>((theme) => ({
  brandImageEmpty: {
    backgroundColor: theme.palette.neutral200,
  },
  name: {
    color: theme.palette.neutral400,
    textAlign: "center",
    lineHeight: "14px",
    marginTop: 4,
  },
  container: {
    // flexDirection: "row",
    // display: "flex",
    alignItems: "center",
    cursor: "pointer",
  },
  eta: {
    color: theme.palette.neutral300,
  },
}))

export default Store
