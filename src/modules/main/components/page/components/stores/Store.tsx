import React, { useC<PERSON>back, useMemo, useState } from "react"
import { useNavigate } from "react-router-dom"

import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import NoImageAvailable from "../../../../../../assets/no_store.png"
import StoreIcon from "../../../../../../assets/no_store_icon.svg"

interface StoreImage {
  source: any
  imageStyle: { width: number; height: number }
}

const StoreImage: React.FC<StoreImage> = ({ source, imageStyle }) => {
  const [imageSource, setImageSource] = useState(source)
  const [imageLoadFailed, setImageLoadFailed] = useState<boolean>(false)

  const styles = useStoreStyles()

  const onError = () => {
    setImageLoadFailed(true)
    setImageSource(NoImageAvailable)
  }

  return source ? (
    <img
      src={imageLoadFailed ? NoImageAvailable : imageSource.uri}
      alt="image"
      style={{
        ...imageStyle,
        objectFit: imageLoadFailed ? "cover" : "contain",
      }}
      onError={onError}
    />
  ) : (
    <Box className={styles.brandImageEmpty} sx={{ ...imageStyle }}>
      <img src={StoreIcon} alt="Store Icon" width={48} height={48} />
    </Box>
  )
}

const Store = ({
  store,
  imageStyle,
}: {
  store: any
  imageStyle: { width: number; height: number }
}) => {
  const navigate = useNavigate()
  const styles = useStoreStyles()

  const navigateToDetails = useCallback(() => {
    const brandOutletId = store.id
    const [bppId, domain, providerId, location] = brandOutletId
      ? brandOutletId.split("_")
      : [null, null, null, null]
    navigate(
      `/store?domain=${domain}&provider_id=${providerId}&bpp_id=${bppId}&locationId=${location}`
    )
  }, [store, navigate])

  const imageSource = useMemo(() => {
    let source = null

    if (store?.provider_descriptor?.symbol) {
      return { uri: store.provider_descriptor.symbol }
    } else if (store?.provider_descriptor?.images?.length > 0) {
      return { uri: store.provider_descriptor.images[0] }
    }

    return source
  }, [store])

  return (
    <Box onClick={navigateToDetails} className={styles.container}>
      <StoreImage source={imageSource} imageStyle={imageStyle} />
      <Typography
        variant="labelLarge"
        className={styles.name}
        sx={{ width: imageStyle.width }}
      >
        {store?.provider_descriptor?.name}
      </Typography>
    </Box>
  )
}

const useStoreStyles = makeStyles<any>((theme) => ({
  brandImageEmpty: {
    backgroundColor: theme.palette.neutral200,
  },
  name: {
    color: theme.palette.neutral400,
    textAlign: "center",
    lineHeight: "14px",
    marginTop: 4,
  },
  container: {
    flexDirection: "row",
    display: "flex",
    alignItems: "center",
    cursor: "pointer",
  },
}))

export default Store
