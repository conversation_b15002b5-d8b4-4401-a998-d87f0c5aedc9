import React, { memo, useEffect } from "react"
import { useSelector } from "react-redux"
import { makeStyles } from "@mui/styles"
import Box from "@mui/material/Box"
import PageSection from "./PageSection"
import { Section } from "./type"
import { emptyAlertCallback } from "../../../../../utils/utils"
import usePageDetails from "../../../hooks/usePageDetails"
import PageSkeleton from "./PageSkeleton"

const PageDetails = ({
  sections,
  pageName,
  pageBackground,
}: {
  sections: Section[]
  pageName: string
  pageBackground: string
}) => {
  const styles = useStyles()
  const { sectionsRequested, sectionData, fetchSectionData, hideSections } =
    usePageDetails()
  const { address } = useSelector((state: any) => state.address)

  useEffect(() => {
    if (sections?.length > 0 && address) {
      fetchSectionData(sections, pageName)
        .then(emptyAlertCallback)
        .catch(emptyAlertCallback)
    } else {
      hideSections()
    }
  }, [sections, address])

  if (sectionsRequested) {
    return <PageSkeleton />
  }
  return (
    <Box className={styles.container}>
      {sections?.map((section: Section) => (
        <PageSection
          sectionsRequested={sectionsRequested}
          key={section.id}
          section={section}
          pageBackground={pageBackground}
          data={sectionData[section.id] ?? []}
        />
      ))}
    </Box>
  )
}
const useStyles = makeStyles<any>((theme) => ({
  container: {
    paddingTop: 16,
    paddingBottom: 100,
  },
}))

export default memo(PageDetails, (prevProps, nextProps) => {
  return (
    prevProps.sections === nextProps.sections &&
    prevProps.pageBackground === nextProps.pageBackground &&
    prevProps.pageName === nextProps.pageName
  )
})
