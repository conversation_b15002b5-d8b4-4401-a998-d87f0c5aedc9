export interface Section {
  allowPadding: boolean
  active: boolean
  id: string
  tagId: string
  pageId: string
  typeOfWidgets: string
  serviceability: boolean
  name: string
  maxItems: number
  updatedBy: string
  createdBy: string
  alignTitle: string
  backgroundColor: string
  typeOfSection: string
  imageHeight: string
  imageWidth: any
  numberOfRows: any
  numberOfColumns: any
  sequence: any
  domain: any
  showViewAll: boolean
  viewAll: boolean
  createdAt: string
  updatedAt: string
  ukid: any
  paddingTop?: any
  paddingBottom?: any
  paddingLeft?: any
  paddingRight?: any
  tags?: {
    id: string
    name: string
    image: string
    pageRedirection: string
    tts: any
    serviceability: any
  }
  dependsOn: string[]
}
