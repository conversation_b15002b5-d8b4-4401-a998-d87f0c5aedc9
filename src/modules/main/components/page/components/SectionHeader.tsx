import {useTranslation} from 'react-i18next';
import ChevronRightIcon from '@mui/icons-material/ChevronRightOutlined';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";

const ViewAll = ({viewAll}: { viewAll: () => void }) => {
  const styles = useStyles();
  const {t} = useTranslation();

  return (
    <Button variant="text" className={styles.viewAllContainer} onClick={viewAll}>
      <Typography variant="bodyMedium" className={styles.viewAllLabel}>
        {t('Home.View All')}
      </Typography>
      <ChevronRightIcon sx={{fontSize: 18}}/>
    </Button>
  );
};

const SectionHeader = ({
                         title,
                         alignTitle,
                         showViewAll,
                         navigateToAll,
                       }: {
  title: string;
  alignTitle: string;
  showViewAll: boolean;
  navigateToAll: () => void;
}) => {
  const styles = useStyles();

  switch (alignTitle) {
    case 'hide':
      return <></>;

    case 'left':
      return (
        <Box className={styles.header}>
          <Typography variant="titleLarge" className={styles.title}>
            {title}
          </Typography>
          <Box className={styles.separatorContainer}>
            <Box className={styles.gradient}/>
          </Box>
          {showViewAll ? <ViewAll viewAll={navigateToAll}/> : <></>}
        </Box>
      );

    case 'center':
      return (
        <Box className={styles.header}>
          <Box className={styles.separatorContainer}>
            <Box className={styles.gradient}/>
          </Box>
          <Typography variant="titleLarge" className={styles.title}>
            {title}
          </Typography>
          <Box className={styles.separatorContainer}>
            <Box className={styles.gradient}/>
          </Box>
          {showViewAll ? <ViewAll viewAll={navigateToAll}/> : <></>}
        </Box>
      );

    case 'right':
      return (
        <Box className={styles.header}>
          {showViewAll ? <ViewAll viewAll={navigateToAll}/> : <></>}
          <Box className={styles.separatorContainer}>
            <Box className={styles.gradient}/>
          </Box>
          <Typography variant="titleLarge" className={styles.title}>
            {title}
          </Typography>
        </Box>
      );

    default:
      return <></>;
  }
};

const useStyles = makeStyles<any>((theme) => ({
  separatorContainer: {
    height: 20,
    flex: 1,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingLeft: 8,
    paddingRight: 8,
  },
  gradient: {
    height: 1,
    width: '100%',
    background: 'linear-gradient(to right, rgba(181, 181, 181, 0), rgba(181, 181, 181, 1))',
  },
  header: {
    display: 'flex',
    paddingLeft: 16,
    paddingRight: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    color: theme.palette.neutral400,
    flexShrink: 1,
  },
  viewAllContainer: {
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
  },
  viewAllLabel: {
    color: theme.palette.neutral400,
  },
}));

export default SectionHeader;
