import React, { useMemo } from "react"
import SectionCarousel from "./widgets/SectionCarousel"
import SectionGrid from "./widgets/SectionGrid"
import SectionVideo from "./widgets/SectionVideo"
import Providers from "./providers/Providers"
import SubCategories from "./subCategories/SubCategories"
import { Section } from "./type"
import Stores from "./stores/Stores"
import Box from "@mui/material/Box"

const PageSection = ({
  section,
  pageBackground = "#FFFFFF",
  sectionsRequested,
  data,
}: {
  section: Section
  pageBackground: string
  sectionsRequested: boolean
  data: any[]
}) => {
  // Memoize the calculated background color to prevent unnecessary recalculations
  const backgroundColor = useMemo(() => {
    const selectedBackground = section?.backgroundColor.toUpperCase()
    return selectedBackground === "#FFFFFF" || selectedBackground === "#FFF"
      ? pageBackground
      : section?.backgroundColor
  }, [section?.backgroundColor, pageBackground])

  if (!sectionsRequested && data.length === 0) {
    return null
  }

  const renderWidget = () => {
    const { paddingBottom, paddingTop, paddingLeft, paddingRight } = section
    const sectionStyle = {
      paddingTop: paddingTop == null ? "12px" : `${paddingTop}px`,
      paddingBottom: paddingBottom == null ? "12px" : `${paddingBottom}px`,
      paddingLeft: paddingLeft == null ? "0px" : `${paddingLeft}px`,
      paddingRight: paddingRight == null ? "0px" : `${paddingRight}px`,
    }
    switch (section.typeOfWidgets) {
      case "widgets":
        const currentTime = Date.now()

        const filteredWidgets = data?.filter(
          (item) =>
            item &&
            currentTime >= Number(item.validFrom) &&
            currentTime <= Number(item.validTo)
        )
        if (section.typeOfSection === "carousel") {
          return (
            <SectionCarousel
              sectionsRequested={sectionsRequested}
              allowPadding={section.allowPadding}
              title={section.name}
              alignTitle={section.alignTitle}
              showViewAll={section?.viewAll ?? true}
              sectionBackgroundColor={backgroundColor}
              imageHeight={Number(section.imageHeight)}
              widgets={filteredWidgets}
              sectionStyle={sectionStyle}
            />
          )
        } else if (section.typeOfSection === "video") {
          return (
            <SectionVideo
              sectionsRequested={sectionsRequested}
              allowPadding={section.allowPadding}
              title={section.name}
              alignTitle={section.alignTitle}
              showViewAll={section?.viewAll ?? true}
              imageHeight={Number(section.imageHeight)}
              widgets={filteredWidgets}
              sectionStyle={sectionStyle}
            />
          )
        } else {
          return (
            <SectionGrid
              sectionsRequested={sectionsRequested}
              allowPadding={section.allowPadding}
              title={section.name}
              alignTitle={section.alignTitle}
              showViewAll={section?.viewAll ?? true}
              imageHeight={Number(section.imageHeight)}
              imageWidth={section?.imageWidth ? Number(section.imageWidth) : 0}
              numberOfColumns={Number(section.numberOfColumns)}
              maxItems={section.maxItems}
              widgets={filteredWidgets}
              sectionStyle={sectionStyle}
            />
          )
        }

      case "providers":
        return (
          <Providers
            sectionsRequested={sectionsRequested}
            title={section.name}
            alignTitle={section.alignTitle}
            sectionId={section.id}
            showViewAll={section?.viewAll ?? true}
            tagName={section.tags?.name ?? ""}
            imageHeight={section.imageHeight}
            imageWidth={section.imageWidth}
            maxItems={section.maxItems}
            tts={section.tags?.tts}
            serviceability={section.tags?.serviceability}
            numberOfColumns={Number(section.numberOfColumns)}
            numberOfRows={Number(section.numberOfRows)}
            providers={data}
            domain={section?.domain ?? ""}
            sectionStyle={sectionStyle}
          />
        )

      case "stores":
        return (
          <Stores
            sectionsRequested={sectionsRequested}
            title={section.name}
            alignTitle={section.alignTitle}
            sectionId={section.id}
            showViewAll={section?.viewAll ?? true}
            tagName={section.tags?.name ?? ""}
            imageHeight={section.imageHeight}
            imageWidth={section.imageWidth}
            maxItems={section.maxItems}
            tts={section.tags?.tts}
            serviceability={section.tags?.serviceability}
            numberOfColumns={Number(section.numberOfColumns)}
            numberOfRows={Number(section.numberOfRows)}
            stores={data}
            domain={section?.domain ?? ""}
            sectionStyle={sectionStyle}
          />
        )

      case "subcategories":
        return (
          <SubCategories
            sectionsRequested={sectionsRequested}
            title={section.name}
            alignTitle={section.alignTitle}
            sectionId={section.id}
            showViewAll={section?.viewAll ?? true}
            numberOfColumns={Number(section.numberOfColumns)}
            numberOfRows={Number(section.numberOfRows)}
            tagName={section.tags?.name ?? ""}
            tagImage={section.tags?.image ?? ""}
            redirectTo={section.tags?.pageRedirection ?? "nonTagPage"}
            domain={section.domain ?? ""}
            imageHeight={section.imageHeight}
            imageWidth={section.imageWidth}
            maxItems={section.maxItems}
            tts={section.tags?.tts}
            serviceability={section.tags?.serviceability}
            subCategories={data}
            sectionStyle={sectionStyle}
          />
        )

      default:
        return null
    }
  }

  return <Box style={{ backgroundColor }}>{renderWidget()}</Box>
}

export default PageSection
