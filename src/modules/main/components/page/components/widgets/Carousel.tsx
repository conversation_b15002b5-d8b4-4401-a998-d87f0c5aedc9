import React, {useRef} from 'react';
import Slider from 'react-slick';
import Box from '@mui/material/Box';
import {makeStyles} from "@mui/styles";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import {CAROUSEL_TIME} from '../../../../../../utils/constants';
import CarouselItem from "./CarouselItem";
import './Carousel.css';

const settings = {
  dots: true,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: CAROUSEL_TIME,
  customPaging: () => (
    <Box className="custom-dot"/>
  )
};

const Carousel = ({
                    allowPadding,
                    imageHeight,
                    sectionWidgets,
                    navigateToPage,
                    selectedDotColor,
                  }: {
  allowPadding: boolean;
  imageHeight: number;
  sectionWidgets: any[];
  selectedDotColor: string;
  navigateToPage: (page: any) => void;
}) => {
  const styles = useCarouselStyles();
  const sliderRef = useRef<any>(null);

  if (!sectionWidgets || sectionWidgets.length === 0) {
    return <></>;
  }

  if (sectionWidgets.length === 1) {
    const item = sectionWidgets[0];
    return <CarouselItem key={item.id} item={item} navigateToPage={navigateToPage} allowPadding={allowPadding}
                         imageHeight={imageHeight}/>;
  }

  return (
    <Box className={styles.sliderContainer} sx={{height: imageHeight + 24}}>
      <Slider ref={sliderRef} {...settings}>
        {sectionWidgets.map((item) => (
          <CarouselItem key={item.id} item={item} navigateToPage={navigateToPage} allowPadding={allowPadding}
                        imageHeight={imageHeight}/>
        ))}
      </Slider>
    </Box>
  );
};

const useCarouselStyles = makeStyles<any>((theme) => ({
  sliderContainer: {
    width: '100%',
    position: 'relative',
  },
}));

export default Carousel;
