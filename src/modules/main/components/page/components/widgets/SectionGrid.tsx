import React, { useMemo } from "react"
import Skeleton from "@mui/material/Skeleton"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import { makeStyles } from "@mui/styles"
import useHandleWidgetCta from "../../../../hooks/useHandleWidgetCta"
import { emptyAlertCallback, skeletonList } from "../../../../../../utils/utils"
import SectionHeader from "../SectionHeader"

const SectionGrid = ({
  sectionsRequested,
  allowPadding,
  imageHeight,
  imageWidth,
  numberOfColumns,
  maxItems,
  title,
  alignTitle,
  showViewAll,
  widgets,
  sectionStyle,
}: {
  sectionsRequested: boolean
  allowPadding: boolean
  imageHeight: number
  imageWidth: number
  numberOfColumns: number
  maxItems: number
  title: string
  alignTitle: string
  showViewAll: boolean
  widgets: any[]
  sectionStyle: any
}) => {
  const styles = useGridStyles()
  const { navigateToPage } = useHandleWidgetCta()

  const imageStyle = useMemo(
    () => ({
      height: imageHeight,
      width: imageWidth,
    }),
    [imageWidth, imageHeight]
  )

  const widgetRows = useMemo(() => {
    const rows = []
    const columns = numberOfColumns > 0 ? numberOfColumns : 4
    for (let i = 0; i < widgets.length; i += columns) {
      rows.push(widgets.slice(i, i + columns))
    }
    return rows
  }, [widgets, numberOfColumns])

  if (sectionsRequested) {
    return (
      <Box
        display="flex"
        flexDirection="row"
        overflow="hidden"
        className={styles.sectionContainer}
      >
        {skeletonList.map((_, index) => (
          <Skeleton
            key={index}
            variant="rectangular"
            width={imageWidth}
            height={imageHeight}
          />
        ))}
      </Box>
    )
  }

  if (!widgets?.length) {
    return null
  }
  if (maxItems === 2) {
    const updatedImageWidth = (window.screen.width - 46) / 2
    return (
      <Box className={styles.sectionContainer} sx={sectionStyle}>
        <SectionHeader
          title={title}
          alignTitle={alignTitle}
          showViewAll={showViewAll}
          navigateToAll={emptyAlertCallback}
        />
        <Box className={styles.columnContainer}>
          {widgets.map((widget) => (
            <Button
              key={widget.id}
              disabled={!widget.allowRedirection}
              className={styles.image}
              sx={{ height: imageHeight }}
              onClick={() => navigateToPage(widget)}
            >
              <img
                src={widget.image}
                className={styles.widgetImage}
                style={{
                  width: updatedImageWidth,
                  height: imageHeight,
                  objectFit: "contain",
                }}
                loading="lazy"
                alt="widget"
              />
            </Button>
          ))}
        </Box>
      </Box>
    )
  }

  return (
    <Box className={styles.sectionContainer}>
      <SectionHeader
        title={title}
        alignTitle={alignTitle}
        showViewAll={showViewAll}
        navigateToAll={emptyAlertCallback}
      />
      <Box sx={{ overflowX: "auto", whiteSpace: "nowrap", paddingBottom: 1 }}>
        {widgetRows.map((row, index) => (
          <Box key={`Row${index}`} className={styles.row}>
            {row.map((widget, widgetIndex) => (
              <Button
                key={widget.id}
                disabled={!widget.allowRedirection}
                className={
                  widgetIndex === 0 && allowPadding ? styles.firstItem : ""
                }
                onClick={() => navigateToPage(widget)}
                sx={{ minWidth: "unset" }}
              >
                <img
                  src={widget.image}
                  style={{ ...imageStyle }}
                  loading="lazy"
                  alt="widget"
                  className={styles.img}
                />
              </Button>
            ))}
          </Box>
        ))}
      </Box>
    </Box>
  )
}

const useGridStyles = makeStyles<any>(() => ({
  sectionContainer: {
    paddingTop: 12,
    paddingBottom: 12,
    marginBottom: 16,
  },
  firstItem: {
    paddingLeft: 16,
  },
  separator: {
    width: 12,
  },
  columnContainer: {
    paddingLeft: 16,
    paddingRight: 16,
    display: "flex",
    flexDirection: "row",
    gap: 8,
  },
  image: {
    flex: 1,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  row: {
    display: "flex",
    flexDirection: "row",
    marginBottom: 20,
    gap: 12,
  },
  img: {
    borderRadius: 8,
    objectFit: "contain",
  },
}))

export default SectionGrid
