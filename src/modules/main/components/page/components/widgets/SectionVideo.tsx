import React, { useCallback, useRef, useState } from "react"
import Skeleton from "@mui/material/Skeleton"
import SectionHeader from "../SectionHeader"
import { emptyAlertCallback } from "../../../../../../utils/utils"
import Box from "@mui/material/Box"
import { makeStyles } from "@mui/styles"

const SectionVideo = ({
  sectionsRequested,
  allowPadding,
  imageHeight,
  title,
  alignTitle,
  showViewAll,
  widgets,
  sectionStyle,
}: {
  sectionsRequested: boolean
  allowPadding: boolean
  imageHeight: number
  title: string
  alignTitle: string
  showViewAll: boolean
  widgets: any[]
  sectionStyle: any
}) => {
  const videoPlayer = useRef<any>(null)
  const styles = useSectionVideoStyles()
  const [showVideo, setShowVideo] = useState<boolean>(true)

  const handleEnd = useCallback(() => {
    videoPlayer.current.seek(0)
  }, [])

  if (sectionsRequested) {
    return (
      <Box className={styles.container}>
        <Skeleton
          variant="rectangular"
          width="100%"
          height={imageHeight}
          sx={{ borderRadius: 8 }}
        />
      </Box>
    )
  }

  if (widgets?.length === 0) {
    return <></>
  }

  if (widgets.length > 0 && showVideo) {
    return (
      <Box className={styles.sectionContainer} sx={sectionStyle}>
        <SectionHeader
          title={title}
          alignTitle={alignTitle}
          showViewAll={showViewAll}
          navigateToAll={emptyAlertCallback}
        />
        <video
          ref={videoPlayer}
          src={widgets[0].image}
          className={`${
            allowPadding ? styles.marginHorizontal : styles.fullWidth
          } ${styles.video}`}
          style={{ height: imageHeight }}
          onError={() => setShowVideo(false)}
          onEnded={handleEnd}
          muted
          autoPlay
          loop
          playsInline
          controls={false}
        />
      </Box>
    )
  }

  return <></>
}

const useSectionVideoStyles = makeStyles<any>((theme) => ({
  sectionContainer: {
    paddingVertical: 12,
    marginBottom: 16,
  },
  container: { paddingHorizontal: 16 },
  content: { borderRadius: 8 },
  marginHorizontal: {
    marginHorizontal: 16,
    width: 100 - 32,
  },
  fullWidth: {
    width: "100%",
  },
  video: {
    borderRadius: 16,
    width: "100%",
  },
}))

export default SectionVideo
