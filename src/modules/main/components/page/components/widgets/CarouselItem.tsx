import React, {useRef} from 'react';
import Button from "@mui/material/Button";
import {makeStyles} from "@mui/styles";

const CarouselItem = ({item, navigateToPage, allowPadding, imageHeight}: {
  item: any;
  navigateToPage: (value: any) => void;
  allowPadding: boolean;
  imageHeight: number
}) => {
  const styles = useCarouselStyles();
  const startXRef = useRef<number | null>(null);
  const movedRef = useRef<boolean>(false);

  const handleMouseDown = (e: React.MouseEvent) => {
    startXRef.current = e.clientX;
    movedRef.current = false;
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (startXRef.current !== null) {
      const diff = Math.abs(e.clientX - startXRef.current);
      if (diff > 5) movedRef.current = true;
    }
  };

  const handleClick = () => {
    if (!movedRef.current) {
      navigateToPage(item); // Only if no swipe
    }
  };

  return (
    <Button
      key={item.id}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onClick={handleClick}
      className={`${allowPadding ? styles.padding : ''} ${styles.sliderView}`}
    >
      <img
        src={item.image}
        className={styles.sliderImage}
        style={{height: imageHeight}}
        loading="lazy"
      />
    </Button>
  );
};

const useCarouselStyles = makeStyles<any>((theme) => ({
  padding: {
    padding: '0 16px',
  },
  sliderView: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  sliderImage: {
    width: '100%',
    objectFit: 'contain',
    borderRadius: '12px'
  },
}));

export default CarouselItem;
