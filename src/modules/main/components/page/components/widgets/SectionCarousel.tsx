import React, { useMemo } from "react"
import Skeleton from "@mui/material/Skeleton"
import Box from "@mui/material/Box"
import { makeStyles } from "@mui/styles"
import { useTheme } from "@mui/material/styles"
import Carousel from "./Carousel"
import useHandleWidgetCta from "../../../../hooks/useHandleWidgetCta"
import SectionHeader from "../SectionHeader"
import { emptyAlertCallback } from "../../../../../../utils/utils"

const SectionCarousel = ({
  sectionsRequested,
  allowPadding,
  sectionBackgroundColor,
  imageHeight,
  title,
  alignTitle,
  showViewAll,
  widgets,
  sectionStyle,
}: {
  sectionsRequested: boolean
  allowPadding: boolean
  sectionBackgroundColor: string
  imageHeight: number
  title: string
  alignTitle: string
  showViewAll: boolean
  widgets: any[]
  sectionStyle: any
}) => {
  const { palette } = useTheme()
  const styles = useCarouselStyles()
  const { navigateToPage } = useHandleWidgetCta()

  const selectedDotColor = useMemo(() => {
    const backgroundColor = sectionBackgroundColor.toUpperCase()
    return backgroundColor === "#FFFFFF" || backgroundColor === "#FFF"
      ? palette.primary
      : "#FB7171"
  }, [sectionBackgroundColor])

  if (sectionsRequested) {
    return (
      <Box className={styles.container}>
        <Skeleton
          variant="rectangular"
          width="100%"
          height={imageHeight}
          sx={{ borderRadius: 8 }}
        />
      </Box>
    )
  }

  if (widgets?.length === 0) {
    return <></>
  }

  return (
    <Box className={styles.sectionContainer} sx={sectionStyle}>
      <SectionHeader
        title={title}
        alignTitle={alignTitle}
        showViewAll={showViewAll}
        navigateToAll={emptyAlertCallback}
      />
      <Carousel
        allowPadding={allowPadding}
        imageHeight={imageHeight}
        selectedDotColor={selectedDotColor}
        navigateToPage={navigateToPage}
        sectionWidgets={widgets}
      />
    </Box>
  )
}

const useCarouselStyles = makeStyles<any>((theme) => ({
  sectionContainer: {
    paddingTop: 12,
    paddingBottom: 12,
    marginBottom: 16,
  },
  container: {
    paddingLeft: 16,
    paddingRight: 16,
  },
  content: { borderRadius: 8 },
}))

export default SectionCarousel
