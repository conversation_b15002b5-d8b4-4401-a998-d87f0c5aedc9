import React from 'react';
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';
import Grid from '@mui/material/Grid2';
import {skeletonList} from '../../../../../utils/utils';

const PageSkeleton = () => {
  return (
    <Box sx={{padding: 2, gap: 2}}>
      <Skeleton variant="rectangular" width="100%" height={156} sx={{borderRadius: 2, mb: 2}}/>
      <Grid container spacing={2} direction="row" justifyContent="flex-start">
        {skeletonList.map(item => (
          <Grid key={item.id}>
            <Skeleton variant="rectangular" width={82} height={92} sx={{borderRadius: 2}}/>
          </Grid>
        ))}
      </Grid>
      <Skeleton variant="rectangular" width="100%" height={156} sx={{borderRadius: 2, mb: 2, mt: 2}}/>
      <Skeleton variant="rectangular" width="100%" height={156} sx={{borderRadius: 2, mb: 2}}/>
      <Grid container spacing={2} direction="row" justifyContent="flex-start">
        {skeletonList.map(item => (
          <Grid key={item.id}>
            <Skeleton variant="rectangular" width={82} height={92} sx={{borderRadius: 2}}/>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default PageSkeleton;
