import { useCallback, useState } from "react"
import { useNavigate } from "react-router-dom"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import Box from "@mui/material/Box"

const SubCategoryImage = ({
  productImageSource,
  defaultImage,
  imageStyle,
}: {
  productImageSource: any
  defaultImage: any
  imageStyle: any
}) => {
  const [error, setError] = useState(false)
  const styles = useSubCategoryStyles()
  const onError = useCallback(() => {
    setError(true)
  }, [])

  return (
    <img
      src={error ? defaultImage : productImageSource}
      style={{ ...imageStyle }}
      onError={onError}
      className={styles.image}
    />
  )
}

const SubCategory = ({
  isFirst,
  categoryDomain,
  categoryName,
  subCategory,
  defaultCategoryImage,
  imageStyle,
  tagName,
  tagImage,
  tts,
  serviceability,
  redirectTo,
}: {
  isFirst: boolean
  categoryDomain: string
  categoryName: string
  tagName: string
  tagImage: string
  tts: any
  serviceability: any
  subCategory: any
  defaultCategoryImage: any
  imageStyle: any
  redirectTo: string
}) => {
  const navigate = useNavigate()
  const styles = useSubCategoryStyles()

  const navigateToCategory = () => {
    if (redirectTo === "tagPage") {
      const subCategoryCode = subCategory.code.replace(/&/g, "|")
      navigate(
        `/tag-subcategory-details?subCategory=${subCategoryCode}&category=${categoryName}&categoryDomain=${categoryDomain}&tagName=${tagName}&tagImage=${tagImage}&tts=${tts}&serviceability=${serviceability}`
      )
    } else {
      navigate(
        `/sub-category?subCategory=${subCategory.code.replace(
          /&/g,
          "|"
        )}&category=${categoryName}&categoryDomain=${categoryDomain}&tagName=${tagName}&tagImage=${tagImage}&tts=${tts}&serviceability=${serviceability}`
      )
    }
  }

  let uri = defaultCategoryImage
  if (subCategory.url) {
    if (subCategory.url.length > 0) {
      uri = subCategory.url
    }
  }

  return (
    <Box
      className={`${styles.category} ${isFirst ? styles.firstItem : ""}`}
      onClick={navigateToCategory}
    >
      <SubCategoryImage
        imageStyle={imageStyle}
        productImageSource={uri}
        defaultImage={defaultCategoryImage}
      />
      <Typography
        variant="labelLarge"
        className={styles.categoryText}
        sx={{ width: imageStyle.width }}
      >
        {subCategory.label}
      </Typography>
    </Box>
  )
}

const useSubCategoryStyles = makeStyles<any>((theme) => ({
  image: {
    marginBottom: 6,
    borderRadius: 8,
  },
  categoryText: {
    color: theme.palette.neutral400,
    textAlign: "center",
    letterSpacing: -0.5,
  },
  category: {
    // alignItems: 'center',
    display: "flex",
    flexDirection: "column",
  },
  firstItem: {
    paddingLeft: 16,
  },
}))

export default SubCategory
