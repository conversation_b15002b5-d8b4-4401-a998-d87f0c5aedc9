import React, { useMemo, useState } from "react"
import Skeleton from "@mui/material/Skeleton"
import { useNavigate } from "react-router-dom"
import Box from "@mui/material/Box"
import { makeStyles } from "@mui/styles"

import {
  getFirstMaxItems,
  keyExtractor,
  skeletonList,
} from "../../../../../../utils/utils"
import SubCategory from "./SubCategory"
import { CATEGORIES } from "../../../../../../utils/categories"
import SectionHeader from "../SectionHeader"
import AllSubCategories from "../../../../modules/pages/SubCategories"

const SubCategories = ({
  sectionsRequested,
  tagName,
  tagImage,
  domain,
  imageHeight,
  imageWidth,
  tts,
  serviceability,
  title,
  alignTitle,
  showViewAll,
  maxItems,
  numberOfColumns,
  numberOfRows,
  redirectTo,
  subCategories,
  sectionStyle,
}: {
  sectionsRequested: boolean
  sectionId: string
  tagName: string
  tagImage: string
  domain: string
  imageHeight: string
  imageWidth: string
  maxItems: number
  tts: any
  serviceability: any
  title: string
  alignTitle: string
  showViewAll: boolean
  numberOfColumns: number
  numberOfRows: number
  redirectTo: string
  subCategories: any[]
  sectionStyle: any
}) => {
  const navigate = useNavigate()
  const styles = useSubCategoriesStyles()
  const [renderViewAll, setRenderViewAll] = useState(false)

  const navigateToAll = () => {
    setRenderViewAll(true)
  }

  const imageStyle = useMemo(
    () => ({
      height: Number(imageHeight),
      width: Number(imageWidth),
    }),
    [imageWidth, imageHeight]
  )

  const { defaultCategoryImage, categoryName } = useMemo(() => {
    const selected = CATEGORIES.find(
      (category: { domain: string }) => category.domain === domain
    )
    return selected
      ? {
          defaultCategoryImage: selected.domain,
          categoryName: selected.shortName,
        }
      : { defaultCategoryImage: "", categoryName: "" }
  }, [domain])

  const currentSubCategories = useMemo(() => {
    return getFirstMaxItems(subCategories, maxItems)
  }, [subCategories, maxItems])

  const subCategoriesRows = useMemo(() => {
    const rows = []
    for (let i = 0; i < currentSubCategories.length; i += numberOfColumns) {
      rows.push(currentSubCategories.slice(i, i + numberOfColumns))
    }
    return rows
  }, [currentSubCategories, numberOfColumns, numberOfRows])

  if (sectionsRequested) {
    return (
      <Box
        className={styles.skeletonContainer}
        display="flex"
        flexDirection="row"
        overflow="auto"
      >
        {skeletonList.map((item) => (
          <Box key={keyExtractor(item)} className={styles.skeletonItem}>
            <Skeleton
              variant="rectangular"
              width={imageStyle.width}
              height={imageStyle.height}
            />
          </Box>
        ))}
      </Box>
    )
  }

  if (subCategories?.length === 0) {
    return null
  }

  return (
    <Box className={styles.sectionContainer} sx={sectionStyle}>
      <SectionHeader
        title={title}
        alignTitle={alignTitle}
        showViewAll={showViewAll}
        navigateToAll={navigateToAll}
      />
      {maxItems <= 4 ? (
        <Box
          display="flex"
          flexDirection="row"
          gap={2}
          className={styles.horizontalRow}
        >
          {currentSubCategories.map((subCategory) => (
            <Box key={subCategory.code} className={styles.providerContainer}>
              <SubCategory
                isFirst={false}
                categoryDomain={domain}
                categoryName={categoryName}
                subCategory={subCategory}
                imageStyle={imageStyle}
                defaultCategoryImage={defaultCategoryImage}
                tagName={tagName}
                tagImage={tagImage}
                tts={tts}
                serviceability={serviceability}
                redirectTo={redirectTo}
              />
            </Box>
          ))}
        </Box>
      ) : (
        <Box
          display="flex"
          flexDirection="column"
          overflow="auto"
          className={styles.scrollContainer}
        >
          {subCategoriesRows.map((row, index) => (
            <Box key={`subCategories${index}`} className={styles.row}>
              {row.map((subcategory: any, subCategoryIndex: number) => (
                <Box
                  key={subcategory.code}
                  className={`${styles.item} ${
                    subCategoryIndex === 0 ? styles.isFirst : ""
                  }`}
                >
                  <SubCategory
                    isFirst={false}
                    categoryDomain={domain}
                    tagName={tagName}
                    tagImage={tagImage}
                    tts={tts}
                    serviceability={serviceability}
                    redirectTo={redirectTo}
                    categoryName={categoryName}
                    subCategory={subcategory}
                    imageStyle={imageStyle}
                    defaultCategoryImage={defaultCategoryImage}
                  />
                </Box>
              ))}
            </Box>
          ))}
        </Box>
      )}
      <AllSubCategories
        allSubCategories={subCategories}
        categoryName={categoryName}
        defaultCategoryImage={defaultCategoryImage}
        domain={domain}
        imageStyle={imageStyle}
        numberOfColumns={numberOfColumns}
        numberOfRows={numberOfRows}
        tagName={tagName}
        tagImage={tagImage}
        tts={tts}
        serviceability={serviceability}
        redirectTo={redirectTo}
        open={renderViewAll}
        handleClose={() => setRenderViewAll(false)}
      />
    </Box>
  )
}

const useSubCategoriesStyles = makeStyles<any>((theme) => ({
  sectionContainer: {
    padding: "12px 0",
    marginBottom: 16,
  },
  separator: {
    width: 12,
  },
  skeletonContainer: {
    display: "flex",
    flexDirection: "row",
    gap: 12,
    overflowX: "auto",
    paddingLeft: 16,
  },
  skeletonItem: {
    flexShrink: 0,
  },
  providerContainer: {
    flex: 1,
  },
  horizontalRow: {
    display: "flex !important",
    alignItems: "flex-start !important",
    gap: 12,
    paddingRight: 16,
    paddingLeft: 16,
  },
  scrollContainer: {
    display: "flex",
    flexDirection: "row",
    overflowX: "auto",
    gap: 12,
    paddingBottom: 8,
  },
  row: {
    display: "flex",
    flexDirection: "row",
    marginBottom: 16,
    width: "100%",
  },
  isFirst: {
    paddingLeft: 16,
  },
  item: {
    marginRight: 12,
  },
}))

export default SubCategories
