import React from 'react';
import Box from "@mui/material/Box";
import {makeStyles} from "@mui/styles";

const SafeAreaPage = ({children}: { children: any }) => {
  const styles = useStyles();
  return <Box className={styles.pageBackground}>{children}</Box>;
};

const useStyles = makeStyles(() => ({
  pageBackground: {
    padding: '0px !important',
    height: '100dvh',
    overflow: 'hidden',
  },
}));

export default SafeAreaPage;
