import ArrowForward from "@mui/icons-material/ArrowForward"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import useFormatNumber from "../../hooks/useFormatNumber"

interface PageProps {
  children: React.ReactNode
  outletId?: string
}

const Page: React.FC<PageProps> = ({ children, outletId = "" }) => {
  const { formatNumber } = useFormatNumber()
  const { t } = useTranslation()
  const theme = useTheme()
  const navigate = useNavigate()
  const { cartItems } = useSelector((state: any) => state.cart)

  const { cartLocationId, count } = useMemo(() => {
    if (outletId !== "") {
      const cartIndex = cartItems.findIndex(
        (one: any) => one.location_id === outletId
      )
      if (
        cartIndex > -1 &&
        cartItems[cartIndex].hasOwnProperty("items") &&
        cartItems[cartIndex].items.length > 0
      ) {
        const cartItem = cartItems[cartIndex]
        return {
          cartLocationId: cartItem.location_id,
          count: cartItem.items.length,
        }
      }
    }
    return { cartLocationId: null, count: 0 }
  }, [outletId, cartItems])
  const locationId = cartLocationId?.replace(/\//g, "-")
  return (
    <Box sx={{ flex: 1, backgroundColor: theme.palette.background.default }}>
      {children}

      {cartLocationId && (
        <Box sx={{}}>
          <Box
            sx={{
              position: "fixed",
              bottom: 0,
              pb: 2,
              zIndex: 999, // Ensure it's above other content
              backgroundColor: "white", // Optional: add background
              boxShadow: 3, // Optional: add shadow for better visibility
              maxWidth: 480,
              width: "100%",
              alignItems: "center",
            }}
          >
            <Button
              fullWidth
              variant="contained"
              color="primary"
              onClick={() => navigate(`/cart/${locationId}`)}
              sx={{
                display: "flex",
                justifyContent: "space-between",
                px: 2,
                py: 1.5,
                mx: 2,
                borderRadius: 2,
                width: "91%",
              }}
            >
              <Typography variant="bodyLarge" sx={{ color: "white" }}>
                {formatNumber(count)}{" "}
                {count > 1 ? t("Page.Items Added") : t("Page.Item Added")}
                {t("Page., Go To Cart")}
              </Typography>
              <ArrowForward sx={{ color: "white" }} />
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  )
}

export const useStyles = makeStyles<any>((theme) => ({
  pageContainer: {
    flex: 1,
    backgroundColor: theme.palette.white,
  },
  container: {
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  button: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: theme.palette.primary,
    paddingHorizontal: 16,
    paddingVertical: 13,
    borderRadius: 8,
  },
  text: {
    color: theme.palette.white,
  },
}))

export default Page
