import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import { makeStyles } from "@mui/styles"
import React from "react"
import { useTranslation } from "react-i18next"

interface ListingTabProps {
  isStore: boolean
  setSearchType: (value: string) => void
  hideStores: boolean
}

const CategoryTab: React.FC<ListingTabProps> = ({
  isStore,
  setSearchType,
  hideStores,
}) => {
  const { t } = useTranslation()
  const styles = useStyles()
  if (hideStores) {
    return <div />
  }

  return (
    <Box className={styles.switchRow}>
      <Box className={styles.switchContainer}>
        <Button
          onClick={() => setSearchType("Stores")}
          className={`${styles.button}
            ${isStore && styles.activeButton}`}
        >
          <Box
            component="span"
            className={isStore ? styles.activeButtonText : styles.buttonText}
          >
            {t("Search.Stores")}
          </Box>
        </Button>
        <Button
          onClick={() => setSearchType("Products")}
          className={`${styles.button}
            ${!isStore && styles.activeButton}`}
        >
          <Box
            component="span"
            className={!isStore ? styles.activeButtonText : styles.buttonText}
          >
            {t("Search.Products")}
          </Box>
        </Button>
      </Box>
    </Box>
  )
}

const useStyles = makeStyles<any>((theme: any) => ({
  switchRow: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    margin: 2,
  },
  switchContainer: {
    height: 35,
    width: 210,
    borderRadius: 24,
    marginTop: 20,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: theme.palette.primary50,
  },
  button: {
    width: 105,
    height: 35,
    display: "flex !important",
    justifyContent: "center !important",
    alignItems: "center !important",
    borderRadius: "24px  !important",
    minWidth: "unset !important",
    padding: "10px  !important",
    textTransform: "none",
  },
  activeButton: {
    backgroundColor: `${theme.palette.primary.main} !important`,
  },
  activeButtonText: {
    color: "white",
  },
  buttonText: {
    color: theme.palette.neutral400,
  },
}))

export default CategoryTab
