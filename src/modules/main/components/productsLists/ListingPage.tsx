import Box from "@mui/material/Box"
import React, { useMemo, useState } from "react"
import Products from "../products/Products"
import SearchProviders from "../provider/components/SearchProviders"
import ListingTab from "./ListingTab"

interface ListingPageProps {
  searchQuery: string
  subCategories: string
  isSearch?: boolean
  fbDomain?: boolean
  tagId?: string
  categoryDomain?: string
  serviceability?: any
  tts?: any
  hideStores: boolean
  bppId?: string
}

const ListingPage: React.FC<ListingPageProps> = ({
  searchQuery,
  subCategories,
  isSearch = false,
  fbDomain = false,
  tagId = "",
  serviceability = "",
  tts = "",
  bppId = "",
  categoryDomain = "",
  hideStores,
}) => {
  const [searchType, setSearchType] = useState<string>("Stores")
  const isStore = useMemo(() => {
    return searchType === "Stores" && !hideStores
  }, [hideStores, searchType])
  return (
    <Box sx={styles.pageContainer}>
      <ListingTab
        isStore={isStore}
        setSearchType={setSearchType}
        hideStores={hideStores}
      />
      {isStore ? (
        <SearchProviders
          searchQuery={searchQuery}
          currentSubCategory={subCategories}
          tagId={tagId}
          serviceability={serviceability}
          tts={tts}
          categoryDomain={categoryDomain}
          bppId={bppId}
        />
      ) : (
        <Products
          tagId={tagId}
          serviceability={serviceability}
          tts={tts}
          categoryDomain={categoryDomain}
          providerId={null}
          subCategories={subCategories.length > 0 ? [subCategories] : []}
          searchText={searchQuery}
          provider={null}
          isOpen
          isSearch={isSearch}
          fbDomain={fbDomain}
          bppId={bppId}
        >
          <></>
        </Products>
      )}
    </Box>
  )
}

const styles = {
  pageContainer: {
    flex: 1,
  },
}

export default ListingPage
