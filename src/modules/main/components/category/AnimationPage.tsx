import React, {ReactNode, useMemo, useState} from 'react';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import {useTheme} from '@mui/material/styles';
import KeyboardArrowLeftIcon from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';

interface AnimationPageProps {
  children: ReactNode;
  list: ReactNode;
  message?: ReactNode;
}

const AnimationPage: React.FC<AnimationPageProps> = ({children, list, message = null}) => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState<boolean>(true);

  const styles = useMemo(() => ({
    container: {
      display: 'flex',
      flexDirection: 'row',
      height: '100%',
      maxWidth: 480,
      marginTop: 3
    },
    listContainer: {
      borderRight: `1px solid ${theme.palette.divider}`,
      width: expanded ? 80 : 0,
      overflow: 'hidden',
      transition: 'width 0.3s ease',
    },
    contentContainer: {
      flex: 1,
      position: 'relative',
      overflow: 'scroll',
    },
    collapsibleButton: {
      position: 'fixed',
      height: 32,
      width: 24,
      backgroundColor: '#02406B',
      bottom: 50,
      borderTopRightRadius: 27,
      borderBottomRightRadius: 27,
      borderTopLeftRadius: 0,
      borderBottomLeftRadius: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: 0,
    },
  }), [theme, expanded]);

  const toggleWidth = () => setExpanded(!expanded);

  return (
    <Box sx={styles.container}>
      <Box sx={styles.listContainer}>
        {message}
        {list}
      </Box>
      <Box sx={styles.contentContainer}>
        {children}
        <IconButton sx={styles.collapsibleButton} onClick={toggleWidth}>
          {expanded ? (
            <KeyboardArrowLeftIcon sx={{color: '#fff'}}/>
          ) : (
            <KeyboardArrowRightIcon sx={{color: '#fff'}}/>
          )}
        </IconButton>
      </Box>
    </Box>
  );
};

export default AnimationPage;
