import React, {useCallback, useState} from 'react';

const NoImageAvailable = require('../../../../assets/noImage.png');

const Image = ({source, imageStyle}: { source: any; imageStyle: any }) => {
  const [error, setError] = useState(false);

  const onError = useCallback(() => {
    setError(true);
  }, []);

  return (
    <img
      src={error ? NoImageAvailable : source}
      className={imageStyle}
      style={{objectFit: 'contain'}}
      onError={onError}
      alt="Image"
    />
  );
};

export default Image;
