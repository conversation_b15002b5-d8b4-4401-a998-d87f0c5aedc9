import React from "react";
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";

/**
 * Component to render a generic input field
 * @param inputLabel - Label for the input field
 * @param props - Other props
 * @returns {JSX.Element}
 */
const InputField: React.FC<any> = ({inputLabel, ...props}) => {
  const classes = useStyles();

  return (
    <Box>
      <Typography variant="labelSmall" className={classes.inputLabel}>
        {inputLabel}
        {props.required && <span className={classes.required}>*</span>}
      </Typography>
      <TextField
        {...props}
        variant="outlined"
        fullWidth
        size="small"
        className={classes.inputText}
        InputProps={{
          style: {
            fontFamily: "Inter-Regular",
            fontWeight: 500,
            fontSize: 14,
            lineHeight: "18px",
            ...(props.style || {}), // Ensure `props.style` is an object before spreading
          },
        }}
        error={!!props.error}
        helperText={props.error ? props.errorMessage : ""}
      />

    </Box>
  );
};

const useStyles = makeStyles((theme: any) => ({
  inputLabel: {
    color: theme.neutral400,
    marginBottom: 4,
  },
  inputText: {
    backgroundColor: theme.white,
    borderRadius: 12,
    "& .MuiOutlinedInput-root": {
      "& fieldset": {
        borderColor: theme.neutral200,
      },
      "&:hover fieldset": {
        borderColor: theme.neutral300,
      },
      "&.Mui-focused fieldset": {
        borderColor: theme.primary,
      },
    },
  },
  inputContent: {
    fontFamily: "Inter-Regular",
    fontWeight: 500,
    fontSize: 14,
    lineHeight: "18px",
    color: theme.neutral400,
  },
  required: {
    color: theme.error600,
    marginLeft: 4,
  },
}));

export default InputField;
