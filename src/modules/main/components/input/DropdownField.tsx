import React, {useState} from 'react';
import Box from '@mui/material/Box';
import FormHelperText from '@mui/material/FormHelperText';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';

const DropdownField: React.FC<any> = ({label, inputLabel, ...props}) => {
  const styles = useStyles();
  const [selectedValue, setSelectedValue] = useState('');

  const handleChange = (event: any) => {
    setSelectedValue(event.target.value);
  };

  return (
    <Box>
      <Typography variant="labelSmall" className={styles.inputLabel}>
        {inputLabel} {props.required && <span className={styles.required}>*</span>}
      </Typography>
      <InputLabel>{label}</InputLabel>
      <Select
        value={selectedValue}
        onChange={handleChange}
        label={label}
        className={styles.inputText}
        {...props}
      >
        {props.options.map((option: any, index: number) => (
          <MenuItem key={index} value={option.value} className={styles.dropdownItem}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
      {props.error && <FormHelperText>{props.errorMessage}</FormHelperText>}
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  inputLabel: {
    color: theme.palette.text.secondary,
    marginBottom: 4,
  },
  outline: {
    borderRadius: 12,
  },
  inputText: {
    backgroundColor: theme.palette.background.paper,
  },
  dropdownText: {
    fontFamily: 'Inter-Regular',
    fontWeight: 400,
    color: theme.palette.text.secondary,
  },
  dropDown: {
    borderRadius: 20,
    paddingBottom: 100,
  },
  dropdownItem: {
    backgroundColor: theme.palette.background.paper,
    padding: '7px 12px',
  },
  selectedItem: {
    color: theme.palette.primary.main,
    fontFamily: 'Inter-Regular',
    fontWeight: 400,
  },
  selectedText: {
    fontFamily: 'Inter-Regular',
    fontWeight: 500,
    fontSize: 14,
    lineHeight: 18,
    color: theme.palette.text.secondary,
  },
  required: {
    color: theme.palette.error.main,
  },
}));
export default DropdownField;
