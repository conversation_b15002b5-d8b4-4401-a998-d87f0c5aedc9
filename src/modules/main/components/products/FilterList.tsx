import Skeleton from "@mui/material/Skeleton"
import React, { useCallback, useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import { Search } from "@mui/icons-material"
import { SvgIcon } from "@mui/material"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import TextField from "@mui/material/TextField"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import useGetFilterAttribute from "../../../../hooks/useGetFilterAttribute"
import { convertHexToName, localSearch } from "../../../../utils/utils"
import PriceSlider from "./PriceSlider"
const CheckedIcon = (
  <SvgIcon>
    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-9 14l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
  </SvgIcon>
)

const UncheckedIcon = (
  <SvgIcon sx={{ color: "#B5B5B5" }}>
    <path d="M19 3H5c-1.1 0-2 .9-2 2v14a2 2 0 0 0 2 2h14c1.1 0 2-.9 2-2V5a2 2 0 0 0-2-2zm0 16H5V5h14v14z" />
  </SvgIcon>
)
interface FilterList {
  attributes: any[]
  getAttributeValues: (attribute: string) => void
  selectedAttributes: any
  setSelectedAttributes: (values: any) => void
  close: () => void
  attributeValuesRequested: boolean
}

const ValuesSkeleton = () => {
  const styles = useFilterListStyles()
  return (
    <Box className={styles.skeletonRow}>
      <Skeleton
        variant="circular"
        width={24}
        height={24}
        className={styles.checkboxSkeleton}
      />
      <Skeleton
        variant="text"
        width="80%"
        height={20}
        className={styles.textSkeleton}
      />
    </Box>
  )
}

const AttributeItem = ({
  item,
  selected,
  onPress,
  selectedValues,
}: {
  item: any
  selected: boolean
  onPress: any
  selectedValues: any
}) => {
  const styles = useFilterListStyles()
  const { translateFilterAttribute } = useGetFilterAttribute()

  return (
    <Button
      className={`${styles.attribute} ${
        selected ? styles.selectedFilterItem : ""
      }`}
      onClick={onPress}
    >
      <Box className={selected ? styles.selected : styles.normal} />
      <Typography
        variant="labelSmall"
        className={`${styles.attributeText} ${selected} ? ${styles.selectedText} : ${styles.normalText}`}
      >
        {translateFilterAttribute(item.code)}
      </Typography>
      {selectedValues[item.code]?.length > 0 && (
        <Typography variant="bodyMedium" className={styles.attributeCount}>
          {selectedValues[item.code]?.length}
        </Typography>
      )}
    </Button>
  )
}

const AttributeValueItem = ({
  value,
  isSelected,
  currentAttribute,
  removeValue,
  addValue,
}: {
  value: any
  isSelected: boolean
  currentAttribute: any
  removeValue: any
  addValue: any
}) => {
  const styles = useFilterListStyles()

  const handlePress = useCallback(() => {
    isSelected ? removeValue(value) : addValue(value)
  }, [isSelected, removeValue, addValue, value])

  if (currentAttribute === "colour") {
    return (
      <Button
        fullWidth
        key={value}
        className={styles.valueRow}
        onClick={handlePress}
      >
        <Box
          className={`${styles.dotContainer} ${isSelected} ? ${styles.selectedDotContainer} : ''`}
        >
          <Box className={styles.colorDot} sx={{ backgroundColor: value }} />
        </Box>
        <Typography variant={"labelMedium"}>
          {convertHexToName(value)}
        </Typography>
      </Button>
    )
  } else {
    return (
      <Button
        fullWidth
        key={value}
        className={`${styles.valueRow} ${styles.checkButton}`}
        onClick={handlePress}
      >
        <div style={{ width: "5px" }} />
        {isSelected ? CheckedIcon : UncheckedIcon}
        <Typography variant={"labelMedium"} className={styles.valueLabel}>
          {value}
        </Typography>
      </Button>
    )
  }
}

const FilterList: React.FC<FilterList> = ({
  attributes,
  getAttributeValues,
  selectedAttributes,
  setSelectedAttributes,
  close,
  attributeValuesRequested,
}) => {
  const { t } = useTranslation()
  const styles = useFilterListStyles()
  const [currentAttribute, setCurrentAttribute] = useState<string>(
    attributes[0].code
  )
  const [currentValuesAll, setCurrentValuesAll] = useState<any[]>([])
  const [currentValues, setCurrentValues] = useState<any[]>([])
  const [selectedValues, setSelectedValues] = useState<any>({})
  const [searchQuery, setSearchQuery] = useState<string>("")
  const priceRangeRef = useRef<any>(null)

  const updateFilters = () => {
    setSelectedAttributes(selectedValues)
    close()
  }

  const clearAll = () => {
    setSelectedValues({})
    if (priceRangeRef.current) {
      priceRangeRef.current.clearRangeSelection()
    }
  }

  const renderAttributeItem = useCallback(
    ({ item }: { item: any }) => {
      const selected = currentAttribute === item.code
      return (
        <AttributeItem
          item={item}
          selected={selected}
          onPress={() => setCurrentAttribute(item.code)}
          selectedValues={selectedValues}
        />
      )
    },
    [selectedValues, currentAttribute, setCurrentAttribute]
  )

  const renderAttributeValueItem = useCallback(
    ({ item }: { item: any }) => {
      const isSelected = selectedValues[currentAttribute]?.includes(item)

      const removeValue = (value: string) => {
        const values = Object.assign({}, selectedValues)
        values[currentAttribute] = values[currentAttribute].filter(
          (one: string) => one !== value
        )
        setSelectedValues(values)
      }

      const addValue = (value: string) => {
        const values = Object.assign({}, selectedValues)
        if (values.hasOwnProperty(currentAttribute)) {
          values[currentAttribute] = values[currentAttribute].concat([value])
        } else {
          values[currentAttribute] = [value]
        }
        setSelectedValues(values)
      }

      return (
        <AttributeValueItem
          value={item}
          isSelected={isSelected}
          currentAttribute={currentAttribute}
          removeValue={removeValue}
          addValue={addValue}
        />
      )
    },
    [selectedValues, currentAttribute]
  )

  const attributeValueKeyExtractor = useCallback((item: any) => item, [])

  const onValuesChange = (values: number[]) => {
    const newAttributes = Object.assign({}, selectedValues)
    newAttributes[currentAttribute] = {
      values,
      skipAttribute: true,
    }
    setSelectedValues(newAttributes)
  }

  useEffect(() => {
    const attribute = attributes.find((one) => one.code === currentAttribute)
    if (attribute && attribute.hasOwnProperty("values")) {
      setCurrentValues(attribute.values)
      setCurrentValuesAll(attribute.values)
    } else {
      setCurrentValues([])
      setCurrentValuesAll([])
      getAttributeValues(currentAttribute)
    }
  }, [currentAttribute, attributes])

  useEffect(() => {
    setSelectedValues(selectedAttributes)
  }, [selectedAttributes])
  const onChangeSearch = (query: string) => {
    setSearchQuery(query)
    const filteredValues = localSearch(currentValuesAll, query)
    if (query.length === 0) {
      setCurrentValues(currentValuesAll)
      return
    }
    setCurrentValues(filteredValues)
  }
  return (
    <Box className={styles.container}>
      <Box className={styles.closers} />
      <Box className={styles.sheetHeader}>
        <Typography variant="titleLarge" className={styles.title}>
          {t("Cart.Filter List.Filters")}
        </Typography>
        <Button onClick={clearAll}>
          <Typography variant="labelSmall" className={styles.clearButton}>
            {t("Cart.Filter List.Clear all")}
          </Typography>
        </Button>
      </Box>
      <Box className={styles.filterContainer}>
        <Box className={styles.attributes}>
          {attributes.map((item) => renderAttributeItem({ item }))}
        </Box>
        <Box className={styles.attributeValues}>
          {attributeValuesRequested ? (
            <Box sx={{ p: 2 }}>
              <ValuesSkeleton />
              <ValuesSkeleton />
              <ValuesSkeleton />
              <ValuesSkeleton />
            </Box>
          ) : currentAttribute === "priceRange" ? (
            <Box sx={{ mt: 2 }}>
              <PriceSlider
                ref={priceRangeRef}
                sliderValues={selectedAttributes[currentAttribute]?.values}
                onValuesChange={onValuesChange}
              />
            </Box>
          ) : (
            <>
              {currentValuesAll.length >= 20 && (
                <Box className={styles.searchContainer}>
                  <Box className={styles.searchbar}>
                    <Search className={styles.searchIcon} />
                    <TextField
                      variant="standard"
                      fullWidth
                      placeholder="Search..."
                      value={searchQuery}
                      onChange={(e) => onChangeSearch(e.target.value)}
                      onKeyDown={(event) => {
                        if (event.key === "Enter") {
                          event.preventDefault()
                        }
                      }}
                      InputProps={{
                        disableUnderline: true,
                        className: styles.searchInput,
                      }}
                    />
                  </Box>
                </Box>
              )}
              <Box>
                {currentValues.map((item) =>
                  renderAttributeValueItem({ item })
                )}
              </Box>
            </>
          )}
        </Box>
      </Box>
      <Box className={styles.footer}>
        <Box className={styles.buttonContainer}>
          <Button
            className={`${styles.button} ${styles.closeButton}`}
            onClick={close}
          >
            <Typography
              variant={"body3"}
              className={`${styles.buttonLabel} ${styles.closeLabel}`}
            >
              {t("Cart.Filter List.Close")}
            </Typography>
          </Button>
        </Box>

        <Box className={styles.buttonContainer}>
          <Button
            className={`${styles.button} ${styles.applyButton}`}
            onClick={updateFilters}
          >
            <Typography
              variant={"body3"}
              className={`${styles.buttonLabel} ${styles.applyLabel}`}
            >
              {t("Cart.Filter List.Apply")}
            </Typography>
          </Button>
        </Box>
      </Box>
    </Box>
  )
}

export const useFilterListStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
  },
  sheetHeader: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    padding: "12px 16px !important",
    paddingVertical: 12,
    borderBottom: "1px solid" + theme.palette.neutral100,
    height: 56,
  },
  closers: {
    width: 52,
    height: 4,
    backgroundColor: theme.palette.black,
    borderRadius: 10,
    alignSelf: "center",
    marginTop: 8,
    marginLeft: "42%",
  },
  title: {
    color: theme.palette.neutral400,
  },
  clearButton: {
    color: theme.palette.primary,
  },
  filterContainer: {
    display: "flex",
    flexDirection: "row",
    height: "375px",
    marginBottom: "70px",
  },
  attributes: {
    flex: 0.7,
    display: "flex",
    flexDirection: "column",
    borderRight: "1px solid" + theme.palette.neutral100,
  },
  searchIcon: {
    color: theme.palette.primary.main,
  },
  attributeValues: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    justifyContent: "left !important",
    // minHeight: '400px',
    overflow: "auto",
    // maxHeight: '100px'
  },
  attribute: {
    display: "flex !important",
    paddingTop: "8px !important",
    paddingBottom: "8px !important",
    paddingLeft: "0px !important",
    paddingRight: "16px !important",
    flexDirection: "row",
    alignItems: "center !important",
    justifyContent: "left !important",
    gap: "8px",
    borderRadius: "0px !important",
  },
  selected: {
    width: 6,
    backgroundColor: theme.palette.primary.main,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    height: 30,
  },
  normal: {
    width: 6,
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
    height: 30,
  },
  selectedText: {
    color: theme.palette.primary,
  },
  normalText: {
    color: theme.palette.neutral400,
  },
  attributeText: {
    textTransform: "capitalize",
    marginLeft: 10,
    flex: 1,
    textAlign: "left",
  },
  footer: {
    position: "fixed",
    width: "100%",
    bottom: 0,
    display: "flex",
    height: 50,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-evenly",
  },
  separator: {
    width: 15,
  },
  buttonContainer: {
    width: "44%",
  },
  button: {
    display: "flex !important",
    justifyContent: "center !important",
    height: "36px !important",
    paddingLeft: "12px !important",
    paddingRight: "12px !important",
  },
  closeLabel: {
    color: theme.palette.neutral400,
  },
  applyLabel: {
    color: theme.palette.white,
  },
  buttonLabel: {
    fontWeight: "600",
    textAlign: "center",
  },
  closeButton: {
    borderRadius: "8px !important",
    border: "1px solid" + theme.palette.neutral400 + "!important",
    width: "100% !important",
  },
  applyButton: {
    borderRadius: "8px !important",
    borderColor: theme.palette.primary,
    backgroundColor: theme.palette.primary.main + "!important",
    width: "100% !important",
  },
  valueRow: {
    display: "flex",
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 5,
    alignItems: "center",
    flex: 1,
    gap: 10,
  },
  checkButton: { height: 50 },
  skeletonRow: {
    display: "flex",
    flexDirection: "row",
    paddingHorizontal: 16,
    paddingVertical: 5,
    alignItems: "center",
    flex: 1,
    marginVertical: 16,
  },
  checkboxSkeleton: {
    width: 16,
    height: 16,
  },
  textSkeleton: {
    marginLeft: 12,
    height: 16,
    width: 100,
  },
  valueLabel: {
    color: theme.palette.neutral400,
    flex: 1,
    textTransform: "none",
    textAlign: "left",
    marginLeft: "8px",
  },
  dotContainer: {
    display: "flex",
    borderWidth: 2,
    borderColor: theme.palette.neutral200,
    width: 24,
    height: 24,
    borderRadius: 18,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 8,
  },
  selectedDotContainer: {
    borderColor: theme.palette.primary,
  },
  colorDot: {
    width: 16,
    height: 16,
    borderRadius: 12,
  },
  sliderContainer: {
    display: "flex",
    alignItems: "center",
    paddingTop: 16,
  },
  sliderValues: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    paddingHorizontal: 16,
  },
  sliderValue: {
    color: theme.palette.neutral400,
  },
  track: {
    height: 6,
    backgroundColor: theme.palette.neutral100,
    borderRadius: 2,
  },
  selectedTrack: {
    backgroundColor: theme.palette.primary,
  },
  unSelectedTrack: {
    backgroundColor: theme.palette.neutral100,
  },
  selectedFilterItem: {
    backgroundColor: `${theme.palette.primary50} !important`,
  },
  searchbar: {
    display: "flex",
    alignItems: "center",
    backgroundColor: "white",
    padding: "8px 14px",
    borderRadius: 30,
    borderWidth: 1,
    borderColor: "rgba(25, 106, 171, 0.19)",
    borderStyle: "solid",
    gap: 10,
    flex: 1,
    marginRight: 12,
    marginLeft: 12,
  },
  searchContainer: {
    width: "100%",
    paddingTop: 12,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: theme.palette.white,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
  attributeCount: { color: theme.palette.primary.main },
}))

export default FilterList
