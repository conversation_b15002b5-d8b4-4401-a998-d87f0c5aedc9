import React, {useEffect, useMemo, useState} from "react";
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from "@mui/icons-material/ExpandMoreOutlined";
import ListProduct from "./ListProduct";
import {makeStyles} from "@mui/styles";

interface Product {
  id: string;

  [key: string]: any;
}

interface Section {
  id: string;
  title: string;
  data: Product[];
}

interface ProductAccordionProps {
  section: Section;
  provider: any;
  defaultExpand: boolean;
  isOpen: boolean;
  openDetailsPage: boolean;
}

const ProductAccordion: React.FC<ProductAccordionProps> = ({
                                                             section,
                                                             provider,
                                                             defaultExpand,
                                                             isOpen,
                                                             openDetailsPage,
                                                           }) => {
  const [expanded, setExpanded] = useState<boolean>(defaultExpand);

  useEffect(() => {
    setExpanded(defaultExpand);
  }, [defaultExpand]);

  const styles = useStyles();
  const handleAccordionClick = () => setExpanded(!expanded);

  const itemLength = useMemo(() => section?.data?.length || 0, [section]);

  return (
    <Accordion expanded={expanded} onChange={handleAccordionClick} className={styles.accordion}>
      <AccordionSummary expandIcon={<ExpandMoreIcon/>} id={section.id}>
        <Typography variant="titleLarge" className={styles.heading}>
          {section?.title} {itemLength > 0 ? `(${itemLength})` : ""}
        </Typography>
      </AccordionSummary>

      <AccordionDetails className={styles.accordionDetails}>
        <Box className={styles.columnWrapper}>
          {section?.data?.map((item: Product) => (
            <ListProduct
              key={item.id}
              product={item}
              provider={provider}
              isOpen={isOpen}
              listView={false}
              openDetailsPage={openDetailsPage}
            />
          ))}
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export const useStyles = makeStyles<any>((theme) => ({
  heading: {
    fontWeight: 'bold !important'
  },
  accordion: {
    backgroundColor: "white",
    boxShadow: "none !important",
    padding: '0px !important'
  },
  columnWrapper: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fill, minmax(150px, 1fr))",
    gap: "8px",
    width: "100%",
    maxWidth: "100%",
    overflowX: "hidden",
  },
  accordionDetails: {
    padding: "0px !important",
  },
}));

export default ProductAccordion;
