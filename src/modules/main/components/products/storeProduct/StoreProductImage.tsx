import {useCallback, useState} from 'react';
import NoImageAvailable from '../../../../../assets/noImage.png';
import {makeStyles} from '@mui/styles';

const StoreProductImage = ({productImageSource}: { productImageSource: any }) => {
  const styles = useProductStyles();
  const [error, setError] = useState<boolean>(false);

  const onError = useCallback(() => {
    setError(true);
  }, []);

  return (
    <img
      src={error ? NoImageAvailable : productImageSource}
      className={styles.image}
      onError={onError}
    />
  );
};

const useProductStyles = makeStyles<any>((theme) => ({
  image: {
    width: 116,
    height: 116,
    marginBottom: 8,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: theme.palette.neutral100,
    objectFit: 'cover'
  },
}));

export default StoreProductImage;
