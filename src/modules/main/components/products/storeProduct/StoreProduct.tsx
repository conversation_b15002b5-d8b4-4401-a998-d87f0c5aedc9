import React from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import Add from '@mui/icons-material/Add';
import Remove from '@mui/icons-material/Remove';
import {useTranslation} from "react-i18next";
import {useTheme} from "@mui/material/styles";
import {makeStyles} from "@mui/styles";

import VegNonVegTag from "../VegNonVegTag";
import useFormatNumber from "../../../hooks/useFormatNumber";
import ProductAmount from "../productAmount/ProductAmount";
import StoreProductImage from "./StoreProductImage";
import DefaultProductAmount from "../productAmount/DefaultProductAmount";
import {FB_DOMAIN, GROCERY_DOMAIN} from "../../../../../utils/constants";

interface StoreProduct {
  apiInProgress: boolean;
  defaultPrice: any;
  priceRange: any;
  showProductDetails: () => void;
  navigateToProductDetails: () => void;
  disabled: boolean;
  imageSource: any;
  isFBDomain: boolean;
  product: any;
  currency: any;
  addItemToWishlist: () => void;
  cartItemDetails: any;
  removeQuantityClick: () => void;
  productLoading: boolean;
  incrementProductQuantity: () => void;
  addToCart: () => void;
  deleteItemFromWishlist: () => void;
}

const Product: React.FC<StoreProduct> = ({
                                           apiInProgress,
                                           defaultPrice,
                                           priceRange,
                                           showProductDetails,
                                           disabled,
                                           imageSource,
                                           product,
                                           currency,
                                           cartItemDetails,
                                           removeQuantityClick,
                                           productLoading,
                                           incrementProductQuantity,
                                           addToCart,
                                         }) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useProductStyles();
  const {formatNumber} = useFormatNumber();

  const renderPrice = () => {
    if (defaultPrice) {
      return (
        <DefaultProductAmount
          currency={currency}
          defaultValue={defaultPrice}
          price={product?.item_details?.price}
        />
      );
    } else if (priceRange) {
      const min = formatNumber(Number(priceRange?.minPrice).toFixed(2));
      const max = formatNumber(Number(priceRange?.maxPrice).toFixed(2));
      return (
        <Typography variant="labelSmall" className={styles.productAmount}>
          {currency}
          {min} - {currency}
          {max}
        </Typography>
      );
    } else {
      return (
        <Box className={styles.amountContainer}>
          <ProductAmount price={product?.item_details?.price} currency={currency}/>
        </Box>
      );
    }
  };

  return (
    <Box className={styles.product}>
      <Button onClick={showProductDetails} sx={{p: 0}}>
        <Box sx={disabled ? {filter: "grayscale(100%)"} : {}}>
          <StoreProductImage productImageSource={imageSource}/>
        </Box>
      </Button>

      {(product?.context?.domain === GROCERY_DOMAIN ||
        product?.context.domain === FB_DOMAIN) && (
        <Box className={styles.vegNonVegContainer}>
          <VegNonVegTag tags={product?.item_details?.tags} showLabel={false}/>
        </Box>
      )}

      <Typography variant="labelSmall" component="div" className={styles.productName} noWrap>
        {product?.item_details?.descriptor?.name}
      </Typography>

      {renderPrice()}

      {cartItemDetails?.productQuantity > 0 ? (
        <Box className={`${styles.quantityView} ${disabled && styles.disabledButton}`}>
          <IconButton disabled={disabled} onClick={removeQuantityClick}>
            <Remove sx={{color: theme.palette.primary.main}}/>
          </IconButton>

          <Box>
            {productLoading ? (
              <CircularProgress size={14} sx={{color: theme.palette.primary.main}}/>
            ) : (
              <Typography variant="labelSmall" className={styles.quantity}>
                {formatNumber(cartItemDetails?.productQuantity)}
              </Typography>
            )}
          </Box>

          <IconButton disabled={disabled} onClick={incrementProductQuantity}>
            <Add sx={{color: theme.palette.primary.main}}/>
          </IconButton>
        </Box>
      ) : (
        <Button
          disabled={disabled}
          className={`${styles.quantityView} ${disabled && styles.disabledButton}`}
          onClick={addToCart}
        >
          {apiInProgress ? (
            <CircularProgress size={20} sx={{color: theme.palette.primary.main}}/>
          ) : (
            <Typography variant="labelSmall" className={`${styles.quantity} ${disabled && styles.disabledText}`}>
              {t("Cart.FBProduct.Add")}
            </Typography>
          )}
        </Button>
      )}
    </Box>
  );
};

const useProductStyles = makeStyles<any>((theme) => ({
  product: {
    marginRight: 16,
    position: 'relative',
    width: 116,
    marginBottom: 20
  },
  productName: {
    color: theme.palette.text.primary,
    overflow: "hidden",
    textOverflow: "ellipsis",
  },
  productAmount: {
    color: theme.palette.text.secondary,
  },
  amountContainer: {
    width: 116,
    overflow: "hidden",
  },
  grayscale: {
    filter: "grayscale(100%)",
  },
  vegNonVegContainer: {
    position: "absolute",
    top: 8,
    left: 8,
  },
  quantityView: {
    height: 28,
    width: "100%",
    borderRadius: 8,
    display: 'flex',
    border: `1px solid ${theme.palette.primary.main} !important`,
    alignItems: "center",
    justifyContent: "space-evenly",
    flexDirection: "row",
    marginTop: '8px !important',
  },
  disabledButton: {
    borderColor: theme.palette.grey[400],
  },
  disabledText: {
    color: theme.palette.grey[400],
  },
  quantity: {
    color: theme.palette.primary.main,
  },
}));

export default Product;
