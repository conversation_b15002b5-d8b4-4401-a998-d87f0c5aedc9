import React from 'react';
import {useTranslation} from 'react-i18next';
import {getFilterCategory} from '../../../../utils/utils';
import VegIcon from '../../../../assets/foodType/veg.svg';
import NonVegIcon from '../../../../assets/foodType/non_veg.svg';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";

interface VegNonVegTag {
  tags: any[];
  showLabel?: boolean;
  size?: string;
}

const VegNonVegTag: React.FC<VegNonVegTag> = ({
                                                tags,
                                                showLabel,
                                                size = 'regular',
                                              }) => {
  const {t} = useTranslation();
  const styles = useVegNonVegTagStyles();

  let category = getFilterCategory(tags);

  switch (category.toLowerCase()) {
    case 'veg':
      return (
        <Box className={styles.iconRow}>
          {size === 'regular' ? (
            <img src={VegIcon} alt="Veg Icon" width={18} height={18}/>
          ) : (
            <img src={VegIcon} alt="Veg Icon" width={12} height={12}/>
          )}
          {showLabel && (
            <Typography variant="labelSmall" className={styles.veg}>
              {t('Cart.Veg')}
            </Typography>
          )}
        </Box>
      );
    case 'nonveg':
      return (
        <Box className={styles.iconRow}>
          {size === 'regular' ? (
            <img src={NonVegIcon} alt="NonVeg Icon" width={18} height={18}/>
          ) : (
            <img src={NonVegIcon} alt="NonVeg Icon" width={12} height={12}/>
          )}
          {showLabel && (
            <Typography variant="labelSmall" className={styles.nonVeg}>
              {t('Cart.Non Veg')}
            </Typography>
          )}
        </Box>
      );
    case 'egg':
      return (
        <Box className={styles.iconRow}>
          {size === 'regular' ? (
            <img src={NonVegIcon} alt="NonVeg Icon" width={18} height={18}/>
          ) : (
            <img src={NonVegIcon} alt="NonVeg Icon" width={12} height={12}/>
          )}
          {showLabel && (
            <Typography variant="labelSmall" className={styles.nonVeg}>
              {t('Cart.Egg')}
            </Typography>
          )}
        </Box>
      );

    default:
      return <></>;
  }
};

const useVegNonVegTagStyles = makeStyles<any>((theme) => ({
  veg: {
    color: theme.palette.success,
  },
  nonVeg: {
    color: theme.palette.red,
  },
  iconRow: {
    display: 'flex',
    gap: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
}));

export default VegNonVegTag;
