import React from "react";
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import FavoriteIcon from "@mui/icons-material/FavoriteOutlined";
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorderOutlined";
import {makeStyles} from "@mui/styles";

interface WishlistButtonProps {
  wishlistLoader: boolean;
  addedToWishlist: boolean;
  deleteItemFromWishlist: () => void;
  addItemToWishlist: () => void;
}

const WishlistButton: React.FC<WishlistButtonProps> = ({
                                                         wishlistLoader,
                                                         addedToWishlist,
                                                         deleteItemFromWishlist,
                                                         addItemToWishlist,
                                                       }) => {
  const styles = useStyles();

  return (
    <div className={styles.wishlist}>
      {wishlistLoader ? (
        <CircularProgress size={18} color="primary"/>
      ) : (
        <IconButton
          onClick={addedToWishlist ? deleteItemFromWishlist : addItemToWishlist}
          size="small"
        >
          {addedToWishlist ? (
            <FavoriteIcon className={styles.heartIcon}/>
          ) : (
            <FavoriteBorderIcon className={styles.heartIcon}/>
          )}
        </IconButton>
      )}
    </div>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  wishlist: {
    position: "absolute",
    top: -4,
    right: 4,
    height: 28,
    width: 28,
    borderRadius: "50%",
    backgroundColor: "#fff",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1
  },
  heartIcon: {
    color: "#D32F2F",
  },
}));

export default WishlistButton;
