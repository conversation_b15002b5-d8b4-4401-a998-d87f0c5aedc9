import Box from "@mui/material/Box"
import Slider from "@mui/material/Slider"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import { forwardRef, useEffect, useImperativeHandle, useState } from "react"
import { PRICE_RANGE } from "../../../../utils/constants"
import { theme } from "../../../../utils/theme"

const PriceSlider = forwardRef(
  (
    {
      sliderValues,
      onValuesChange,
    }: {
      sliderValues: any
      onValuesChange: (values: number[]) => void
    },
    ref
  ) => {
    const styles = usePriceSliderStyles()
    const [currentValue, setCurrentValue] = useState<number[]>(PRICE_RANGE)

    const onSliderValuesChange = (_event: Event, value: number | number[]) => {
      if (Array.isArray(value)) {
        setCurrentValue(value)
        onValuesChange(value)
      }
    }

    const clearRangeSelection = () => {
      setCurrentValue(PRICE_RANGE)
    }

    useImperativeHandle(ref, () => ({
      clearRangeSelection,
    }))

    useEffect(() => {
      setCurrentValue(sliderValues || PRICE_RANGE)
    }, [sliderValues])

    return (
      <Box className={styles.sliderContainer}>
        <Slider
          value={currentValue}
          onChange={onSliderValuesChange}
          valueLabelDisplay="auto"
          min={PRICE_RANGE[0]}
          max={PRICE_RANGE[1]}
          step={50}
          sx={{
            "& .MuiSlider-thumb:after": {
              content: "''",
              background: "white",
              width: "10px",
              height: "10px",
            },
            "& .MuiSlider-rail": {
              backgroundColor: theme.palette.neutral300,
            },
            "& .MuiSlider-track": {
              backgroundColor: theme.palette.primary,
            },
          }}
        />
        <Box className={styles.sliderValues}>
          <Typography variant="labelLarge" className={styles.sliderValue}>
            ₹{currentValue[0]}
          </Typography>
          <Typography variant="labelLarge" className={styles.sliderValue}>
            ₹{currentValue[1]}
          </Typography>
        </Box>
      </Box>
    )
  }
)

const usePriceSliderStyles = makeStyles<any>((theme) => ({
  sliderContainer: {
    alignItems: "center",
    paddingTop: 16,
    paddingRight: 16,
    paddingLeft: 16,
  },
  sliderValues: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  sliderValue: {
    color: theme.palette.neutral400,
  },
  track: {
    height: 6,
    backgroundColor: theme.palette.neutral100,
    borderRadius: 2,
  },
  selectedTrack: {
    backgroundColor: theme.palette.primary,
  },
  unSelectedTrack: {
    backgroundColor: theme.palette.neutral100,
  },
}))

export default PriceSlider
