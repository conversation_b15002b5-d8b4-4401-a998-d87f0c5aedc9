import React from "react";
import Typography from '@mui/material/Typography';
import {useTheme} from "@mui/material/styles";
import {makeStyles} from "@mui/styles";
import WishlistButton from "../WishlistButton";
import GridProductQuantityControl from "./GridProductQuantityControl";
import GridProductAddProductButton from "./GridProductAddProductButton";
import FoodType from "./FoodType";
import GridProductImage from "./GridProductImage";
import GroceryProductAmount from "./GroceryProductAmount";
import Units from "./Units";
import Box from "@mui/material/Box";

interface GroceryProductUIProps {
  apiInProgress: boolean;
  navigateToProductDetails: () => void;
  disabled: boolean;
  imageSource: any;
  product: any;
  currency: any;
  cartItemDetails: any;
  removeQuantityClick: () => void;
  productLoading: boolean;
  incrementProductQuantity: () => void;
  addToCart: () => void;
  addItemToWishlist: () => void;
  deleteItemFromWishlist: () => void;
  addedToWishlist: boolean;
  wishlistLoader: boolean;
}

const GroceryProductUI: React.FC<GroceryProductUIProps> = ({
                                                             apiInProgress,
                                                             navigateToProductDetails,
                                                             disabled,
                                                             imageSource,
                                                             product,
                                                             currency,
                                                             cartItemDetails,
                                                             removeQuantityClick,
                                                             productLoading,
                                                             incrementProductQuantity,
                                                             addToCart,
                                                             addItemToWishlist,
                                                             deleteItemFromWishlist,
                                                             addedToWishlist,
                                                             wishlistLoader,
                                                           }) => {
  const theme = useTheme();
  const styles = useStyles(theme);

  const renderProductImage = () => {
    return <GridProductImage productImageSource={imageSource}/>;
  };

  return (
    <Box className={styles.container}>
      <Box onClick={navigateToProductDetails} className={styles.clickable}>
        {disabled ? <Box className={styles.grayscale}>{renderProductImage()}</Box> : renderProductImage()}
        <FoodType tags={product.item_details.tags} domain={product?.context.domain}/>
      </Box>
      <Box onClick={navigateToProductDetails} className={styles.meta} sx={{pointerEvents: disabled ? "none" : "auto"}}>
        <Units unitized={product?.item_details?.quantity?.unitized}/>
        <Typography variant="labelLarge" component="div" className={styles.name} noWrap>
          {product?.item_details?.descriptor?.name}
        </Typography>
        <Box className={styles.footerContainer}>
          <GroceryProductAmount currency={currency} price={product?.item_details?.price}/>
          {cartItemDetails?.productQuantity > 0 ? (
            <GridProductQuantityControl
              disabled={disabled}
              cartItemDetails={cartItemDetails}
              removeQuantityClick={removeQuantityClick}
              productLoading={productLoading}
              incrementProductQuantity={incrementProductQuantity}
            />
          ) : (
            <GridProductAddProductButton apiInProgress={apiInProgress} disabled={disabled} addToCart={addToCart}/>
          )}
        </Box>
      </Box>
      <WishlistButton
        wishlistLoader={wishlistLoader}
        addedToWishlist={addedToWishlist}
        deleteItemFromWishlist={deleteItemFromWishlist}
        addItemToWishlist={addItemToWishlist}
      />
    </Box>
  );
};

const useStyles = makeStyles((theme: any) => ({
  container: {
    flex: 1,
    marginBottom: 15,
    maxWidth: "100%",
    position: 'relative'
  },
  clickable: {
    cursor: "pointer",
  },
  grayscale: {
    filter: "grayscale(100%)",
  },
  meta: {
    flex: 1,
    marginTop: 12,
  },
  name: {
    color: theme.palette.neutral400,
    lineHeight: "14px",
    marginTop: '4px !important',
  },
  footerContainer: {
    display: "flex",
    flexDirection: "column",
    gap: 8,
  },
  quantity: {
    color: theme.palette.primary.main,
  },
}));

export default GroceryProductUI;
