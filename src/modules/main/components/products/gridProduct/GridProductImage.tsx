import React, {useCallback, useState} from "react";
import Box from '@mui/material/Box';
import NoImageAvailable from "../../../../../assets/noImage.png";

const GridProductImage = ({productImageSource}: { productImageSource: string }) => {
  const [error, setError] = useState(false);

  const onError = useCallback(() => {
    setError(true);
  }, []);

  return (
    <Box
      component="img"
      src={error ? NoImageAvailable : productImageSource}
      onError={onError}
      alt="Product"
      sx={{
        width: "100%",
        aspectRatio: "1 / 1",
        borderRadius: "15px",
        objectFit: "cover",
      }}
    />
  );
};

export default GridProductImage;
