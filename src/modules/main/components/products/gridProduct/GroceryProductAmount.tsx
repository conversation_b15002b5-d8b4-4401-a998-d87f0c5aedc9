import React, {useMemo} from "react";
import Typography from '@mui/material/Typography';
import {useTheme} from "@mui/material/styles";
import {makeStyles} from "@mui/styles";
import {useTranslation} from "react-i18next";
import {formatToIndianCurrency} from "../../../../../utils/utils";
import Box from "@mui/material/Box";

interface GroceryProductAmountProps {
  price: any;
  currency: any;
}

const GroceryProductAmount: React.FC<GroceryProductAmountProps> = ({price, currency}) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);

  const {discount, productPrice} = useMemo(() => {
    let percentage = 0;
    const maxValue = Number(price?.maximum_value);
    const defaultValue = Number(price?.value);
    const value =
      defaultValue !== 0
        ? defaultValue
        : price?.with_customisation_value
          ? Number(price?.with_customisation_value)
          : 0;
    if (maxValue !== 0 && maxValue !== value) {
      percentage = 100 - Math.floor((100 * price?.value) / maxValue);
    }
    return {
      discount: percentage,
      productPrice: value,
    };
  }, [price]);

  const maxValue = Number(price?.maximum_value);

  if (productPrice > 0 || maxValue > 0) {
    return (
      <Box className={styles.container}>
        {discount !== 0 ? (
          <Typography variant="caption" className={styles.discount} noWrap>
            {t("Product.discount", {discount})}
          </Typography>
        ) : (
          <Box className={styles.emptyDiscount}/>
        )}
        <Box className={styles.amountContainer}>
          <Typography variant="labelLarge" className={styles.amount}>
            {currency}
            {formatToIndianCurrency(productPrice.toFixed(0))}
          </Typography>
          {discount !== 0 && (
            <Box className={styles.amountContainer}>
              <Typography variant="caption" className={styles.mrp}>
                MRP
              </Typography>
              <Typography variant="caption" className={styles.amountStrike}>
                {currency}
                {formatToIndianCurrency(maxValue.toFixed(0))}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    );
  }

  return <Box className={styles.emptyAmountContainer}/>;
};

const useStyles = makeStyles((theme: any) => ({
  container: {
    marginTop: 4,
  },
  amountContainer: {
    display: "flex",
    flexDirection: "row",
    gap: 4,
    flex: 1,
    alignItems: 'center'
  },
  emptyAmountContainer: {
    height: 27,
  },
  amount: {
    color: "#242424",
    fontWeight: "bold",
  },
  mrp: {
    color: "#242424",
  },
  amountStrike: {
    color: "#242424",
    textDecoration: "line-through",
  },
  discount: {
    color: "#02406B",
    flexShrink: 1,
    fontWeight: "bold",
  },
  emptyDiscount: {
    height: 22,
    marginBottom: 2,
  },
}));

export default GroceryProductAmount;
