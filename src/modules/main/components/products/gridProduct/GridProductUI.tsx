import React from "react";
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import {useTheme} from "@mui/material/styles";
import {makeStyles} from "@mui/styles";
import ProductAmount from "../productAmount/ProductAmount";
import WishlistButton from "../WishlistButton";
import GridProductQuantityControl from "./GridProductQuantityControl";
import GridProductAddProductButton from "./GridProductAddProductButton";
import FoodType from "./FoodType";
import GridProductImage from "./GridProductImage";

interface GridProductUIProps {
  apiInProgress: boolean;
  navigateToProductDetails: () => void;
  disabled: boolean;
  imageSource: any;
  isFBDomain: boolean;
  product: any;
  currency: any;
  cartItemDetails: any;
  removeQuantityClick: () => void;
  productLoading: boolean;
  incrementProductQuantity: () => void;
  addToCart: () => void;
  addItemToWishlist: () => void;
  deleteItemFromWishlist: () => void;
  addedToWishlist: boolean;
  wishlistLoader: boolean;
}

const GridProductUI: React.FC<GridProductUIProps> = ({
                                                       apiInProgress,
                                                       navigateToProductDetails,
                                                       disabled,
                                                       imageSource,
                                                       isFBDomain,
                                                       product,
                                                       currency,
                                                       cartItemDetails,
                                                       removeQuantityClick,
                                                       productLoading,
                                                       incrementProductQuantity,
                                                       addToCart,
                                                       addItemToWishlist,
                                                       deleteItemFromWishlist,
                                                       addedToWishlist,
                                                       wishlistLoader,
                                                     }) => {
  const theme = useTheme();
  const styles = useStyles(theme);

  const renderProductImage = () => <GridProductImage productImageSource={imageSource}/>;

  return (
    <Box className={styles.container}>
      <WishlistButton
        wishlistLoader={wishlistLoader}
        addedToWishlist={addedToWishlist}
        deleteItemFromWishlist={deleteItemFromWishlist}
        addItemToWishlist={addItemToWishlist}
      />
      <IconButton onClick={navigateToProductDetails} className={styles.imageWrapper}>
        {renderProductImage()}
        <FoodType tags={product.item_details.tags} domain={product?.context.domain}/>
      </IconButton>

      <Box onClick={navigateToProductDetails} className={styles.meta}
           style={{cursor: disabled ? "default" : "pointer"}}>
        <Typography variant="labelLarge" component="div" className={styles.name} noWrap>
          {product?.item_details?.descriptor?.name}
        </Typography>
        <Typography variant="caption" component="div" className={styles.provider} noWrap>
          {product?.provider_details?.descriptor?.name}
        </Typography>

        <Box className={styles.footerContainer}>
          <ProductAmount currency={currency} price={product?.item_details?.price}/>

          {cartItemDetails?.productQuantity > 0 ? (
            <GridProductQuantityControl
              disabled={disabled}
              cartItemDetails={cartItemDetails}
              removeQuantityClick={removeQuantityClick}
              productLoading={productLoading}
              incrementProductQuantity={incrementProductQuantity}
            />
          ) : (
            <GridProductAddProductButton apiInProgress={apiInProgress} disabled={disabled} addToCart={addToCart}/>
          )}
        </Box>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    marginBottom: 15,
    maxWidth: "100%",
    position: 'relative'
  },
  imageWrapper: {
    width: "100%",
    display: "flex",
    justifyContent: "center",
    position: 'relative'
  },
  meta: {
    flex: 1,
    marginTop: 12,
  },
  name: {
    color: theme.palette.neutral400,
  },
  provider: {
    color: theme.palette.neutral300,
    maxWidth: '100%',
    overflow: "hidden !important",
    textOverflow: "ellipsis !important",
    whiteSpace: "nowrap !important",
    display: "block"
  },
  footerContainer: {
    display: "flex",
    gap: 8,
    flexDirection: 'column'
  },
}));

export default GridProductUI;
