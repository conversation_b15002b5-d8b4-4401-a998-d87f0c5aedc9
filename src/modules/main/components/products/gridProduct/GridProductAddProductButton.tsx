import React from "react";
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import {useTranslation} from "react-i18next";
import {useTheme} from "@mui/material/styles";
import {makeStyles} from "@mui/styles";

interface GridProductAddProductButtonProps {
  apiInProgress: boolean;
  disabled: boolean;
  addToCart: () => void;
}

const GridProductAddProductButton: React.FC<GridProductAddProductButtonProps> = ({
                                                                                   apiInProgress,
                                                                                   disabled,
                                                                                   addToCart,
                                                                                 }) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);

  return (
    <Button
      variant="outlined"
      className={disabled ? styles.disabledButton : styles.quantityView}
      disabled={disabled}
      onClick={(e) => {
        e.stopPropagation();
        addToCart();
      }}
    >
      {apiInProgress ? (
        <CircularProgress size={20} color="primary"/>
      ) : (
        t("Cart.FBProduct.Add")
      )}
    </Button>
  );
};

const useStyles = makeStyles((theme: any) => ({
  quantityView: {
    height: 28,
    width: "100%",
    borderRadius: 8,
    border: `1px solid ${theme.palette.primary.main} !important`,
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
  },
  disabledButton: {
    height: 28,
    borderColor: theme.palette.neutral200,
    color: theme.palette.neutral200,
  },
}));

export default GridProductAddProductButton;
