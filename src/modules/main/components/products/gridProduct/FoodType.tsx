import React from 'react';
import VegNonVegTag from '../VegNonVegTag';
import {FB_DOMAIN, GROCERY_DOMAIN} from '../../../../../utils/constants';
import Box from '@mui/material/Box';

const FoodType = ({domain, tags}: { tags: any; domain: string | undefined }) => {
  switch (domain) {
    case FB_DOMAIN:
    case GROCERY_DOMAIN:
      return (
        <Box sx={{position: 'absolute', paddingTop: 1, paddingLeft: 1, top: 8, left: 8}}>
          <VegNonVegTag tags={tags}/>
        </Box>
      );

    default:
      return null;
  }
};

export default FoodType;
