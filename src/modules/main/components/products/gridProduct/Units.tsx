import React from "react";
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";
import Box from "@mui/material/Box";

interface UnitsProps {
  unitized: Record<string, { value: number; unit: string }> | null;
}

const Units: React.FC<UnitsProps> = ({unitized}) => {
  const styles = useStyles();

  if (!unitized) {
    return <Box className={styles.empty}/>;
  }

  return (
    <Box className={styles.container}>
      {Object.keys(unitized).map((key) => (
        <Box key={unitized[key].value} className={styles.tag}>
          <Typography className={styles.tagText}>
            {unitized[key].value} {unitized[key].unit}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

const useStyles = makeStyles({
  tag: {
    backgroundColor: "#E2F6FF",
    borderRadius: 3,
    padding: 3,
  },
  tagText: {
    color: "#02406B",
    fontSize: 10,
    fontWeight: 600,
  },
  container: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  empty: {
    height: 12,
  },
});

export default Units;
