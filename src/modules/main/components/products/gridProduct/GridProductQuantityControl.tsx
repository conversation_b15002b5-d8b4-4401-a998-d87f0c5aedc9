import React from "react";
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import RemoveIcon from "@mui/icons-material/Remove";
import AddIcon from "@mui/icons-material/Add";
import {useTheme} from "@mui/material/styles";
import {makeStyles} from '@mui/styles';
import useFormatNumber from "../../../hooks/useFormatNumber";

interface GridProductQuantityControlProps {
  disabled: boolean;
  cartItemDetails: any;
  removeQuantityClick: () => void;
  productLoading: boolean;
  incrementProductQuantity: () => void;
}

const GridProductQuantityControl: React.FC<GridProductQuantityControlProps> = ({
                                                                                 disabled,
                                                                                 cartItemDetails,
                                                                                 removeQuantityClick,
                                                                                 productLoading,
                                                                                 incrementProductQuantity,
                                                                               }) => {
  const theme = useTheme();
  const styles = useStyles(theme);
  const {formatNumber} = useFormatNumber();

  return (
    <Box className={`${styles.quantityView} ${disabled ? styles.disabledButton : ""}`}>
      <IconButton
        disabled={disabled}
        onClick={(e) => {
          e.stopPropagation();
          removeQuantityClick();
        }}
        className={styles.iconButton}
      >
        <RemoveIcon color="primary"/>
      </IconButton>

      <Box>
        {productLoading ? (
          <CircularProgress size={20} color="primary"/>
        ) : (
          <Typography variant="bodyLarge" className={styles.quantity}>
            {formatNumber(cartItemDetails?.productQuantity)}
          </Typography>
        )}
      </Box>

      <IconButton
        disabled={disabled}
        onClick={(e) => {
          e.stopPropagation();
          incrementProductQuantity();
        }}
        className={styles.iconButton}
      >
        <AddIcon color="primary"/>
      </IconButton>
    </Box>
  );
};

const useStyles = makeStyles((theme: any) => ({
  quantityView: {
    height: 28,
    width: "100%",
    borderRadius: 8,
    border: `1px solid ${theme.palette.primary.main} !important`,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-evenly",
    flexDirection: "row",
  },
  disabledButton: {
    borderColor: theme.palette.neutral200,
  },
  iconButton: {
    width: 24,
    height: 24,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  quantity: {
    color: theme.palette.primary.main,
  },
}));

export default GridProductQuantityControl;
