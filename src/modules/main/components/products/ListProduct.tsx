import ClearIcon from "@mui/icons-material/ClearRounded"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Dialog from "@mui/material/Dialog"
import IconButton from "@mui/material/IconButton"
import Slide from "@mui/material/Slide"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import axios from "axios"
import React, { useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import NoImageAvailable from "../../../../assets/noImage.png"
import { useToast } from "../../../../hooks/toastProvider"
import useNetworkErrorHandling from "../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../hooks/useNetworkHandling"
import { updateCartItems } from "../../../../toolkit/reducer/cart"
import { setProduct } from "../../../../toolkit/reducer/product"
import { removeProductFromWishlist } from "../../../../toolkit/reducer/wishlist"
import {
  API_BASE_URL,
  CART,
  ITEM_DETAILS,
  WISHLIST,
} from "../../../../utils/apiActions"
import { CURRENCY_SYMBOLS, FB_DOMAIN } from "../../../../utils/constants"
import {
  emptyAlertCallback,
  getPriceWithCustomisations,
} from "../../../../utils/utils"
import useAddToCart from "../../hooks/useAddToCart"
import useCartItems from "../../hooks/useCartItems"
import useCustomizationStateHelper from "../../hooks/useCustomizationStateHelper"
import useFormatNumber from "../../hooks/useFormatNumber"
import useNavigateToProduct from "../../hooks/useNavigateToProduct"
import useUpdateSpecificItemCount from "../../hooks/useUpdateSpecificItemCount"
import useWishlistItems from "../../hooks/useWishlistItems"
import ModalProductDetails from "../../modules/product/details/ModalProductDetails"
import Customizations from "../customization/Customizations"
import ManageQuantity from "../customization/ManageQuantity"
import ListProductUI from "../listProduct/ListProductUI"
import CustomizationFooterButtons from "../provider/components/CustomizationFooterButtons"
import FBProductCustomization from "../provider/components/FBProductCustomization"
import GridProductUI from "./gridProduct/GridProductUI"
import GroceryProductUI from "./gridProduct/GroceryProductUI"
import StoreProduct from "./storeProduct/StoreProduct"
import VegNonVegTag from "./VegNonVegTag"

interface ListProductProps {
  product: any
  provider: any
  isOpen: boolean
  listView?: boolean
  smallView?: boolean
  grocery?: boolean
  openDetailsPage: boolean
  isShowProductDetails?: boolean
}

interface RootState {
  auth: {
    uid: string
  }
}

const CancelToken = axios.CancelToken
const ListProduct: React.FC<ListProductProps> = ({
  product,
  isOpen,
  listView = false,
  smallView = false,
  grocery = false,
  openDetailsPage,
  isShowProductDetails,
}) => {
  const { formatNumber } = useFormatNumber()
  const { t } = useTranslation()
  const styles = useProductStyles()
  const theme = useTheme()
  const { deleteDataWithAuth, getDataWithAuth, postDataWithAuth } =
    useNetworkHandling()
  const { getWishlistItems } = useWishlistItems()
  const { handleApiError } = useNetworkErrorHandling()
  const { uid } = useSelector((state: RootState) => state.auth)
  const { cartItems } = useSelector((state: any) => state.cart)
  const { wishlistItems } = useSelector((state: any) => state.cart)
  const [wishlistLoader, setWishlistLoader] = useState(false)
  const { getCartItems } = useCartItems()
  const { updatingCartItem, updateSpecificCartItem } =
    useUpdateSpecificItemCount()
  const { customizationState, setCustomizationState, customizationPrices } =
    useCustomizationStateHelper()
  const { addDetailsToCart, addRequested } = useAddToCart()
  const dispatch = useDispatch()
  const source = useRef<any>(null)
  const navigateToProduct = useNavigateToProduct()
  const { showToast } = useToast()
  // const customizationSheet = useRef<any>(null);
  // const quantitySheet = useRef<any>(null);
  const productSource = useRef<any>(null)
  const [itemToDelete, setItemToDelete] = useState<any>(null)
  const [apiInProgress, setApiInProgress] = useState<boolean>(false)
  const [itemOutOfStock, setItemOutOfStock] = useState<boolean>(false)
  const [productLoading, setProductLoading] = useState<boolean>(false)
  const [addedToWishlist, setAddedToWishlist] = useState<boolean>(false)
  const [cartItemDetails, setCartItemDetails] = useState<any>({
    items: [],
    productQuantity: 0,
  })
  const [productDetails, setProductDetails] = useState<any>(null)
  const [productModalDetails, setProductModalDetails] = useState<any>(null)
  const [itemQty, setItemQty] = useState<number>(1)
  const [priceRange, setPriceRange] = useState<any>(null)
  const [defaultPrice, setDefaultPrice] = useState<any>(0)
  const wishlistProductId = useRef<any>(null)
  const wishlistId = useRef<any>(null)
  const [quantitySheet, setQuantitySheet] = React.useState(false)
  const [customizationSheet, setCustomizationSheet] = React.useState(false)

  const customizable = useMemo(() => {
    return !!product?.item_details?.tags?.find(
      (item: any) => item.code === "custom_group"
    )
  }, [product])

  const variationsAvailable = useMemo(() => {
    return !!product.item_details.parent_item_id
  }, [product])

  useEffect(() => {
    if (isShowProductDetails) {
      showProductDetails()
    }
  }, [])
  const showCustomization = () => {
    setTimeout(() => {
      setCustomizationSheet(true)
    }, 200)
  }

  const hideCustomization = () => setCustomizationSheet(false)
  const showQuantitySheet = () => setQuantitySheet(true)
  const hideQuantitySheet = () => setQuantitySheet(false)

  const addNewCustomization = () => {
    setItemQty(1)
    hideQuantitySheet()
    addToCart().then(emptyAlertCallback).catch(emptyAlertCallback)
  }

  const showProductDetails = async () => {
    let qty = 1

    if (!customizable) {
      qty =
        cartItemDetails?.productQuantity !== 0
          ? cartItemDetails?.productQuantity
          : 1
    }
    hideQuantitySheet()

    if (!productDetails) {
      await getProductDetails(true)
    } else {
      dispatch(setProduct(productDetails))
    }
    setProductModalDetails({ inStock, qty, isOpen })
  }

  const removeQuantityClick = () => {
    if (cartItemDetails?.items.length === 1) {
      const cartItem = cartItemDetails?.items[0]
      if (cartItem?.item?.quantity?.count === 1) {
        setProductLoading(true)
        deleteCartItem(cartItem?._id)
          .then(() => {
            setProductLoading(false)
          })
          .catch(() => {
            setProductLoading(false)
          })
      } else {
        setProductLoading(true)
        updateSpecificItem(
          cartItem?.item?.location_details.id,
          cartItem?.item?.id,
          false,
          cartItem?._id
        )
          .then(() => {
            setProductLoading(false)
          })
          .catch(() => {
            setProductLoading(false)
          })
      }
    } else {
      showQuantitySheet()
    }
  }

  const incrementProductQuantity = async () => {
    if (customizable) {
      showQuantitySheet()
    } else {
      let items: any[] = cartItemDetails.items.filter(
        (ci: any) => ci.item.id === product.id
      )
      if (items.length > 0) {
        try {
          setProductLoading(true)
          await updateSpecificItem(
            items[0]?.item?.location_details.id,
            items[0]?.item?.id,
            true,
            items[0]?._id
          )
        } catch (error: any) {
        } finally {
          setProductLoading(false)
        }
      }
    }
  }

  const navigateToProductDetails = async () => {
    if (isFBDomain || !openDetailsPage) {
      await showProductDetails()
    } else {
      navigateToProduct(product.id ?? "")
    }
  }

  const deleteItemFromWishlist = async () => {
    setWishlistLoader(true)
    try {
      source.current = CancelToken.source()
      await deleteDataWithAuth(
        `${API_BASE_URL}${WISHLIST}/${uid}/${wishlistProductId.current}`,
        source.current.token
      )
      dispatch(
        removeProductFromWishlist({
          wishlistId: wishlistId.current,
          productId: wishlistProductId.current,
        })
      )
      setAddedToWishlist(false)
    } catch (error) {
    } finally {
      setWishlistLoader(false)
    }
  }

  const addItemToWishlist = async () => {
    setWishlistLoader(true)
    try {
      source.current = CancelToken.source()
      await postDataWithAuth(
        `${API_BASE_URL}${WISHLIST}/${uid}`,
        {
          id: product.id,
          locationId: product.location_details.id,
        },
        source.current.token
      )
      setAddedToWishlist(true)
      await getWishlistItems()
    } catch (error: any) {
    } finally {
      setWishlistLoader(false)
    }
  }

  const addToCart = async () => {
    if (customizable) {
      if (productDetails) {
        showCustomization()
      } else {
        await getProductDetails()
      }
    } else if (variationsAvailable) {
      await showProductDetails()
    } else {
      await addNonCustomizableProduct()
    }
  }

  const addItemDetailsToCart = async () => {
    await addDetailsToCart(
      productDetails,
      customizationState,
      customizationPrices,
      itemQty,
      setCustomizationState,
      hideCustomization,
      emptyAlertCallback
    )
  }

  const addNonCustomizableProduct = async () => {
    try {
      setApiInProgress(true)
      productSource.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${ITEM_DETAILS}?id=${product.id}`,
        productSource.current.token
      )
      setProductDetails(data)
      const subtotal = data?.item_details?.price?.value
      const payload: any = {
        id: data.id,
        local_id: data.local_id,
        bpp_id: data.bpp_details.bpp_id,
        bpp_uri: data.context.bpp_uri,
        contextCity: data.context.city,
        domain: data.context.domain,
        tags: data.item_details.tags,
        customisationState: customizationState,
        quantity: {
          count: itemQty,
        },
        provider: {
          id: data.bpp_details.bpp_id,
          locations: data.locations,
          ...data.provider_details,
        },
        location_details: data.location_details,
        product: {
          id: data.id,
          subtotal,
          ...data.item_details,
        },
        customisations: null,
        hasCustomisations: false,
      }

      const url = `${API_BASE_URL}${CART}/${uid}`
      source.current = CancelToken.source()
      await postDataWithAuth(url, payload, source.current.token)
      setCustomizationState({})
      showToast(t("Product Summary.Item added to cart successfully"), "info")
      await getCartItems()
      setProductLoading(false)
    } catch (error) {
      handleApiError(error)
    } finally {
      setApiInProgress(false)
    }
  }

  const getProductDetails = async (preventCustomizeOpening = false) => {
    try {
      setApiInProgress(true)
      productSource.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${ITEM_DETAILS}?id=${product.id}`,
        productSource.current.token
      )
      setProductDetails(data)
      dispatch(setProduct(data))
      if (!preventCustomizeOpening) {
        showCustomization()
      }
    } catch (error) {
      handleApiError(error)
    } finally {
      setApiInProgress(false)
    }
  }

  const setCartItems = (items: any[]) => {
    dispatch(updateCartItems(items))
  }

  const updateSpecificItem = async (
    locationId: any,
    itemId: any,
    increment: boolean,
    uniqueId: any
  ) => {
    await updateSpecificCartItem(
      locationId,
      itemId,
      increment,
      uniqueId,
      cartItems,
      setCartItems
    )
  }

  const deleteCartItem = async (itemId: any) => {
    try {
      setItemToDelete(itemId)
      source.current = CancelToken.source()
      await deleteDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/${itemId}`,
        source.current.token
      )
      const list = cartItems.filter((item: any) => item._id !== itemId)
      dispatch(updateCartItems(list))
      await getCartItems()
    } catch (error: any) {
    } finally {
      setItemToDelete(null)
    }
  }

  const getWishlistStatus = () => {
    let findWishlistStatus = false
    wishlistItems?.forEach((element: any) => {
      element?.items?.forEach((item: any) => {
        if (item.id === product.id) {
          wishlistId.current = element._id
          wishlistProductId.current = item._id
          findWishlistStatus = true
        }
      })
    })
    setAddedToWishlist(findWishlistStatus)
  }

  useEffect(() => {
    if (product && wishlistItems) {
      getWishlistStatus()
      setWishlistLoader(false)
    }
  }, [product, wishlistItems])

  useEffect(() => {
    if (product && cartItems.length > 0) {
      let providerCart: any = cartItems?.find(
        (cart: any) => cart.location_id === product.location_details.id
      )

      if (providerCart) {
        let items: any[] = providerCart?.items?.filter(
          (one: any) => one.item.id === product.id
        )
        let quantity = 0
        const productQuantity = items?.reduce(
          (accumulator, item) => accumulator + item.item.quantity.count,
          quantity
        )
        setCartItemDetails({ items, productQuantity })
      } else {
        setCartItemDetails({ items: [], productQuantity: 0 })
      }
    } else {
      setCartItemDetails({ items: [], productQuantity: 0 })
      // quantitySheet.current.close();
      setQuantitySheet(false)
    }
  }, [product, cartItems])

  useEffect(() => {
    let rangePriceTag = null
    let price = 0
    if (
      product?.item_details?.price?.tags &&
      Array.isArray(product?.item_details?.price?.tags)
    ) {
      const defaultSelectionTag = product?.item_details?.price?.tags?.find(
        (item: any) => item?.code === "default_selection"
      )
      if (defaultSelectionTag) {
        const findDefaultTag = defaultSelectionTag?.list?.find(
          (item: any) => item?.code === "value"
        )
        if (findDefaultTag) {
          price = findDefaultTag.value
        }
      } else {
        const findRangePriceTag = product?.item_details?.price?.tags?.find(
          (item: any) => item.code === "range"
        )
        if (findRangePriceTag) {
          const findLowerPriceObj = findRangePriceTag.list.find(
            (item: any) => item.code === "lower"
          )
          const findUpperPriceObj = findRangePriceTag.list.find(
            (item: any) => item.code === "upper"
          )
          rangePriceTag = {
            maxPrice: findUpperPriceObj.value,
            minPrice: findLowerPriceObj.value,
          }
        }
      }
    }
    setPriceRange(rangePriceTag)
    setDefaultPrice(price)
  }, [product])

  const inStock = Number(product?.item_details?.quantity?.available?.count) >= 1
  const disabled = apiInProgress || !inStock || !isOpen

  const currency = useMemo(
    () => CURRENCY_SYMBOLS[product?.item_details?.price?.currency],
    [product, CURRENCY_SYMBOLS]
  )

  const isFBDomain = useMemo(() => {
    return product.context.domain === FB_DOMAIN
  }, [product])

  const productImageSource = useMemo(() => {
    if (product?.item_details?.descriptor?.symbol) {
      return product.item_details.descriptor.symbol
    } else if (product?.item_details?.descriptor?.images?.length > 0) {
      return product.item_details.descriptor.images[0]
    } else {
      return NoImageAvailable
    }
  }, [product?.item_details?.descriptor])

  const renderProductUI = () => {
    if (smallView) {
      return (
        <StoreProduct
          apiInProgress={apiInProgress}
          product={product}
          incrementProductQuantity={incrementProductQuantity}
          removeQuantityClick={removeQuantityClick}
          productLoading={productLoading || addRequested}
          showProductDetails={showProductDetails}
          cartItemDetails={cartItemDetails}
          addToCart={addToCart}
          disabled={disabled}
          currency={currency}
          defaultPrice={defaultPrice}
          priceRange={priceRange}
          imageSource={productImageSource}
          addItemToWishlist={addItemToWishlist}
          deleteItemFromWishlist={deleteItemFromWishlist}
          navigateToProductDetails={navigateToProductDetails}
          isFBDomain={isFBDomain}
        />
      )
    }

    if (listView) {
      return (
        <ListProductUI
          apiInProgress={apiInProgress}
          product={product}
          currency={currency}
          disabled={disabled}
          productLoading={productLoading || addRequested}
          cartItemDetails={cartItemDetails}
          addToCart={addToCart}
          customizable={customizable}
          defaultPrice={defaultPrice}
          incrementProductQuantity={incrementProductQuantity}
          inStock={inStock}
          priceRange={priceRange}
          productImageSource={productImageSource}
          removeQuantityClick={removeQuantityClick}
          showProductDetails={showProductDetails}
          addItemToWishlist={addItemToWishlist}
          deleteItemFromWishlist={deleteItemFromWishlist}
          addedToWishlist={addedToWishlist}
          wishlistLoader={wishlistLoader}
        />
      )
    }

    if (grocery) {
      return (
        <GroceryProductUI
          apiInProgress={apiInProgress}
          product={product}
          incrementProductQuantity={incrementProductQuantity}
          removeQuantityClick={removeQuantityClick}
          productLoading={productLoading || addRequested}
          cartItemDetails={cartItemDetails}
          addToCart={addToCart}
          disabled={disabled}
          currency={currency}
          imageSource={productImageSource}
          navigateToProductDetails={navigateToProductDetails}
          addItemToWishlist={addItemToWishlist}
          deleteItemFromWishlist={deleteItemFromWishlist}
          addedToWishlist={addedToWishlist}
          wishlistLoader={wishlistLoader}
        />
      )
    }

    return (
      <GridProductUI
        apiInProgress={apiInProgress}
        product={product}
        incrementProductQuantity={incrementProductQuantity}
        removeQuantityClick={removeQuantityClick}
        productLoading={productLoading || addRequested}
        cartItemDetails={cartItemDetails}
        addToCart={addToCart}
        disabled={disabled}
        currency={currency}
        imageSource={productImageSource}
        navigateToProductDetails={navigateToProductDetails}
        isFBDomain={isFBDomain}
        addItemToWishlist={addItemToWishlist}
        deleteItemFromWishlist={deleteItemFromWishlist}
        addedToWishlist={addedToWishlist}
        wishlistLoader={wishlistLoader}
      />
    )
  }

  return (
    <>
      {renderProductUI()}
      <Dialog
        open={customizationSheet}
        onClose={() => setCustomizationSheet(false)}
        fullWidth
        maxWidth="sm"
      >
        <Slide
          direction="up"
          in={customizationSheet}
          mountOnEnter
          unmountOnExit
        >
          <Box
            sx={{
              position: "fixed",
              bottom: 0,
              left: 0,
              right: 0,
              boxShadow: 24,
              maxHeight: "85vh",
              // overflow: 'auto',
            }}
          >
            <Box display="flex" justifyContent="center" mb={2}>
              <IconButton
                onClick={() => setCustomizationSheet(false)}
                sx={{
                  width: 36,
                  height: 36,
                  bgcolor: theme.palette.grey[900],
                  color: theme.palette.common.white,
                  "&:hover": {
                    bgcolor: theme.palette.grey[500],
                  },
                }}
              >
                <ClearIcon />
              </IconButton>
            </Box>
            <Box display="flex" justifyContent="center">
              <Box
                sx={{
                  bgcolor: "#fff",
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                  width: "100%",
                  overflow: "hidden",
                }}
                // display="flex" justifyContent="center"
              >
                <Box className={styles.header}>
                  <img
                    src={productImageSource}
                    alt="Product"
                    className={styles.sheetProductSymbol}
                  />
                  <Box className={styles.titleContainer}>
                    <Typography
                      component="div"
                      variant="titleMedium"
                      className={styles.title}
                      noWrap
                    >
                      {product?.item_details?.descriptor?.name}
                    </Typography>
                    <Typography
                      component="div"
                      variant="bodyLarge"
                      className={styles.prize}
                    >
                      {currency}
                      {formatNumber(
                        Number(product?.item_details?.price?.value).toFixed(2)
                      )}
                    </Typography>
                  </Box>
                </Box>

                <Box
                  className={styles.customizationContainer}
                  style={{
                    overflowY: "auto",
                    marginBottom: "60px",
                    height: "60vh",
                  }}
                >
                  <FBProductCustomization
                    product={productDetails}
                    customizationState={customizationState}
                    setCustomizationState={setCustomizationState}
                    setItemOutOfStock={setItemOutOfStock}
                    disabled={!isOpen || !inStock}
                  />
                </Box>
                <Box className={styles.customizationFooterButtons}>
                  <CustomizationFooterButtons
                    productLoading={productLoading || addRequested}
                    itemQty={itemQty}
                    setItemQty={setItemQty}
                    itemOutOfStock={itemOutOfStock}
                    addDetailsToCart={addItemDetailsToCart}
                    product={product}
                    customizationPrices={customizationPrices}
                    isOpen={isOpen}
                  />
                </Box>
              </Box>
            </Box>
          </Box>
        </Slide>
      </Dialog>

      <Dialog open={quantitySheet} fullWidth maxWidth="sm">
        <Slide direction="up" in={quantitySheet} mountOnEnter unmountOnExit>
          <Box
            sx={{
              position: "fixed",
              bottom: 0,
              left: 0,
              right: 0,
              boxShadow: 24,
              maxHeight: "85vh",
              // overflow: 'auto',
            }}
          >
            <Box display="flex" justifyContent="center" mb={2}>
              <IconButton
                onClick={() => hideQuantitySheet()}
                sx={{
                  width: 36,
                  height: 36,
                  bgcolor: theme.palette.grey[900],
                  color: theme.palette.common.white,
                  "&:hover": {
                    bgcolor: theme.palette.grey[500],
                  },
                }}
              >
                <ClearIcon />
              </IconButton>
            </Box>
            <Box>
              <Box className={styles.header}>
                <Typography variant="headlineSmall">
                  Customization for
                </Typography>
              </Box>
              <Box
                className={styles.customizationForMainContainer}
                style={{
                  overflowY: "auto",
                  marginBottom: "60px",
                  height: "60vh",
                }}
              >
                <Box
                  className={styles.customizationForContainer}
                  sx={{ maxHeight: "50dvh", overflowY: "auto" }}
                >
                  {cartItemDetails?.items?.map((item: any) => (
                    <Box
                      key={item?._id}
                      display="flex"
                      mb={2}
                      p={1}
                      borderBottom={1}
                      borderColor="divider"
                    >
                      <Box className={styles.productMeta}>
                        <VegNonVegTag tags={item.item?.tags} />

                        <Typography component="div" variant="bodyLarge">
                          {item?.item?.product?.descriptor?.name}
                        </Typography>

                        <Customizations cartItem={item} />

                        <Typography
                          component="div"
                          variant="labelSmall"
                          fontWeight="bold"
                        >
                          ₹
                          {item.item.hasCustomisations
                            ? (
                                getPriceWithCustomisations(item) *
                                Number(item?.item?.quantity?.count)
                              ).toFixed(2)
                            : (
                                item?.item?.product?.subtotal *
                                Number(item?.item?.quantity?.count)
                              ).toFixed(2)}
                        </Typography>
                      </Box>
                      <Box>
                        <ManageQuantity
                          allowDelete
                          cartItem={item}
                          updatingCartItem={updatingCartItem ?? itemToDelete}
                          deleteCartItem={deleteCartItem}
                          updateCartItem={updateSpecificItem}
                        />
                      </Box>
                    </Box>
                  ))}
                </Box>
              </Box>
              <Box className={styles.customizationFooterButtons}>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={addNewCustomization}
                  sx={{ mt: 2 }}
                >
                  Add New Customization
                </Button>
              </Box>
            </Box>
          </Box>
        </Slide>
      </Dialog>
      {/* <Box
        sx={{
          position: "fixed",
          bottom: 0,
          width: "100%",
          zIndex: 1301,
        }}
      >
        {quantitySheet && (
          <Box
            sx={{
              position: "fixed",
              top: "28%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              backgroundColor: "#000",
              color: "#fff",
              borderRadius: "50%",
              width: 40,
              height: 40,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1302, // Ensure it's above the dialog
              boxShadow: "0px 4px 10px rgba(0,0,0,0.2)",
            }}
            onClick={() => hideQuantitySheet()}
          >
            <CloseIcon sx={{fontSize: 26, color: '#fff'}}/>
          </Box>
        )}
        <Dialog
          open={quantitySheet} fullWidth maxWidth="sm"
          sx={{
            "& .MuiPaper-root": {
              borderRadius: "20px 20px 0 0",
              position: "fixed",
              bottom: 0,
              width: "100%",
              margin: 0,
              paddingBottom: "15px",
              boxShadow: "0px -5px 20px rgba(0, 0, 0, 0.2)",
            },
          }}
        >
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <DialogTitle>
              <Typography variant="headlineSmall">Customization for</Typography>
            </DialogTitle>
          </Box>

          <DialogContent>
            <Box sx={{maxHeight: "50dvh", overflowY: "auto"}}>
              {cartItemDetails?.items?.map((item: any) => (
                <Box key={item?._id} display="flex" flexDirection="column" mb={2} p={1} borderBottom={1}
                     borderColor="divider">
                  <Typography variant="bodyLarge">{item?.item?.product?.descriptor?.name}</Typography>

                  <Customizations cartItem={item}/>

                  <Typography variant="labelSmall" fontWeight="bold">
                    ₹
                    {item.item.hasCustomisations
                      ? (
                        getPriceWithCustomisations(item) * Number(item?.item?.quantity?.count)
                      ).toFixed(2)
                      : (item?.item?.product?.subtotal * Number(item?.item?.quantity?.count)).toFixed(2)}
                  </Typography>

                  <ManageQuantity
                    allowDelete
                    cartItem={item}
                    updatingCartItem={updatingCartItem ?? itemToDelete}
                    deleteCartItem={deleteCartItem}
                    updateCartItem={updateSpecificItem}
                  />
                </Box>
              ))}
            </Box>

            <Button variant="contained" color="primary" fullWidth onClick={addNewCustomization} sx={{mt: 2}}>
              Add New Customization
            </Button>
          </DialogContent>
        </Dialog>
      </Box> */}
      {productModalDetails && (
        <ModalProductDetails
          closeModal={() => setProductModalDetails(null)}
          wishlistProductId={""}
          wishlistId={""}
          qty={productModalDetails.qty}
          isOpen={productModalDetails.isOpen}
          inStock={productModalDetails.inStock}
        />
      )}
    </>
  )
}

const useProductStyles = makeStyles<any>((theme) => ({
  product: {
    paddingRight: 16,
    paddingLeft: 16,
    flexDirection: "row",
  },
  customizationName: {
    color: theme.palette.neutral400,
    marginTop: 8,
    marginBottom: 4,
  },
  rbSheet: {
    backgroundColor: "rgba(47, 47, 47, 0.75)",
  },
  sheetContainer: {
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: theme.palette.neutral50,
    flex: 1,
  },
  header: {
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    backgroundColor: theme.palette.white,
    padding: "16px 16px",
    flexDirection: "row",
    alignItems: "center",
    display: "flex",
  },
  titleContainer: {
    marginLeft: 8,
    flex: 1,
  },
  title: {
    color: theme.palette.neutral400,
    flexShrink: 1,
  },
  prize: {
    color: theme.palette.primary.main,
  },
  customizationContainer: {
    padding: 16,
    backgroundColor: theme.palette.neutral50,
  },
  customizationForContainer: {
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 12,
    border: `1px solid ${theme.palette.neutral100}`,
  },
  customizationForMainContainer: {
    padding: 16,
    backgroundColor: theme.palette.neutral50,
  },
  quantitySheetContainer: {
    padding: 16,
    flex: 1,
    backgroundColor: theme.palette.neutral50,
  },
  customizationListContainer: {
    backgroundColor: theme.palette.white,
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: theme.palette.neutral100,
    flex: 1,
  },
  customizationList: {
    flex: 1,
  },
  quantity: {
    display: "flex",
    alignItems: "center",
    textAlign: "center",
    minWidth: 50,
  },
  cartItem: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    borderBottomWidth: 1,
    borderBottomColor: theme.palette.neutral100,
    marginBottom: 8,
    paddingBottom: 8,
  },
  productMeta: {
    display: "flex",
    flexDirection: "column",
    gap: 6,
    flex: 1,
    paddingRight: 8,
  },
  addNewCustomizationButton: {
    paddingTop: 8,
    paddingBottom: 8,
  },
  addNewCustomizationLabel: {
    display: "flex",
    color: theme.palette.primary,
    textAlign: "center",
  },
  cartQuantity: {
    marginTop: 4,
    color: theme.palette.neutral400,
  },
  sheetProductSymbol: {
    width: "36px",
    height: "36px",
    objectFit: "cover",
  },
  customizationFooterButtons: {
    position: "fixed",
    bottom: 0,
    width: "100%",
  },
}))

export default ListProduct
