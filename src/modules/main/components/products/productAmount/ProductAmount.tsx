import React, {useMemo} from 'react';
import ProductAmountUI from './ProductAmountUI';

const ProductAmount = ({price, currency}: { price: any; currency: any }) => {
  const {discount, productPrice} = useMemo(() => {
    let percentage = 0;
    const maxValue = Number(price?.maximum_value);
    const defaultValue = Number(price?.value);
    const value =
      defaultValue !== 0
        ? defaultValue
        : price?.with_customisation_value
          ? Number(price?.with_customisation_value)
          : 0;
    if (maxValue !== 0 && maxValue !== value) {
      percentage = 100 - Math.floor((100 * price?.value) / maxValue);
    }
    return {
      discount: percentage,
      productPrice: value,
    };
  }, [price]);

  const maxValue = Number(price?.maximum_value);

  return (
    <ProductAmountUI
      productPrice={productPrice}
      maxValue={maxValue}
      discount={discount}
      currency={currency}
    />
  );
};

export default ProductAmount;
