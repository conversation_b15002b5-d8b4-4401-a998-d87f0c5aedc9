import React, {useMemo} from 'react';
import ProductAmountUI from './ProductAmountUI';

const DefaultProductAmount = ({
                                price,
                                defaultValue,
                                currency,
                              }: {
  price: any;
  defaultValue: number;
  currency: any;
}) => {
  const {discount, productPrice} = useMemo(() => {
    let percentage = 0;
    const maxValue = Number(price?.maximum_value);
    const value =
      defaultValue !== 0
        ? defaultValue
        : price?.with_customisation_value
          ? Number(price?.with_customisation_value)
          : 0;
    if (maxValue !== 0 && maxValue !== value) {
      percentage = 100 - Math.floor((100 * price?.value) / maxValue);
    }
    return {
      discount: percentage,
      productPrice: value,
    };
  }, [price, defaultValue]);

  const maxValue = Number(price?.maximum_value);

  return (
    <ProductAmountUI
      productPrice={productPrice}
      maxValue={maxValue}
      discount={discount}
      currency={currency}
    />
  );
};

export default DefaultProductAmount;
