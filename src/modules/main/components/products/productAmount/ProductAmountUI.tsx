import React from 'react';
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";
import {formatToIndianCurrency} from '../../../../../utils/utils';
import Box from "@mui/material/Box";

const ProductAmountUI = ({
                           productPrice,
                           maxValue,
                           discount,
                           currency,
                         }: {
  productPrice: number;
  maxValue: number;
  discount: number;
  currency: any;
}) => {
  const styles = useProductStyles();

  if (!!productPrice && (productPrice > 0 || maxValue > 0)) {
    return (
      <Box className={styles.amountContainer}>
        <Typography variant="bodyLarge" className={styles.amount}>
          {currency}
          {formatToIndianCurrency(isNaN(Number(productPrice)) ? "0" : Number(productPrice).toFixed(0))}
        </Typography>

        {discount !== 0 && (
          <>
            <Typography
              variant="labelSmall"
              className={styles.amountStrike}
            >
              {currency}
              {formatToIndianCurrency(maxValue.toFixed(0))}
            </Typography>

            <Typography
              variant="bodyLarge"
              className={styles.discount}
            >
              {discount}% off
            </Typography>
          </>
        )}
      </Box>
    );
  }

  return <Box sx={{height: 18}}/>;
};

const useProductStyles = makeStyles<any>((theme) => ({
  amountContainer: {
    display: 'flex',
    flexDirection: 'row',
    gap: 4,
    flex: 1,
    alignItems: 'center',
    maxWidth: '100%',
    overflow: 'hidden',
  },
  emptyAmountContainer: {
    height: 18,
  },
  amount: {
    color: theme.palette.neutral400,
  },
  amountStrike: {
    color: theme.palette.neutral200,
    textDecorationLine: 'line-through',
  },
  discount: {
    color: theme.palette.success600,
    flexShrink: 1,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  }
}));

export default ProductAmountUI;
