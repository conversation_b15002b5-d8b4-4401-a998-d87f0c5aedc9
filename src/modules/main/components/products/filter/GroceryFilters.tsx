import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Typography from "@mui/material/Typography"
import { useTheme } from "@mui/material/styles"
import axios from "axios"
import React, { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import FilterIcon from "../../../../../assets/filter.svg"

import Modal from "@mui/material/Modal"
import Slide from "@mui/material/Slide"
import useNetworkErrorHandling from "../../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import {
  API_BASE_URL,
  PRODUCT_ATTRIBUTE_VALUES,
  PRODUCT_ATTRIBUTES,
} from "../../../../../utils/apiActions"
import { PRICE_RANGE } from "../../../../../utils/constants"
import { emptyAlertCallback } from "../../../../../utils/utils"
import FilterList from "../FilterList"
import { filterStyles } from "./filterStyles"
import SortFilter from "./SortFilter"
import BasilSortOutline from "../../../../../assets/sort.svg"
interface GroceryFilters {
  searchText: string
  providerId: any
  category: any
  providerDomain: any
  searchTag: any
  selectedAttributes: any
  setSelectedAttributes: (values: any) => void
  isSearch?: boolean
  sortingValue: any
  setSortingValue: (values: any) => void
}

const CancelToken = axios.CancelToken

const GroceryFilters: React.FC<GroceryFilters> = ({
  isSearch = false,
  searchText,
  providerId = "",
  category = "",
  providerDomain = "",
  searchTag = "",
  selectedAttributes,
  setSelectedAttributes,
  sortingValue,
  setSortingValue,
}) => {
  const { t } = useTranslation()
  const attributeSource = useRef<any>(null)
  const [attributes, setAttributes] = useState<any[]>([])
  const [attributesRequested, setAttributesRequested] = useState<boolean>(true)
  const [attributeValuesRequested, setAttributesValuesRequested] =
    useState<boolean>(false)
  const { address } = useSelector((state: any) => state.address)
  const { getDataWithWithoutEncode } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const theme = useTheme()
  const styles = filterStyles(theme.palette)
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [isSortFilterOpen, setIsSortFilterOpen] = useState(false)
  const [sortSelectedValue, setSortSelectedValue] = useState<any>(sortingValue)
  useEffect(() => {
    setSortingValue(sortSelectedValue)
  }, [sortSelectedValue])
  const handleCloseSortFilter = () => setIsSortFilterOpen(false)
  const getData = async (params: URLSearchParams, url: string) => {
    if (providerId) params.append("provider", providerId)
    if (category) params.append("category", category)
    if (searchText) params.append("name", searchText)
    if (providerDomain) params.append("domain", providerDomain)
    if (searchTag) params.append("searchTag", searchTag)
    if (address?.address?.lat && address?.address?.lng) {
      params.append("latitude", address.address.lat)
      params.append("longitude", address.address.lng)
    }
    const response = await getDataWithWithoutEncode(
      `${url}?${params.toString()}`,
      attributeSource.current.token
    )
    return response.data
  }

  const getAttributes = async () => {
    try {
      setAttributesRequested(true)
      setAttributes([])
      attributeSource.current = CancelToken.source()
      const url = `${API_BASE_URL}${PRODUCT_ATTRIBUTES}`
      const params = new URLSearchParams()
      const data = await getData(params, url)
      setAttributes(
        data.response.data.length > 0
          ? [
              ...data.response.data,
              { code: "priceRange", values: PRICE_RANGE, skipAttribute: true },
            ]
          : [{ code: "priceRange", values: PRICE_RANGE, skipAttribute: true }]
      )
    } catch (error) {
      handleApiError(error)
    } finally {
      setAttributesRequested(false)
    }
  }

  const getAttributeValues = async (attribute: string) => {
    try {
      setAttributesValuesRequested(true)
      attributeSource.current = CancelToken.source()
      const url = `${API_BASE_URL}${PRODUCT_ATTRIBUTE_VALUES}`
      const params = new URLSearchParams()
      params.append("attribute_code", attribute)
      params.append("latitude", address.address.lat)
      params.append("longitude", address.address.lng)
      const data = await getData(params, url)
      const list = [...attributes]
      const selectedAttribute: any = list.find(
        (one: any) => one.code === attribute
      )
      if (selectedAttribute) {
        selectedAttribute.values = data.response.data
      }
      setAttributes(list)
    } catch (error) {
      handleApiError(error)
    } finally {
      setAttributesValuesRequested(false)
    }
  }

  useEffect(() => {
    getAttributes().then(emptyAlertCallback).catch(emptyAlertCallback)
    return () => {
      if (attributeSource.current) attributeSource.current.cancel()
    }
  }, [category, providerId])

  if (attributes.length === 0) return null

  return (
    <>
      <Box className={styles.container}>
        <Box className={styles.filterContainer}>
          <Button
            variant="contained"
            className={
              attributesRequested
                ? styles.disabledFilterButton
                : styles.activeFilterButton
            }
            onClick={() => setIsFilterOpen(true)}
            startIcon={
              <img src={FilterIcon} width={13} height={13} alt="Filter" />
            }
          >
            <Typography
              variant="labelSmall"
              className={
                attributesRequested
                  ? styles.disabledFilterLabel
                  : styles.filterLabel
              }
            >
              {t("Product SubCategories.Filter")}
            </Typography>
          </Button>
          <Button
            variant="contained"
            className={`${
              attributesRequested
                ? styles.disabledFilterButton
                : styles.activeFilterButton
            } ${sortSelectedValue !== "" && styles.attributeSelected}`}
            onClick={() => setIsSortFilterOpen(true)}
            startIcon={<img src={BasilSortOutline} width={13} height={13} />}
          >
            <Typography
              variant="labelLarge"
              className={
                attributesRequested
                  ? styles.disabledFilterLabel
                  : styles.filterLabel
              }
            >
              {t("Product SubCategories.Sort")}
            </Typography>
          </Button>
        </Box>
      </Box>

      <Modal open={isFilterOpen} onClose={() => setIsFilterOpen(false)}>
        <Slide direction="up" in={isFilterOpen} mountOnEnter unmountOnExit>
          <Box
            sx={{
              position: "fixed",
              bottom: 0,
              left: 0,
              right: 0,
              boxShadow: 24,
              maxHeight: "85vh",
              // overflow: 'auto',
            }}
          >
            <Box display="flex" justifyContent="center">
              <Box
                sx={{
                  bgcolor: "#fff",
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                  width: "100%",
                  overflow: "hidden",
                }}
                display="flex"
                justifyContent="center"
              >
                <FilterList
                  selectedAttributes={selectedAttributes}
                  setSelectedAttributes={setSelectedAttributes}
                  attributes={attributes}
                  getAttributeValues={getAttributeValues}
                  close={() => setIsFilterOpen(false)}
                  attributeValuesRequested={attributeValuesRequested}
                />
              </Box>
            </Box>
          </Box>
        </Slide>
      </Modal>
      <Modal open={isSortFilterOpen} onClose={handleCloseSortFilter}>
        <Slide direction="up" in={isSortFilterOpen} mountOnEnter unmountOnExit>
          <Box
            sx={{
              position: "fixed",
              bottom: 0,
              left: 0,
              right: 0,
              boxShadow: 24,
              maxHeight: "85vh",
              // overflow: 'auto',
            }}
          >
            <Box display="flex" justifyContent="center">
              <Box
                sx={{
                  bgcolor: "#fff",
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                  width: "100%",
                  overflow: "hidden",
                }}
                display="flex"
                justifyContent="center"
              >
                <SortFilter
                  selected={sortSelectedValue}
                  onSelect={setSortSelectedValue}
                  onDismiss={handleCloseSortFilter}
                />
              </Box>
            </Box>
          </Box>
        </Slide>
      </Modal>
    </>
  )
}

export default GroceryFilters
