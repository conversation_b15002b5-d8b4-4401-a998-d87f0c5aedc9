import { makeStyles } from "@mui/styles"

export const filterStyles = makeStyles<any>((theme) => ({
  filterLabel: {
    color: theme.palette.primary.main,
  },
  filterButton: {
    borderRadius: 8,
    borderWidth: 1,
    width: 68,
    height: 32,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
  },
  disabledFilterButton: {
    borderColor: theme.palette.neutral100,
  },
  activeFilterButton: {
    backgroundColor: `${theme.palette.white} !important`,
    border: `1px solid ${theme.palette.primary.main} !important`,
    borderRadius: 7,
    boxShadow: "none",
  },
  disabledFilterLabel: {
    color: theme.palette.neutral100,
  },
  rbSheet: { borderTopLeftRadius: 15, borderTopRightRadius: 15 },
  filterContainer: {
    display: "flex",
    flexDirection: "row",
    // justifyContent: 'space-between',
    alignItems: "center",
    marginBottom: "16px",
    boxShadow: "none !important",
    gap: 16,
    overflow: "auto",
    paddingBottom: "12px",
  },
  groceryFilterContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  container: {
    boxShadow: "none !important",
  },
  filtersContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    paddingHorizontal: 16,
    gap: 8,
    // marginBottom: 20,
    paddingBottom: "20px",
  },
  chip: {
    display: "flex",
    // paddingVertical: 4,
    // paddingHorizontal: 9,
    padding: "2px 8px 2px 16px",
    gap: 2,
    flexDirection: "row",
    backgroundColor: theme.palette.primary50,
    borderRadius: 7,
    alignItems: "center",
  },
  chipLabel: {
    color: theme.palette.primary.main,
    whiteSpace: "nowrap",
  },
  attributeSelected: {
    backgroundColor: "#E2F6FF !important",
  },
  minWidthFilter: {
    minWidth: "100px !important",
  },
}))
