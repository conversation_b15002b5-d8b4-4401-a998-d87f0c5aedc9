import React from "react"
import { useTranslation } from "react-i18next"

import Divider from "@mui/material/Divider"
import FormControlLabel from "@mui/material/FormControlLabel"
import Paper from "@mui/material/Paper"
import Radio from "@mui/material/Radio"
import RadioGroup from "@mui/material/RadioGroup"
import Typography from "@mui/material/Typography"
import { Box } from "@mui/material/node"

interface SortFilterProps {
  onDismiss: () => void
  selected: string
  onSelect: (value: string) => void
}

const SortFilter: React.FC<SortFilterProps> = ({
  onDismiss,
  selected,
  onSelect,
}) => {
  const { t } = useTranslation()

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onSelect(event.target.value)
    onDismiss()
  }

  return (
    <Paper elevation={3} sx={styles.surface}>
      <Box>
        <Typography variant="titleLarge" sx={styles.heading}>
          {t("Product SubCategories.Sort")}
        </Typography>
      </Box>
      <Divider />

      <RadioGroup value={selected} onChange={handleChange}>
        <FormControlLabel
          value=""
          control={<Radio />}
          dir="rtl"
          sx={styles.radioLabelContent}
          label={t("Orders.Relevance")}
        />
        <FormControlLabel
          value="desc"
          control={<Radio />}
          dir="rtl"
          sx={styles.radioLabelContent}
          label={t("Orders.Price (High to Low)")}
        />
        <FormControlLabel
          value="asc"
          control={<Radio />}
          dir="rtl"
          sx={styles.radioLabelContent}
          label={t("Orders.Price (Low to High)")}
        />
      </RadioGroup>
    </Paper>
  )
}

const styles = {
  surface: {
    width: "100%",
    backgroundColor: "white",
    p: 2,
    borderRadius: 7,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  heading: {
    ml: 2,
    mb: 1.5,
    color: "black",
    display: "block",
  },
  radioLabelContent: {
    justifyContent: "space-between",
    margin: 0,
  },
}

export default SortFilter
