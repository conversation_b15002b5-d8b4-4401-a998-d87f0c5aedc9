import CloseIcon from "@mui/icons-material/Close"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import IconButton from "@mui/material/IconButton"
import Modal from "@mui/material/Modal"
import Slide from "@mui/material/Slide"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import axios from "axios"
import React, { useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import FilterIcon from "../../../../../assets/filter.svg"
import BasilSortOutline from "../../../../../assets/sort.svg"

import { useSearchParams } from "react-router-dom"
import useNetworkErrorHandling from "../../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import {
  API_BASE_URL,
  PRODUCT_ATTRIBUTE_VALUES,
  PRODUCT_ATTRIBUTES,
} from "../../../../../utils/apiActions"
import {
  FB_DOMAIN,
  FOOD_TYPE,
  PRICE_RANGE,
} from "../../../../../utils/constants"
import {
  convertHexToName,
  emptyAlertCallback,
} from "../../../../../utils/utils"
import useCategoriesForFilter from "../../../hooks/useCategoriesForFilter"
import FilterList from "../FilterList"
import { filterStyles } from "./filterStyles"
import SortFilter from "./SortFilter"

interface FiltersProps {
  searchText: string
  providerId?: string | null
  category?: string | null
  providerDomain?: any
  selectedAttributes: any
  setSelectedAttributes: (values: any) => void
  sortingValue: any
  setSortingValue: (values: any) => void
  isSearch?: boolean
  tagId?: string
  serviceability?: string
  tts?: string
  bppId: string
  locationId?: any
}

const CancelToken = axios.CancelToken

const Filters: React.FC<FiltersProps> = ({
  isSearch = false,
  searchText,
  providerId = "",
  category = "",
  providerDomain = "",
  selectedAttributes,
  setSelectedAttributes,
  sortingValue = "",
  setSortingValue,
  tagId = "",
  bppId = "",
  serviceability = "",
  tts = "",
  locationId,
}) => {
  const { t } = useTranslation()
  const attributeSource = useRef<any>(null)
  const [attributes, setAttributes] = useState<any[]>([])
  const [attributesRequested, setAttributesRequested] = useState<boolean>(true)
  const [attributeValuesRequested, setAttributesValuesRequested] =
    useState<boolean>(false)
  const { address } = useSelector((state: any) => state.address)
  const { getDataWithWithoutEncode } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const { getCategoriesForSearch } = useCategoriesForFilter()
  const theme = useTheme()
  const styles = filterStyles()
  const [sortSelectedValue, setSortSelectedValue] = useState<any>(sortingValue)

  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [isSortFilterOpen, setIsSortFilterOpen] = useState(false)

  useEffect(() => {
    setSortingValue(sortSelectedValue)
  }, [sortSelectedValue])

  const filterCounters = useMemo(() => {
    return Object.keys(selectedAttributes).reduce((acc: any, key: any) => {
      const value = selectedAttributes[key]
      if (Array.isArray(value)) {
        acc.push(...value)
      } else {
        acc.push(value)
      }
      return acc
    }, [])
  }, [selectedAttributes])

  const handleCloseFilter = () => setIsFilterOpen(false)
  const handleCloseSortFilter = () => setIsSortFilterOpen(false)
  const [searchParams] = useSearchParams()
  const providerIdss = searchParams.get("provider_id") || ""

  const getCategories = async () => {
    const data = await getCategoriesForSearch(
      providerDomain,
      "",
      locationId,
      bppId,
      "",
      providerIdss,
      searchText
    )

    let formattedCategories: any = {}
    if (data) {
      let codeList = []
      if (providerDomain !== "") {
        codeList = data[providerDomain]?.map((item: any) => {
          return item.code
        })
      } else {
        Object.keys(data).forEach((key: any) => {
          data[key]?.map((item: any) => {
            codeList.push(item.code)
          })
        })
      }

      const uniqueSortedCodes = Array.from(new Set(codeList)).sort(
        (a: any, b: any) => a.toLowerCase().localeCompare(b.toLowerCase())
      )

      formattedCategories = {
        code: "Categories",
        values: uniqueSortedCodes,
      }
    }
    return formattedCategories
  }

  const getData = async (params: URLSearchParams, url: string) => {
    if (providerId) params.append("provider", providerId)
    if (searchText) params.append("name", searchText)
    if (providerDomain) params.append("domain", providerDomain)
    if (providerDomain) {
      params.append("category", providerDomain)
    }
    if (tagId) params.append("searchTag", tagId)
    if (serviceability) params.append("serviceability", serviceability)
    if (tts) params.append("tts", tts)
    if (address?.address?.lat && address?.address?.lng) {
      params.append("latitude", address.address.lat)
      params.append("longitude", address.address.lng)
    }
    if (bppId) {
      params.append("bpp_ids", bppId)
      // url += `&bpp_ids=${bppId}`
    }
    const response = await getDataWithWithoutEncode(
      `${url}?${params.toString()}`,
      attributeSource.current.token
    )
    return response.data
  }

  const getAttributes = async () => {
    try {
      setAttributesRequested(true)
      setAttributes([])
      attributeSource.current = CancelToken.source()
      const url = `${API_BASE_URL}${PRODUCT_ATTRIBUTES}`
      const params = new URLSearchParams()
      const data = await getData(params, url)
      let additionalFilters: any = [
        { code: "priceRange", values: PRICE_RANGE, skipAttribute: true },
      ]
      if (providerDomain === FB_DOMAIN) {
        additionalFilters.push({ code: "foodType", values: FOOD_TYPE })
      }
      if (
        data?.response?.domains?.length > 0 &&
        (providerDomain === null || providerDomain === "")
      ) {
        additionalFilters.push({
          code: "domain",
          values: data?.response?.domains.map((domain: any) => domain.domain),
        })
      }
      if (providerDomain) {
        const categoryData = await getCategories()
        if (
          categoryData.hasOwnProperty("code") &&
          categoryData?.values?.length > 0
        ) {
          additionalFilters.push(categoryData)
        }
      }
      if (data.response.data.length > 0) {
        setAttributes(data.response.data.concat(additionalFilters))
      } else {
        setAttributes(additionalFilters)
      }
    } catch (error) {
      handleApiError(error)
    } finally {
      setAttributesRequested(false)
    }
  }

  const getAttributeValues = async (attribute: string) => {
    try {
      setAttributesValuesRequested(true)
      attributeSource.current = CancelToken.source()
      const url = `${API_BASE_URL}${PRODUCT_ATTRIBUTE_VALUES}`
      const params = new URLSearchParams()
      params.append("attribute_code", attribute)

      const data = await getData(params, url)
      const updatedAttributes = [...attributes]
      const selectedAttribute = updatedAttributes.find(
        (attr) => attr.code === attribute
      )

      if (selectedAttribute) {
        selectedAttribute.values = data.response.data
      }
      setAttributes(updatedAttributes)
    } catch (error) {
      handleApiError(error)
    } finally {
      setAttributesValuesRequested(false)
    }
  }

  const removeItemFromFilter = (value: string) => {
    const updatedValues = { ...selectedAttributes }
    Object.keys(updatedValues).forEach((key) => {
      if (key !== "priceRange") {
        updatedValues[key] = updatedValues[key]?.filter(
          (one: string) => one !== value
        )
      }
    })
    setSelectedAttributes(updatedValues)
  }

  const removePriceRange = () => {
    const { priceRange, ...updatedData } = selectedAttributes
    setSelectedAttributes(updatedData)
  }

  useEffect(() => {
    getAttributes().then(emptyAlertCallback).catch(emptyAlertCallback)

    return () => {
      if (attributeSource.current) {
        attributeSource.current.cancel()
      }
    }
  }, [category, providerId])

  if (attributes.length === 0) return null

  const attributeKeys = Object.keys(selectedAttributes).filter(
    (key) => selectedAttributes[key].length > 0
  )

  return (
    <>
      <Box className={styles.container}>
        <Box className={styles.filterContainer}>
          <Button
            variant="contained"
            className={`${styles.activeFilterButton} ${
              filterCounters.length > 0 && styles.attributeSelected
            } ${filterCounters.length > 0 && styles.minWidthFilter} `}
            onClick={() => setIsFilterOpen(true)}
            startIcon={<img src={FilterIcon} width={13} height={13} />}
          >
            <Typography
              variant="labelLarge"
              className={
                attributesRequested
                  ? styles.disabledFilterLabel
                  : styles.filterLabel
              }
            >
              {t("Product SubCategories.Filter")}
              {filterCounters.length > 0 ? `(${filterCounters.length})` : ""}
            </Typography>
          </Button>

          <Button
            variant="contained"
            className={`${
              attributesRequested
                ? styles.disabledFilterButton
                : styles.activeFilterButton
            } ${sortSelectedValue !== "" && styles.attributeSelected}`}
            onClick={() => setIsSortFilterOpen(true)}
            startIcon={<img src={BasilSortOutline} width={13} height={13} />}
          >
            <Typography
              variant="labelLarge"
              className={
                attributesRequested
                  ? styles.disabledFilterLabel
                  : styles.filterLabel
              }
            >
              {t("Product SubCategories.Sort")}
            </Typography>
          </Button>

          {attributeKeys.length > 0 &&
            attributeKeys.map((key) =>
              selectedAttributes[key].map((value: string) => (
                <Box className={styles.chip} key={value}>
                  <Typography
                    component="div"
                    variant="labelLarge"
                    className={styles.chipLabel}
                  >
                    {key === "color" ? convertHexToName(value) : value}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={() => removeItemFromFilter(value)}
                  >
                    <CloseIcon
                      fontSize="small"
                      sx={{ color: theme.palette.primary.main }}
                    />
                  </IconButton>
                </Box>
              ))
            )}
        </Box>
      </Box>

      <Modal open={isFilterOpen} onClose={handleCloseFilter}>
        <Slide direction="up" in={isFilterOpen} mountOnEnter unmountOnExit>
          <Box
            sx={{
              position: "fixed",
              bottom: 0,
              left: 0,
              right: 0,
              boxShadow: 24,
              maxHeight: "85vh",
              // overflow: 'auto',
            }}
          >
            <Box display="flex" justifyContent="center">
              <Box
                sx={{
                  bgcolor: "#fff",
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                  width: "100%",
                  overflow: "hidden",
                }}
                display="flex"
                justifyContent="center"
              >
                <FilterList
                  selectedAttributes={selectedAttributes}
                  setSelectedAttributes={setSelectedAttributes}
                  attributes={attributes}
                  getAttributeValues={getAttributeValues}
                  close={() => setIsFilterOpen(false)}
                  attributeValuesRequested={attributeValuesRequested}
                />
              </Box>
            </Box>
          </Box>
        </Slide>
      </Modal>

      <Modal open={isSortFilterOpen} onClose={handleCloseSortFilter}>
        <Slide direction="up" in={isSortFilterOpen} mountOnEnter unmountOnExit>
          <Box
            sx={{
              position: "fixed",
              bottom: 0,
              left: 0,
              right: 0,
              boxShadow: 24,
              maxHeight: "85vh",
              // overflow: 'auto',
            }}
          >
            <Box display="flex" justifyContent="center">
              <Box
                sx={{
                  bgcolor: "#fff",
                  borderTopLeftRadius: 16,
                  borderTopRightRadius: 16,
                  width: "100%",
                  // overflow: "hidden"
                }}
                display="flex"
                justifyContent="center"
              >
                <SortFilter
                  selected={sortSelectedValue}
                  onSelect={setSortSelectedValue}
                  onDismiss={handleCloseSortFilter}
                />
              </Box>
            </Box>
          </Box>
        </Slide>
      </Modal>
    </>
  )
}

export default Filters
