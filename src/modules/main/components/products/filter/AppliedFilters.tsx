import React from "react";
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import {useTheme} from "@mui/material/styles";
import {makeStyles} from "@mui/styles";
import CloseIcon from "@mui/icons-material/Close";
import {convertHexToName} from "../../../../../utils/utils";

interface AppliedFiltersProps {
  selectedAttributes: Record<string, any>;
  setSelectedAttributes: (values: Record<string, any>) => void;
}

const AppliedFilters: React.FC<AppliedFiltersProps> = ({
                                                         selectedAttributes,
                                                         setSelectedAttributes,
                                                       }) => {
  const theme = useTheme();
  const styles = useFiltersStyles(theme);

  const removeItemFromFilter = (value: string) => {
    const values = {...selectedAttributes};
    Object.keys(values).forEach((key) => {
      if (key !== "priceRange") {
        values[key] = values[key]?.filter((one: string) => one !== value);
      }
    });
    setSelectedAttributes(values);
  };

  const removePriceRange = () => {
    const {priceRange, ...updatedData} = selectedAttributes;
    setSelectedAttributes(updatedData);
  };

  const attributeKeys = Object.keys(selectedAttributes).filter(
    (key) => selectedAttributes[key].length > 0
  );

  if (attributeKeys.length === 0) return null;

  return (
    <Box className={styles.filtersContainer}>
      {attributeKeys.map((key) => {
        if (key === "color") {
          return selectedAttributes[key].map((value: string) => (
            <Box className={styles.chip} key={value}>
              <Typography variant="labelSmall" className={styles.chipLabel}>
                {convertHexToName(value)}
              </Typography>
              <IconButton onClick={() => removeItemFromFilter(value)} size="small">
                <CloseIcon fontSize="small" sx={{color: theme.palette.primary.main}}/>
              </IconButton>
            </Box>
          ));
        } else if (key === "priceRange") {
          return (
            <Box className={styles.chip} key="priceRange">
              <Typography variant="labelSmall" className={styles.chipLabel}>
                {selectedAttributes[key].values[0]} - {selectedAttributes[key].values[1]}
              </Typography>
              <IconButton onClick={removePriceRange} size="small">
                <CloseIcon fontSize="small" sx={{color: theme.palette.primary.main}}/>
              </IconButton>
            </Box>
          );
        } else {
          return selectedAttributes[key].map((value: string) => (
            <Box className={styles.chip} key={value}>
              <Typography variant="labelSmall" className={styles.chipLabel}>
                {value}
              </Typography>
              <IconButton onClick={() => removeItemFromFilter(value)} size="small">
                <CloseIcon fontSize="small" sx={{color: theme.palette.primary.main}}/>
              </IconButton>
            </Box>
          ));
        }
      })}
    </Box>
  );
};

const useFiltersStyles = makeStyles((theme: any) => ({
  filtersContainer: {
    display: "flex",
    flexWrap: "wrap",
    alignItems: "center",
    padding: "16px",
    gap: "8px",
    marginBottom: "20px",
  },
  chip: {
    display: "flex",
    alignItems: "center",
    padding: "4px 9px",
    gap: "4px",
    backgroundColor: theme.palette.primary.light,
    borderRadius: "28px",
  },
  chipLabel: {
    color: theme.palette.primary.main,
  },
}));

export default AppliedFilters;
