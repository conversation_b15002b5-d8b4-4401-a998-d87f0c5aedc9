import Box from "@mui/material/Box"
import Paper from "@mui/material/Paper"
import Typography from "@mui/material/Typography"
import { makeStyles, useTheme } from "@mui/styles"
import axios, { AxiosResponse } from "axios"
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import EmptyProductsIcon from "../../../../assets/search/products.svg"
import useNetworkErrorHandling from "../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../hooks/useNetworkHandling"
import {
  API_BASE_URL,
  CUSTOM_MENU,
  GLOBAL_SEARCH_ITEMS,
  ITEMS,
  PRODUCT_SEARCH,
} from "../../../../utils/apiActions"
import { BRAND_PRODUCTS_LIMIT, FB_DOMAIN } from "../../../../utils/constants"
import { mergeAttributesUrls } from "../../../../utils/productUtils"
import { emptyAlertCallback } from "../../../../utils/utils"
import useAddressParams from "../../hooks/useAddressParams"
import ProductSkeleton from "../skeleton/ProductSkeleton"
import Filters from "./filter/Filters"
import ListProduct from "./ListProduct"
import ProductAccordion from "./ProductAccordion"

interface Products {
  providerId: any
  locationId?: any
  subCategories: any[]
  searchText: string
  provider: any
  providerDomain?: string
  isOpen?: boolean
  isSearch: boolean
  fbDomain: boolean
  children: any
  tagId?: string
  serviceability?: any
  tts?: any
  categoryDomain?: string
  bppId?: string
}

interface ProductResponse {
  response: {
    data: any[] // Replace `any[]` with actual data type if known
    count: number
  }
}

export const ProductsListEmptyComponent = () => {
  const theme = useTheme()
  const { t } = useTranslation()
  const styles = useStyles()

  return (
    <Box className={styles.emptyContainer}>
      <Box className={styles.emptyMessageContainer}>
        <img
          src={EmptyProductsIcon}
          width={128}
          height={128}
          alt="Empty Products"
        />
        <Typography variant="headlineSmall" className={styles.emptyTitle}>
          {t("Home.Search Product List.No products available")}
        </Typography>
        <Typography variant="labelSmall" className={styles.emptyMessage}>
          {t("Home.Search Product List.No products available message")}
        </Typography>
      </Box>
    </Box>
  )
}

const Products: React.FC<Products> = ({
  providerId = null,
  locationId = null,
  subCategories = [],
  searchText,
  provider,
  providerDomain,
  isOpen = false,
  isSearch,
  fbDomain,
  children,
  tagId = "",
  serviceability = "",
  tts = "",
  categoryDomain = "",
  bppId = "",
}) => {
  const sectionListRef = useRef<any>(null)
  const productSearchSource = useRef<AbortController>(new AbortController())
  const theme = useTheme()
  const styles = useStyles()
  const [moreListRequested, setMoreListRequested] = useState<boolean>(true)
  const [searchQuery] = useState<string>("")
  const { getAddressParams } = useAddressParams()
  const [page, setPage] = useState<number>(1)
  const [products, setProducts] = useState<any[]>([])
  const [totalProducts, setTotalProducts] = useState<number>(0)
  const [selectedAttributes, setSelectedAttributes] = useState<any>({})
  const [sortingValue, setSortingValue] = useState<any>("")
  const [customData, setCustomData] = useState<any[]>([])
  const [menuList, setMenuList] = useState<any[]>([])
  const { getDataWithAuth, getDataWithWithoutEncode } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const loadMoreRef = useRef(null)
  const scrollContainerRef = useRef(null)

  const handleResponseData = (
    responseData: any,
    total: number,
    pageNumber: number
  ) => {
    if (responseData.length > 0) {
      let listData =
        pageNumber === 1 ? responseData : [...products, ...responseData]
      setPage(pageNumber + 1)
      setTotalProducts(total)
      setProducts(listData)
    } else {
      if (pageNumber === 1) {
        setTotalProducts(0)
        setProducts([])
      }
    }
  }

  const getMenuList = async (customMenu: any[]) => {
    if (!productSearchSource.current) {
      productSearchSource.current = new AbortController()
    }

    const menuResponses = await Promise.all(
      customMenu.map((one: any) =>
        getDataWithAuth(
          `${API_BASE_URL}${ITEMS}?customMenu=${one.id}`,
          productSearchSource.current.signal
        )
      )
    )

    const list = customMenu.map((one: any, index: number) => ({
      title: one.descriptor.name,
      data: menuResponses[index]?.data,
    }))

    setMenuList(list)
  }

  const getProductsForProvider = async (
    pageNumber: number,
    selectedProvider: any,
    subCategoryIds: any,
    attributes: any,
    sortedValue: any
  ) => {
    let url =
      `${API_BASE_URL}${PRODUCT_SEARCH}?pageNumber=${pageNumber}` +
      `&limit=${BRAND_PRODUCTS_LIMIT}&providerIds=${encodeURIComponent(
        selectedProvider
      )}${bppId ? `&bpp_ids=${bppId}` : ""}${
        providerDomain ? `&domain=${providerDomain}` : ""
      }`

    url += locationId ? `&locationIds=${encodeURIComponent(locationId)}` : ""

    if (fbDomain) {
      url += `&domain=${FB_DOMAIN}`
    } else {
      url +=
        subCategoryIds.length > 0
          ? `&categoryIds=${encodeURIComponent(subCategoryIds.join(","))}`
          : ""
    }
    if (sortedValue !== "") {
      url += `&sortBy=${sortedValue || "asc"}`
      url += "&sort=minPrice"
    }
    url += mergeAttributesUrls(attributes)
    url += searchText.length > 0 ? `&name=${searchText}` : ""

    if (!productSearchSource.current) {
      productSearchSource.current = new AbortController()
    }

    const response = await getDataWithWithoutEncode(
      url,
      productSearchSource.current.signal
    )
    const typedResponse = response as AxiosResponse<{
      response: { data: any[]; count: number }
    }>
    handleResponseData(
      typedResponse.data.response.data,
      typedResponse.data.response.count,
      pageNumber
    )
  }

  const getProducts = async (
    pageNumber: number,
    subCategoryIds: any,
    attributes: any,
    sortedValue: any
  ) => {
    const params = getAddressParams()
    params.append("pageNumber", String(pageNumber))
    params.append("limit", String(BRAND_PRODUCTS_LIMIT))
    if (categoryDomain.length > 0) {
      params.append("category", categoryDomain)
    }
    if (tagId.length > 0) {
      params.append("searchTag", tagId)
    }
    if (serviceability.length > 0) {
      params.append("serviceability", serviceability)
    }
    if (tts) {
      params.append("tts", tts)
    }
    if (searchText.length > 0) {
      params.append("name", searchText)
    }
    if (sortingValue !== "") {
      params.append("sortBy", sortingValue || "asc")
      params.append("sort", "minPrice")
    }
    let url = `${API_BASE_URL}${GLOBAL_SEARCH_ITEMS}?${params.toString()}`
    url +=
      subCategoryIds.length > 0
        ? `&subcategory=${encodeURIComponent(subCategoryIds.join(","))}`
        : ""
    url += mergeAttributesUrls(attributes)
    const { data } = await getDataWithWithoutEncode(
      url,
      productSearchSource.current.signal
    )
    handleResponseData(data.data, data.count, pageNumber)
  }

  const searchProducts = async (
    pageNumber: number,
    selectedProvider: any,
    customMenu: any,
    subCategoryIds: any,
    attributes: any,
    sortingValue: any
  ) => {
    try {
      if (productSearchSource.current) {
        productSearchSource.current.abort()
      }
      if (customMenu.length > 0) {
        await getMenuList(customMenu)
      } else if (selectedProvider) {
        await getProductsForProvider(
          pageNumber,
          selectedProvider,
          subCategoryIds,
          attributes,
          sortingValue
        )
      } else {
        await getProducts(pageNumber, subCategoryIds, attributes, sortingValue)
      }
    } catch (error) {
      handleApiError(error)
    } finally {
      setMoreListRequested(false)
    }
  }

  const loadMoreList = () => {
    if (products?.length > 0) {
      if (totalProducts !== products?.length) {
        setMoreListRequested(true)
        searchProducts(
          page,
          providerId,
          customData,
          subCategories,
          selectedAttributes,
          sortingValue
        ).then(emptyAlertCallback)
      }
    }
  }

  const updateSelectedAttributes = (newAttributes: any) => {
    setPage(1)
    setSelectedAttributes(newAttributes)
  }

  const filteredProducts = useMemo(() => {
    const lowerQuery = searchQuery.toLowerCase()
    // Filter the products based on the search query
    return products.filter(
      (product: any) =>
        product?.item_details?.descriptor?.name
          ?.toLowerCase()
          .includes(lowerQuery) ||
        product?.provider_details?.descriptor?.name
          ?.toLowerCase()
          .includes(lowerQuery)
    )
  }, [products, searchQuery])

  const filteredMenuList = useMemo(() => {
    const lowerQuery = searchQuery.toLowerCase()

    const list = menuList.map((item: any) => {
      return {
        id: item.id,
        title: item.title,
        data: item.data.data.filter(
          (product: any) =>
            product?.item_details?.descriptor?.name
              ?.toLowerCase()
              .includes(lowerQuery) ||
            product?.provider_details?.descriptor?.name
              ?.toLowerCase()
              .includes(lowerQuery)
        ),
      }
    })

    return list.filter((one) => one.data.length > 0)
  }, [menuList, searchQuery])

  const renderFlatListItem = useCallback(
    ({ item }: { item: any }) => (
      <ListProduct
        product={item}
        isOpen={isOpen}
        listView={false}
        provider={provider}
        openDetailsPage={!!providerId}
      />
    ),
    [provider]
  )

  const ListFooterComponent = useMemo(() => {
    return moreListRequested ? <ProductSkeleton /> : null
  }, [moreListRequested])

  const headerComponent = useMemo(() => {
    return (
      <>
        {children}
        <Filters
          isSearch={isSearch}
          searchText={searchText}
          selectedAttributes={selectedAttributes}
          setSelectedAttributes={updateSelectedAttributes}
          providerId={providerId}
          category={subCategories.length ? subCategories[0] : null}
          providerDomain={
            categoryDomain
              ? categoryDomain
              : providerDomain
              ? providerDomain
              : ""
          }
          bppId={bppId}
          sortingValue={sortingValue}
          setSortingValue={setSortingValue}
        />
      </>
    )
  }, [
    children,
    isSearch,
    searchText,
    selectedAttributes,
    providerId,
    subCategories,
  ])

  useEffect(() => {
    if (sectionListRef?.current) {
      try {
        sectionListRef?.current?.scrollToLocation({
          sectionIndex: 0,
          itemIndex: 0,
          animated: true,
        })
      } catch (error: any) {}
    }

    if (providerId) {
      const abortController = new AbortController()
      productSearchSource.current = abortController

      // const result = parseBrandOutletId(providerId)

      getDataWithAuth(
        `${API_BASE_URL}${CUSTOM_MENU}?provider=${providerId}${
          providerDomain ? `&domain=${providerDomain}` : ""
        }${locationId ? `&locationIds=${locationId}` : ""}`,
        { signal: abortController.signal } // Pass signal for cancellation
      )
        .then((menu) => {
          setCustomData(menu?.data?.data)
          searchProducts(
            1,
            providerId,
            menu?.data?.data,
            subCategories,
            selectedAttributes,
            sortingValue
          ).then(emptyAlertCallback)
        })
        .catch((error) => {
          if (axios.isCancel(error)) {
            console.log("Request canceled:", error.message)
          }
        })

      return () => {
        abortController.abort()
      }
    } else {
      searchProducts(
        1,
        providerId,
        [],
        subCategories,
        selectedAttributes,
        sortingValue
      ).then(emptyAlertCallback)
    }
  }, [providerId, selectedAttributes, subCategories, sortingValue])

  useEffect(() => {
    setSelectedAttributes({})
  }, [subCategories])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMoreList()
        }
      },
      {
        root: scrollContainerRef.current ?? null, // Use scroll container
        threshold: 0.5,
      }
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current)
      }
    }
  }, [filteredProducts])

  return (
    <Box
      className={styles.container}
      ref={scrollContainerRef} // Optional, if using as IntersectionObserver root
    >
      {customData.length > 0 ? (
        <Box
          sx={{
            overflowY: "auto",
            overflowX: "hidden",
            maxHeight: "80dvh",
            scrollbarWidth: "none",
            "&::-webkit-scrollbar": {
              display: "none",
            },
          }}
        >
          {children}
          <Paper className={styles.customMenuContainer} elevation={3}>
            <Filters
              isSearch={isSearch}
              searchText={searchText}
              selectedAttributes={selectedAttributes}
              setSelectedAttributes={updateSelectedAttributes}
              providerId={providerId}
              category={subCategories.length ? subCategories[0] : null}
              providerDomain={categoryDomain ?? ""}
              tagId={tagId}
              serviceability={serviceability}
              tts={tts}
              bppId={bppId}
              sortingValue={sortingValue}
              setSortingValue={setSortingValue}
            />
          </Paper>
          {filteredMenuList?.map((section, index) => (
            <ProductAccordion
              key={section.title}
              section={section}
              provider={provider}
              isOpen={isOpen}
              defaultExpand={index === 0}
              openDetailsPage={!!providerId}
            />
          ))}
        </Box>
      ) : (
        <Box className={styles.nestedListContainer}>
          {headerComponent}
          <Box className={styles.listContainer}>
            {filteredProducts?.map((item) => (
              <Box key={item.id} className={styles.gridItem}>
                {renderFlatListItem({ item })}
              </Box>
            ))}
          </Box>

          {!moreListRequested && filteredProducts.length === 0 && (
            <ProductsListEmptyComponent />
          )}
          <div ref={loadMoreRef} className={styles.loadMoreTrigger} />
          <Box className={styles.footerContainer}>{ListFooterComponent}</Box>
        </Box>
      )}
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
    padding: 16,
    height: "calc(100vh - 100px)", // or any value to allow proper scrolling
    overflowY: "auto",
    overflowX: "hidden",
    scrollbarWidth: "none",
    "&::-webkit-scrollbar": {
      display: "none",
    },
  },
  customMenuContainer: {
    paddingHorizontal: 16,
    boxShadow: "none !important",
  },
  nestedListContainer: {
    flexGrow: 1,
    paddingHorizontal: 16,
  },
  emptyContainer: {
    flex: 1,
    display: "flex",
    justifyContent: "center",
    height: "100%",
  },
  emptyMessageContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    marginTop: "20px",
  },
  listContainer: {
    display: "flex",
    flexWrap: "wrap",
    gap: 10,
  },
  gridItem: {
    width: "48%",
    display: "flex",
    justifyContent: "center",
  },
  columnWrapper: {
    display: "flex",
    justifyContent: "space-between",
    gap: 16,
  },
  emptyTitle: {
    color: theme.palette.neutral400,
  },
  emptyMessage: {
    marginTop: 8,
    color: theme.palette.neutral400,
    width: 260,
    textAlign: "center",
  },
  loadMoreTrigger: {
    height: "100px !important",
  },
}))

export default Products
