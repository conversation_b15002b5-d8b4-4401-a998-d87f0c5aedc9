import Box from '@mui/material/Box';
import {makeStyles} from "@mui/styles";

const CustomSliderMarker = () => {
  const styles = useCustomSliderMarkerStyles();

  return <Box className={styles.container}/>;
};

const useCustomSliderMarkerStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 20,
    width: 20,
    height: 20,
    borderWidth: 5,
    borderColor: theme.palette.primary,
    backgroundColor: theme.palette.white,
    marginBottom: -3,
  },
}));

export default CustomSliderMarker;
