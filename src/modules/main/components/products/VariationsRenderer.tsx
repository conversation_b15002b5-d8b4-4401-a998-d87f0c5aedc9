import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import KeyboardArrowRightOutlinedIcon from '@mui/icons-material/KeyboardArrowRightOutlined';

import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
// import useTheme from '@mui/material/';
// import SxProps from '@mui/material/SxProps';
// import Theme from '@mui/material/Theme';
import SizeChart from '../../modules/product/details/components/SizeChart';
import useTheme from '@mui/material/styles/useTheme';

interface VariationsRendererProps {
  product: any;
  variationState: any;
  setVariationState: (variations: any[]) => void;
  isFashion: boolean;
  disabled?: boolean;
  handleVariationChange: (id: string) => void;
}

const VariationsRenderer: React.FC<VariationsRendererProps> = ({
  product,
  variationState,
  setVariationState,
  isFashion = false,
  disabled = false,
  handleVariationChange,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const styles = useProductStyles(theme);
  const globalStyles = makeGlobalStyles(theme);

  const [variationGroups, setVariationGroups] = useState<any[]>([]);
  const [variations, setVariations] = useState<any[]>([]);
  const [initialVariationState, setInitialVariationState] = useState<any>({});
  const [isUOM, setIsUOM] = useState<boolean>(false);
  const [showSizeChart, setShowSizeChart] = useState<boolean>(false);

  const getVariationGroups = () => {
    const groupInfo = Array.from(
      new Set(product?.variant_group?.attribute_codes || [])
    ).map((tag, index) => ({ name: tag, seq: index + 1 }));

    setVariationGroups(groupInfo);
    getRelatedVariations(groupInfo);
    getInitialVariationState(groupInfo);
  };

  const getInitialVariationState = (groupInfo: any) => {
    if (groupInfo[0]?.name === 'measure') {
      setInitialVariationState({ isUOM: true });
      setIsUOM(true);
    } else {
      setIsUOM(false);
      const newState: any = {};
      groupInfo.forEach((group: any) => {
        const attributeName = group.name;
        newState[attributeName] = product.attributes[attributeName];
      });
      setInitialVariationState(newState);
    }
  };

  const getRelatedVariations = (variationList: any) => {
    const relatedItems = product?.related_items?.map((item: any) => {
      const attributes = item?.attributes;
      const variationsInfo: any = {};
      variationList.forEach((variation: any) => {
        if (attributes[variation?.name]) {
          variationsInfo[variation?.name] = attributes[variation?.name];
        }
      });
      return {
        id: item.id,
        price: item.item_details.price.value,
        img: item.item_details.descriptor.symbol,
        ...variationsInfo,
      };
    });

    setVariations(relatedItems);
  };

  const findMatchingVariation = (updatedVariationState: any) => {
    for (const variation of variations) {
      let isMatch = true;
      for (const groupId in updatedVariationState) {
        const groupData = updatedVariationState[groupId];
        const groupName = groupData.name;
        const selectedOption = groupData.selected[0];
        if (variation[groupName] !== selectedOption) {
          isMatch = false;
        }
      }
      if (isMatch) {
        return variation;
      }
    }
    return null;
  };

  const handleVariationClick = (groupData: any, option: any) => {
    try {
      const updatedState = { ...variationState };
      updatedState[groupData.id].selected = [option];
      const matchingVariation = findMatchingVariation(updatedState);
      if (matchingVariation) {
        handleVariationChange(matchingVariation.id);
      }
    } catch (error: any) {
      console.error(error);
    }
  };

  const handleUOMClick = (groupData: any, option: any) => {
    const words = option.split(' ');
    let unitValue = option;
    let currentUnit = '';
    if (words.length > 1) {
      currentUnit = words.pop()!;
      unitValue = words.join(' ');
    }

    const relatedItem = product.related_items.find((item: any) => {
      const { value, unit } = item.item_details.quantity.unitized.measure;
      if (currentUnit) {
        return value === unitValue && unit === currentUnit;
      }
      return value === unitValue;
    });

    if (relatedItem) {
      handleVariationChange(relatedItem.id);
    }
  };

  const openSizeChart = () => setShowSizeChart(true);
  const closeSizeChart = () => setShowSizeChart(false);

  useEffect(() => {
    if (product) {
      const relatedItemLength = product?.related_items?.length;
      if (
        relatedItemLength > 1 ||
        (relatedItemLength === 1 &&
          product?.related_items[0]?.item_details?.id !==
            product?.item_details?.id)
      ) {
        getVariationGroups();
      }
    }
  }, [product]);

  useEffect(() => {
    if (variationGroups && initialVariationState) {
      const result: any = {};
      variationGroups.forEach((group, index) => {
        const groupName = group.name;
        const groupId = group.seq;

        let groupData: any = {
          id: groupId,
          productId: '',
          name: groupName,
          selected: [],
          options: [],
        };

        if (initialVariationState?.isUOM === true) {
          const selectedOption =
            product.item_details.quantity.unitized?.measure;
          groupData.selected = [
            `${selectedOption.value} ${selectedOption.unit}`,
          ];

          product.related_items.forEach((item: any) => {
            const option = item.item_details.quantity.unitized.measure;
            groupData.options.push(`${option.value} ${option.unit}`);
          });
        } else {
          groupData.selected = [initialVariationState[groupName]];

          if (index === 0) {
            variations.forEach((variation) => {
              groupData.productId = variation.id;
              if (
                variation[groupName] &&
                !groupData.options.includes(variation[groupName])
              ) {
                groupData.options.push(variation[groupName]);
              }
            });
          } else {
            const prevGroupName = variationGroups[index - 1].name;
            const prevGroupSelection = initialVariationState[prevGroupName];
            variations.forEach((variation) => {
              groupData.productId = variation.id;
              if (variation[prevGroupName] === prevGroupSelection) {
                if (
                  variation[groupName] &&
                  !groupData.options.includes(variation[groupName])
                ) {
                  groupData.options.push(variation[groupName]);
                }
              }
            });
          }
        }
        result[groupId] = groupData;
      });

      setVariationState(result);
    }
  }, [variationGroups, initialVariationState, variations]);

  return (
    <>
      {Object.keys(variationState).map((groupId) => {
        const groupData = variationState[groupId];
        const groupName = groupData.name;
        return (
          <Box sx={styles.group} key={groupId}>
            <Box sx={styles.groupHeader}>
              <Typography variant="body1" sx={styles.groupTitle}>
                {t('Variations.Available Options', { groupName })}
              </Typography>
              {groupName === 'size' && isFashion && (
                <Button disabled={disabled} onClick={openSizeChart} sx={styles.sizeChart}>
                  <Typography variant="body2" sx={styles.sizeGuide}>
                    {t('Variations.Size Guide')}
                  </Typography>
                  <KeyboardArrowRightOutlinedIcon />
                </Button>
              )}
            </Box>
            <Box sx={styles.groupOptions}>
              {groupData?.options?.map((item: any) => {
                const isSelected = groupData?.selected.includes(item);
                if (groupName === 'colour') {
                  return disabled ? (
                    <Box key={item} sx={styles.dotContainer}>
                      <Box
                        sx={[
                          styles.colorDot,
                          styles.disabledColor,
                          { backgroundColor: item },
                        ]}
                      />
                    </Box>
                  ) : (
                    <Button
                      key={item}
                      sx={[
                        styles.dotContainer,
                        isSelected ? styles.selectedDotContainer : {},
                      ]}
                      onClick={() =>
                        isUOM
                          ? handleUOMClick(groupData, item)
                          : handleVariationClick(groupData, item)
                      }
                    >
                      <Box sx={[styles.colorDot, { backgroundColor: item }]} />
                    </Button>
                  );
                } else {
                  return disabled ? (
                    <Box key={item} sx={[styles.outlineButton, styles.customization]}>
                      <Typography variant="body1" sx={styles.disabledButtonText}>
                        {item}
                      </Typography>
                    </Box>
                  ) : (
                    <Button
                      key={item}
                      onClick={() =>
                        isUOM
                          ? handleUOMClick(groupData, item)
                          : handleVariationClick(groupData, item)
                      }
                      // className={`${styles.customization} ${isSelected ? globalStyles.containedButton : styles.outlineButton}`}
                      sx={[
                        styles.customization,
                        isSelected ? globalStyles.containedButton : styles.outlineButton,
                      ]}
                    >
                      <Typography component="div" variant="body1" 
                        className={isSelected ? globalStyles.containedButtonText : {}}
                        sx={[
                          isSelected ? globalStyles.containedButtonText : {},
                        ]}
                      >
                        {item}</Typography>
                    </Button>
                  );
                }
              })}
            </Box>
          </Box>
        );
      })}
      {showSizeChart && (
        <SizeChart
          sizeChart={product?.attributes?.size_chart}
          closeSizeChart={closeSizeChart}
        />
      )}
    </>
  );
};

const useProductStyles = (theme: any): any => ({
  group: {
    mt: 2,
    borderRadius: 2,
    border: `1px solid ${theme.palette.neutral100}`,
    p: 2,
  },
  groupHeader: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    borderBottom: `1px solid ${theme.palette.neutral100}`,
    pb: 1,
  },
  groupTitle: {
    textTransform: 'capitalize',
    color: theme.palette.neutral400,
  },
  sizeGuide: {
    color: theme.palette.primary.main,
    mr: 1,
  },
  sizeChart: {
    display: 'flex',
    alignItems: 'center',
    ml: 2,
  },
  groupOptions: {
    mt: 1.5,
    display: 'flex',
    flexWrap: 'wrap',
    gap: 1,
  },
  customization: {
    textTransform: 'capitalize',
    borderRadius: 1,
    mr: 2,
    py: 1,
    px: 2,
    textAlign: 'center',
    border: `1px solid ${theme.palette.neutral300}`,
  },
  outlineButton: {
    border: `1px solid ${theme.palette.neutral300}`,
  },
  dotContainer: {
    // borderWidth: 2,
    border: '2px solid',
    borderColor: theme.palette.neutral200,
    width: '36px !important',
    minWidth: '36px !important',
    height: 36,
    borderRadius: '50%',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
    padding: 0
  },
  selectedDotContainer: {
    borderColor: theme.palette.primary.main,
  },
  disabledButtonText: {
    color: theme.palette.neutral200,
  },
  colorDot: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },
  disabledColor: {
    opacity: 0.3,
  },
});

const makeGlobalStyles = (theme: any): any => ({
  disabledOutlineButton: {
    borderColor: theme.palette.disabled,
  },
  outlineButton: {
    borderColor: theme.palette.primary.main,
  },
  disabledOutlineButtonText: {
    color: theme.palette.disabled,
  },
  outlineButtonText: {
    color: theme.palette.primary.main,
  },
  disabledContainedButton: {
    backgroundColor: theme.palette.surfaceDisabled,
    borderColor: theme.palette.surfaceDisabled,
  },
  containedButton: {
    backgroundColor: theme.palette.primary.main,
    borderColor: theme.palette.primary.main,
  },
  disabledContainedButtonText: {
    color: theme.palette.text,
  },
  containedButtonText: {
    color: theme.palette.white,
  },
});

export default VariationsRenderer;
