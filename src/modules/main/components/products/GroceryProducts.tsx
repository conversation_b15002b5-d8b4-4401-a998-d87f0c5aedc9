import React, { use<PERSON>allback, useEffect, useMemo, useRef, useState } from "react"
import axios from "axios"
import Box from "@mui/material/Box"
import Grid from "@mui/material/Grid"
import Typography from "@mui/material/Typography"
import IconButton from "@mui/material/IconButton"
import CancelIcon from "@mui/icons-material/Cancel"

import { useSelector } from "react-redux"
import useNetworkHandling from "../../../../hooks/useNetworkHandling"
import useNetworkErrorHandling from "../../../../hooks/useNetworkErrorHandling"
import { API_BASE_URL, GLOBAL_SEARCH_ITEMS } from "../../../../utils/apiActions"
import { BRAND_PRODUCTS_LIMIT } from "../../../../utils/constants"
import ProductSkeleton from "../skeleton/ProductSkeleton"
import ListProduct from "./ListProduct"
import { ProductsListEmptyComponent } from "./Products"
import { emptyAlertCallback } from "../../../../utils/utils"
import { mergeAttributesUrls } from "../../../../utils/productUtils"
import GroceryFilters from "./filter/GroceryFilters"
import AppliedFilters from "./filter/AppliedFilters"
import useCategories from "../../hooks/useCategories"
import { makeStyles } from "@mui/styles"

interface GroceryProducts {
  currentSubCategory: string | null
  searchText: string
  provider: any
  providerDomain?: string
  isOpen?: boolean
  isSearch: boolean
  fbDomain: boolean
  children: any
  tagId?: string
  categoryDomain?: string
  grocery?: boolean
  serviceability?: any
  tts?: any
}

const CancelToken = axios.CancelToken

const GroceryProducts: React.FC<GroceryProducts> = ({
  currentSubCategory = null,
  searchText,
  provider,
  providerDomain,
  isOpen = false,
  grocery = false,
  isSearch,
  children,
  tagId = "",
  categoryDomain = "",
  serviceability = "",
  tts = "",
}) => {
  const sectionListRef = useRef<any>(null)
  const productSearchSource = useRef<any>(null)
  const styles = useStyles()
  const [moreListRequested, setMoreListRequested] = useState<boolean>(true)
  const [searchQuery] = useState<string>("")
  const [page, setPage] = useState<number>(1)
  const [products, setProducts] = useState<any[]>([])
  const [totalProducts, setTotalProducts] = useState<number>(0)
  const [selectedAttributes, setSelectedAttributes] = useState<any>({})
  const { getDataWithWithoutEncode } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const { address } = useSelector((state: any) => state.address)
  const { protocolCategories } = useSelector((state: any) => state.categories)
  const [subCategories, setSubCategories] = useState<any[]>([])
  const [selectedSubCategories, setSelectedSubCategories] = useState<any[]>([])
  const { getMappedSubCategories } = useCategories()
  const handleResponseData = (
    responseData: any,
    total: number,
    pageNumber: number
  ) => {
    if (responseData.length > 0) {
      let listData =
        pageNumber === 1 ? responseData : [...products, ...responseData]
      setPage(pageNumber + 1)
      setTotalProducts(total)
      setProducts(listData)
    } else {
      if (pageNumber === 1) {
        setTotalProducts(0)
        setProducts([])
      }
    }
  }

  const getProducts = async (
    pageNumber: number,
    subCategory: any,
    attributes: any,
    subcategoryIds: any[]
  ) => {
    const queryParams = [
      `pageNumber=${pageNumber}`,
      `limit=${BRAND_PRODUCTS_LIMIT}`,
      `latitude=${address?.address?.lat}`,
      `longitude=${address.address.lng}`,
      `pincode=${address.address.areaCode}`,
      categoryDomain.length > 0 && `category=${categoryDomain}`,
      tagId.length > 0 && `searchTag=${encodeURIComponent(tagId)}`,
      serviceability.length > 0 && `serviceability=${serviceability}`,
      tts && `tts=${tts}`,
      searchText.length > 0 && `name=${searchText}`,
      subCategory && `subcategory=${encodeURIComponent(subCategory)}`,
      subcategoryIds.length > 0 &&
        `&subcategoryIds=${encodeURIComponent(subcategoryIds.join(","))}`,
      mergeAttributesUrls(attributes), // Assuming it returns a valid query string
    ].filter(Boolean) // Remove any falsy values

    const url = `${API_BASE_URL}${GLOBAL_SEARCH_ITEMS}?${queryParams.join("&")}`
    const { data } = await getDataWithWithoutEncode(
      url,
      productSearchSource.current.token
    )
    handleResponseData(data.data, data.count, pageNumber)
  }

  const searchProducts = async (
    pageNumber: number,
    subCategory: any,
    attributes: any,
    subcategoryIds: any[]
  ) => {
    try {
      productSearchSource.current = CancelToken.source()
      await getProducts(pageNumber, subCategory, attributes, subcategoryIds)
    } catch (error) {
      handleApiError(error)
    } finally {
      setMoreListRequested(false)
    }
  }

  const loadMoreList = () => {
    if (products?.length > 0) {
      if (totalProducts !== products?.length) {
        setMoreListRequested(true)
        searchProducts(
          page,
          currentSubCategory,
          selectedAttributes,
          selectedSubCategories
        ).then(emptyAlertCallback)
      }
    }
  }

  const updateSelectedAttributes = (newAttributes: any) => {
    setPage(1)
    setSelectedAttributes(newAttributes)
  }

  const filteredProducts = useMemo(() => {
    const lowerQuery = searchQuery.toLowerCase()
    // Filter the products based on the search query
    return products.filter(
      (product: any) =>
        product?.item_details?.descriptor?.name
          ?.toLowerCase()
          .includes(lowerQuery) ||
        product?.provider_details?.descriptor?.name
          ?.toLowerCase()
          .includes(lowerQuery)
    )
  }, [products, searchQuery])

  const renderFlatListItem = useCallback(
    ({ item }: { item: any }) => (
      <ListProduct
        product={item}
        isOpen={isOpen}
        listView={false}
        grocery={grocery}
        provider={provider}
        openDetailsPage={false}
      />
    ),
    [provider]
  )

  const updateSelectedSubCategories = (code: string) => {
    const list = [...selectedSubCategories]
    list.push(code)
    setSelectedSubCategories(list)
  }

  const removeFromSelectedSubCategories = (code: string) => {
    setSelectedSubCategories(
      selectedSubCategories.filter((one) => one !== code)
    )
  }

  const getSubCategories = async (subCategory: string) => {
    setSubCategories([])
    setSelectedSubCategories([])
    try {
      const list = await getMappedSubCategories(categoryDomain, subCategory)
      if (list && list.hasOwnProperty(categoryDomain)) {
        setSubCategories(list[categoryDomain])
      }
    } catch (error: any) {
      console.log(error)
    }
  }

  const ListFooterComponent = useMemo(() => {
    return moreListRequested ? <ProductSkeleton /> : null
  }, [moreListRequested])

  useEffect(() => {
    if (sectionListRef?.current) {
      try {
        sectionListRef?.current?.scrollToLocation({
          sectionIndex: 0,
          itemIndex: 0,
          animated: true,
        })
      } catch (error: any) {
        console.log(error)
      }
    }
    setProducts([])
    setMoreListRequested(true)
    searchProducts(
      1,
      currentSubCategory,
      selectedAttributes,
      selectedSubCategories
    ).then(emptyAlertCallback)
  }, [
    selectedAttributes,
    categoryDomain,
    currentSubCategory,
    selectedSubCategories,
  ])

  useEffect(() => {
    setSelectedAttributes({})
    if (currentSubCategory) {
      getSubCategories(currentSubCategory).then(emptyAlertCallback)
    }
  }, [currentSubCategory])

  useEffect(() => {
    if (categoryDomain && protocolCategories) {
      setSelectedSubCategories([])
    }
  }, [categoryDomain])

  return (
    <>
      <Box>
        {children}
        <Box
          sx={{
            display: "flex",
            overflowX: "auto",
            gap: 1,
            pl: 2,
            pt: 1,
          }}
        >
          <GroceryFilters
            isSearch={isSearch}
            searchText={searchText}
            selectedAttributes={selectedAttributes}
            setSelectedAttributes={updateSelectedAttributes}
            providerId={null}
            searchTag={tagId}
            category={currentSubCategory ? currentSubCategory : null}
            providerDomain={providerDomain ?? null}
          />
          {subCategories?.map((subCategory: any) => {
            const selectedIndex = selectedSubCategories.findIndex(
              (selected) => selected === subCategory.code
            )
            if (selectedIndex > -1) {
              return (
                <Box key={subCategory.code} sx={{ display: "flex", pr: 1 }}>
                  <Box className={styles.selectedCategory}>
                    <Typography
                      variant="labelSmall"
                      className={styles.categoryText}
                    >
                      {subCategory.label}
                    </Typography>
                    <IconButton
                      onClick={() =>
                        removeFromSelectedSubCategories(subCategory.code)
                      }
                      size="small"
                      className={styles.closeButton}
                    >
                      <CancelIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Box>
              )
            }
            return (
              <Box
                key={subCategory.code}
                className={styles.category}
                onClick={() => updateSelectedSubCategories(subCategory.code)}
              >
                <Typography
                  variant="labelSmall"
                  className={styles.categoryText}
                >
                  {subCategory.label}
                </Typography>
              </Box>
            )
          })}
        </Box>
      </Box>
      <Box>
        <AppliedFilters
          selectedAttributes={selectedAttributes}
          setSelectedAttributes={setSelectedAttributes}
        />
      </Box>
      <Box className={styles.container}>
        <Grid container spacing={2} className={styles.nestedListContainer}>
          {filteredProducts.length === 0 && !moreListRequested ? (
            <ProductsListEmptyComponent />
          ) : (
            filteredProducts.map((item: any) => (
              <Grid item xs={6} key={item.id}>
                <ListProduct
                  product={item}
                  isOpen={isOpen}
                  listView={false}
                  grocery={grocery}
                  provider={provider}
                  openDetailsPage={false}
                />
              </Grid>
            ))
          )}
          {moreListRequested && (
            <Grid item xs={12}>
              <ProductSkeleton />
            </Grid>
          )}
        </Grid>
      </Box>
    </>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
    paddingTop: 20,
  },
  nestedListContainer: {
    flexGrow: 1,
    paddingRight: 16,
    paddingLeft: 16,
  },
  emptyContainer: {
    display: "flex",
    flex: 1,
    justifyContent: "center",
    height: "100%",
  },
  emptyMessageContainer: {
    display: "flex",
    alignItems: "center",
  },
  listContainer: {},
  columnWrapper: {
    display: "flex",
    justifyContent: "space-between",
    gap: 16,
  },
  emptyTitle: {
    color: theme.palette.neutral400,
  },
  emptyMessage: {
    marginTop: 8,
    color: theme.palette.neutral400,
    width: 260,
    textAlign: "center",
  },
  filtersContainer: {
    gap: 5,
    paddingLeft: 16,
    paddingTop: 5,
  },
  selectedCategory: {
    display: "flex",
    backgroundColor: "#E2F6FF",
    border: "1px solid" + theme.palette.primary.main,
    borderRadius: 7,
    paddingRight: 7,
    paddingLeft: 7,
    alignItems: "center",
    justifyContent: "center",
    height: 32,
    whiteSpace: "nowrap",
    position: "relative",
  },
  category: {
    display: "flex",
    border: "1px solid" + theme.palette.primary.main,
    borderRadius: 7,
    paddingRight: 7,
    paddingLeft: 7,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
    whiteSpace: "nowrap",
  },
  categoryText: {
    color: theme.palette.primary.main,
  },
  closeButtonChip: {
    paddingRight: 8,
  },
  closeButton: {
    position: "absolute",
    right: -6,
    marginTop: -30,
    backgroundColor: theme.palette.white,
    borderRadius: 10,
    width: 20,
    height: 20,
  },
}))

export default GroceryProducts
