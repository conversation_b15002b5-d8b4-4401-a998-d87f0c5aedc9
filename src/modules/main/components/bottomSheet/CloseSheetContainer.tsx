import React from "react";
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import CloseIcon from "@mui/icons-material/Close";
import {makeStyles} from "@mui/styles";

const CloseSheetContainer = ({
                               closeSheet,
                               children,
                             }: {
  closeSheet: () => void;
  children: React.ReactNode;
}) => {
  const styles = useStyles();

  return (
    <Box className={styles.backdrop}>
      <Box className={styles.closeSheet}>
        <IconButton onClick={closeSheet} className={styles.closeButton}>
          <CloseIcon sx={{color: '#fff'}}/>
        </IconButton>
      </Box>
      <Box className={styles.sheetWrapper}>
        {children}
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  backdrop: {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1600,
    backgroundColor: 'rgba(0,0,0,0.4)',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
  },
  closeSheet: {
    position: 'absolute',
    top: '9%',
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: 1601,
  },
  closeButton: {
    width: 48,
    height: 48,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 2px 6px rgba(0,0,0,0.3)',
    '&:hover': {
      backgroundColor: 'rgba(0, 0, 0, 0.85)',
    },
  },
  sheetWrapper: {
    height: '85%',
    backgroundColor: theme.palette.common.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    boxShadow: '0px -2px 10px rgba(0, 0, 0, 0.1)',
    overflowY: 'auto',
    width: '100vw'
  },
}));

export default CloseSheetContainer;
