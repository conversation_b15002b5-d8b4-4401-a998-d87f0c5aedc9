import {useCallback, useEffect, useState} from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {useTranslation} from 'react-i18next';
import {makeStyles} from "@mui/styles";
import Slider from 'react-slick';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import Coupon from '../../../../../assets/coupon.svg';
import useCoupons from '../../../hooks/useCoupons';
import CouponSkeleton from './CouponSkeleton';
import CouponsSheet from '../CouponsSheet';

const OutletCoupons = ({bppId, domain, providerId}: { bppId: string; domain: string; providerId: string; }) => {
  const styles = useStyles();
  const {t} = useTranslation();
  const {getCoupons} = useCoupons();
  const [coupons, setCoupons] = useState<any[]>([]);
  const [couponRequested, setCouponRequested] = useState<boolean>(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [showSheet, setShowSheet] = useState(false);

  const viewAllCoupons = useCallback(() => {
    setShowSheet(true);
  }, []);

  const closeSheet = () => setShowSheet(false);

  useEffect(() => {
    setCouponRequested(true);
    getCoupons(bppId, domain, providerId)
      .then((list: any) => {
        setCoupons(list);
        setCouponRequested(false);
      })
      .catch(() => {
        setCouponRequested(false);
      });
  }, []);

  if (couponRequested) {
    return <CouponSkeleton/>;
  }

  if (coupons.length === 0) {
    return null;
  }

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    arrows: true,
  };

  if (coupons.length === 1) {
    const coupon = coupons[0];
    return (
      <Box key={coupon.id} className={styles.couponContainer}>
        <Box className={styles.coupon}>
          <Box className={styles.metaContainer}>
            <img src={Coupon} height={40} width={40} alt={coupon?.offerId}/>
            <Box className={styles.meta}>
              <Typography variant="labelLarge" component="div" className={styles.textColor} noWrap>
                {coupon?.offerId}: {coupon?.shortDescription}
              </Typography>
              <Button size="small" className={styles.viewMoreText} onClick={viewAllCoupons}>
                {t('Coupon.View More')}
              </Button>
            </Box>
          </Box>
          <Box className={styles.slideIndicator}>
            <Typography variant="labelSmall" className={styles.textColor} noWrap>
              {1}/{coupons.length}
            </Typography>
            <Box className={styles.dotsContainer}>
              {[0, 1, 2].map((dotIndex) => (
                <Box
                  key={dotIndex}
                  className={styles.dot}
                  sx={{backgroundColor: dotIndex === currentSlide ? "primary.main" : "grey.300"}}
                />
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
    )
  }

  return (
    <Box className={styles.container}>
      <Slider {...settings}>
        {coupons.slice(0, 3).map((coupon: any, index: number) => (
          <Box key={coupon.id} className={styles.couponContainer}>
            <Box className={styles.coupon}>
              <Box className={styles.metaContainer}>
                <img src={Coupon} height={40} width={40} alt={coupon?.offerId}/>
                <Box className={styles.meta}>
                  <Typography variant="labelLarge" component="div" className={styles.textColor} noWrap>
                    {coupon?.offerId}: {coupon?.shortDescription}
                  </Typography>
                  <Button size="small" className={styles.viewMoreText} onClick={viewAllCoupons}>
                    {t('Coupon.View More')}
                  </Button>
                </Box>
              </Box>
              <Box className={styles.slideIndicator}>
                <Typography variant="labelSmall" className={styles.textColor} noWrap>
                  {index + 1}/{coupons.length}
                </Typography>
                <Box className={styles.dotsContainer}>
                  {[0, 1, 2].map((dotIndex) => (
                    <Box
                      key={dotIndex}
                      className={styles.dot}
                      sx={{backgroundColor: dotIndex === currentSlide ? "primary.main" : "grey.300"}}
                    />
                  ))}
                </Box>
              </Box>
            </Box>
          </Box>
        ))}
      </Slider>
      {showSheet && (
        <>
          {/* <Box className={styles.backdrop} onClick={closeSheet} /> */}
          <Box className={styles.sheetWrapper}>
            <CouponsSheet coupons={coupons} closeSheet={closeSheet}/>
          </Box>
        </>
      )}
    </Box>
  );
};
const useStyles = makeStyles<any>((theme) => ({
  container: {
    backgroundColor: theme.palette.common.white,
    marginTop: 10,
    position: 'relative',
  },
  couponContainer: {
    overflow: 'hidden',
  },
  coupon: {
    display: 'flex',
    padding: '12px',
    border: '1px solid' + theme.palette.neutral100,
    borderRadius: '12px',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  metaContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  meta: {
    flex: 1,
  },
  textColor: {
    color: theme.palette.neutral300,
  },
  viewMoreText: {
    color: theme.palette.primary.main,
    paddingTop: '4px',
  },
  slideIndicator: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: "4px",
  },
  dotsContainer: {
    display: "flex",
    gap: "4px",
    marginTop: "2px",
  },
  dot: {
    width: "8px",
    height: "8px",
    borderRadius: "50%",
  },
  sheetWrapper: {
    position: 'fixed',
    bottom: 0,
    left: 0,
    right: 0,
    height: '85%',
    backgroundColor: 'white',
    zIndex: 1500,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    boxShadow: '0px -2px 10px rgba(0, 0, 0, 0.1)',
    overflowY: 'auto',
    overflow: 'visible',
  },
  backdrop: {
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
    height: '100dvh',
    backgroundColor: 'rgba(59, 57, 57, 0.5)', // deeper grey
    zIndex: 1400,
  },

}));

export default OutletCoupons;
