import React, {useCallback, useMemo} from "react";
import Box from '@mui/material/Box';
import ButtonBase from '@mui/material/ButtonBase';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import {useNavigate} from "react-router-dom";
import WatchLaterIcon from '@mui/icons-material/WatchLater';
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIosRounded";
import {useTheme} from "@mui/material/styles";
import useMinutesToString from "../../../hooks/useMinutesToString";
import ListProduct from "../../products/ListProduct";
import {getNextStartTime, isCurrentTimeInRange} from "../../../../../utils/utils";
import OpensAtPopup from "./OpensAtPopup";
import NoImageAvailable from "../../../../../assets/noImage.png";
import {makeStyles} from "@mui/styles";
import {distributeProviderId} from "../../../../../utils/utils";

const Provider = React.memo(({provider}: { provider: any }) => {
  const navigate = useNavigate();
  const theme = useTheme();
  const styles = useStyles();
  const {convertMinutesToHumanReadable, translateMinutesToHumanReadable} = useMinutesToString();

  const navigateToProviderDetails = () => {
    if (!!provider.items && provider.items.length > 0) {
      const latestItem = provider.items[0];
      const brandOutletId = latestItem.location_details?.local_id;
      const {bppId, domain, providerId} = distributeProviderId(latestItem.provider_details?.id);
      navigate(`/store?domain=${domain}&provider_id=${providerId}&bpp_id=${bppId}&locationId=${brandOutletId}`);
    }
  };

  const {timeToShip, locality, imageSource, isOpen, timeFrom} = useMemo(() => {
    let source: any = NoImageAvailable;
    const open = isCurrentTimeInRange(provider.location_availabilities);

    if (provider?.descriptor?.symbol) {
      source = {uri: provider?.descriptor?.symbol};
    } else if (provider?.descriptor?.images?.length > 0) {
      source = {uri: provider?.descriptor?.images[0]};
    }

    if (provider && provider?.items.length > 0) {
      const locationDetails = provider?.items[0].location_details;
      return {
        timeToShip: convertMinutesToHumanReadable(Number(provider?.minDaysWithTTS / 60)),
        locality: locationDetails.address?.locality || "NA",
        imageSource: source,
        isOpen: open,
        timeFrom: getNextStartTime(provider.location_availabilities),
      };
    } else {
      return {
        timeToShip: {type: "minutes", time: 0},
        locality: "",
        imageSource: source,
        isOpen: open,
        timeFrom: getNextStartTime(provider.location_availabilities),
      };
    }
  }, [convertMinutesToHumanReadable, provider]);

  const renderItem = useCallback(
    (item: any) => (
      <ListProduct product={item} isOpen={isOpen} provider={provider} smallView openDetailsPage={true}/>
    ),
    [isOpen, provider]
  );

  return (
    <Box sx={{position: 'relative'}}>
      {!isOpen && <OpensAtPopup timeFrom={timeFrom}/>}
      <Box className={styles.container}>
        <ButtonBase className={styles.header} onClick={navigateToProviderDetails}>
          <Box
            component="img"
            src={imageSource.uri || NoImageAvailable}
            alt="Provider Image"
            sx={{
              filter: !isOpen ? "grayscale(100%)" : "none"
            }}
            className={styles.image}
          />

          <Box className={styles.providerMeta}>
            <Typography variant="titleLarge" component="div" className={styles.providerName} noWrap>
              {provider?.descriptor?.name}
            </Typography>

            <Box className={styles.providerLocalityView}>
              <Typography variant="labelSmall" className={styles.providerLocality} noWrap>
                {locality}
              </Typography>

              <Box className={styles.providerLocalityView}>
                <Box className={styles.dotView}/>
                <WatchLaterIcon fontSize="small" sx={{color: theme.palette.grey[500]}}/>
                <Typography variant="labelSmall" className={styles.distance}>
                  {translateMinutesToHumanReadable(timeToShip.type, timeToShip.time)}
                </Typography>
              </Box>
            </Box>
          </Box>

          <IconButton>
            <ArrowForwardIosIcon sx={{color: theme.palette.grey[600]}}/>
          </IconButton>
        </ButtonBase>

        <Stack direction="row" spacing={2} sx={{overflowX: "auto"}}>
          {provider?.items?.map((item: any) => (
            <Box key={item.id}>{renderItem(item)}</Box>
          ))}
        </Stack>
      </Box>
    </Box>
  );
});

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: "16px",
    padding: "12px",
    border: `0.5px solid ${theme.palette.neutral200}`,
    marginBottom: "16px",
  },
  header: {
    display: "flex",
    alignItems: "center",
    width: "100%",
    marginBottom: "20px !important",
  },
  image: {
    width: "48px",
    height: "48px",
    borderRadius: "8px",
    marginRight: "12px",
    marginLeft: '5px',
    objectFit: 'contain'
  },
  providerMeta: {
    flex: 1,
    paddingRight: "8px",
    width: '100px'
  },
  providerName: {
    textAlign: 'left',
    marginBottom: "8px",
    color: theme.palette.neutral500,
  },
  providerLocalityView: {
    display: "flex",
    alignItems: "center",
  },
  dotView: {
    height: "3px",
    width: "3px",
    borderRadius: "50%",
    backgroundColor: theme.palette.neutral300,
    marginLeft: "5px",
    marginRight: "5px",
  },
  providerLocality: {
    color: theme.palette.neutral300,
    flexShrink: 1,
    overflow: "hidden",
    textOverflow: "ellipsis",
    maxWidth: '40%',
    textAlign: 'left'
  },
  distance: {
    color: theme.palette.neutral300,
    marginLeft: "3px !important",
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
  },
}));

export default Provider;
