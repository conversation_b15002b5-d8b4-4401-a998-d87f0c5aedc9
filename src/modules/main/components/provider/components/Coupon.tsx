import React, {useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {makeStyles} from '@mui/styles';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CouponDetails from '../../../modules/cart/provider/components/CouponDetails';
import CouponIcon from '../../../../../assets/coupon.svg';

interface CouponProps {
  coupon: any;
}

const Coupon: React.FC<CouponProps> = ({coupon}) => {
  const styles = useStyle();
  const [detailsVisible, setDetailsVisible] = useState(false);
  const {t} = useTranslation();
  const [clipboardContent, setClipboardContent] = useState('');

  useEffect(() => {
    const checkClipboard = async () => {
      try {
        const text = await navigator.clipboard.readText();
        if (text !== clipboardContent) {
          setClipboardContent(text);
        }
      } catch (err) {
        // Clipboard access might be denied
      }
    };

    const intervalId = setInterval(checkClipboard, 1000);
    return () => clearInterval(intervalId);
  }, [clipboardContent]);

  const toggleDetailsView = () => {
    setDetailsVisible(!detailsVisible);
  };

  const copyCoupon = async () => {
    try {
      await navigator.clipboard.writeText(coupon.offerId);
      setClipboardContent(coupon.offerId);
    } catch (err) {
      console.log(err)
    }
  };

  return (
    <Box className={styles.container}>
      <Box className={styles.subCard}>
        <img src={CouponIcon} alt="Coupon" height={24} width={24}/>
        <Box className={styles.metaInformation}>
          <Typography variant="titleLarge" className={styles.title}>
            {coupon.shortDescription}
          </Typography>
          <Typography variant="labelSmall" className={styles.description}>
            {coupon.description}
          </Typography>
          <Box className={styles.bottomLine}>
            <Typography className={styles.codeText}>
              {coupon?.offerId}
            </Typography>
            <Button onClick={toggleDetailsView} className={styles.toggleButton}>
              <Typography variant="labelSmall" className={styles.toggleText}>
                {detailsVisible
                  ? t('Coupon.Hide Details')
                  : t('Coupon.View Details')}
              </Typography>
              {detailsVisible ? <ExpandLessIcon fontSize="small"/> : <ExpandMoreIcon fontSize="small"/>}
            </Button>
          </Box>
        </Box>
      </Box>

      <CouponDetails coupon={coupon} detailsVisible={detailsVisible}/>

      {clipboardContent === coupon.offerId ? (
        <Box className={`${styles.button} ${styles.successButton}`}>
          <Typography className={styles.successButtonText}>
            {t('Coupon.Copied')}
          </Typography>
        </Box>
      ) : (
        <Button onClick={copyCoupon} className={styles.button}>
          <Typography className={styles.activeButtonText}>
            {t('Coupon.Copy')}
          </Typography>
        </Button>
      )}
    </Box>
  );
};

const useStyle = makeStyles((theme: any) => ({
  container: {
    borderRadius: 12,
    border: `1px solid ${theme.palette.neutral100}`,
    overflow: 'hidden',
    marginBottom: 20
  },
  subCard: {
    display: 'flex',
    flexDirection: 'row',
    padding: 12,
    gap: 16,
  },
  title: {
    color: theme.palette.neutral400,
  },
  description: {
    color: theme.palette.primary.main,
  },
  metaInformation: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    gap: 12,
  },
  bottomLine: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  codeText: {
    border: `1px solid ${theme.palette.neutral100}`,
    borderRadius: 6,
    padding: '4px 8px',
  },
  toggleText: {
    color: theme.palette.neutral400,
  },
  toggleButton: {
    display: 'flex',
    alignItems: 'center',
    gap: 4,
    textTransform: 'none',
  },
  button: {
    display: 'flex',
    padding: 12,
    backgroundColor: theme.palette.white,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    borderTop: `1px solid ${theme.palette.neutral100}`,
    borderRadius: 0,
  },
  activeButtonText: {
    color: theme.palette.primary.main,
  },
  successButton: {
    backgroundColor: theme.palette.background.paper,
    borderTop: `1px solid ${theme.palette.neutral100}`,
  },
  successButtonText: {
    color: theme.palette.success.main,
  },
}));

export default Coupon;
