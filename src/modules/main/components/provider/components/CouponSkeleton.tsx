import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';
import {makeStyles} from "@mui/styles";

const CouponSkeleton = () => {
  const styles = useStyles();

  return (
    <Box className={styles.card}>
      <Box className={styles.header}>
        <Skeleton variant="circular" className={styles.image}/>
        <Box className={styles.meta}>
          <Box className={styles.marginBottom}>
            <Skeleton variant="rectangular" className={styles.name}/>
          </Box>
          <Skeleton variant="rectangular" className={styles.name}/>
        </Box>
        <Skeleton variant="rectangular" className={styles.count}/>
      </Box>
    </Box>
  );
};

export const useStyles = makeStyles<any>((theme) => ({
  card: {
    paddingHorizontal: 16,
    borderRadius: 12,
    borderColor: theme.palette.neutral100,
    borderWidth: 1,
    height: 56,
    marginTop: 12,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    paddingVertical: 8,
    gap: 8,
  },
  meta: {
    flex: 1,
  },
  name: {
    height: 18,
    width: 150,
  },
  image: {
    width: 32,
    height: 32,
  },
  count: {
    width: 20,
    height: 32,
  },
  marginBottom: {
    marginBottom: 3,
  },
}));

export default CouponSkeleton;
