import React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import ClearIcon from '@mui/icons-material/ClearRounded';
import {makeStyles} from "@mui/styles";
import {useTheme} from "@mui/material/styles";

interface FBFilterProps {
  selectedFilter: string;
  label: string;
  value: string;
  setSelectedFilter: (value: string) => void;
  ImageSource: React.ElementType;
}

const FBFilter: React.FC<FBFilterProps> = ({
                                             selectedFilter,
                                             label,
                                             value,
                                             setSelectedFilter,
                                             ImageSource,
                                           }) => {
  const styles = useStyles();
  const theme = useTheme();

  return (
    <Button
      className={`${styles.filter} ${
        selectedFilter === value ? styles.selectedFilter : ''
      }`}
      onClick={() =>
        selectedFilter === value ? setSelectedFilter('') : setSelectedFilter(value)
      }
    >
      <ImageSource/>
      <Typography variant="labelSmall" className={styles.filterLabel}>
        {label}
      </Typography>
      {selectedFilter === value ? (
        <ClearIcon fontSize="small" sx={{color: theme.palette.primary}} className={styles.icon}/>
      ) : (
        <Box className={styles.emptyIcon}/>
      )}
    </Button>
  );
};

export const useStyles = makeStyles<any>((theme) => ({
  filter: {
    borderRadius: '8px !important',
    borderWidth: '1px  !important',
    borderStyle: "solid  !important",
    borderColor: `${theme.palette.neutral100} !important`,
    padding: "6px 12px !important",
    display: "flex !important",
    flexDirection: "row",
    alignItems: "center !important",
    gap: 4,
  },
  selectedFilter: {
    backgroundColor: `${theme.palette.primary50} !important`,
    borderColor: `${theme.palette.primary.main} !important`,
  },
  filterIcon: {
    width: 20,
    height: 20,
  },
  filterLabel: {
    color: theme.palette.neutral400,
    marginLeft: 4,
  },
  icon: {
    marginLeft: 12,
  },
  emptyIcon: {
    height: 20,
  },
}));

export default FBFilter;
