import ExpandMoreIcon from "@mui/icons-material/ExpandMoreRounded"
import Accordion from "@mui/material/Accordion"
import AccordionDetails from "@mui/material/AccordionDetails"
import AccordionSummary from "@mui/material/AccordionSummary"
import Box from "@mui/material/Box"
import Divider from "@mui/material/Divider"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useEffect, useMemo, useState } from "react"
import ListProduct from "../../../components/products/ListProduct"

interface CustomMenuAccordionProps {
  section: any
  provider: any
  defaultExpand: boolean
  isOpen: boolean
  ref?: any
  showProductDetails?: boolean
  productId?: string
}

const CustomMenuAccordion: React.FC<CustomMenuAccordionProps> = ({
  section,
  provider,
  defaultExpand,
  isOpen,
  ref,
  showProductDetails,
  productId = "",
}) => {
  const [expanded, setExpanded] = useState<boolean>(defaultExpand)

  useEffect(() => {
    setExpanded(defaultExpand)
  }, [defaultExpand])

  const styles = useStyles()

  const handleAccordionClick = () => setExpanded(!expanded)

  let itemLength = useMemo(() => section?.items?.length, [section])

  return (
    <Accordion
      ref={ref}
      expanded={expanded}
      onChange={handleAccordionClick}
      className={styles.accordion}
    >
      <AccordionSummary
        className={styles.accordionHeader}
        expandIcon={<ExpandMoreIcon />}
      >
        <Typography variant="titleLarge" className={styles.heading}>
          {section?.descriptor?.name} {itemLength > 0 ? `(${itemLength})` : ""}
        </Typography>
      </AccordionSummary>
      <AccordionDetails className={styles.accordionDetails}>
        {section?.items?.map((item: any, index: number) => (
          <Box key={item.id} width="100%">
            <ListProduct
              product={item}
              provider={provider}
              isOpen={isOpen}
              listView
              openDetailsPage={false}
              isShowProductDetails={
                showProductDetails && item?.id === productId
              }
            />
            {itemLength === index + 1 ? (
              <Box className={styles.lastItem} />
            ) : (
              <Divider className={styles.itemSeparator} />
            )}
          </Box>
        ))}
      </AccordionDetails>
    </Accordion>
  )
}

export const useStyles = makeStyles<any>((theme) => ({
  heading: {
    color: theme.palette.neutral400,
  },
  itemSeparator: {
    marginVertical: 24,
    backgroundColor: theme.palette.neutral100,
    height: 1,
  },
  lastItem: {
    marginBottom: 24,
  },
  accordion: {
    padding: 0,
    backgroundColor: theme.palette.white,
    boxShadow: "none !important",
  },
  accordionHeader: {
    padding: 0,
    "& .MuiAccordionSummary-content": {
      margin: 0,
    },
  },
  accordionDetails: {
    padding: 0,
  },
}))

export default CustomMenuAccordion
