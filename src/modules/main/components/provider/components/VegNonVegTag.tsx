import React from 'react';
import VegIcon from '../../../../../assets/foodType/veg.svg';
import NonVegIcon from '../../../../../assets/foodType/non_veg.svg';

const VegNonVegTag = ({category}: { category: string }) => {
  let iconSrc = '';

  switch (category.toLowerCase()) {
    case 'veg':
      iconSrc = VegIcon;
      break;
    case 'non_veg':
    case 'egg':
      iconSrc = NonVegIcon;
      break;
    default:
      return null;
  }

  return <img src={iconSrc} alt={category} width={18} height={18}/>;
};

export default VegNonVegTag;
