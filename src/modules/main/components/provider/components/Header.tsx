import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import InputAdornment from "@mui/material/InputAdornment"
import { useTheme } from "@mui/material/styles"
import TextField from "@mui/material/TextField"
import { makeStyles } from "@mui/styles"
import React, { useRef } from "react"
import { useNavigate } from "react-router-dom"

import ArrowBackIcon from "@mui/icons-material/ArrowBackRounded"
import ClearIcon from "@mui/icons-material/ClearRounded"
import SearchIcon from "@mui/icons-material/SearchRounded"
import CartAction from "../../../modules/dashboard/components/header/CartAction"
import WishListAction from "../../../modules/dashboard/components/header/WishListAction"

interface HeaderProps {
  searchQuery: string
  setSearchQuery: (values: any) => void
}

const Header: React.FC<HeaderProps> = ({ searchQuery, setSearchQuery }) => {
  const theme = useTheme()
  const styles = useStyles()
  const searchSource = useRef<HTMLInputElement | null>(null)
  const navigate = useNavigate()

  const goBack = () => navigate(-1) // Equivalent to useBackHandler

  const onClearIconPress = () => {
    setSearchQuery("")
  }

  return (
    <Box className={styles.headerContainer}>
      <ArrowBackIcon className={styles.searchIcon} onClick={goBack} />
      <Box className={styles.searchBar}>
        {!searchQuery && <SearchIcon className={styles.searchIcon} />}
        <TextField
          inputRef={searchSource}
          variant="standard"
          fullWidth
          placeholder="Search"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            disableUnderline: true,
            endAdornment: searchQuery && (
              <InputAdornment position="end">
                <IconButton onClick={onClearIconPress} size="small">
                  <ClearIcon sx={{ color: theme.palette.neutral300 }} />
                </IconButton>
              </InputAdornment>
            ),
          }}
          onKeyDown={(event) => {
            if (event.key === "Enter") {
              event.preventDefault() // Prevent form submit or blur
              event.target?.blur()
            }
          }}
        />
      </Box>
      <Box className={styles.iconRow}>
        <WishListAction color={theme.palette.primary.main} />
        <CartAction color={theme.palette.primary.main} />
      </Box>
    </Box>
  )
}

export const useStyles = makeStyles<any>((theme) => ({
  headerContainer: {
    padding: "16px",
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    display: "flex",
    alignItems: "center",
  },
  topRow: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  locationBox: {
    display: "flex",
    alignItems: "center",
    gap: 6,
    color: "white",
  },
  locationText: {
    fontSize: 14,
    fontWeight: 500,
    color: "white",
  },
  iconRow: {
    display: "flex",
    alignItems: "center",
    gap: 20,
    paddingLeft: 20,
  },
  searchBar: {
    display: "flex",
    alignItems: "center",
    backgroundColor: "white",
    padding: "8px 14px",
    borderRadius: 30,
    borderWidth: 1,
    borderColor: "rgba(25, 106, 171, 0.19)",
    borderStyle: "solid",
    gap: 10,
    flex: 1,
  },
  searchIcon: {
    color: theme.palette.primary.main,
    marginRight: "8px !important",
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
  actionIcon: {
    color: theme.palette.primary.main,
  },
  clearIcon: {
    color: "#666",
  },
  whiteIcon: {
    color: "white",
  },
}))

export default Header
