import ChevronRight from "@mui/icons-material/ChevronRight"
import InfoOutlined from "@mui/icons-material/InfoOutlined"
import KeyboardArrowDown from "@mui/icons-material/KeyboardArrowDown"
import AccessTime from "@mui/icons-material/WatchLater"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Dialog from "@mui/material/Dialog"
import DialogContent from "@mui/material/DialogContent"
import IconButton from "@mui/material/IconButton"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useState } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"

import { Share } from "@mui/icons-material"
import StoreIcon from "../../../../../assets/no_store_icon.svg"
import useStoreImageTime from "../../../../../hooks/useStoreImageTime"
import { parseBrandOutletId } from "../../../../../utils/utils"
import BrandSkeleton from "../../../components/skeleton/BrandSkeleton"
import useMinutesToString from "../../../hooks/useMinutesToString"
import OutletCoupons from "./OutletCoupons"

const Closed = require("../../../../../assets/closed.png")
const NoImageAvailable = require("../../../../../assets/no_store.png")

interface OutletDetailsProps {
  provider: any
  outlet: any
  apiRequested: boolean
}

const OutletImage = ({ source, isOpen }: { source: any; isOpen: boolean }) => {
  const styles = useOutletStyles()
  const [imageSource, setImageSource] = useState(source)

  const onError = () => {
    setImageSource(NoImageAvailable)
  }

  return source ? (
    <img
      src={imageSource}
      alt="Outlet"
      className={styles.headerImage}
      onError={onError}
      style={isOpen ? {} : { filter: "grayscale(100%)" }}
    />
  ) : (
    <Box className={`${styles.headerImage} ${styles.brandImageEmpty}`}>
      <img src={StoreIcon} width={48} height={48} />
    </Box>
  )
}

const OutletDetails: React.FC<OutletDetailsProps> = ({
  provider,
  outlet,
  apiRequested,
}) => {
  const { t } = useTranslation()
  const { translateMinutesToHumanReadable } = useMinutesToString()
  const theme = useTheme()
  const styles = useOutletStyles()
  const navigate = useNavigate()
  const [openDialog, setOpenDialog] = useState(false)
  const { timeToShip, imageSource } = useStoreImageTime(outlet)

  const moveOnStoreInfo = () => {
    navigate("/store-info", { state: { provider, outlet } })
  }
  const onShareBrandDetails = async () => {
    const brandOutletId = outlet.id
    const result = parseBrandOutletId(brandOutletId)
    const shareUri = `https://digihaat.in/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${brandOutletId}`
    const shareData = {
      text: `Thousands are shopping at ${provider?.descriptor?.name}`,
      url: shareUri,
    }

    try {
      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        // Fallback for browsers that don't support Web Share API
        await navigator.clipboard.writeText(`${shareData.text}\n${shareUri}`)
        alert("Link copied to clipboard!")
      }
    } catch (err) {
      console.error("Error sharing:", err)
    }
  }
  if (apiRequested) {
    return <BrandSkeleton />
  }

  return (
    <>
      <Box>
        {!outlet?.isOpen && (
          <img src={Closed} alt="Closed" className={styles.brandImage} />
        )}
        <Box className={styles.brandDetails}>
          <Box className={styles.providerDetails}>
            <Box className={styles.titleView}>
              <Typography variant="titleLarge" className={styles.title} noWrap>
                {provider?.descriptor?.name}
              </Typography>
              <IconButton onClick={onShareBrandDetails}>
                <Share
                  fontSize="medium"
                  style={{ color: theme.palette.primary.main }}
                />
              </IconButton>
            </Box>
            <Box className={styles.localityView}>
              <Typography
                variant="labelLarge"
                className={styles.address}
                noWrap
              >
                {outlet?.address?.locality
                  ? `${outlet?.address?.locality},`
                  : ""}{" "}
                {outlet?.address?.city}
              </Typography>
              <IconButton onClick={moveOnStoreInfo}>
                <InfoOutlined
                  fontSize="small"
                  style={{ color: theme.palette.neutral400 }}
                />
              </IconButton>
              {outlet?.locations?.length > 1 && (
                <IconButton onClick={() => setOpenDialog(true)}>
                  <KeyboardArrowDown
                    fontSize="medium"
                    style={{ color: theme.palette.neutral400 }}
                  />
                </IconButton>
              )}
            </Box>
            <Box className={styles.providerLocalityView}>
              <AccessTime
                fontSize="small"
                style={{ color: theme.palette.neutral200 }}
              />
              <Typography variant="labelLarge" className={styles.address}>
                {translateMinutesToHumanReadable(
                  timeToShip.type,
                  timeToShip.time
                )}
              </Typography>
              {!outlet?.isOpen && (
                <>
                  <Box className={styles.dotView} />
                  <Typography variant="titleMedium" className={styles.address}>
                    {t("Store.Opens at", { time: outlet?.time_from })}
                  </Typography>
                </>
              )}
            </Box>
          </Box>
          <OutletImage source={imageSource} isOpen={outlet?.isOpen} />
        </Box>
        <OutletCoupons
          bppId={provider.context.bpp_id}
          domain={provider.domain}
          providerId={provider.id}
        />
        <Box className={styles.borderBottom} />
      </Box>

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)}>
        <DialogContent>
          <Typography variant="headlineSmall">{t("Store.Outlets")}</Typography>
          {outlet?.locations?.map((item: any, index: number) => (
            <Button
              key={index}
              onClick={() => {
                const brandOutletId = item?.id
                const result = parseBrandOutletId(brandOutletId)

                navigate(
                  `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${brandOutletId}`
                )
              }}
            >
              {item?.address?.locality}
              <ChevronRight fontSize="medium" />
            </Button>
          ))}
        </DialogContent>
      </Dialog>
    </>
  )
}

export const useOutletStyles = makeStyles<any>((theme) => ({
  brandImage: {
    height: 220,
    width: "100%",
  },
  brandDetails: {
    display: "flex",
    paddingTop: 16,
    flexDirection: "row",
  },
  borderBottom: {
    backgroundColor: theme.palette.neutral100,
    height: 1,
    marginTop: 20,
    marginBottom: 20,
  },
  titleView: {
    display: "flex",
    flex: 1,
    flexDirection: "row",
    marginRight: 24,
    alignItems: "center",
  },
  title: {
    color: theme.palette.neutral400,
    fontSize: 18,
    fontWeight: "bold",
  },
  localityView: {
    flex: 1,
    height: 24,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    display: "flex",
  },
  address: {
    color: theme.palette.neutral300,
    marginLeft: 3,
  },
  open: {
    color: theme.palette.success600,
  },
  providerLocalityView: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    marginTop: 10,
  },
  dotView: {
    height: 4,
    width: 4,
    borderRadius: 4,
    backgroundColor: theme.palette.neutral300,
    marginHorizontal: 5,
  },
  headerImage: { height: 80, width: 80, borderRadius: 15 },
  providerDetails: {
    flex: 1,
    gap: 8,
    width: "68%",
  },
  brandImageEmpty: {
    backgroundColor: theme.palette.neutral200,
  },
  outletSheet: {
    backgroundColor: "transparent",
  },
  outletContainer: {
    flex: 1,
    backgroundColor: theme.palette.neutral50,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
  headerOutlet: {
    display: "flex",
    height: 52,
    backgroundColor: theme.palette.white,
    justifyContent: "center",
    paddingHorizontal: 16,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
  },
  outletDetails: {
    padding: 16,
    gap: 12,
  },
  outletItem: {
    display: "flex",
    padding: 12,
    backgroundColor: theme.palette.white,
    flexDirection: "row",
    borderWidth: 1,
    borderRadius: 12,
    borderColor: theme.palette.neutral100,
    alignItems: "center",
  },
  outletTitleView: {
    flex: 1,
    gap: 4,
  },
}))

export default OutletDetails
