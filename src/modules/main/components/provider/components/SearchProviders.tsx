import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import { makeStyles } from "@mui/styles"
import EmptyStoresIcon from "../../../../../assets/search/stores.svg"
import useNetworkErrorHandling from "../../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import {
  API_BASE_URL,
  GLOBAL_SEARCH_STORES,
} from "../../../../../utils/apiActions"
import { BRAND_PRODUCTS_LIMIT, FB_DOMAIN } from "../../../../../utils/constants"
import { emptyAlertCallback } from "../../../../../utils/utils"
import useAddressParams from "../../../hooks/useAddressParams"
import Filters from "../../products/filter/Filters"
import ProductSkeleton from "../../skeleton/ProductSkeleton"
import Provider from "./Provider"

interface SearchProviders {
  searchQuery: string
  currentSubCategory: string
  tagId?: string
  categoryDomain?: string
  serviceability?: any
  tts?: any
  bppId?: string
}

interface ProviderResponse {
  response: {
    pages: number
    data: any[]
  }
}

const ListEmptyComponent = () => {
  const { t } = useTranslation()
  const styles = useStyles()
  return (
    <Box className={styles.emptyContainer}>
      <img
        src={EmptyStoresIcon}
        alt="No Providers Available"
        width={128}
        height={128}
      />
      <Typography variant="headlineSmall" className={styles.emptyTitle}>
        {t("Home.Search Provider List.No providers available")}
      </Typography>
      <Typography variant="labelSmall" className={styles.emptyMessage}>
        {t("Home.Search Provider List.No providers available message")}
      </Typography>
    </Box>
  )
}

const SearchProviders: React.FC<SearchProviders> = ({
  searchQuery,
  currentSubCategory,
  tagId = "",
  serviceability = "",
  tts = "",
  categoryDomain = "",
  bppId = "",
}) => {
  const productSearchSource = useRef<any>(null)
  const { getDataWithWithoutEncode } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const totalProviders = useRef<number>(0)
  const { getAddressParams } = useAddressParams()
  const [providers, setProviders] = useState<any[]>([])
  const [productsRequested, setProductsRequested] = useState<boolean>(true)
  const [moreListRequested, setMoreListRequested] = useState<boolean>(false)
  const [selectedAttributes, setSelectedAttributes] = useState<any>({})
  const [sortingValue, setSortingValue] = useState<any>("")
  const [page, setPage] = useState<number>(0)
  const loadMoreRef = useRef(null)
  const scrollContainerRef = useRef(null)
  const styles = useStyles()

  const loadMoreList = () => {
    if (totalProviders.current > page && !moreListRequested) {
      setMoreListRequested(true)
      searchProviders(page)
        .then(() => setMoreListRequested(false))
        .catch(() => setMoreListRequested(false))
    }
  }
  const ListFooterComponent = useMemo(() => {
    return moreListRequested ? <ProductSkeleton /> : null
  }, [moreListRequested])

  const searchProviders = async (pageNumber: number) => {
    try {
      const newAbortController = new AbortController()
      productSearchSource.current = newAbortController
      const signal = newAbortController.signal

      let url = `${API_BASE_URL}${GLOBAL_SEARCH_STORES}`
      const params = getAddressParams()

      if (searchQuery.length > 0) params.append("name", searchQuery)
      if (currentSubCategory) {
        if (currentSubCategory !== "F&B") {
          params.append("subcategory", currentSubCategory)
        } else {
          params.append("category", FB_DOMAIN)
        }
      }
      if (categoryDomain.length > 0) params.append("category", categoryDomain)
      if (tagId.length > 0) params.append("searchTag", tagId)
      if (serviceability.length > 0)
        params.append("serviceability", serviceability)
      if (tts) params.append("tts", String(tts))

      if (sortingValue !== "") {
        params.append("sortBy", sortingValue || "asc")
        params.append("sort", "minPrice")
      }
      if (bppId) params.append("bpp_ids", bppId)
      // if (bppId) {
      //   url += `&bpp_ids=${bppId}`
      // }
      url = `${url}?${params.toString()}&size=${BRAND_PRODUCTS_LIMIT}&from=${
        pageNumber * BRAND_PRODUCTS_LIMIT
      }`

      Object.keys(selectedAttributes).forEach((key) => {
        const value = selectedAttributes[key]
        if (!value.skipAttribute) {
          url += `&product_attr_${key}=${value
            .map((one: string) => encodeURIComponent(one))
            .join(",")}`
        } else {
          url += `&minPrice=${value.values[0]}&maxPrice=${value.values[1]}`
        }
      })

      const response = await getDataWithWithoutEncode(url, { signal })
      if (signal.aborted) return // Prevent state updates if the request was aborted

      const data: ProviderResponse = response.data

      setPage(pageNumber + 1)
      totalProviders.current = data.response.pages

      if (pageNumber > 0) {
        setProviders((prev) => [...prev, ...data.response.data])
      } else {
        setProviders(data.response.data)
      }
      setProductsRequested(false)
    } catch (error: any) {
      if (error.name === "AbortError") {
        return
      }
      handleApiError(error)
      setProductsRequested(false)
    }
  }

  const renderItem = useCallback(
    (item: any) => <Provider provider={item} />,
    []
  )

  useEffect(() => {
    setProductsRequested(true)
    if (
      currentSubCategory ||
      tagId ||
      searchQuery.length > 0 ||
      categoryDomain
    ) {
      searchProviders(0).then(emptyAlertCallback)
    }
  }, [
    currentSubCategory,
    searchQuery,
    selectedAttributes,
    tagId,
    categoryDomain,
    sortingValue,
  ])

  const scrollToTop = () => {
    if (scrollContainerRef) {
      // scrollContainerRef.current?.scrollTo({top: 0, behavior: 'smooth'});
    }
  }

  useEffect(() => {
    if (page === 1) {
      scrollToTop()
    }
  }, [sortingValue])

  useEffect(() => {
    setSelectedAttributes({})
    setProductsRequested(true)
  }, [currentSubCategory])

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMoreList()
        }
      },
      {
        root: scrollContainerRef.current ?? null, // Use scroll container
        threshold: 0.5,
      }
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current)
      }
    }
  }, [providers])

  return (
    <Box className={styles.container} ref={scrollContainerRef}>
      <Filters
        isSearch
        searchText={searchQuery}
        selectedAttributes={selectedAttributes}
        setSelectedAttributes={setSelectedAttributes}
        providerId={null}
        category={currentSubCategory || null}
        providerDomain={categoryDomain}
        sortingValue={sortingValue}
        setSortingValue={setSortingValue}
        bppId={bppId}
      />

      {productsRequested ? (
        <Box className={styles.listContainer}>
          {Array(5)
            .fill(null)
            .map((_, index) => (
              <ProductSkeleton key={index} />
            ))}
        </Box>
      ) : (
        <Box className={styles.listContainer}>
          {providers.length > 0 ? (
            providers.map((provider, index) => (
              <Box key={`${provider.id}${index}`} sx={{ marginBottom: 2 }}>
                {renderItem(provider)}
              </Box>
            ))
          ) : (
            <ListEmptyComponent />
          )}
          <div ref={loadMoreRef} className={styles.loadMoreTrigger} />
          <Box className={styles.footerContainer}>{ListFooterComponent}</Box>
        </Box>
      )}
    </Box>
  )
}

export default SearchProviders

const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    padding: "16px",
  },
  listContainer: {
    paddingTop: "16px",
  },
  emptyContainer: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  },
  emptyTitle: {
    color: "#757575",
  },
  emptyMessage: {
    marginTop: "8px",
    color: "#757575",
    width: "260px",
    textAlign: "center",
  },
  loadMoreTrigger: {
    height: "100px",
  },
}))
