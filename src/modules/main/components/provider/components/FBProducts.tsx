import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Modal from "@mui/material/Modal"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import axios from "axios"
import { useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import NonVegIcon from "../../../../../assets/foodType/non_veg.svg"
import VegIcon from "../../../../../assets/foodType/veg.svg"
import useNetworkErrorHandling from "../../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"

import List from "@mui/material/List"
import ListItem from "@mui/material/ListItem"
import Portal from "@mui/material/Portal"
import FloatMenu from "../../../../../assets/floatMenu.svg"
import {
  API_BASE_URL,
  CUSTOM_MENU,
  ITEMS,
  PRODUCT_SEARCH,
} from "../../../../../utils/apiActions"
import {
  emptyAlertCallback,
  getFilterCategory,
} from "../../../../../utils/utils"
import ListProduct from "../../../components/products/ListProduct"
import ProductSkeleton from "../../../components/skeleton/ProductSkeleton"
import CustomMenuAccordion from "./CustomMenuAccordion"
import FBFilter from "./FBFilter"

const CancelToken = axios.CancelToken

const VegImageSource = () => (
  <img src={VegIcon} width={20} height={20} alt="Veg" />
)
const NonVegImageSource = () => (
  <img src={NonVegIcon} width={20} height={20} alt="Non veg" />
)

const FBProducts = ({
  provider,
  domain,
  location,
  isOpen,
  searchQuery,
  children,
  showProductDetails,
  productId = "",
}: {
  provider: any
  domain: string
  location: string
  isOpen: boolean
  searchQuery: string
  children: any
  showProductDetails: boolean
  productId: string
}) => {
  const { t } = useTranslation()
  const theme = useTheme()
  const styles = useStyles()
  const customMenuSource = useRef<any>(null)
  const [menu, setMenu] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [menuRequested, setMenuRequested] = useState<boolean>(true)
  const [selectedFilter, setSelectedFilter] = useState<string>("")
  const { getDataWithAuth } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const [menuItem, setMenuItem] = useState<boolean>(false)
  const [defaultExpandVal, setDefaultExpandVal] = useState<number>(0)
  const [availableTags, setAvailableTags] = useState<any>(new Set())
  const sectionRefs = useRef<(HTMLDivElement | null)[]>([])

  const getCustomMenu = async () => {
    try {
      setMenuRequested(true)
      customMenuSource.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${CUSTOM_MENU}?provider=${provider?.id}&domain=${domain}`,
        customMenuSource.current.token
      )

      if (data.data.length > 0) {
        const menuResponses = await Promise.all(
          data.data.map((one: any) =>
            getDataWithAuth(
              `${API_BASE_URL}${ITEMS}?customMenu=${one.id}`,
              customMenuSource.current.token
            )
          )
        )
        const list = data.data.map((one: any, index: number) => {
          one.items = menuResponses[index].data.data
          return one
        })
        const uniqueTags = new Set()
        list.forEach((section: any) => {
          section.items.forEach((item: any) => {
            uniqueTags.add(getFilterCategory(item?.item_details?.tags))
          })
        })
        setAvailableTags(uniqueTags)
        setMenu(list)
      } else {
        const domainQuery = domain ? `&domain=${domain}` : ""
        let url = `${API_BASE_URL}${PRODUCT_SEARCH}?pageNumber=1&limit=500&providerIds=${provider?.id}&locationIds=${location}${domainQuery}`
        const searchedProductsResponse = await getDataWithAuth(
          url,
          customMenuSource.current.token
        )
        const uniqueTags = new Set()
        searchedProductsResponse.data.response.data.forEach((item: any) => {
          uniqueTags.add(getFilterCategory(item?.item_details?.tags))
        })
        setAvailableTags(uniqueTags)
        setProducts(searchedProductsResponse.data.response.data)
      }
    } catch (error) {
      handleApiError(error)
    } finally {
      setMenuRequested(false)
    }
  }

  const { filteredSections, filteredProducts } = useMemo(() => {
    const lowerSearch = searchQuery.toLowerCase()

    if (menu.length > 0) {
      // Filter products based on search query
      const list = menu.map((section) => {
        let filteredSectionItems = section.items.filter((item: any) => {
          const itemDetails = item?.item_details
          const itemName = itemDetails?.descriptor?.name?.toLowerCase()
          const categoryId = itemDetails?.category_id
          const matchesSearch =
            itemName?.includes(lowerSearch) || categoryId?.includes(lowerSearch)
          const matchesFilter =
            selectedFilter.length === 0 ||
            getFilterCategory(itemDetails?.tags) === selectedFilter
          return matchesSearch && matchesFilter
        })
        return Object.assign({}, section, {
          items: filteredSectionItems,
        })
      })

      return {
        filteredSections: list.filter((section) => section.items.length > 0),
        filteredProducts: [],
      }
    } else {
      return {
        filteredProducts: products.filter((item: any) => {
          const itemDetails = item?.item_details
          const itemName = itemDetails?.descriptor?.name?.toLowerCase()
          const categoryId = itemDetails?.category_id
          const matchesSearch =
            itemName?.includes(lowerSearch) || categoryId?.includes(lowerSearch)
          const matchesFilter =
            selectedFilter.length === 0 ||
            getFilterCategory(itemDetails?.tags) === selectedFilter
          return matchesSearch && matchesFilter
        }),
        filteredSections: [],
      }
    }
  }, [menu, products, searchQuery, selectedFilter])

  useEffect(() => {
    getCustomMenu().then(emptyAlertCallback)
    return () => {
      if (customMenuSource.current) {
        customMenuSource.current.cancel()
      }
    }
  }, [])

  if (menuRequested) {
    return <ProductSkeleton />
  }
  const scrollToSection = (index: number) => {
    sectionRefs.current[index]?.scrollIntoView({ behavior: "instant" })
  }
  return (
    <Box className={styles.container}>
      {children}
      <Box className={styles.filterContainer}>
        {availableTags.has("veg") && (
          <FBFilter
            selectedFilter={selectedFilter}
            label={t("Veg")}
            value="veg"
            setSelectedFilter={setSelectedFilter}
            ImageSource={VegImageSource}
          />
        )}
        {availableTags.has("nonveg") && (
          <FBFilter
            selectedFilter={selectedFilter}
            label={t("Non Veg")}
            value="nonveg"
            setSelectedFilter={setSelectedFilter}
            ImageSource={NonVegImageSource}
          />
        )}
      </Box>
      {menu.length > 0
        ? filteredSections.map((section, index) => (
            <CustomMenuAccordion
              ref={(el: any) => (sectionRefs.current[index] = el)}
              key={section.id}
              section={section}
              provider={provider}
              isOpen={isOpen}
              defaultExpand={index === defaultExpandVal}
              showProductDetails={showProductDetails}
              productId={productId}
            />
          ))
        : filteredProducts.map((item) => (
            <ListProduct
              key={item.id}
              product={item}
              provider={provider}
              isOpen={isOpen}
              listView
              openDetailsPage={false}
              isShowProductDetails={
                showProductDetails && item?.id === productId
              }
            />
          ))}
      {!menuItem && filteredSections.length > 0 ? (
        <Button
          variant="contained"
          sx={{
            position: "fixed",
            bottom: "15%",
            right: 16,
            borderRadius: 28,

            boxShadow: 3,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: 1,
            zIndex: 1000,
            width: 75,
            height: 75,
            backgroundColor: theme.palette.black,
          }}
          onClick={() => setMenuItem(true)}
        >
          <img alt="floating button" src={FloatMenu} width={20} height={24} />
          <Typography variant="labelSmall">{t("Store.Menu")}</Typography>
        </Button>
      ) : null}

      <Portal>
        <Modal
          open={menuItem}
          onClose={() => setMenuItem(false)}
          sx={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "center",
            backdropFilter: "blur(2px)",
          }}
        >
          <Box
            sx={{
              width: "100%",
              maxHeight: "70dvh",
              bgcolor: "background.paper",
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
              p: 2,
              overflow: "auto",
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <List>
              {filteredSections?.map((section, index) => {
                const itemLength = section?.items?.length
                const selected = index === defaultExpandVal

                return (
                  <ListItem
                    key={section.id}
                    button
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      px: 2,
                      py: 1.5,
                      borderBottom: "1px solid",
                      borderColor: "divider",
                    }}
                    onClick={() => {
                      setMenuItem(false)
                      setDefaultExpandVal(index)
                      scrollToSection(index)
                      setTimeout(() => {
                        scrollToSection(index)
                      }, 300)
                    }}
                  >
                    <Typography
                      variant={selected ? "h6" : "subtitle1"}
                      sx={{
                        fontWeight: selected ? 600 : 400,
                        color: selected ? "primary.main" : "text.primary",
                      }}
                    >
                      {section?.descriptor?.name}
                    </Typography>
                    {itemLength > 0 && (
                      <Typography
                        variant={selected ? "h6" : "subtitle1"}
                        sx={{
                          fontWeight: selected ? 600 : 400,
                          color: selected ? "primary.main" : "text.primary",
                        }}
                      >
                        {itemLength}
                      </Typography>
                    )}
                  </ListItem>
                )
              })}
            </List>
          </Box>
        </Modal>
      </Portal>
    </Box>
  )
}

export const useStyles = makeStyles<any>((theme) => ({
  container: { flex: 1 },
  filterContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    marginStart: 16,
    gap: 8,
  },
  searchContainer: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  itemSeparator: {
    marginVertical: 24,
    backgroundColor: theme.palette.neutral100,
    height: 1,
  },
  listContainer: {
    paddingTop: 24,
  },
  itemModal: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  menuText: {
    color: theme.palette.white,
  },
  containerModal: {
    flex: 1,
    display: "flex",
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "flex-end",
    alignItems: "flex-end",
  },
  subContainerModal: {
    minHeight: 50,
    width: 260,
    backgroundColor: theme.palette.white,
    borderRadius: 16,
    marginBottom: 20,
    marginRight: 20,
    padding: 16,
    gap: 16,
  },
  flotingButton: {
    display: "flex",
    height: 64,
    width: 64,
    borderRadius: 64,
    backgroundColor: theme.palette.black,
    alignItems: "center",
    justifyContent: "center",
    position: "absolute",
    bottom: 20,
    right: 20,
  },
  textmodal: {
    color: theme.palette.neutral400,
  },
}))

export default FBProducts
