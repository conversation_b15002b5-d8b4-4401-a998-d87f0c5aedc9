import { Share } from "@mui/icons-material"
import DirectionsIcon from "@mui/icons-material/DirectionsRounded"
import PhoneIcon from "@mui/icons-material/PhoneRounded"
import WatchLaterIcon from "@mui/icons-material/WatchLater"
import Box from "@mui/material/Box"
import Divider from "@mui/material/Divider"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import { useCallback, useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import {
  formatTime12Hour,
  parseBrandOutletId,
  TimeRange,
} from "../../../../../utils/utils"
import Header from "../../../modules/cart/provider/components/Header"
import SafeAreaPage from "../../page/SafeAreaPage"

const StoreInfo = () => {
  const { t } = useTranslation()
  const location = useLocation()
  const { address } = useSelector((state: any) => state.address)
  const params = location.state || {}
  const styles = useStyles()
  const { outlet, provider } = params
  const navigateToMappls = useCallback(() => {
    const destinationAddress = params?.outlet?.address?.locality || ""
    window.open(
      `https://www.mappls.com/direction?places=${address?.lat},${address?.lng};${params?.outlet?.gps},${destinationAddress}`,
      "_blank"
    )
  }, [params, address])

  const { imageSource, outletPhone } = useMemo(() => {
    let source = null
    let phone = ""

    if (params?.outlet) {
      if (params?.outlet?.provider_descriptor?.symbol) {
        source = params?.outlet?.provider_descriptor?.symbol
      } else if (params?.outlet?.provider_descriptor?.images?.length > 0) {
        source = params?.outlet?.provider_descriptor?.images[0]
      }
      phone = params?.outlet?.contact || ""
    }
    return { imageSource: source, outletPhone: phone }
  }, [params?.outlet])
  const onShareBrandDetails = async () => {
    const brandOutletId = outlet.id
    const result = parseBrandOutletId(brandOutletId)
    const shareUri = `https://digihaat.in/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${brandOutletId}`
    const shareData = {
      text: `Thousands are shopping at ${provider?.descriptor?.name}`,
      url: shareUri,
    }

    try {
      if (navigator.share) {
        await navigator.share(shareData)
      } else {
        // Fallback for browsers that don't support Web Share API
        await navigator.clipboard.writeText(`${shareData.text}\n${shareUri}`)
        alert("Link copied to clipboard!")
      }
    } catch (err) {
      console.error("Error sharing:", err)
    }
  }
  return (
    <SafeAreaPage>
      <Header label="" />
      <Box className={styles.container}>
        <Box className={styles.topHeader}>
          <Box className={styles.textView}>
            <Typography variant="headlineSmall" className={styles.title}>
              {params?.provider?.descriptor?.name}
            </Typography>
            <Typography variant="body1" className={styles.address}>
              {params?.outlet?.address?.locality},{" "}
              {params?.outlet?.address?.city}
            </Typography>
          </Box>

          {params?.outlet?.storeTime?.map((range: TimeRange) => {
            const time = `${formatTime12Hour(
              range.start
            )} to ${formatTime12Hour(range.end)}`
            return (
              <Box key={time} className={styles.timeContainer}>
                <WatchLaterIcon className={styles.clockIcon} />
                <Typography variant="body1" className={styles.time}>
                  {time}
                </Typography>
              </Box>
            )
          })}

          <Box className={styles.icons}>
            {outletPhone && (
              <IconButton
                className={styles.actionButton}
                onClick={() => window.open(`tel:${outletPhone}`)}
              >
                <PhoneIcon />
              </IconButton>
            )}
            <IconButton
              className={styles.actionButton}
              onClick={navigateToMappls}
            >
              <DirectionsIcon />
            </IconButton>
            <IconButton
              className={styles.actionButton}
              onClick={onShareBrandDetails}
            >
              <Share />
            </IconButton>
          </Box>
        </Box>
        <Divider className={styles.line} />
        <Box className={styles.imageView}>
          <Typography className={styles.photo} variant="titleLarge">
            {t("Stores Info.Photo")}
          </Typography>
          {imageSource && (
            <img src={imageSource} alt="Store" className={styles.image} />
          )}
        </Box>
        <Divider className={styles.line} />
        <Box className={styles.detailView}>
          {params.provider?.bpp_details?.bpp_id && (
            <>
              <Typography variant="body1" className={styles.seller}>
                {t("Stores Info.Seller Network Partner")}
              </Typography>
              <Typography variant="body1" className={styles.sellerInfo}>
                {params.provider?.bpp_details?.bpp_id}
              </Typography>
            </>
          )}
          {params?.provider?.["@ondc/org/fssai_license_no"] && (
            <>
              <Typography variant="body1" className={styles.seller}>
                {t("Stores Info.FSSAI Lic No")}
              </Typography>
              <Typography variant="body1" className={styles.sellerInfo}>
                {params?.provider["@ondc/org/fssai_license_no"]}
              </Typography>
            </>
          )}
        </Box>
      </Box>
    </SafeAreaPage>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
    padding: 16,
    gap: 24,
  },

  textView: {
    gap: 8,
  },

  title: {
    color: theme.palette.neutral500,
    fontWeight: "bold !important",
  },

  address: {
    marginTop: "10px !important",
    color: "#686868 !important",
    fontWeight: "600 !important",
  },

  seller: {
    marginTop: "20px !important",
    color: "#686868 !important",
    fontWeight: "600 !important",
  },

  sellerInfo: {
    color: "#686868 !important",
    marginTop: "10px !important",
  },

  time: {
    color: "#686868 !important",
    fontWeight: "600 !important",
  },

  topHeader: {
    display: "flex",
    flexDirection: "column",
    gap: 12,
  },

  timeContainer: {
    display: "flex",
    alignItems: "center",
    gap: 8,
  },

  clockIcon: {
    fontSize: 16,
    color: "#686868 !important",
  },

  icons: {
    display: "flex",
    gap: 12,
    marginTop: 15,
    boxShadow: "none !important",
  },

  imageView: {
    paddingTop: 10,
    paddingBottom: 10,
  },

  photo: {
    fontWeight: "bold !important",
    marginBottom: "20px !important",
  },

  image: {
    height: 120,
    width: 120,
    borderRadius: 16,
    objectFit: "contain",
  },

  detailView: {
    gap: 16,
  },

  line: {
    height: 1,
    marginTop: "20px !important",
    marginBottom: "20px !important",
  },

  actionButton: {
    color: `${theme.palette.primary.main} !important`,
    border: `1px solid ${theme.palette.primary.main} !important`,
    backgroundColor: "rgba(236, 243, 248, 1) !important",
  },
}))

export default StoreInfo
