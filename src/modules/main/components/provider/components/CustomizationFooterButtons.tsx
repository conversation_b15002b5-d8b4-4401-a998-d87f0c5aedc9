import React from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import Icon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import {useTranslation} from "react-i18next";
import {useTheme} from "@mui/material/styles";
import useFormatNumber from "../../../hooks/useFormatNumber";

interface CustomizationFooterButtonsProps {
  productLoading: boolean;
  itemQty: number;
  setItemQty: (quantity: number) => void;
  itemOutOfStock: boolean;
  addDetailsToCart: () => void;
  product: any;
  customizationPrices: any;
  update?: boolean;
  isOpen?: boolean;
}

const CustomizationFooterButtons: React.FC<CustomizationFooterButtonsProps> = ({
                                                                                 productLoading,
                                                                                 itemQty,
                                                                                 setItemQty,
                                                                                 itemOutOfStock,
                                                                                 addDetailsToCart,
                                                                                 product,
                                                                                 customizationPrices,
                                                                                 update = false,
                                                                                 isOpen = true,
                                                                               }) => {
  const {formatNumber} = useFormatNumber();
  const {t} = useTranslation();
  const theme = useTheme();

  return (
    <Box sx={{...styles.customizationButtons, backgroundColor: theme.palette.grey[100]}}>
      {isOpen && (
        <Box sx={styles.quantityContainer}>
          <Button
            disabled={productLoading || !isOpen}
            onClick={() => itemQty > 1 && setItemQty(itemQty - 1)}
            sx={styles.iconButton}
          >
            <RemoveIcon sx={{color: productLoading || !isOpen ? theme.palette.grey[400] : theme.palette.primary.main}}/>
          </Button>

          <Typography variant="titleMedium" sx={styles.quantity}>
            {formatNumber(itemQty)}
          </Typography>

          <Button
            disabled={productLoading || itemOutOfStock || !isOpen}
            onClick={() => setItemQty(itemQty + 1)}
            sx={styles.iconButton}
          >
            <Icon sx={{color: productLoading || !isOpen ? theme.palette.grey[400] : theme.palette.primary.main}}/>
          </Button>
        </Box>
      )}

      <Button
        disabled={itemOutOfStock || productLoading || !isOpen}
        sx={{
          ...styles.addToCartButton,
          backgroundColor: itemOutOfStock || productLoading || !isOpen ? theme.palette.grey[400] : theme.palette.primary.main,
        }}
        onClick={addDetailsToCart}
      >
        {productLoading ? (
          <CircularProgress size={14} sx={{color: theme.palette.primary.main}}/>
        ) : (
          <Typography variant="body1" sx={styles.addToCartText}>
            {update
              ? t("Product Summary.Update Item Total", {
                total: `₹${formatNumber(
                  Number(((product?.item_details?.price.value + customizationPrices) * itemQty).toFixed(2))
                )}`,
              })
              : t("Product Summary.Add Item Total", {
                total: `₹${formatNumber(
                  Number(((product?.item_details?.price.value + customizationPrices) * itemQty).toFixed(2))
                )}`,
              })}
          </Typography>
        )}
      </Button>
    </Box>
  );
};

const styles = {
  customizationButtons: {
    display: "flex",
    justifyContent: "space-between",
    padding: "16px",
    gap: "15px",
  },
  quantityContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    borderRadius: "8px",
    border: "1px solid",
    borderColor: "primary.main",
    backgroundColor: "#ECF3F8",
    height: "44px",
    width: "108px",
  },
  quantity: {
    flex: 1,
    textAlign: "center",
    color: "primary.main",
  },
  iconButton: {
    minWidth: "unset",
    padding: 0,
    width: "24px",
    height: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  addToCartButton: {
    flex: 1,
    borderRadius: "8px",
    padding: "10px",
    justifyContent: "center",
    alignItems: "center",
  },
  addToCartText: {
    color: "white",
  },
};

export default CustomizationFooterButtons;
