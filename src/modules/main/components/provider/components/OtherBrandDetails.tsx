import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {useTranslation} from 'react-i18next';
import Products from '../../../components/products/Products';
import OutletDetails from './OutletDetails';
import {makeStyles} from "@mui/styles";
import BrandSkeleton from '../../skeleton/BrandSkeleton';

interface OtherBrandDetailsProps {
  provider: any;
  outlet: any;
  apiRequested: boolean;
  searchQuery: string;
}

const OtherBrandDetails: React.FC<OtherBrandDetailsProps> = React.memo(
  ({provider, outlet, apiRequested, searchQuery}) => {
    const {t} = useTranslation();
    const styles = useStyles();

    if (apiRequested) {
      return <BrandSkeleton/>
    }

    if (provider) {
      return (
        <Box className={styles.container}>
          <Products
            providerId={provider.id}
            locationId={outlet?.id}
            providerDomain={provider.domain}
            provider={provider}
            searchText={searchQuery}
            subCategories={[]}
            isOpen={outlet?.isOpen || false}
            isSearch={false}
            fbDomain={false}
          >
            <OutletDetails
              provider={provider}
              outlet={outlet}
              apiRequested={apiRequested}
            />
          </Products>
        </Box>
      );
    } else {
      return (
        <Box className={styles.providerNotFound}>
          <Typography variant="body1">
            {t('Global.Details not available')}
          </Typography>
        </Box>
      );
    }
  }
);

export const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
    backgroundColor: theme.palette.common.white,
  },
  providerNotFound: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100dvh',
  },
}));

export default OtherBrandDetails;
