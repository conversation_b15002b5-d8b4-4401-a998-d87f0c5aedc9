import React, {useEffect, useState} from 'react';
import {
  createCustomizationAndGroupMapping,
  formatCustomizationGroups,
  formatCustomizations,
} from '../../../../../utils/utils';
import CustomizationGroup from './CustomizationGroup';

const FBProductCustomization = ({
                                  product,
                                  customizationState,
                                  setCustomizationState,
                                  isEditFlow = false,
                                  setItemOutOfStock,
                                  disabled = false,
                                }: {
  product: any;
  customizationState: any;
  setCustomizationState: (state: any) => void;
  isEditFlow?: boolean;
  setItemOutOfStock: (flag: boolean) => void;
  hideProductDetails?: boolean;
  disabled?: boolean;
}) => {
  const [customizationGroups, setCustomizationGroups] = useState<any[]>([]);
  const [customizations, setCustomizations] = useState<any[]>([]);
  const [parentCustomizations, setParentCustomizations] = useState<any[]>([]);
  const [customizationToGroupMap, setCustomizationToGroupMap] = useState<any>(
    {},
  );

  useEffect(() => {
    if (product) {
      const {customisation_groups, customisation_items} = product;
      const customGroup = product?.item_details?.tags?.find(
        (item: any) => item.code === 'custom_group',
      );
      if (customGroup && customGroup.list.length > 0) {
        setCustomizationGroups(formatCustomizationGroups(customisation_groups));
      } else {
        setCustomizationGroups([]);
      }
      setCustomizations(formatCustomizations(customisation_items));
    }
  }, [product]);

  useEffect(() => {
    const mappings = createCustomizationAndGroupMapping(customizations);
    setCustomizationToGroupMap(mappings.customizationToGroupMap);
  }, [customizationGroups, customizations]);

  useEffect(() => {
    const initializeCustomizationState = () => {
      const sortedCustomizationGroups = [...customizationGroups].sort(
        (a, b) => a.seq - b.seq,
      );
      const allGroupIds = sortedCustomizationGroups.map(one => one.id);
      const trackedIds: any[] = [];
      const parentIds: any[] = [];
      const firstGroup = sortedCustomizationGroups[0];
      const state: any = {firstGroup};

      const processCustomizationGroup = (id: any) => {
        const group: any = customizationGroups.find(item => item.id === id);
        if (!group) {
          return;
        }
        const groupId = group.id;
        const groupName = group.name;
        const isMandatory = group.minQuantity > 0;
        trackedIds.push(groupId);

        state[groupId] = {
          id: groupId,
          name: groupName,
          seq: group.seq,
          options: [],
          selected: [],
          childs: [],
          isMandatory,
          type: group.maxQuantity > 1 ? 'Checkbox' : 'Radio',
        };

        const childCustomizations = customizations.filter(
          (customization: any) => customization.parent === groupId,
        );

        state[groupId].options = childCustomizations;
        state[groupId].selected = findSelectedCustomizationForGroup(
          state[groupId],
          childCustomizations,
        );

        let childGroups: any =
          state[groupId].selected[0]?.id != undefined
            ? customizationToGroupMap?.[state[groupId]?.selected[0]?.id]
            : [];
        state[groupId].childs = childGroups;

        if (childGroups) {
          for (const childGroup of childGroups) {
            processCustomizationGroup(childGroup);
          }
        }
      };

      if (firstGroup) {
        let difference = allGroupIds.filter(item => !trackedIds.includes(item));
        while (difference.length > 0) {
          parentIds.push(difference[0]);
          processCustomizationGroup(difference[0]);
          difference = allGroupIds.filter(item => !trackedIds.includes(item));
        }
        state.parentCustomizations = parentIds;
        setCustomizationState(state);
        setParentCustomizations(parentIds);
      }
    };

    if (!isEditFlow) {
      initializeCustomizationState();
    }
  }, [customizationGroups, customizations, customizationToGroupMap]);

  const findSelectedCustomizationForGroup = (
    group: any,
    childCustomizations: any,
  ) => {
    if (!group.isMandatory) {
      return [];
    }
    let selectedGroups = [];
    let defaultCustomization = childCustomizations.filter(
      (customization: any) => customization.isDefault && customization.inStock,
    );

    if (defaultCustomization.length) {
      selectedGroups = defaultCustomization;
    }
    setItemOutOfStock(selectedGroups.length === 0);
    return selectedGroups;
  };

  const processGroup = (
    groupId: any,
    updatedCustomizationState1: any,
    selectedGroup: any,
    selectedOption: any,
  ) => {
    const currentGroup = customizationGroups.find(item => item.id === groupId);
    if (!currentGroup) {
      return;
    }

    const groupName = currentGroup.name;
    const isMandatory = currentGroup.minQuantity > 0;

    const currentGroupOldState = updatedCustomizationState1[currentGroup.id];

    updatedCustomizationState1[groupId] = {
      id: groupId,
      name: groupName,
      seq: currentGroup.seq,
      options: [],
      selected: [],
      childs: [],
      isMandatory,
      type: currentGroup.maxQuantity > 1 ? 'Checkbox' : 'Radio',
    };
    updatedCustomizationState1[groupId].options = [];

    const childCustomizations = customizations.filter(
      customization => customization.parent === groupId,
    );
    updatedCustomizationState1[groupId].options = childCustomizations;

    let childGroups = [];
    if (currentGroup.id === selectedGroup.id) {
      let new_selected_options = [];
      const presentInList = currentGroupOldState.selected.some(
        (optn: any) => optn.id === selectedOption.id,
      );

      if (!isMandatory && presentInList) {
        // Remove the option if it's present and not mandatory
        new_selected_options = currentGroupOldState.selected.filter(
          (item: any) => item.id !== selectedOption.id,
        );
        updatedCustomizationState1[groupId].selected = new_selected_options;
      } else {
        if (currentGroup.maxQuantity === 1) {
          // Single selection scenario
          childGroups = customizationToGroupMap[selectedOption.id];
          updatedCustomizationState1[groupId].selected = [selectedOption];
        } else {
          // Multi-selection scenario
          const currentCount = currentGroupOldState.selected.length;

          if (presentInList) {
            // Remove if the item is already selected and above the minimum
            if (currentCount > currentGroup.minQuantity) {
              new_selected_options = currentGroupOldState.selected.filter(
                (item: any) => item.id !== selectedOption.id,
              );
              updatedCustomizationState1[groupId].selected =
                new_selected_options;
            } else {
              updatedCustomizationState1[groupId].selected =
                currentGroupOldState.selected;
            }
          } else {
            // Add if below the maximum limit
            if (currentCount < currentGroup.maxQuantity) {
              new_selected_options = [
                ...currentGroupOldState.selected,
                selectedOption,
              ];
              updatedCustomizationState1[groupId].selected =
                new_selected_options;
            } else {
              updatedCustomizationState1[groupId].selected =
                currentGroupOldState.selected;
            }
          }
        }
      }
      updatedCustomizationState1[groupId].childs = childGroups;
    } else {
      const selectedCustomization = findSelectedCustomizationForGroup(
        updatedCustomizationState1[groupId],
        childCustomizations,
      );

      updatedCustomizationState1[groupId].selected = selectedCustomization;

      if (selectedCustomization.length) {
        childGroups = customizationToGroupMap[selectedCustomization[0].id];
        updatedCustomizationState1[groupId].childs = childGroups;
      }
    }

    // Recursively process child groups
    for (const childGroup of childGroups) {
      processGroup(
        childGroup,
        updatedCustomizationState1,
        selectedGroup,
        selectedOption,
      );
    }

    return updatedCustomizationState1;
  };

  const handleClick = (group: any, selectedOption: any) => {
    let updatedCustomizationState = {...customizationState};
    let updatedState = processGroup(
      group.id,
      updatedCustomizationState,
      group,
      selectedOption,
    );
    const allValid = customizationGroups.every(singleGroup => {
      return (
        updatedState[singleGroup.id].selected?.length >= singleGroup.minQuantity
      );
    });
    if (allValid) {
      setItemOutOfStock(false);
    }
    setCustomizationState(updatedState);
  };

  return (
    <>
      {parentCustomizations?.map(one => {
        const group = customizationState[one];
        return (
          <CustomizationGroup
            key={group?.id}
            group={group}
            customizationState={customizationState}
            customizationGroups={customizationGroups}
            handleClick={handleClick}
            disabled={disabled}
          />
        );
      })}
    </>
  );
};

export default FBProductCustomization;
