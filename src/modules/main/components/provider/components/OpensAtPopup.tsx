import React from "react";
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import {useTheme} from "@mui/material/styles";
import Box from "@mui/material/Box";

interface OpensAtPopupProps {
  timeFrom: string;
}

const OpensAtPopup: React.FC<OpensAtPopupProps> = ({timeFrom}) => {
  const theme = useTheme();
  const {t} = useTranslation();

  return (
    <Box sx={styles.opensAtRow}>
      <Box sx={{...styles.opensAtContainer, backgroundColor: theme.palette.primary.light}}>
        <Typography variant="labelSmall" sx={styles.opensAt}>
          {t("Store.Opens at", {time: timeFrom})}
        </Typography>
      </Box>
    </Box>
  );
};

const styles = {
  opensAtRow: {
    position: "absolute",
    width: "100%",
    display: "flex",
    alignItems: "center",
    marginTop: "-8px",
    justifyContent: "center",
  },
  opensAtContainer: {
    borderRadius: "22px",
    display: "flex",
    alignItems: "center",
    padding: "4px 8px",
  },
  opensAt: {
    color: "white",
  },
};

export default OpensAtPopup;
