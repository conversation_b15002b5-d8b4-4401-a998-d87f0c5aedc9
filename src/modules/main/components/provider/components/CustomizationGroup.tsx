import React, {useState} from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import Radio from '@mui/material/Radio';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import {useTranslation} from "react-i18next";
import useFormatNumber from "../../../hooks/useFormatNumber";
import VegNonVegTag from "./VegNonVegTag";

interface CustomizationGroupProps {
  group: any;
  customizationState: any;
  customizationGroups: any;
  handleClick: (group: any, option: any) => void;
  disabled: boolean;
}

const CustomizationGroup: React.FC<CustomizationGroupProps> = ({
                                                                 group,
                                                                 customizationState,
                                                                 customizationGroups,
                                                                 handleClick,
                                                                 disabled,
                                                               }) => {
  const {formatNumber} = useFormatNumber();
  const {t} = useTranslation();
  const [showAll, setShowAll] = useState<boolean>(false);

  if (!group) return null;

  const customizationGroup = customizationGroups.find((one: any) => one.id === group?.id);
  const totalRecords = group?.options?.length;
  const options = showAll ? group?.options : group?.options?.slice(0, 4);

  return (
    <Box key={group?.id} sx={styles.filterContainer}>
      <Box sx={styles.groupHeader}>
        <Box sx={styles.groupMeta}>
          <Typography variant="bodyLarge" sx={styles.groupName}>
            {group?.name}
          </Typography>

          {customizationGroup?.minQuantity !== 0 && customizationGroup?.maxQuantity !== 0 ? (
            <Typography variant="labelSmall" sx={styles.selectionLabel}>
              {t("Customisation.Select any and upto", {
                minQuantity: customizationGroup.minQuantity,
                maxQuantity: customizationGroup?.maxQuantity,
              })}
            </Typography>
          ) : customizationGroup?.minQuantity !== 0 ? (
            <Typography variant="labelSmall" sx={styles.selectionLabel}>
              {t("Customisation.Select any", {minQuantity: customizationGroup.minQuantity})}
            </Typography>
          ) : customizationGroup?.maxQuantity !== 0 ? (
            <Typography variant="labelSmall" sx={styles.selectionLabel}>
              {t("Customisation.Select upto", {maxQuantity: customizationGroup.maxQuantity})}
            </Typography>
          ) : null}
        </Box>

        {group?.isMandatory && (
          <Box sx={styles.mandatory}>
            <Typography variant="caption" sx={styles.mandatoryLabel}>
              {t("Cart.Required")}
            </Typography>
          </Box>
        )}
      </Box>

      <Box sx={styles.groupContainer}>
        {options.map((option: any) => {
          const selected = group?.selected?.some(
            (selectedOption: any) => selectedOption?.id === option?.id
          );

          return (
            <Box key={option.id} sx={styles.optionContainer}>
              <Box sx={styles.meta}>
                <VegNonVegTag category={option.vegNonVeg}/>
                <Typography
                  variant={selected ? "body1" : "body2"}
                  sx={styles.option}
                >
                  {option.name}
                </Typography>
              </Box>

              {option.inStock ? (
                <Box sx={styles.optionActionContainer}>
                  <Typography
                    variant={selected ? "body1" : "body2"}
                    sx={styles.amount}
                  >
                    ₹{formatNumber(Number(option.price).toFixed(2))}
                  </Typography>

                  {group?.type === "Radio" ? (
                    <FormControlLabel
                      control={
                        <Radio
                          checked={selected}
                          onChange={() => handleClick(group, option)}
                          disabled={disabled}
                        />
                      }
                      label=""
                    />
                  ) : (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={selected}
                          onChange={() => handleClick(group, option)}
                        />
                      }
                      label=""
                    />
                  )}
                </Box>
              ) : (
                <Typography variant="labelSmall" sx={styles.outOfStock}>
                  {t("Cart.FBProduct.Out of stock")}
                </Typography>
              )}
            </Box>
          );
        })}

        {totalRecords > 4 && (
          <Button
            onClick={() => setShowAll(!showAll)}
            sx={styles.toggleButton}
            endIcon={showAll ? <ExpandLessIcon/> : <ExpandMoreIcon/>}
          >
            {showAll ? "View Less" : `${totalRecords - options.length} more`}
          </Button>
        )}
      </Box>

      {group?.childs &&
        group?.childs.map((child: any) => (
          <CustomizationGroup
            key={child}
            group={customizationState[child]}
            customizationState={customizationState}
            customizationGroups={customizationGroups}
            handleClick={handleClick}
            disabled={disabled}
          />
        ))}
    </Box>
  );
};

const styles = {
  filterContainer: {
    padding: "12px",
    backgroundColor: "white",
    borderRadius: "12px",
    marginBottom: "16px",
    border: "1px solid",
    borderColor: "grey.300",
  },
  groupHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    borderBottom: "1px solid",
    borderBottomColor: "grey.300",
    paddingBottom: "8px",
  },
  groupMeta: {
    flex: 1,
    paddingRight: "8px",
  },
  groupName: {
    color: "grey.700",
  },
  selectionLabel: {
    color: "grey.500",
  },
  mandatory: {
    padding: "6px 10px",
    borderRadius: "6px",
    backgroundColor: "warning.main",
  },
  mandatoryLabel: {
    color: "white",
  },
  groupContainer: {
    paddingTop: "6px",
  },
  optionContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "8px 0",
  },
  meta: {
    display: "flex",
    alignItems: "center",
    flex: 1,
  },
  option: {
    color: "grey.600",
    paddingLeft: "8px",
    flex: 1,
  },
  amount: {
    color: "grey.600",
  },
  outOfStock: {
    color: "error.main",
    paddingRight: "8px",
  },
  optionActionContainer: {
    display: "flex",
    alignItems: "center",
    gap: "8px",
  },
  toggleButton: {
    display: "flex",
    alignItems: "center",
    marginTop: "6px",
    color: "primary.main",
  },
};

export default CustomizationGroup;
