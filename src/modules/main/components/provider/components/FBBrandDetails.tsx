import Box from "@mui/material/Box"
import { makeStyles } from "@mui/styles"
import React from "react"
import BrandSkeleton from "../../../components/skeleton/BrandSkeleton"
import FBProducts from "./FBProducts"
import OutletDetails from "./OutletDetails"

interface FBBrandDetailsProps {
  provider: any
  outlet: any
  apiRequested: boolean
  searchQuery: string
  showProductDetails: boolean
  productId?: string
}

const FBBrandDetails: React.FC<FBBrandDetailsProps> = ({
  provider,
  outlet,
  apiRequested,
  searchQuery,
  showProductDetails,
  productId = "",
}) => {
  const styles = useStyles()

  if (apiRequested) {
    return <BrandSkeleton />
  }

  return (
    <Box className={styles.container}>
      <FBProducts
        provider={provider}
        domain={provider?.domain}
        location={outlet?.id}
        isOpen={outlet?.isOpen || false}
        searchQuery={searchQuery}
        showProductDetails={showProductDetails}
        productId={productId}
      >
        <OutletDetails
          provider={provider}
          outlet={outlet}
          apiRequested={apiRequested}
        />
      </FBProducts>
    </Box>
  )
}

export const useStyles = makeStyles<any>(() => ({
  container: {
    flex: 1,
    padding: 16,
  },
}))

export default FBBrandDetails
