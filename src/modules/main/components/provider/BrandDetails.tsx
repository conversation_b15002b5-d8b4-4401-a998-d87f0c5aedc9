import Box from "@mui/material/Box"
import { makeStyles } from "@mui/styles"
import { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import { useNavigate, useSearchParams } from "react-router-dom"

import useNetworkErrorHandling from "../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../hooks/useNetworkHandling"
import {
  API_BASE_URL,
  PROVIDER,
  SERVICEABLE_LOCATIONS,
  STORE_DETAILS,
} from "../../../../utils/apiActions"
import { FB_DOMAIN } from "../../../../utils/constants"
import BrandSkeleton from "../../components/skeleton/BrandSkeleton"
import Page from "../page/Page"
import FBBrandDetails from "./components/FBBrandDetails"
import OtherBrandDetails from "./components/OtherBrandDetails"

import {
  emptyAlertCallback,
  getNextStartTime,
  getStoreTimeForDay,
  isCurrentTimeInRange,
} from "../../../../utils/utils"
import Header from "./components/Header"

const BrandDetails = () => {
  const { t } = useTranslation()
  const { address } = useSelector((state: any) => state.address)
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()

  const domain = searchParams.get("domain")
  const providerId = searchParams.get("provider_id")
  const bppId = searchParams.get("bpp_id")
  const outletLocationId = searchParams.get("locationId")
  const productId = searchParams.get("itemId") || ""
  const showProductDetails = productId !== ""

  const hasCoreParams = domain && providerId && bppId

  const brandId = hasCoreParams ? `${bppId}_${domain}_${providerId}` : null
  const outletId =
    hasCoreParams && outletLocationId ? `${brandId}_${outletLocationId}` : null

  const source = useRef<AbortController | null>(null)
  const styles = useStyles()
  const [provider, setProvider] = useState<any>(null)
  const [outlet, setOutlet] = useState<any>(null)
  const [apiRequested, setApiRequested] = useState<boolean>(true)
  const [outletDetailsRequested, setOutletDetailsRequested] =
    useState<boolean>(true)
  const { getDataWithAuth } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const [searchQuery, setSearchQuery] = useState<string>("")

  const getOutletDetails = async (domain: string, providerId: any = "") => {
    try {
      setOutletDetailsRequested(true)
      source.current = new AbortController()
      let locationId = outletId
      const domainQuery = domain ? `&domain=${domain}` : ""
      const pinCodeQuery = `&pincode=${address.address.areaCode}`

      const url = `${API_BASE_URL}${SERVICEABLE_LOCATIONS}?providerId=${
        brandId || providerId
      }&latitude=${address.address?.lat}&longitude=${
        address.address?.lng
      }${pinCodeQuery}${domainQuery}`
      const locationListResponse = await getDataWithAuth(url, {
        signal: source.current.signal,
      })

      if (!outletId && locationListResponse.data.data.length > 0) {
        locationId = locationListResponse.data.data[0].id
      }

      if (locationId) {
        const { data } = await getDataWithAuth(
          `${API_BASE_URL}${STORE_DETAILS}?id=${locationId}${pinCodeQuery}${domainQuery}`,
          { signal: source.current.signal }
        )

        if (data) {
          const isOpen = isCurrentTimeInRange(data.location_availabilities)
          setOutlet({
            ...data,
            isOpen,
            storeTime: getStoreTimeForDay(data.location_availabilities),
            time_from: getNextStartTime(data.location_availabilities),
            locations: locationListResponse?.data?.data,
          })
        }
      } else {
        navigate("/invalid-store-details", {
          state: {
            message: t(
              "Provider Details.This store does not service your location"
            ),
          },
        })
      }
    } catch (error) {
      handleApiError(error)
    } finally {
      setOutletDetailsRequested(false)
    }
  }

  const getProviderDetails = async () => {
    try {
      setApiRequested(true)
      source.current = new AbortController()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${PROVIDER}?id=${bppId}_${domain}_${providerId}`,
        { signal: source.current.signal }
      )

      document.title = data?.descriptor?.name
      await getOutletDetails(data?.domain, providerId)
      setProvider(data)
    } catch (error) {
      handleApiError(error)
    } finally {
      setApiRequested(false)
    }
  }

  useEffect(() => {
    getProviderDetails().then(emptyAlertCallback)

    return () => {
      if (source.current) {
        source.current.abort()
      }
    }
  }, [providerId])

  if (apiRequested) {
    return <BrandSkeleton />
  }

  return (
    <Page outletId={outlet?.id}>
      <Box className={styles.headerWrapper}>
        <Header searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
      </Box>
      <Box className={styles.container}>
        {provider?.domain === FB_DOMAIN ? (
          <FBBrandDetails
            provider={provider}
            outlet={outlet}
            apiRequested={apiRequested || outletDetailsRequested}
            searchQuery={searchQuery}
            showProductDetails={showProductDetails}
            productId={productId}
          />
        ) : (
          <OtherBrandDetails
            provider={provider}
            outlet={outlet}
            apiRequested={apiRequested || outletDetailsRequested}
            searchQuery={searchQuery}
          />
        )}
      </Box>
    </Page>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  headerWrapper: {
    position: "sticky",
    top: 0,
    zIndex: 1000,
    backgroundColor: "white",
  },
  container: {
    backgroundColor: theme.palette.white,
    height: "100dvh",
    overflow: "scroll",
    paddingBottom: "18%",
  },
  brandImage: {
    height: 268,
  },
  brandDetails: {
    padding: theme.spacing(2),
  },
  borderBottom: {
    backgroundColor: "#E0E0E0",
    height: 1,
    margin: theme.spacing(3, 0),
  },
}))

export default BrandDetails
