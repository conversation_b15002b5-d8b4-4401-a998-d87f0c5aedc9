import React, {useCallback} from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {useLocation, useNavigate} from 'react-router-dom';
import {useTranslation} from 'react-i18next';
import {makeStyles} from "@mui/styles";

import {ReactComponent as InvalidBarcode} from '../../../../assets/invalid_barcode.svg';

const InvalidBrandDetails: React.FC = () => {
  const styles = useStyles();
  const navigate = useNavigate();
  const {t} = useTranslation();
  const location = useLocation();

  // Extracting the message from route state
  const params = location.state || {};

  const navigateToHome = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  return (
    <Box className={styles.container}>
      <InvalidBarcode width={100} height={100}/>
      <Typography variant="labelSmall" className={styles.message}>
        {params.message || t('Default Error Message')}
      </Typography>
      <Button onClick={navigateToHome} className={styles.button}>
        <Typography variant="body1" className={styles.buttonText}>
          {t('Provider Details.Go Home')}
        </Typography>
      </Button>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100dvh',
    backgroundColor: theme.palette.common.white,
    padding: '20px',
  },
  message: {
    color: theme.palette.neutral400,
    textAlign: 'center',
    marginTop: '20px',
  },
  button: {
    marginTop: '28px',
    borderRadius: '8px',
    backgroundColor: theme.palette.primary.main,
    padding: '13px 24px',
    textAlign: 'center',
    '&:hover': {
      backgroundColor: theme.palette.primary.dark,
    },
  },
  buttonText: {
    color: theme.palette.common.white,
    textAlign: 'center',
  },
}));

export default InvalidBrandDetails;
