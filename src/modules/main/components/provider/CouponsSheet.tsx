import React, {useCallback} from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {useTranslation} from 'react-i18next';
import {useSelector} from 'react-redux';
import CloseSheetContainer from '../../components/bottomSheet/CloseSheetContainer';
import Coupon from './components/Coupon';
import {gestureStyle} from '../../../../utils/gestureStyle';
import {makeStyles} from "@mui/styles";

const CouponsSheet = ({coupons, closeSheet}: { coupons: any[], closeSheet: () => void }) => {
  const styles = useStyles();
  const gestureStyles = gestureStyle();
  const {t} = useTranslation();
  const {gestureEnabled} = useSelector((state: any) => state.gesture);

  const renderItem = useCallback(({item}: { item: any }) => {
    return <Coupon coupon={item}/>;
  }, []);

  return (
    <Box className={styles.container}>
      <CloseSheetContainer closeSheet={closeSheet}>
        <Box className={styles.header}>
          <Typography variant="headlineMedium" className={styles.title}>{t('Coupon.All Offers')}</Typography>
        </Box>
        <Box
          className={`${styles.couponsContainer}
            ${gestureEnabled ? gestureStyles.gestureContainer : gestureStyles.nonGestureContainer}`}>
          <Box className={styles.pageContainer}>
            {coupons?.map((item: any, index: number) => (
              <Box key={index}>{renderItem({item})}</Box>
            ))}
          </Box>
        </Box>
      </CloseSheetContainer>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  title: {
    fontWeight: 'bold'
  },
  container: {
    display: 'flex',
    justifyContent: 'flex-end',
    flexDirection: 'column',
  },
  couponsContainer: {
    backgroundColor: theme.palette.neutral50,
    flex: 1,
    paddingRight: 16,
    paddingLeft: 16,
  },
  header: {
    display: 'flex',
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: '14px 16px',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.palette.white,
    borderBottom: '1px solid' + theme.palette.neutral100,
  },
  pageContainer: {
    gap: 20,
    paddingTop: 16,
    paddingBottom: 16,
  },
}));

export default CouponsSheet;
