import React from "react";
import Typography from "@mui/material/Typography";
import {makeStyles} from "@mui/styles";
import {useTheme} from "@mui/material/styles";
import useFormatNumber from "../../hooks/useFormatNumber";

const Customizations: React.FC<{ cartItem: any }> = ({cartItem}) => {
  const {formatNumber} = useFormatNumber();
  const theme = useTheme();
  const classes = useStyles(theme);

  const {customisations} = cartItem.item;

  if (customisations && customisations.length > 0) {
    return (
      <Typography variant="labelSmall" className={classes.label}>
        {customisations.map((customization: any, index: number) => {
          const isLastItem = index === customisations.length - 1;
          return `${
            customization.item_details.descriptor.name
          } (₹${formatNumber(customization.item_details.price.value)})${
            isLastItem ? "" : " + "
          }`;
        })}
      </Typography>
    );
  }

  return null;
};
const useStyles = makeStyles<any>((theme) => ({
  label: {
    color: theme.palette.text.secondary,
  },
}));

export default Customizations;
