import React from "react";
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import AddIcon from '@mui/icons-material/Add';
import RemoveIcon from '@mui/icons-material/Remove';
import {makeStyles} from "@mui/styles";
import {useTheme} from "@mui/material/styles";
import useFormatNumber from "../../hooks/useFormatNumber";

interface ManageQuantityProps {
  cartItem: any;
  updatingCartItem: any;
  updateCartItem: (
    locationId: any,
    item: any,
    increment: boolean,
    uniqueId: any
  ) => void;
  allowDelete?: boolean;
  deleteCartItem?: (itemId: any) => void;
}

const ManageQuantity: React.FC<ManageQuantityProps> = ({
                                                         cartItem,
                                                         updatingCartItem,
                                                         updateCartItem,
                                                         allowDelete = false,
                                                         deleteCartItem = () => {
                                                         },
                                                       }) => {
  const {formatNumber} = useFormatNumber();
  const theme = useTheme();
  const classes = useStyles(theme);

  return (
    <Box className={classes.quantityContainer}>
      {allowDelete && cartItem?.item?.quantity?.count === 1 ? (
        <IconButton
          disabled={!!updatingCartItem || cartItem?.item?.quantity?.count === 0}
          onClick={() => deleteCartItem(cartItem._id)}
          size="small"
        >
          <RemoveIcon color="primary"/>
        </IconButton>
      ) : (
        <IconButton
          disabled={!!updatingCartItem || cartItem?.item?.quantity?.count === 0}
          onClick={() =>
            updateCartItem(
              cartItem.item.location_details.id,
              cartItem.item.id,
              false,
              cartItem._id
            )
          }
          size="small"
        >
          <RemoveIcon color="primary"/>
        </IconButton>
      )}
      <Box className={classes.quantity}>
        {updatingCartItem === cartItem._id ? (
          <CircularProgress color="primary" size={16}/>
        ) : (
          <Typography variant="bodyMedium" className={classes.quantityText}>
            {formatNumber(cartItem?.item?.quantity?.count)}
          </Typography>
        )}
      </Box>
      <IconButton
        disabled={!!updatingCartItem}
        onClick={() =>
          updateCartItem(
            cartItem.item.location_details.id,
            cartItem.item.id,
            true,
            cartItem._id
          )
        }
        size="small"
      >
        <AddIcon color="primary"/>
      </IconButton>
    </Box>
  );
};
const useStyles = makeStyles((theme: any) => ({
  quantityContainer: {
    borderRadius: 12,
    border: `1px solid ${theme.palette.divider}`,
    backgroundColor: theme.palette.background.paper,
    display: "flex",
    alignItems: "center",
    padding: "4px 8px",
    width: 90,
  },
  quantity: {
    flex: 1,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    margin: "0 4px",
  },
  quantityText: {
    color: theme.palette.text.primary,
  },
}));

export default ManageQuantity;
