import { createContext, ReactNode, useContext } from "react"
import { useNavigate } from "react-router-dom"
import useFulfillmentErrorModal from "../../hooks/useFulfillmentErrorModal"
import FulfillmentErrorModal from "../modals/FulfillmentErrorModal"

interface FulfillmentErrorContextType {
  showFulfillmentError: (errorCode: string, cartLocationId: string) => void
  hideFulfillmentError: () => void
}

const FulfillmentErrorContext = createContext<
  FulfillmentErrorContextType | undefined
>(undefined)

export const useFulfillmentErrorContext = () => {
  const context = useContext(FulfillmentErrorContext)
  if (!context) {
    throw new Error(
      "useFulfillmentErrorContext must be used within FulfillmentErrorProvider"
    )
  }
  return context
}

interface FulfillmentErrorProviderProps {
  children: ReactNode
}

const FulfillmentErrorProvider = ({
  children,
}: FulfillmentErrorProviderProps) => {
  const { isOpen, errorData, showFulfillmentError, hideFulfillmentError } =
    useFulfillmentErrorModal()
  const navigate = useNavigate()

  return (
    <FulfillmentErrorContext.Provider
      value={{ showFulfillmentError, hideFulfillmentError }}
    >
      {children}
      {errorData && (
        <FulfillmentErrorModal
          open={isOpen}
          onClose={hideFulfillmentError}
          errorCode={errorData.errorCode}
          cartLocationId={errorData.cartLocationId}
          navigate={navigate}
        />
      )}
    </FulfillmentErrorContext.Provider>
  )
}

export default FulfillmentErrorProvider
