import ClearIcon from "@mui/icons-material/Clear"
import Box from "@mui/material/Box"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useCallback, useState } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import NoImageAvailable from "../../../../assets/noImage.png"
import StoreIcon from "../../../../assets/no_store_icon.svg"
interface StoreImage {
  source: any
  imageStyle: { width: number; height: number }
}

const StoreImage: React.FC<StoreImage> = ({ source, imageStyle }) => {
  const [imageSource, setImageSource] = useState(source)
  const [imageLoadFailed, setImageLoadFailed] = useState<boolean>(false)

  const styles = useStoreStyles()

  const onError = () => {
    setImageLoadFailed(true)
    setImageSource(NoImageAvailable)
  }

  return source ? (
    <img
      src={imageLoadFailed ? NoImageAvailable : imageSource}
      alt="image"
      style={{
        ...imageStyle,
        width: 46,
        height: 46,
        borderRadius: 12,
        flexShrink: 0,
        objectFit: imageLoadFailed ? "cover" : "contain",
      }}
      onError={onError}
    />
  ) : (
    <Box className={styles.brandImageEmpty} sx={{ ...imageStyle }}>
      <img src={StoreIcon} alt="Store Icon" width={48} height={48} />
    </Box>
  )
}
interface StoreCartProps {
  cartItem: any
  confirmDelete: (value: string) => void
}

const StoreCart: React.FC<StoreCartProps> = ({ cartItem, confirmDelete }) => {
  const { t } = useTranslation()
  const theme = useTheme()
  const navigate = useNavigate()
  const styles = useStyles()

  const navigateToStoreCart = useCallback(() => {
    navigate(`/cart/${cartItem.location_id}`)
  }, [cartItem, navigate])

  return (
    <Box className={styles.container}>
      <Box onClick={navigateToStoreCart} className={styles.button}>
        <Box className={styles.provider}>
          <StoreImage
            source={
              cartItem?.location?.provider_descriptor?.symbol ||
              NoImageAvailable
            }
            // imageStyle={styles.providerImage}
          />
          {/* <Box
            component="img"
            src={
              cartItem?.location?.provider_descriptor?.symbol ||
              NoImageAvailable
            }
            alt="Provider"
            className={styles.providerImage}
          /> */}
          <Box className={styles.providerMetaContainer}>
            <Typography
              variant="bodyLarge"
              className={styles.text}
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              {cartItem?.location?.provider_descriptor?.name
                ? cartItem?.location?.provider_descriptor?.name
                : cartItem?.items?.[0]?.item?.provider?.descriptor?.name}
            </Typography>

            <Typography variant="labelMedium" className={styles.text}>
              {cartItem?.items?.length} {t("Cart.items")}
            </Typography>
          </Box>
        </Box>
        <Box className={styles.actionContainer}>
          <Box className={styles.viewCart}>
            <Typography variant="bodyLarge" className={styles.viewCartLabel}>
              {t("Cart.View Cart")}
            </Typography>
          </Box>
          <Box
            onClick={(e) => {
              e.stopPropagation()
              confirmDelete(cartItem)
            }}
            className={styles.cancelButton}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <ClearIcon
              sx={{ fontSize: 18, color: theme.palette.common.white }}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    backgroundColor: "transparent",
    cursor: "pointer",
    width: "100%",
    marginBottom: 8,
  },

  providerMetaContainer: {
    flex: 1,
    minWidth: 0, // Allows text truncation
    marginLeft: 12,
  },
  button: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: theme.palette.primary.main,
    padding: "12px 16px",
    borderRadius: 12,
    textTransform: "none",
    width: "100%",
    boxSizing: "border-box",
  },
  provider: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    minWidth: 0, // Allows flex shrinking
    marginRight: 12,
  },
  text: {
    color: theme.palette.common.white,
    textAlign: "left",
    display: "block",
  },
  actionContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    flexShrink: 0,
  },
  viewCart: {
    height: 36,
    minWidth: 80,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: theme.palette.common.white,
    borderRadius: 8,
    padding: "0 12px",
    boxSizing: "border-box",
  },
  viewCartLabel: {
    color: theme.palette.primary.main,
  },
  cancelButton: {
    backgroundColor: "rgba(244, 249, 251, 0.5)",
    width: 32,
    height: 32,
    borderRadius: 16,
    minWidth: 32,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    cursor: "pointer",
    flexShrink: 0,
    "&:hover": {
      backgroundColor: "rgba(244, 249, 251, 0.8)",
    },
  },
}))

export default StoreCart
const useStoreStyles = makeStyles<any>((theme) => ({
  brandImageEmpty: {
    backgroundColor: theme.palette.neutral200,
  },
  name: {
    color: theme.palette.neutral400,
    textAlign: "center",
    lineHeight: "14px",
    marginTop: 4,
  },
  container: {
    // flexDirection: "row",
    // display: "flex",
    alignItems: "center",
    cursor: "pointer",
  },
  eta: {
    color: theme.palette.neutral300,
  },
  providerImage: {
    width: 46,
    height: 46,
    borderRadius: 12,
    flexShrink: 0,
  },
}))
