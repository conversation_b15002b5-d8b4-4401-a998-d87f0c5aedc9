import React, { useCallback } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import { useTheme } from "@mui/material/styles"
import { makeStyles } from "@mui/styles"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import ClearIcon from "@mui/icons-material/Clear"
import NoImageAvailable from "../../../../assets/noImage.png"

interface StoreCartProps {
  cartItem: any
  confirmDelete: (value: string) => void
}

const StoreCart: React.FC<StoreCartProps> = ({ cartItem, confirmDelete }) => {
  const { t } = useTranslation()
  const theme = useTheme()
  const navigate = useNavigate()
  const styles = useStyles()

  const navigateToStoreCart = useCallback(() => {
    navigate(`/cart/${cartItem.location_id}`)
  }, [cartItem, navigate])

  return (
    <Box className={styles.container}>
      <Box onClick={navigateToStoreCart} className={styles.button}>
        <Box className={styles.provider}>
          <Box
            component="img"
            src={
              cartItem?.location?.provider_descriptor?.symbol ||
              NoImageAvailable
            }
            alt="Provider"
            className={styles.providerImage}
          />
          <Box className={styles.providerMetaContainer}>
            <Typography
              variant="bodyLarge"
              className={styles.text}
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              {cartItem?.location?.provider_descriptor?.name
                ? cartItem?.location?.provider_descriptor?.name
                : cartItem?.items?.[0]?.item?.provider?.descriptor?.name}
            </Typography>

            <Typography variant="labelMedium" className={styles.text}>
              {cartItem?.items?.length} {t("Cart.items")}
            </Typography>
          </Box>
        </Box>
        <Box className={styles.actionContainer}>
          <Box className={styles.viewCart}>
            <Typography variant="bodyLarge" className={styles.viewCartLabel}>
              {t("Cart.View Cart")}
            </Typography>
          </Box>
          <Box
            onClick={(e) => {
              e.stopPropagation()
              confirmDelete(cartItem)
            }}
            className={styles.cancelButton}
          >
            <ClearIcon
              sx={{ fontSize: 16, color: theme.palette.common.white }}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    backgroundColor: "transparent",
    cursor: "pointer",
  },
  providerImage: {
    width: 46,
    height: 46,
    borderRadius: 12,
    marginLeft: 10,
  },
  providerMetaContainer: {
    width: "65%",
  },
  button: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: theme.palette.primary.main,
    paddingTop: 7,
    paddingBottom: 9,
    borderRadius: 12,
    textTransform: "none",
    width: "100%",
  },
  provider: {
    gap: 14,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    width: "55%",
  },
  text: {
    color: theme.palette.common.white,
    textAlign: "left",
    display: "block",
  },
  actionContainer: {
    gap: 8,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    width: 120,
    marginLeft: 34,
  },
  viewCart: {
    height: 43,
    width: 88,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: theme.palette.common.white,
    borderRadius: 5,
  },
  viewCartLabel: {
    color: theme.palette.primary.main,
  },
  cancelButton: {
    backgroundColor: "rgba(244, 249, 251, 0.5)",
    width: 24,
    height: 24,
    borderRadius: 24,
    minWidth: 24,
    alignItems: "center",
    justifyContent: "center",
  },
}))

export default StoreCart
