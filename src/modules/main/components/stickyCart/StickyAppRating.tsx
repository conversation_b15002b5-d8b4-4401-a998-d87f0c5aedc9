import { useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import { useTheme } from "@mui/material/styles"
import { makeStyles } from "@mui/styles"
import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import CloseIcon from "@mui/icons-material/Close"
import StarIcon from "@mui/icons-material/Star"
import StarBorderIcon from "@mui/icons-material/StarBorder"
import ShoppingBagIcon from "@mui/icons-material/ShoppingBag"
import {
  getMessage,
  getMongoId,
  getOrderId,
  resetAppRatingEligibility,
} from "../../../../toolkit/reducer/appRating"
import useAppRating from "../../hooks/useAppRating"
import { API_BASE_URL, APP_RATING_DISMISS } from "../../../../utils/apiActions"
import axios from "axios"
import useNetworkHandling from "../../../../hooks/useNetworkHandling"

const StickyAppRating = () => {
  const theme = useTheme()
  const styles = useStyles()
  const dispatch = useDispatch()
  const message = useSelector(getMessage)
  const mongoId = useSelector(getMongoId)
  const businessOrderId = useSelector(getOrderId) // Get business order ID at component level
  const navigate = useNavigate()
  const { resetEligibility } = useAppRating()
  const { postDataWithAuth } = useNetworkHandling()
  const source = useRef<any>(null)
  const [selectedRating, setSelectedRating] = useState<number>(0)
  const [isDismissing, setIsDismissing] = useState<boolean>(false)

  const handleStarPress = (rating: number) => {
    setSelectedRating(rating)

    if (mongoId) {
      navigate(
        `/rating-detail/${mongoId}?rating=${rating}&source=StickyAppRating`
      )
    } else {
      alert("Cannot find order information. Please try again later.")
    }
  }

  const handleClose = async () => {
    setIsDismissing(true)

    try {
      // Dismiss UI first for better UX
      dispatch(resetAppRatingEligibility())
      resetEligibility()

      // Create a new cancel token source
      source.current = axios.CancelToken.source()

      // Call the dismiss API endpoint if businessOrderId is available
      if (businessOrderId) {
        const url = `${API_BASE_URL}${APP_RATING_DISMISS}${businessOrderId}`
        await postDataWithAuth(url, {}, source.current.token)
      }
    } catch (error) {
      console.error("Error dismissing rating:", error)
      // No need to show an alert since the UI is already dismissed
    } finally {
      setIsDismissing(false)
    }
  }

  return (
    <Box className={styles.outerContainer}>
      <Box className={styles.innerContainer}>
        {/* Left side - Shopping Bag Icon */}
        <Box className={styles.imageContainer}>
          <ShoppingBagIcon
            sx={{ fontSize: 60, color: theme.palette.primary.main }}
          />
        </Box>

        {/* Right side - Rating content */}
        <Box className={styles.contentContainer}>
          {/* First row - Message */}
          <Typography className={styles.messageText}>
            {message || "Rate your Order Experience"}
          </Typography>

          {/* Second row - Star rating */}
          <Box className={styles.starsContainer}>
            {[1, 2, 3, 4, 5].map((rating) => (
              <IconButton
                key={rating}
                onClick={() => handleStarPress(rating)}
                className={styles.starButton}
                size="small"
              >
                {rating <= selectedRating ? (
                  <StarIcon
                    sx={{ fontSize: 26, color: theme.palette.warning.main }}
                  />
                ) : (
                  <StarBorderIcon
                    sx={{ fontSize: 26, color: theme.palette.grey[400] }}
                  />
                )}
              </IconButton>
            ))}
          </Box>
        </Box>

        {/* Close button - Make it more prominent */}
        <IconButton
          className={styles.closeButton}
          onClick={handleClose}
          disabled={isDismissing}
          size="small"
        >
          <CloseIcon sx={{ fontSize: 24, color: "#000000" }} />
        </IconButton>
      </Box>
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  outerContainer: {
    height: 86,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    paddingTop: 12,
    paddingRight: 12,
    paddingLeft: 12,
    backgroundColor: theme.palette.common.white,
    boxShadow: "0px -2px 4px rgba(0, 0, 0, 0.2)",
  },
  innerContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    height: "100%",
  },
  imageContainer: {
    width: 60,
    height: 60,
    marginRight: 12,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  contentContainer: {
    flex: 1,
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
  },
  messageText: {
    fontSize: 16,
    fontWeight: 600,
    color: theme.palette.primary.main,
    marginBottom: 8,
    lineHeight: 1.2,
  },
  starsContainer: {
    display: "flex",
    flexDirection: "row",
    height: 26,
    width: 154,
  },
  starButton: {
    marginRight: 5,
    padding: 0,
    minWidth: 26,
    width: 26,
    height: 26,
  },
  closeButton: {
    padding: 8,
    marginLeft: 8,
    height: 40,
    width: 40,
    minWidth: 40,
  },
}))

export default StickyAppRating
