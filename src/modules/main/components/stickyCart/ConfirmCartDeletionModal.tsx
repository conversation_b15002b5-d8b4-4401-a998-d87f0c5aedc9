import CloseIcon from "@mui/icons-material/Close"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import IconButton from "@mui/material/IconButton"
import Modal from "@mui/material/Modal"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useMemo } from "react"
import { useTranslation } from "react-i18next"

import useClearCart from "../../hooks/useClearCart"

interface ConfirmCartDeletionModalProps {
  selectedCart: any
  closeDeletionModal: () => void
}

const ConfirmCartDeletionModal: React.FC<ConfirmCartDeletionModalProps> = ({
  selectedCart,
  closeDeletionModal,
}) => {
  const { t } = useTranslation()
  const theme = useTheme()
  const styles = useStyles()
  const { deleteStoreCart, deleteInProgress } = useClearCart()

  const deleteCartStore = async () => {
    try {
      await deleteStoreCart(selectedCart)
      closeDeletionModal()
    } catch (error) {
      console.log(error)
    }
  }

  const providerName = useMemo(() => {
    return selectedCart?.location?.provider_descriptor?.name
      ? selectedCart?.location?.provider_descriptor?.name
      : selectedCart?.items?.[0]?.item?.provider?.descriptor?.name
  }, [selectedCart])

  return (
    <Modal
      open={!!selectedCart}
      onClose={closeDeletionModal}
      closeAfterTransition
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        padding: 2,
      }}
    >
      <Box
        className={styles.modal}
        sx={{
          width: "100%",
          maxWidth: "400px",
          outline: "none",
          padding: 2,
        }}
      >
        <Box className={styles.modalHeader}>
          <Typography
            variant="headlineSmall"
            className={styles.modalTitle}
            sx={{
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
            }}
          >
            {t("Cart.Remove from Cart")}
          </Typography>
          <IconButton
            disabled={deleteInProgress}
            onClick={closeDeletionModal}
            size="small"
          >
            <CloseIcon sx={{ fontSize: 24, color: theme.palette.grey[600] }} />
          </IconButton>
        </Box>
        <Typography variant="bodySmall" className={styles.message}>
          {t("Cart.Are you sure you want to remove cart", {
            provider: providerName,
          })}
        </Typography>
        <Box className={styles.modalFooter}>
          <Button
            disabled={deleteInProgress}
            className={styles.containButton}
            onClick={closeDeletionModal}
            variant="contained"
          >
            <Typography variant="bodyLarge" className={styles.text}>
              {t("WishList.No")}
            </Typography>
          </Button>

          <Button
            disabled={deleteInProgress}
            className={styles.button}
            onClick={deleteCartStore}
            variant="outlined"
          >
            <Typography variant="bodyLarge" className={styles.yesLabel}>
              {deleteInProgress ? (
                <CircularProgress size={20} color="primary" />
              ) : (
                t("WishList.Yes")
              )}
            </Typography>
          </Button>
        </Box>
      </Box>
    </Modal>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  text: {
    color: theme.palette.common.white,
  },
  modal: {
    backgroundColor: theme.palette.common.white,
    borderRadius: 16,
  },
  modalHeader: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  modalTitle: {
    color: theme.palette.primary.main,
    flex: 1,
  },
  message: {
    marginTop: 8,
    color: theme.palette.grey[600],
    marginBottom: 28,
  },
  modalFooter: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    gap: 15,
    marginTop: 10,
  },
  button: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    textTransform: "none",
  },
  containButton: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    textTransform: "none",
  },
  yesLabel: {
    color: theme.palette.primary.main,
  },
}))

export default ConfirmCartDeletionModal
