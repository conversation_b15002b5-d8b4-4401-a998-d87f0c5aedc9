import useNetworkHandling from '../../../../../hooks/useNetworkHandling';
import {
  API_BASE_URL,
  APP_RATING_ELIGIBILITY,
  POST_APP_RATING,
} from '../../../../../utils/apiActions';
import {CancelToken} from 'axios';

/**
 * App Rating Service that provides functions for checking eligibility and submitting ratings
 */
const useAppRatingService = () => {
  const networkHandling = useNetworkHandling();

  /**
   * Checks if a user is eligible for app rating
   * @param userId The user ID to check for rating eligibility
   * @param cancelToken Cancel token from axios.CancelToken.source().token
   */
  const checkAppRatingEligibility = async (
    userId: string,
    cancelToken?: CancelToken,
  ) => {
    try {
      const {data} = await networkHandling.getDataWithAuth(
        `${API_BASE_URL}${APP_RATING_ELIGIBILITY}`,
        cancelToken,
      );
      return data;
    } catch (error) {
      // Returning default "not eligible" response on error
      return {apprating: false};
    }
  };

  /**
   * Submits a user's app rating
   * @param orderId The order ID being rated
   * @param mongoId MongoDB ID for the order
   * @param storeId Store ID in the format required by the API
   * @param orderRating Overall order rating (1-5)
   * @param storeRating Rating for the store (1-5)
   * @param itemRatings Ratings for individual items
   * @param deliveryRating Rating for delivery experience (1-5)
   * @param userExperience Optional text comment about the experience
   * @param dismissed Whether the rating was dismissed (0 = not dismissed, other values = reason codes)
   * @param storeProviderID Provider ID for the store (optional)
   * @param cancelToken Cancel token for the request
   */
  const submitAppRating = async (
    orderId: string,
    mongoId: string,
    storeId: string,
    orderRating: number,
    storeRating: number,
    itemRatings: Array<{id: string; item_id: string; rating: number}>,
    deliveryRating: number,
    userExperience: string = '',
    dismissed: number = 0,
    storeProviderID: string = '',
    cancelToken?: CancelToken,
  ) => {
    try {
      // Validate required parameters
      if (!orderId) throw new Error('orderId is required');
      if (!mongoId) throw new Error('mongoId is required');
      if (!storeId) throw new Error('storeId is required');
      if (!orderRating || orderRating < 1 || orderRating > 5) {
        throw new Error('orderRating must be between 1 and 5');
      }

      // Format items with proper nesting structure
      const formattedItems = itemRatings.map(item => ({
        id: item.id,
        item_id: item.item_id,
        rating: {
          count: {'1': '', '2': '', '3': '', '4': '', '5': ''},
          userating: item.rating,
        },
      }));

      // Build the payload with the correct structure according to schema
      const payload = {
        _id: mongoId,
        userId: '', // Will be populated by backend
        order_id: orderId,
        orderMongoId: mongoId,
        orderrating: orderRating.toString(), // Use orderRating for overall rating
        dismissed: dismissed,
        store: {
          providerID: storeProviderID,
          id: storeId,
          rating: {
            count: {'1': '', '2': '', '3': '', '4': '', '5': ''},
            'avg rating': '',
            userrating: storeRating, // Use storeRating for store
          },
        },
        items: formattedItems,
        deliveryexp: {
          userating: deliveryRating,
        },
        userexperience: userExperience,
      };


      const url = `${API_BASE_URL}${POST_APP_RATING}${orderId}`;

      const {data} = await networkHandling.postDataWithAuth(
        url,
        payload,
        cancelToken,
      );


      return data;
    } catch (error) {
      console.error('Rating submission error in service:', error);
      throw error;
    }
  };

  return {
    checkAppRatingEligibility,
    submitAppRating,
  };
};

export default useAppRatingService;
