import React, { useState } from "react"
import { useSelector } from "react-redux"
import { useTranslation } from "react-i18next"
import { useTheme } from "@mui/material/styles"
import { makeStyles } from "@mui/styles"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Typography from "@mui/material/Typography"
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp"

import StoreCart from "./StoreCart"
import ConfirmCartDeletionModal from "./ConfirmCartDeletionModal"
import CartSheet from "../../modules/cart/all/CartSheet"
import { getAppRating } from "../../../../toolkit/reducer/appRating"
import StickyAppRating from "./StickyAppRating"

interface StickyCartProps {
  children: React.ReactNode
}

const StickyCart: React.FC<StickyCartProps> = ({ children }) => {
  const { t } = useTranslation()
  const { cartItems } = useSelector((state: any) => state.cart)
  console.log("🚀 ~ cartItems:", cartItems)
  const theme = useTheme()
  const styles = useStyles()
  const [selectedCart, setSelectedCart] = useState<any>(null)
  const [isCartSheetOpen, setIsCartSheetOpen] = useState(false)
  //for app rating
  const apprating = useSelector(getAppRating)
  // const message = useSelector(getMessage)

  const confirmDelete = (cart: string) => setSelectedCart(cart)
  const closeDeletionModal = () => setSelectedCart(null)
  const openList = () => setIsCartSheetOpen(true)
  const closeCartSheet = () => setIsCartSheetOpen(false)
  // logic for future : if cart.length >0 then stick cart, else if apprating= true then stickapprating, else none
  return (
    <>
      {children}
      {
        cartItems.length > 0 ? (
          // Container for cart UI - original styling (priority 1)
          <Box className={styles.cartContainer}>
            {cartItems.length > 1 && (
              <>
                {/* <Box className={styles.backgroundCards} /> */}

                <Box className={styles.allStoresButton} onClick={openList}>
                  <Typography variant="body2" className={styles.allStoresLabel}>
                    {t("Cart.All Stores")}
                  </Typography>
                  <KeyboardArrowUpIcon
                    sx={{ fontSize: 12, color: theme.palette.common.white }}
                  />
                </Box>
              </>
            )}
            <StoreCart cartItem={cartItems[0]} confirmDelete={confirmDelete} />
          </Box>
        ) : apprating ? (
          // Container for rating UI - only shows if cart is empty (priority 2)
          <Box className={styles.ratingContainer}>
            <StickyAppRating />
          </Box>
        ) : null /* Show nothing if cart is empty and no rating */
      }
      {selectedCart && (
        <ConfirmCartDeletionModal
          closeDeletionModal={closeDeletionModal}
          selectedCart={selectedCart}
        />
      )}
      {isCartSheetOpen && (
        <CartSheet isOpen={isCartSheetOpen} onClose={closeCartSheet} />
      )}
    </>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  // Separate container styles for cart and rating
  cartContainer: {
    paddingLeft: 12,
    paddingRight: 20,
    paddingBottom: 4,
    backgroundColor: theme.palette.primary.main,
    paddingTop: 2,
    borderRadius: 12,
    position: "relative",
    width: "85%",
    marginLeft: "3%",
    marginRight: "3%",
    // height: 86,
  },
  ratingContainer: {
    backgroundColor: theme.palette.common.white,
    // No padding here since StickyAppRating has its own padding
    boxShadow: "0px -2px 4px rgba(0, 0, 0, 0.1)",
    height: 86,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  backgroundCards: {
    boxShadow: "0px 0px 2px rgba(0, 0, 0, 0.25)",
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: theme.palette.grey[100],
    marginRight: 16,
    height: 20,
    marginBottom: -10,
  },

  allStoresButton: {
    backgroundColor: theme.palette.primary.dark,
    paddingLeft: 12,
    paddingRight: 8,
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    borderRadius: 21,
    paddingTop: 4,
    paddingBottom: 4,
    minWidth: "auto",
    textTransform: "none",
    left: "35%",
    display: "flex",

    position: "absolute",
    zIndex: 1,
    width: 85,
    top: -21,
  },
  allStoresLabel: {
    color: theme.palette.common.white,
  },
}))

export default StickyCart
