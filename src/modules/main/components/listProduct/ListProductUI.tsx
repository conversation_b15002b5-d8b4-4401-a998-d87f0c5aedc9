import React from 'react';
import {useTranslation} from 'react-i18next';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import VegNonVegTag from '../products/VegNonVegTag';
import WishlistButton from '../products/WishlistButton';
import ListProductQuantityControl from './ListProductQuantityControl';
import ListProductAddProduct from './ListProductAddProduct';
import ListProductImage from './ListProductImage';
import DescriptionText from './DescriptionText';
import {formatToIndianCurrency} from '../../../../utils/utils';
import ProductAmount from '../products/productAmount/ProductAmount';
import DefaultProductAmount from '../products/productAmount/DefaultProductAmount';

interface ListProductUI {
  apiInProgress: boolean;
  defaultPrice: any;
  disabled: boolean;
  currency: any;
  priceRange: any;
  product: any;
  productLoading: boolean;
  showProductDetails: () => void;
  productImageSource: any;
  cartItemDetails: any;
  removeQuantityClick: () => void;
  incrementProductQuantity: () => void;
  customizable: boolean;
  inStock: boolean;
  addToCart: () => {};
  addItemToWishlist: () => void;
  deleteItemFromWishlist: () => void;
  addedToWishlist: boolean;
  wishlistLoader: boolean;
}

const ListProductUI: React.FC<ListProductUI> = ({
                                                  apiInProgress,
                                                  defaultPrice,
                                                  disabled,
                                                  currency,
                                                  priceRange,
                                                  product,
                                                  productLoading,
                                                  showProductDetails,
                                                  productImageSource,
                                                  cartItemDetails,
                                                  removeQuantityClick,
                                                  incrementProductQuantity,
                                                  customizable,
                                                  inStock,
                                                  addToCart,
                                                  addItemToWishlist,
                                                  deleteItemFromWishlist,
                                                  addedToWishlist,
                                                  wishlistLoader,
                                                }) => {
  const {t} = useTranslation();
  const styles = useStyles();

  const renderProductImage = () => {
    return <ListProductImage productImageSource={productImageSource}/>;
  };

  const renderPrice = () => {
    if (defaultPrice) {
      return (
        <DefaultProductAmount
          currency={currency}
          defaultValue={defaultPrice}
          price={product?.item_details?.price}
        />
      );
    } else if (priceRange) {
      const min = formatToIndianCurrency(
        Number(priceRange?.minPrice).toFixed(2),
      );
      const max = formatToIndianCurrency(
        Number(priceRange?.maxPrice).toFixed(2),
      );
      return (
        <Typography variant="labelLarge" color="textSecondary">
          {currency}
          {min} - {currency}
          {max}
        </Typography>
      );
    } else if (product?.item_details?.price?.value) {
      return (
        <ProductAmount
          currency={currency}
          price={product?.item_details?.price}
        />
      );
    } else if (product?.item_details?.price?.with_customisation_value) {
      return (
        <Typography variant="labelLarge" color="textSecondary">
          {currency}
          {formatToIndianCurrency(Number(product?.item_details?.price?.with_customisation_value).toFixed(2))}
        </Typography>
      );
    } else {
      return <></>;
    }
  };

  // @ts-ignore
  return (
    <>
      <Box className={styles.product}>
        <Box className={styles.meta}>
          <Button
            className={styles.buttonContainer}
            sx={{padding: 0, textAlign: 'left', display: 'block'}}
            onClick={showProductDetails}
            disabled={disabled}
          >
            <Typography variant="labelLarge" className={styles.name} color="textPrimary" noWrap>
              {product?.item_details?.descriptor?.name}
            </Typography>
            <DescriptionText description={product?.item_details?.descriptor?.short_desc}/>
            {renderPrice()}
          </Button>
        </Box>
        <Box className={styles.actionContainer}>
          <Box className={styles.imageContainer} onClick={showProductDetails}>
            {disabled ? (
              <Box sx={{filter: 'grayscale(100%)'}}>
                {renderProductImage()}
              </Box>
            ) : (
              renderProductImage()
            )}
            <Box className={styles.productTag} sx={{position: 'absolute', top: 8, left: 8}}>
              <VegNonVegTag tags={product?.item_details?.tags}/>
            </Box>
          </Box>
          <WishlistButton
            wishlistLoader={wishlistLoader}
            addedToWishlist={addedToWishlist}
            deleteItemFromWishlist={deleteItemFromWishlist}
            addItemToWishlist={addItemToWishlist}
          />
          {cartItemDetails?.productQuantity > 0 ? (
            <ListProductQuantityControl
              disabled={disabled}
              customizable={customizable}
              productLoading={productLoading}
              removeQuantityClick={removeQuantityClick}
              incrementProductQuantity={incrementProductQuantity}
              productQuantity={cartItemDetails?.productQuantity}
            />
          ) : inStock ? (
            <ListProductAddProduct
              apiInProgress={apiInProgress}
              customizable={customizable}
              disabled={disabled}
              addToCart={addToCart}
            />
          ) : (
            <Box className={styles.outOfStockButtonContainer}>
              <Typography variant="labelLarge" color="textSecondary" className={styles.outOfStock}>
                {t('Cart.FBProduct.Out of stock')}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  product: {
    display: 'flex',
    padding: 4,
    flexDirection: 'row',
    marginTop: 20,
    width: '100%',
    justifyContent: 'space-between'
  },
  meta: {
    flex: 1,
    paddingRight: 10,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    maxWidth: '65% !important'
  },
  buttonContainer: {
    maxWidth: '100% !important'
  },
  actionContainer: {
    display: 'flex',
    width: 126,
    alignItems: 'center',
    flexDirection: 'column',
    position: 'relative',
  },
  imageContainer: {
    marginBottom: 2,
    height: 126,
  },
  outOfStockButtonContainer: {
    textAlign: 'center',
    backgroundColor: theme.palette.white,
    paddingTop: 2,
    paddingBottom: 2,
  },
  name: {
    color: theme.palette.neutral400,
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    display: 'block',
    maxWidth: '100%',
  },
  outOfStock: {
    display: 'flex',
    textAlign: 'center',
    color: theme.palette.neutral300,
  },
  productTag: {
    position: 'absolute',
    top: 0,
    left: 8,
  },
}));

export default ListProductUI;
