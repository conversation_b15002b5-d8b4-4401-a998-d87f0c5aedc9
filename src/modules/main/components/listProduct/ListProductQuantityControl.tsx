import React from 'react';
import {useTranslation} from 'react-i18next';
import {useTheme} from '@mui/material/styles';
import Box from '@mui/material/Box';
import IconButton from "@mui/material/IconButton";
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import RemoveOutlinedIcon from '@mui/icons-material/RemoveOutlined';
import AddOutlinedIcon from '@mui/icons-material/AddOutlined';
import {makeStyles} from '@mui/styles';
import useFormatNumber from '../../hooks/useFormatNumber';

const ListProductQuantityControl = ({
                                      disabled,
                                      customizable,
                                      productLoading,
                                      removeQuantityClick,
                                      incrementProductQuantity,
                                      productQuantity,
                                    }: {
  disabled: boolean;
  customizable: boolean;
  productLoading: boolean;
  removeQuantityClick: () => void;
  incrementProductQuantity: () => void;
  productQuantity: string;
}) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useStyles();
  const {formatNumber} = useFormatNumber();

  return (
    <Box className={styles.buttonContainer}>
      <Box className={styles.actionButton}>
        <IconButton
          className={styles.iconButton}
          onClick={removeQuantityClick}
          disabled={disabled}
        >
          <RemoveOutlinedIcon color="primary" fontSize="small"/>
        </IconButton>

        <Typography
          variant="bodyLarge"
          style={{
            color: disabled ? theme.palette.text.disabled : theme.palette.text.primary,
          }}
        >
          {productLoading ? (
            <CircularProgress size={18}/>
          ) : (
            formatNumber(productQuantity)
          )}
        </Typography>

        <IconButton
          className={styles.iconButton}
          onClick={incrementProductQuantity}
          disabled={disabled}
        >
          <AddOutlinedIcon color="primary" fontSize="small"/>
        </IconButton>
      </Box>

      {customizable && (
        <Typography variant="caption" className={styles.customise}>
          {t('Cart.FBProduct.Customizable')}
        </Typography>
      )}
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  buttonContainer: {
    marginTop: -24,
    paddingBottom: 12,
  },
  actionButton: {
    width: 72,
    height: 28,
    borderRadius: 8,
    borderWidth: 1,
    backgroundColor: theme.palette.white,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    display: 'flex',
  },
  iconButton: {
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customise: {
    textAlign: 'center',
    color: theme.palette.neutral300,
    marginTop: 4,
  },
}));

export default ListProductQuantityControl;
