import React, {useRef, useState} from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import {makeStyles} from "@mui/styles";

interface ReadMoreTextProps {
  description: string;
}

const ReadMoreText: React.FC<ReadMoreTextProps> = ({description}) => {
  const {t} = useTranslation();
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const [isOverflowing, setIsOverflowing] = useState<boolean>(false);
  const textRef = useRef<HTMLParagraphElement | null>(null);

  const styles = useStyles();

  const handleToggle = () => setIsExpanded(!isExpanded);

  return (
    <Box>
      <Typography
        variant="labelSmall"
        className={styles.description}
        ref={textRef}
      >
        {description}
      </Typography>

      {isOverflowing && !isExpanded && (
        <Button onClick={handleToggle} className={styles.toggleButton}>
          {t("Global.Read More")}
        </Button>
      )}
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  description: {
    maxWidth: '100%',
    color: theme.palette.text.secondary,
    display: "block",
    WebkitBoxOrient: "vertical",
    overflow: "hidden",
    WebkitLineClamp: 3,
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  },
  toggleButton: {
    color: theme.palette.primary.main,
    textTransform: "none",
    mt: 1,
  },
}));

export default ReadMoreText;
