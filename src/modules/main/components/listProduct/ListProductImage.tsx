import React, {useCallback, useState} from 'react';
// Replace FastImage with a regular HTML <img> element
import NoImageAvailable from '../../../../assets/noImage.png';

const ListProductImage = ({productImageSource}: { productImageSource: any }) => {
  const [error, setError] = useState(false);

  const onError = useCallback(() => {
    setError(true);
  }, []);

  return (
    <img
      src={error ? NoImageAvailable : productImageSource}
      alt="Product"
      style={{
        width: '126px',
        height: '126px',
        borderRadius: '16px'
      }}
      onError={onError}
    />
  );
};

export default ListProductImage;
