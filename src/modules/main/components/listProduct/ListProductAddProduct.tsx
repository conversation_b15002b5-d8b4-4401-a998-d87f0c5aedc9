import React from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import {useTheme} from "@mui/material/styles";
import {makeStyles} from "@mui/styles";

interface ListProductAddProductProps {
  apiInProgress: boolean;
  customizable: boolean;
  disabled: boolean;
  addToCart: () => void;
}

const ListProductAddProduct: React.FC<ListProductAddProductProps> = ({
                                                                       apiInProgress,
                                                                       customizable,
                                                                       disabled,
                                                                       addToCart,
                                                                     }) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);

  return (
    <Box className={styles.buttonContainer}>
      <Button
        variant="outlined"
        className={`
          ${styles.actionButton}
          ${disabled ? styles.disabledOutlineButton : styles.outlineButton}
        `}
        onClick={addToCart}
        disabled={disabled}
      >
        {apiInProgress ? (
          <CircularProgress size={18} color="primary"/>
        ) : (
          <Typography variant="labelSmall" className={styles.buttonText}>
            {t("Cart.FBProduct.Add")}
          </Typography>
        )}
      </Button>

      {customizable && (
        <Typography variant="caption" className={styles.customise}>
          {t("Cart.FBProduct.Customizable")}
        </Typography>
      )}
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  buttonContainer: {
    marginTop: -14,
    zIndex: 999,
    backgroundColor: theme.palette.white,
    borderRadius: '8px !important',
    borderWidth: 1,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  actionButton: {
    width: 72,
    height: 28,
    borderRadius: '8px !important',
    borderWidth: 1,
    backgroundColor: theme.palette.white,
  },
  addButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 17,
  },
  customise: {
    textAlign: 'center',
    color: theme.palette.neutral300,
    marginTop: 4,
  },
}));

export default ListProductAddProduct;
