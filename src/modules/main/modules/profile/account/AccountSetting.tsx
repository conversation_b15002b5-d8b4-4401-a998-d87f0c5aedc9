import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos"
import Box from "@mui/material/Box"
import {useTheme} from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import {useCallback} from "react"
import {useTranslation} from "react-i18next"
import {useNavigate} from "react-router-dom"
import Header from "../../../components/header/Header"

/**
 * Component to render profile screen which shows user profile
 * @constructor
 * @returns {JSX.Element}
 */
const AccountSetting = () => {
  const {t} = useTranslation()
  const theme = useTheme()
  const navigate = useNavigate()

  const navigateToDeleteAccount = useCallback(() => {
    navigate("/delete-account")
  }, [navigate])

  return (
    <Box sx={{bgcolor: "background.paper", height: "100dvh"}}>
      <Header label={t("Profile.Account Settings")}/>
      <Box
        onClick={navigateToDeleteAccount}
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          pt: 3,
          pl: 2,
          pr: 2,
          cursor: "pointer",
        }}
      >
        <Typography variant="titleMedium" color="text.primary">
          {t("Profile.Delete Account")}
        </Typography>
        <ArrowForwardIosIcon sx={{color: theme.palette.text.primary}}/>
      </Box>
    </Box>
  )
}

export default AccountSetting
