import React, {useCallback} from 'react';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import {useTranslation} from 'react-i18next';
import {useTheme} from '@mui/material/styles';
import {useNavigate} from 'react-router-dom';
import Header from '../../../components/header/Header'; // Keeping as you requested

const DeleteAccount = () => {
  const {t} = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();

  const navigateToAccountDeletion = useCallback(() => {
    navigate('/account-deletion');
  }, [navigate]);

  const menuOptions = [
    t('Profile.I am using different account'),
    t('Profile.This app is not working properly'),
    t('Profile.Other'),
  ];

  return (
    <Box sx={{bgcolor: theme.palette.background.paper, minHeight: '100dvh'}}>
      <Header label={t('Profile.Delete Account')}/>
      <Box sx={{pt: 2, pb: 2}}>
        {menuOptions.map((label, index) => (
          <React.Fragment key={label}>
            <Box
              onClick={navigateToAccountDeletion}
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                p: 2,
                cursor: 'pointer',
              }}
            >
              <Typography variant="labelLarge" color="text.primary">
                {label}
              </Typography>
              <ArrowForwardIosIcon sx={{color: theme.palette.text.primary}}/>
            </Box>
            {index < menuOptions.length - 1 && <Divider sx={{my: 1.5, bgcolor: theme.palette.divider}}/>}
          </React.Fragment>
        ))}
      </Box>
    </Box>
  );
};

export default DeleteAccount;
