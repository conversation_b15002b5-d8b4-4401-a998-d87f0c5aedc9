import React, {useCallback, useState} from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import {useTheme} from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import {useNavigate} from 'react-router-dom';
import {useTranslation} from 'react-i18next';
import Header from '../../../components/header/Header';
import {ReactComponent as RoundEmail} from '../../../../../assets/round-email.svg';

const AccountDeletion = () => {
  const {t} = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();

  const [modalOpen, setModalOpen] = useState(false);

  const openAccountModal = useCallback(() => setModalOpen(true), []);
  const closeAccountModal = useCallback(() => setModalOpen(false), []);
  const navigateToProfile = () => navigate(-3);

  return (
    <Box sx={{bgcolor: theme.palette.background.paper, minHeight: '100dvh'}}>
      <Header label={t('Profile.Account Deletion')}/>

      <Box sx={{p: 2}}>
        <Typography variant="titleMedium" component="div" color="text.primary">
          {t('Profile.You have requested the deletion of your account')}
        </Typography>
        <Typography variant="labelSmall" component="div" color="text.disabled" sx={{mt: 1}}>
          {t('Profile.Account Deletion Description')}
        </Typography>

        <Button
          fullWidth
          variant="contained"
          sx={{mt: 4, borderRadius: 2}}
          onClick={openAccountModal}
        >
          {t('Profile.Delete my Account')}
        </Button>
        <Button
          fullWidth
          sx={{mt: 0.5}}
          onClick={navigateToProfile}
        >
          {t('Profile.Back to Profile')}
        </Button>
      </Box>

      <Modal
        open={modalOpen}
        onClose={closeAccountModal}
        aria-labelledby="account-deletion-modal"
        aria-describedby="account-deletion-description"
      >
        <Box
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            mx: 'auto',
            my: 'auto',
            borderRadius: 2,
            boxShadow: 24,
            maxWidth: 400,
            position: 'relative',
            top: '50%',
            transform: 'translateY(-50%)',
          }}
        >
          <IconButton
            onClick={closeAccountModal}
            sx={{position: 'absolute', top: 12, right: 12}}
          >
            <CloseIcon sx={{color: theme.palette.text.secondary}}/>
          </IconButton>

          <Box display="flex" flexDirection="column" alignItems="center" gap={2}>
            <RoundEmail height={100} width={100}/>
            <Typography variant="headlineMedium" textAlign="center" color="text.secondary">
              {t('Profile.Delete Account')}
            </Typography>
            <Typography variant="labelSmall" textAlign="center" color="text.secondary">
              {t(
                'Profile.In order to delete your account please send an <NAME_EMAIL> with your account details'
              )}
            </Typography>
            <Button
              fullWidth
              variant="contained"
              sx={{mt: 3, borderRadius: 2}}
              onClick={closeAccountModal}
            >
              {t('Profile.Okay')}
            </Button>
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

export default AccountDeletion;
