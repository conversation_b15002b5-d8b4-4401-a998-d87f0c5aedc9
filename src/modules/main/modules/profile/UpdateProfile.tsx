import React from 'react';
import Modal from '@mui/material/Modal';
import Box from '@mui/material/Box';
import Slide from '@mui/material/Slide';
import IconButton from "@mui/material/IconButton";
import ClearIcon from "@mui/icons-material/ClearRounded";
import {useTheme} from "@mui/material/styles";
import UpdateProfileForm from "./components/UpdateProfileForm";
import UpdateNameForm from "./components/UpdateNameForm";

const UpdateProfile = ({open, handleClose, updateName}: {
  open: boolean;
  handleClose: () => void;
  updateName: boolean
}) => {
  const theme = useTheme();

  return (
    <Box>
      <Modal open={open} onClose={handleClose}>
        <Slide direction="up" in={open} mountOnEnter unmountOnExit>
          <Box
            sx={{
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              boxShadow: 24,
              maxHeight: '70dvh',
              overflow: 'auto',
            }}
          >
            <Box display="flex" justifyContent="center" mb={2}>
              <IconButton
                onClick={handleClose}
                sx={{
                  width: 36,
                  height: 36,
                  bgcolor: theme.palette.grey[400],
                  color: theme.palette.common.white,
                  '&:hover': {
                    bgcolor: theme.palette.grey[500],
                  },
                }}
              >
                <ClearIcon/>
              </IconButton>
            </Box>
            <Box display="flex" justifyContent="center">
              <Box sx={{
                bgcolor: '#fff',
                borderTopLeftRadius: 16,
                borderTopRightRadius: 16,
                width: '100%',
              }} display="flex" justifyContent="center">
                {updateName ? <UpdateNameForm closeModal={handleClose}/> : (
                  <UpdateProfileForm closeModal={handleClose}/>
                )}
              </Box>
            </Box>
          </Box>
        </Slide>
      </Modal>
    </Box>
  );
}

export default UpdateProfile;

