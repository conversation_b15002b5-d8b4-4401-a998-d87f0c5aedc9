import React, {useMemo, useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';
import * as Yup from 'yup';
import {Formik} from 'formik';
import axios from 'axios';
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import {useTheme} from "@mui/material/styles";
import useNetworkErrorHandling from '../../../../../hooks/useNetworkErrorHandling';
import {API_BASE_URL, UPDATE_PROFILE} from '../../../../../utils/apiActions';
import useNetworkHandling from '../../../../../hooks/useNetworkHandling';
import {setStoredData} from '../../../../../utils/storage';
import {setProfile} from '../../../../../toolkit/reducer/auth';


const UserProfileForm = ({closeModal}: { closeModal: () => void }) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const {name, emailId} = useSelector((state: any) => state.auth);
  const source = useRef<any>(null);
  const dispatch = useDispatch();
  const {handleApiError} = useNetworkErrorHandling();
  const {putDataWithAuth} = useNetworkHandling();
  const [apiInProgress, setApiInProgress] = useState<boolean>(false);

  const handleSubmit = async (values: any) => {
    const CancelToken = axios.CancelToken;

    source.current = CancelToken.source();
    try {
      setApiInProgress(true);
      await putDataWithAuth(
        `${API_BASE_URL}${UPDATE_PROFILE}`,
        values,
        source.current.token,
      );
      setStoredData('name', values.name);
      setStoredData('emailId', values.email);
      dispatch(setProfile(values));
      closeModal();
    } catch (error) {
      handleApiError(error);
    } finally {
      setApiInProgress(false);
    }
  };

  const validationSchema = useMemo(() => {
    return Yup.object().shape({
      name: Yup.string()
        .matches(
          /^[a-zA-Z\s]+$/,
          t('Profile.Name can only contain letters and spaces'),
        )
        .required(t('Profile.Name is required')),
      email: Yup.string()
        .trim()
        .matches(
          /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
          t('Profile.Please enter valid email address'),
        ),
    });
  }, []);

  return (
    <Box sx={{width: '100%'}}>
      <Box
        sx={{
          bgcolor: theme.palette.common.white,
          borderTopLeftRadius: 12,
          borderTopRightRadius: 12,
          px: 2,
          py: 1.75,
        }}
      >
        <Typography variant="headlineSmall" color={theme.palette.grey[700]}>
          {t('Cart.Update Your Profile')}
        </Typography>
      </Box>

      <Box sx={{bgcolor: theme.palette.grey[50], p: 2}}>
        <Formik
          initialValues={{name, email: emailId ?? ''}}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({
              handleChange,
              handleBlur,
              handleSubmit,
              values,
              errors,
              touched,
            }) => {
            const disabled = values.name?.trim().length === 0;

            return (
              <form onSubmit={handleSubmit}>
                <Typography variant="bodySmall" component="div" sx={{
                  color: theme.palette.neutral.main,
                  marginBottom: '4px !important'
                }}>
                  {t('Profile.Full Name')}
                  <Typography component="span" sx={{
                    color: theme.palette.error.main,
                  }}>*</Typography>
                </Typography>
                <TextField
                  sx={{marginTop: 0}}
                  label=""
                  name="name"
                  value={values.name}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  fullWidth
                  required
                  disabled={apiInProgress}
                  error={touched.name && Boolean(errors.name)}
                  helperText={touched.name && typeof errors.name === 'string' ? errors.name : undefined}
                  margin="normal"
                />

                <Typography variant="bodySmall" component="div" sx={{
                  color: theme.palette.neutral.main,
                  marginBottom: '4px !important',
                  marginTop: 2,
                }}>
                  {t('Profile.Email')}
                </Typography>
                <TextField
                  sx={{marginTop: 0}}
                  label=""
                  name="email"
                  value={values.email}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  fullWidth
                  disabled={apiInProgress}
                  error={touched.email && Boolean(errors.email)}
                  helperText={touched.email && typeof errors.email === 'string' ? errors.email : undefined}
                  margin="normal"
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  color="primary"
                  disabled={disabled || apiInProgress}
                  sx={{
                    mt: 4,
                    py: 1.5,
                    borderRadius: 2,
                    bgcolor: disabled
                      ? theme.palette.grey[300]
                      : theme.palette.primary.main,
                    color: theme.palette.common.white,
                    '&:hover': {
                      bgcolor: disabled
                        ? theme.palette.grey[300]
                        : theme.palette.primary.dark,
                    },
                  }}
                >
                  {apiInProgress ? (
                    <CircularProgress size={24} sx={{color: theme.palette.common.white}}/>
                  ) : (
                    <Typography variant="body1">
                      {name ? t('Profile.Update') : t('Profile.Add')}
                    </Typography>
                  )}
                </Button>
              </form>
            );
          }}
        </Formik>
      </Box>
    </Box>
  );
};

export default UserProfileForm;
