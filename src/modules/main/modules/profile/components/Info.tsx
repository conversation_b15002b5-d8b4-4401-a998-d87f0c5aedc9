import {useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import {makeStyles} from '@mui/styles';
import {useTheme} from '@mui/material/styles';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from "@mui/material/IconButton";

import backgroundImage from '../../../../../assets/profile/profileBackground.png';

const Info = ({navigateToNameEditor}: { navigateToNameEditor: () => void }) => {
  const {t} = useTranslation();
  const {palette} = useTheme();
  const styles = useInfoStyles();
  const {name, phoneNumber} = useSelector((state: any) => state.auth);

  return (
    <Box className={styles.container}>
      <Box
        className={styles.imageContainer}
        sx={{
          backgroundImage: `url(${backgroundImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <Box className={styles.infoContainer}>
          <Box className={styles.headingRow}>
            <Typography variant="headlineSmall" className={styles.heading}>
              {name?.length > 0 ? name : t('Add Your Name')}
            </Typography>
            <IconButton size="small" onClick={navigateToNameEditor}>
              <EditOutlinedIcon sx={{fontSize: 24, color: palette.primary.main}}/>
            </IconButton>
          </Box>
          <Typography variant="labelMedium" className={styles.mobileNumber}>
            +91-{phoneNumber}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

const useInfoStyles = makeStyles<any>((theme) => ({
  imageContainer: {
    height: 86,
    width: '100%',
    borderRadius: 15,
    overflow: 'hidden',
    display: 'flex',
    alignItems: 'center',
  },
  container: {
    padding: '0 16px',
  },
  infoContainer: {
    padding: 20,
    borderRadius: 15,
  },
  headingRow: {
    display: 'flex',
    alignItems: 'center',
  },
  heading: {
    color: theme.palette.neutral400,
    flexShrink: 1,
  },
  mobileNumber: {
    color: theme.palette.neutral300,
    marginTop: 4,
  },
}));

export default Info;
