import React from 'react';
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';

const StoreSkeleton: React.FC = () => {
  return (
    <Box p={2} borderRadius={2} border={1} borderColor="grey.300" mb={2}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
        <Box display="flex" alignItems="center" gap={2}>
          <Skeleton variant="circular" width={48} height={48}/>
          <Box>
            <Skeleton variant="text" width={150} height={20} sx={{mb: 0.5}}/>
            <Skeleton variant="text" width={150} height={14}/>
          </Box>
        </Box>
        <Skeleton variant="circular" width={24} height={24}/>
      </Box>
      <Skeleton variant="text" width={100} height={12} sx={{mt: 1.5}}/>
      <Box borderBottom={1} borderColor="grey.300" my={1.5}/>
      <Box display="flex" alignItems="center" gap={2}>
        <Skeleton variant="rectangular" width={40} height={40}/>
        <Skeleton variant="rectangular" width={40} height={40}/>
      </Box>
      <Skeleton variant="text" width={200} height={14} sx={{mt: 1, mb: 2}}/>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1.5}>
        <Skeleton variant="text" width={200} height={18}/>
        <Skeleton variant="rectangular" width={100} height={32}/>
      </Box>
    </Box>
  );
};

export default StoreSkeleton;
