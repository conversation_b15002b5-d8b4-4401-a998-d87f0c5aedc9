.store-cart {
    border-radius: 16px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    background: #fff;
    margin-bottom: 16px;
}

.store-cart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ECF3F8;
    padding: 16px;
}

.store-cart-header-img {
    height: 48px !important;
    width: 48px !important;
    border-radius: 8px;
}

.store-cart-details {
    flex-grow: 1;
    padding-left: 12px;
    max-width: calc(100vw - 160px);
}

.store-cart-locality {
    color: #757575;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.store-cart-items {
    padding: 12px;
}

.store-cart-description {
    margin-top: 8px;
    color: #757575;
}

.store-cart-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
}

.cart-item {
    width: 40px;
}

.item-image {
    width: 40px;
    height: 40px;
    border-radius: 10px;
}

.item-main-view {
    display: flex;
    flex-direction: row;
    gap: 8px;
}

.DeleteWishlistIcon-image {
    height: 100px;
    width: 100px;
}

.remove-cart-icon, .remove-cart-title-container, .remove-cart-content {
    text-align: center;
}

.remove-cart-icon {
    margin-bottom: 16px;
}

.remove-cart-title-container {
    padding: 0px !important;
    color: #1A1A1A;
}

.remove-cart-content {
    color: #686868 !important;
}

.dialog-action-button {
    border-radius: 8px !important;
}

.dialog-close-icon {
    text-align: right;
}

.view-cart-button {
    border-radius: 20px !important;
}
