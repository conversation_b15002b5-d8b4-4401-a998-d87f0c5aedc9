import React from 'react';
import Box from '@mui/material/Box';

const screenWidth = window.innerWidth; // Equivalent of screenWidth from React Native

const DashLine = ({dashWidth = 5, dashGap = 5}) => {
  const length = screenWidth - 48;
  const dashCount = Math.floor(length / (dashWidth + dashGap));

  return (
    <Box display="flex" flexDirection="row" alignItems="center" my={1.5}>
      {Array.from({length: dashCount}).map((_, index) => (
        <Box
          key={index}
          width={dashWidth}
          height={1}
          color="grey.300"
          mr={`${dashGap}px`}
        >-</Box>
      ))}
    </Box>
  );
};

export default DashLine;
