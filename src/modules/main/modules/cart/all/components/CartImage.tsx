import React, {useCallback, useState} from 'react';
import Box from '@mui/material/Box';
import NoImageAvailable from '../../../../../../assets/noImage.png';

const CartImage = ({imageSource, imageStyle, className}: any) => {
  const [error, setError] = useState(false);

  const onError = useCallback(() => {
    setError(true);
  }, []);

  return (
    <Box
      component="img"
      src={error ? NoImageAvailable : imageSource?.uri}
      alt="Cart Item"
      style={{...imageStyle, objectFit: 'contain'}}
      onError={onError}
      className={className}
    />
  );
};

export default CartImage;
