import NavigateNextRoundedIcon from "@mui/icons-material/NavigateNextRounded"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import Dialog from "@mui/material/Dialog"
import DialogActions from "@mui/material/DialogActions"
import DialogContent from "@mui/material/DialogContent"
import DialogTitle from "@mui/material/DialogTitle"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import axios from "axios"
import React, { useCallback, useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"

import { faTrashCan } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import CloseRoundedIcon from "@mui/icons-material/CloseRounded"
import NoImageAvailable from "../../../../../../assets/noImage.png"
import DeleteWishlistIcon from "../../../../../../assets/remove-cart.svg"
import useNetworkHandling from "../../../../../../hooks/useNetworkHandling"
import { removeStoreFromCart } from "../../../../../../toolkit/reducer/cart"
import { API_BASE_URL, CART } from "../../../../../../utils/apiActions"
import { theme } from "../../../../../../utils/theme"
import { emptyAlertCallback } from "../../../../../../utils/utils"
import CartImage from "./CartImage"
import DashLine from "./DashLine"
import "./StoreCart.css"

interface StoreCartProps {
  item: any
}

const StoreCart: React.FC<StoreCartProps> = ({ item }) => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { uid } = useSelector((state: any) => state.auth)
  const source = useRef<any>(null)

  const [cartTotal, setCartTotal] = useState<number>(0)
  const [locality, setLocality] = useState<string>("")
  const [itemsCart, setItemsCart] = useState<string>("")
  const [deleteInProgress, setDeleteInProgress] = useState<boolean>(false)
  const [providerImageSource, setProviderImageSource] =
    useState<string>(NoImageAvailable)
  const [deleteStoreModalVisible, setDeleteStoreModalVisible] =
    useState<boolean>(false)
  const { deleteDataWithAuth } = useNetworkHandling()

  const confirmDelete = useCallback(async () => {
    try {
      setDeleteInProgress(true)
      source.current = axios.CancelToken.source()
      await deleteDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/${item?._id}/clear`,
        source.current.token
      )
      dispatch(removeStoreFromCart(item?._id))
      setDeleteStoreModalVisible(false)
    } catch (error) {
      console.error("Delete error:", error)
    } finally {
      setDeleteInProgress(false)
    }
  }, [uid, item, dispatch])

  useEffect(() => {
    if (item) {
      if (item.items.length === 0) {
        confirmDelete().then(emptyAlertCallback)
      } else {
        let total = 0
        let description = ""
        let imageSource: any = { uri: NoImageAvailable }

        if (item?.items[0]?.item?.provider?.descriptor?.symbol) {
          imageSource = {
            uri: item?.items[0]?.item?.provider?.descriptor?.symbol,
          }
        }

        setProviderImageSource(imageSource)

        item?.items.forEach((one: any, ind: number) => {
          total += one?.item?.product?.subtotal * one?.item?.quantity?.count
          description += one?.item?.product?.descriptor?.name
          if (item?.items.length - 1 !== ind) {
            description += ", "
          }
        })

        setItemsCart(description)
        setCartTotal(total)
        setLocality(
          item?.items[0]?.item?.location_details?.address?.locality || ""
        )
      }
    }
  }, [item])

  const renderItem = useCallback((item: any) => {
    let imageSource: any = { uri: NoImageAvailable }

    if (item?.item?.product?.descriptor?.symbol) {
      imageSource = { uri: item?.item?.product?.descriptor?.symbol }
    } else if (item?.item?.product?.descriptor?.images?.length > 0) {
      imageSource = { uri: item?.item?.product?.descriptor?.images[0] }
    }

    return (
      <Box key={item?.item?.product?.descriptor?.name} className="cart-item">
        <CartImage imageSource={imageSource} className="item-image" />
      </Box>
    )
  }, [])

  return (
    <Box className="store-cart">
      {/* Header */}
      <Box className="store-cart-header">
        <CartImage
          imageSource={providerImageSource}
          className="store-cart-header-img"
        />
        <Box className="store-cart-details">
          <Typography variant="titleLarge">
            {item?.location?.provider_descriptor?.name}
          </Typography>
          <Typography
            variant="labelSmall"
            className="store-cart-locality"
            component="div"
          >
            {locality}
          </Typography>
        </Box>
        <IconButton
          onClick={() => setDeleteStoreModalVisible(true)}
          color="error"
        >
          <FontAwesomeIcon
            icon={faTrashCan}
            color={theme.palette.neutral.light}
          />
        </IconButton>
      </Box>

      {/* <DashLine /> */}

      {/* Cart Items */}
      <Box className="store-cart-items">
        <Typography variant="labelLarge">
          {t("Cart.Items in cart", { count: item?.items?.length })}
        </Typography>
        <DashLine />
        <Box className="item-main-view">
          {item?.items?.map((item: any) => renderItem(item))}
        </Box>
        <Typography
          variant="labelSmall"
          className="store-cart-description"
          noWrap
        >
          {itemsCart}
        </Typography>
      </Box>

      {/* Bottom Section */}
      <Box className="store-cart-footer">
        <Typography variant="bodyLarge">
          {t("Cart.Total Amount", { total: `₹${cartTotal.toFixed(2)}` })}
        </Typography>
        <Button
          className="view-cart-button"
          variant="contained"
          color="primary"
          endIcon={<NavigateNextRoundedIcon />}
          onClick={() => {
            const locationId = item.location_id.replace(/\//g, "-")
            navigate(`/cart/${locationId}`)
          }}
        >
          {t("Cart.View Cart")}
        </Button>
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteStoreModalVisible}
        onClose={() => setDeleteStoreModalVisible(false)}
        sx={{
          "& .MuiPaper-root": {
            borderRadius: "20px",
            position: "fixed",
            padding: "18px",
            boxShadow: "0px -5px 20px rgba(0, 0, 0, 0.2)",
          },
        }}
      >
        <DialogTitle className="remove-cart-title-container">
          <Box className="dialog-close-icon">
            <CloseRoundedIcon
              onClick={() => setDeleteStoreModalVisible(false)}
            />
          </Box>
          <Box className="remove-cart-icon">
            <CartImage
              imageSource={{ uri: DeleteWishlistIcon }}
              className="DeleteWishlistIcon-image"
            />
          </Box>
          {t("Cart.Remove Cart")}
        </DialogTitle>
        <DialogContent>
          <Typography className="remove-cart-content" variant="labelSmall">
            {t("Cart.Are you sure you would like to remove the cart?")}
          </Typography>
        </DialogContent>
        <DialogActions
          sx={{
            gap: "15px",
            padding: 0,
          }}
        >
          <Button
            className="dialog-action-button"
            fullWidth
            onClick={() => setDeleteStoreModalVisible(false)}
            color="primary"
            variant="outlined"
          >
            {t("WishList.No")}
          </Button>
          <Button
            className="dialog-action-button"
            onClick={confirmDelete}
            color="primary"
            variant="contained"
            disabled={deleteInProgress}
            fullWidth
          >
            {deleteInProgress ? (
              <CircularProgress size={20} />
            ) : (
              t("WishList.Yes")
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default StoreCart
