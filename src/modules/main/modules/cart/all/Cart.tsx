import Box from "@mui/material/Box"
import List from "@mui/material/List"
import { makeStyles } from "@mui/styles"
import axios from "axios"
import { useEffect, useMemo, useRef, useState } from "react"
import { useDispatch, useSelector } from "react-redux"

import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import { updateCartItems } from "../../../../../toolkit/reducer/cart"
import { API_BASE_URL, CART } from "../../../../../utils/apiActions"
import Header from "../../../components/header/HeaderWithActions"
import SafeAreaPage from "../../../components/page/SafeAreaPage"
import EmptyCart from "../provider/components/EmptyCart"
import SkeletonLoader from "./components/SkeletonLoader"
import StoreCart from "./components/StoreCart"

interface RootState {
  auth: {
    uid: string
  }
}

const CancelToken = axios.CancelToken

const Cart = ({ navigation }: any) => {
  const dispatch = useDispatch()
  const styles = useStyles()
  const { uid } = useSelector((state: RootState) => state.auth)
  const { cartItems } = useSelector((state: any) => state.cart)
  const source = useRef<any>(null)
  const [apiInProgress, setApiInProgress] = useState(true)
  const { getDataWithAuth } = useNetworkHandling()

  const getAllCartList = async () => {
    try {
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/all`,
        { signal: source.current.signal }
      )
      dispatch(updateCartItems(data))
      if (data?.length === 1) {
        const locationId = data[0]?.location_id.replace(/\//g, "-")
        navigation.navigate(`/cart/${locationId}`)
      }
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    setApiInProgress(true)
    dispatch(updateCartItems([]))
    getAllCartList().finally(() => setApiInProgress(false))
    return () => {
      if (source.current) {
        source.current.cancel()
      }
    }
  }, [])

  const sortedCartItems = useMemo(() => {
    return [...cartItems].sort((a, b) => {
      const latestA: any = new Date(
        Math.max(...a.items.map((item: any) => new Date(item.updatedAt)))
      )
      const latestB: any = new Date(
        Math.max(...b.items.map((item: any) => new Date(item.updatedAt)))
      )
      return latestB - latestA
    })
  }, [cartItems])

  return (
    <SafeAreaPage>
      <Box className={styles.container}>
        <Box className={styles.header}>
          <Header
            label={apiInProgress ? "" : `All Carts (${cartItems.length})`}
          />
        </Box>
        <Box className={styles.subContainer}>
          {apiInProgress ? (
            <SkeletonLoader />
          ) : (
            <List>
              {sortedCartItems.length > 0 ? (
                sortedCartItems.map((item) => (
                  <StoreCart key={item._id} item={item} />
                ))
              ) : (
                <EmptyCart />
              )}
            </List>
          )}
        </Box>
      </Box>
    </SafeAreaPage>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100dvh",
    backgroundColor: theme.palette.background.default,
  },
  header: {
    position: "sticky",
    top: 0,
    zIndex: 10,
    backgroundColor: theme.palette.background.default,
  },
  subContainer: {
    flex: 1,
    overflowY: "auto",
    padding: "0 16px 16px !important",
    backgroundColor: theme.palette.background.paper,
  },
}))

export default Cart
