import ChevronRightIcon from "@mui/icons-material/ChevronRight"
import CloseIcon from "@mui/icons-material/Close"
import DeleteIcon from "@mui/icons-material/Delete"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import IconButton from "@mui/material/IconButton"
import List from "@mui/material/List"
import ListItem from "@mui/material/ListItem"
import Modal from "@mui/material/Modal"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import { useState, useRef } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import ConfirmCartDeletionModal from "../../../components/stickyCart/ConfirmCartDeletionModal"
import StoreCart from "../../../components/stickyCart/StoreCart"

interface CartSheetProps {
  isOpen?: boolean
  onClose?: () => void
}

// Swipeable Cart Item Component
interface SwipeableCartItemProps {
  item: any
  onDelete: () => void
  isLast: boolean
}

const SwipeableCartItem: React.FC<SwipeableCartItemProps> = ({
  item,
  onDelete,
  isLast,
}) => {
  const theme = useTheme()
  const styles = useStyles()

  const [translateX, setTranslateX] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const startX = useRef(0)
  const currentX = useRef(0)
  const containerRef = useRef<HTMLDivElement>(null)

  const SWIPE_THRESHOLD = 80 // Minimum swipe distance to reveal button
  const DELETE_BUTTON_WIDTH = 80 // Width of the delete button

  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true)
    startX.current = e.touches[0].clientX
    currentX.current = e.touches[0].clientX
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return

    currentX.current = e.touches[0].clientX
    const deltaX = currentX.current - startX.current

    // Only allow left swipe (negative deltaX)
    if (deltaX < 0) {
      const newTranslateX = Math.max(deltaX, -DELETE_BUTTON_WIDTH)
      setTranslateX(newTranslateX)
    }
  }

  const handleTouchEnd = () => {
    if (!isDragging) return
    setIsDragging(false)

    const deltaX = currentX.current - startX.current

    // If swiped far enough, show delete button, otherwise snap back
    if (Math.abs(deltaX) > SWIPE_THRESHOLD) {
      setTranslateX(-DELETE_BUTTON_WIDTH)
    } else {
      setTranslateX(0)
    }
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    startX.current = e.clientX
    currentX.current = e.clientX
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return

    currentX.current = e.clientX
    const deltaX = currentX.current - startX.current

    // Only allow left swipe (negative deltaX)
    if (deltaX < 0) {
      const newTranslateX = Math.max(deltaX, -DELETE_BUTTON_WIDTH)
      setTranslateX(newTranslateX)
    }
  }

  const handleMouseUp = () => {
    if (!isDragging) return
    setIsDragging(false)

    const deltaX = currentX.current - startX.current

    // If swiped far enough, show delete button, otherwise snap back
    if (Math.abs(deltaX) > SWIPE_THRESHOLD) {
      setTranslateX(-DELETE_BUTTON_WIDTH)
    } else {
      setTranslateX(0)
    }
  }

  const resetSwipe = () => {
    setTranslateX(0)
  }

  return (
    <ListItem
      className={!isLast ? styles.marginBottom : styles.noMargin}
      sx={{
        position: "relative",
        padding: 0,
        overflow: "hidden",
        cursor: isDragging ? "grabbing" : "grab",
      }}
    >
      {/* Delete Button Background */}
      <Box
        sx={{
          position: "absolute",
          right: 0,
          top: 0,
          bottom: 0,
          width: DELETE_BUTTON_WIDTH,
          backgroundColor: theme.palette.error.main,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          zIndex: 1,
        }}
      >
        <IconButton
          onClick={onDelete}
          sx={{
            color: "white",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          }}
        >
          <DeleteIcon />
        </IconButton>
      </Box>

      {/* Swipeable Content */}
      <Box
        ref={containerRef}
        sx={{
          width: "100%",
          transform: `translateX(${translateX}px)`,
          transition: isDragging ? "none" : "transform 0.3s ease-out",
          backgroundColor: theme.palette.background.paper,
          zIndex: 2,
          position: "relative",
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onClick={translateX < 0 ? resetSwipe : undefined}
      >
        <StoreCart cartItem={item} confirmDelete={onDelete} />
      </Box>
    </ListItem>
  )
}

const CartSheet: React.FC<CartSheetProps> = ({ isOpen = true, onClose }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const theme = useTheme()
  const styles = useStyles()
  const { cartItems } = useSelector((state: any) => state.cart)

  const [selectedCart, setSelectedCart] = useState<any>(null)

  const confirmDelete = (cart: any) => {
    setSelectedCart(cart)
  }

  const closeDeletionModal = () => setSelectedCart(null)

  const handleClose = () => {
    if (onClose) {
      onClose()
    } else {
      navigate(-1) // Go back
    }
  }

  const showAllCarts = () => {
    if (onClose) {
      onClose()
    }
    navigate("/cart")
  }

  return (
    <>
      <Modal
        open={isOpen}
        onClose={handleClose}
        closeAfterTransition
        sx={{
          display: "flex",
          alignItems: "flex-end",
          justifyContent: "center",
          WebkitJustifyContent: "center",
        }}
      >
        <Box
          sx={{
            width: "100%",
            WebkitJustifyContent: "center",
            maxWidth: "600px",
            maxHeight: "90vh",
          }}
        >
          <Box sx={{ position: "relative" }}>
            <IconButton
              onClick={handleClose}
              size="medium"
              sx={{
                backgroundColor: "rgba(0,0,0,0.8)",
                marginBottom: 2,
                marginLeft: "44%",
              }}
            >
              <CloseIcon sx={{ color: "#fff" }} />
            </IconButton>
          </Box>
          <Box
            className={styles.cartsContainer}
            sx={{
              width: "100%",
              maxWidth: "600px",
              maxHeight: "90vh",
              borderRadius: "16px 16px 0 0",
              outline: "none",
              overflow: "auto",
            }}
          >
            <Box className={styles.listHeaderContainer}>
              <Typography variant="bodyLarge" className={styles.listHeaderText}>
                {t("Cart.Your Carts", { count: cartItems.length })}
              </Typography>
              <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                <Button
                  className={styles.checkoutButton}
                  onClick={showAllCarts}
                  variant="outlined"
                >
                  <Typography
                    variant="body2"
                    className={styles.checkoutButtonLabel}
                  >
                    {t("Cart.Checkout")}
                  </Typography>
                  <ChevronRightIcon
                    sx={{ fontSize: 24, color: theme.palette.primary.main }}
                  />
                </Button>
              </Box>
            </Box>
            <Box className={styles.listViewContainer}>
              <List>
                {cartItems.map((item: any, index: number) => (
                  <SwipeableCartItem
                    key={item.location_id || index}
                    item={item}
                    onDelete={() => confirmDelete(item)}
                    isLast={cartItems.length - 1 === index}
                  />
                ))}
              </List>
            </Box>
          </Box>
        </Box>
      </Modal>
      {selectedCart && (
        <ConfirmCartDeletionModal
          closeDeletionModal={closeDeletionModal}
          selectedCart={selectedCart}
        />
      )}
    </>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  cartsContainer: {
    backgroundColor: theme.palette.common.white,
    paddingTop: 16,
    paddingBottom: 20,
  },
  listHeaderContainer: {
    display: "flex",
    flexDirection: "row",
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 17,
    paddingRight: 17,
    justifyContent: "space-between",
    alignItems: "center",
  },
  listHeaderText: {
    fontWeight: "bold",
    color: theme.palette.neutral400,
  },
  checkoutButton: {
    borderWidth: 1,
    borderColor: theme.palette.primary.main,
    borderRadius: "8px !important",
    paddingTop: 5,
    paddingBottom: 5,
    paddingLeft: 8,
    paddingRight: 8,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    textTransform: "none",
    minWidth: "auto",
  },
  checkoutButtonLabel: {
    color: theme.palette.primary.main,
  },
  marginBottom: {
    marginBottom: 11,
  },
  noMargin: {
    marginBottom: 0,
  },
  listViewContainer: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
  },
}))

export default CartSheet
