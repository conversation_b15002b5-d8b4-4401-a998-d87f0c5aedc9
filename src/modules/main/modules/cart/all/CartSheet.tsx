import { useState } from "react"
import { useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import { useTranslation } from "react-i18next"
import { useTheme } from "@mui/material/styles"
import { makeStyles } from "@mui/styles"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import Dialog from "@mui/material/Dialog"
import DialogContent from "@mui/material/DialogContent"
import List from "@mui/material/List"
import ListItem from "@mui/material/ListItem"
import ChevronRightIcon from "@mui/icons-material/ChevronRight"
import CloseIcon from "@mui/icons-material/Close"
import DeleteIcon from "@mui/icons-material/Delete"
import StoreCart from "../../../components/stickyCart/StoreCart"
import ConfirmCartDeletionModal from "../../../components/stickyCart/ConfirmCartDeletionModal"

interface CartSheetProps {
  isOpen?: boolean
  onClose?: () => void
}

const CartSheet: React.FC<CartSheetProps> = ({ isOpen = true, onClose }) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const theme = useTheme()
  const styles = useStyles()
  const { cartItems } = useSelector((state: any) => state.cart)

  const [selectedCart, setSelectedCart] = useState<any>(null)

  const confirmDelete = (cart: any) => {
    setSelectedCart(cart)
  }

  const closeDeletionModal = () => setSelectedCart(null)

  const handleClose = () => {
    if (onClose) {
      onClose()
    } else {
      navigate(-1) // Go back
    }
  }

  const showAllCarts = () => {
    if (onClose) {
      onClose()
    }
    navigate("/cart")
  }

  return (
    <>
      <Dialog
        open={isOpen}
        onClose={handleClose}
        // maxWidth="md"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            margin: 0,
            maxHeight: "90vh",
            borderRadius: "16px 16px 0 0",
            position: "fixed",
            bottom: 0,
            top: "auto",
          },
        }}
      >
        <DialogContent className={styles.cartsContainer}>
          <Box className={styles.listHeaderContainer}>
            <Typography variant="h6" className={styles.listHeaderText}>
              {t("Cart.Your Carts", { count: cartItems.length })}
            </Typography>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Button
                className={styles.checkoutButton}
                onClick={showAllCarts}
                variant="outlined"
              >
                <Typography
                  variant="body2"
                  className={styles.checkoutButtonLabel}
                >
                  {t("Cart.Checkout")}
                </Typography>
                <ChevronRightIcon
                  sx={{ fontSize: 24, color: theme.palette.primary.main }}
                />
              </Button>
              <IconButton onClick={handleClose} size="small">
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>
          <Box className={styles.listViewContainer}>
            <List>
              {cartItems.map((item: any, index: number) => (
                <ListItem
                  key={item.location_id || index}
                  className={
                    cartItems.length - 1 !== index
                      ? styles.marginBottom
                      : styles.noMargin
                  }
                  sx={{
                    position: "relative",
                    padding: 0,
                    "&:hover .delete-button": {
                      opacity: 1,
                    },
                  }}
                >
                  <Box sx={{ width: "100%" }}>
                    <StoreCart
                      cartItem={item}
                      confirmDelete={() => confirmDelete(item)}
                    />
                  </Box>
                  <IconButton
                    className="delete-button"
                    onClick={() => confirmDelete(item)}
                    sx={{
                      position: "absolute",
                      right: 8,
                      top: "50%",
                      transform: "translateY(-50%)",
                      backgroundColor: theme.palette.error.main,
                      color: theme.palette.common.white,
                      opacity: 0,
                      transition: "opacity 0.2s",
                      "&:hover": {
                        backgroundColor: theme.palette.error.dark,
                      },
                    }}
                    size="small"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </ListItem>
              ))}
            </List>
          </Box>
        </DialogContent>
      </Dialog>
      {selectedCart && (
        <ConfirmCartDeletionModal
          closeDeletionModal={closeDeletionModal}
          selectedCart={selectedCart}
        />
      )}
    </>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  cartsContainer: {
    backgroundColor: theme.palette.common.white,
    paddingTop: 16,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    paddingBottom: 20,
    maxHeight: "80vh",
    overflow: "auto",
    width: "100%",
  },
  listHeaderContainer: {
    boxShadow: "0px 0px 12px rgba(0, 0, 0, 0.25)",
    display: "flex",
    flexDirection: "row",
    paddingTop: 8,
    paddingBottom: 8,
    paddingLeft: 17,
    paddingRight: 17,
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  listHeaderText: {
    fontWeight: "bold",
    color: theme.palette.grey[600],
  },
  checkoutButton: {
    borderWidth: 1,
    borderColor: theme.palette.primary.main,
    borderRadius: 8,
    paddingTop: 5,
    paddingBottom: 5,
    paddingLeft: 8,
    paddingRight: 8,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    textTransform: "none",
    minWidth: "auto",
  },
  checkoutButtonLabel: {
    color: theme.palette.primary.main,
  },
  marginBottom: {
    marginBottom: 11,
  },
  noMargin: {
    marginBottom: 0,
  },
  listViewContainer: {
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 12,
    paddingBottom: 12,
  },
}))

export default CartSheet
