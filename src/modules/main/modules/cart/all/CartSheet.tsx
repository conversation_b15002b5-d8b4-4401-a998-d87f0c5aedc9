import {StyleSheet, TouchableOpacity, View} from 'react-native';
import React, {useState} from 'react';
import {useSelector} from 'react-redux';
import {useNavigation} from '@react-navigation/native';
import {Text} from 'react-native-paper';
import {useTranslation} from 'react-i18next';
import {SwipeListView} from 'react-native-swipe-list-view';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAppTheme} from '../../../../../utils/theme';
import useBackHandler from '../../../hooks/useBackHandler';
import CloseSheetContainer from '../../../components/bottomSheet/CloseSheetContainer';
import {isIOS, screenHeight} from '../../../../../utils/constants';
import {keyExtractor} from '../../../../../utils/utils';
import StoreCart from '../../../components/stickyCart/StoreCart';
import ConfirmCartDeletionModal from '../../../components/stickyCart/ConfirmCartDeletionModal';

const CartSheet = () => {
  const {t} = useTranslation();
  const navigation = useNavigation<any>();
  const theme = useAppTheme();
  const styles = makeStyles(theme.colors);
  const {goBack} = useBackHandler();
  const {cartItems} = useSelector(({cart}) => cart);
  const {gestureEnabled} = useSelector(({gesture}) => gesture);

  const [selectedCart, setSelectedCart] = useState<any>(null);

  const confirmDelete = (cart: string) => {
    setSelectedCart(cart);
  };

  const closeDeletionModal = () => setSelectedCart(null);

  const showAllCarts = () => {
    goBack();
    navigation.navigate('Cart');
  };

  return (
    <>
      <View style={styles.container}>
        <CloseSheetContainer closeSheet={goBack}>
          <View
            style={[
              styles.cartsContainer,
              gestureEnabled
                ? styles.gestureContainer
                : styles.nonGestureContainer,
            ]}>
            <View style={styles.listHeaderContainer}>
              <Text variant="bodyLarge" style={styles.listHeaderText}>
                {t('Cart.Your Carts', {count: cartItems.length})}
              </Text>
              <TouchableOpacity
                style={styles.checkoutButton}
                onPress={showAllCarts}>
                <Text variant="bodyMedium" style={styles.checkoutButtonLabel}>
                  {t('Cart.Checkout')}
                </Text>
                <Icon
                  name="chevron-right"
                  color={theme.colors.primary}
                  size={24}
                />
              </TouchableOpacity>
            </View>
            <View style={styles.listViewContainer}>
              <SwipeListView
                data={cartItems}
                showsVerticalScrollIndicator={false}
                keyExtractor={keyExtractor}
                renderItem={({item, index}) => (
                  <View
                    style={[
                      cartItems.length - 1 !== index
                        ? styles.marginBottom
                        : styles.noMargin,
                    ]}>
                    <StoreCart
                      cartItem={item}
                      confirmDelete={() => confirmDelete(item)}
                    />
                  </View>
                )}
                renderHiddenItem={({item, index}) => (
                  <View
                    style={[
                      cartItems.length - 1 !== index
                        ? styles.marginBottom
                        : styles.noMargin,
                      styles.rowBack,
                    ]}>
                    <TouchableOpacity
                      style={styles.clearButton}
                      onPress={() => confirmDelete(item)}>
                      <Text
                        variant="bodyMedium"
                        style={styles.clearButtonLabel}>
                        {t('Cart.Clear')}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}
                rightOpenValue={-75}
              />
            </View>
          </View>
        </CloseSheetContainer>
      </View>
      {selectedCart && (
        <ConfirmCartDeletionModal
          closeDeletionModal={closeDeletionModal}
          selectedCart={selectedCart}
        />
      )}
    </>
  );
};

const makeStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      justifyContent: 'flex-end',
      height: screenHeight,
      paddingTop: isIOS ? 32 : 0,
      backgroundColor: 'rgba(47, 47, 47, 0.75)',
    },
    cartsContainer: {
      maxHeight: screenHeight - 100,
      backgroundColor: colors.white,
      paddingTop: 16,
      borderTopLeftRadius: 16,
      borderTopRightRadius: 16,
      paddingBottom: 120,
    },
    gestureContainer: {
      paddingBottom: 68,
    },
    nonGestureContainer: {
      paddingBottom: isIOS ? 68 : 128,
    },
    listHeaderContainer: {
      shadowColor: '#000',
      shadowOffset: {width: 0, height: 0},
      shadowOpacity: 0.25,
      shadowRadius: 12,
      elevation: 4,
      flexDirection: 'row',
      paddingVertical: 8,
      paddingHorizontal: 17,
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    listHeaderText: {
      fontWeight: 'bold',
      color: colors.neutral400,
    },
    checkoutButton: {
      borderWidth: 1,
      borderColor: colors.primary,
      borderRadius: 8,
      paddingVertical: 5,
      paddingHorizontal: 8,
      flexDirection: 'row',
      alignItems: 'center',
    },
    checkoutButtonLabel: {
      color: colors.primary,
    },
    rowBack: {
      alignItems: 'center',
      backgroundColor: colors.primary50,
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'flex-end',
      paddingRight: 13,
      borderRadius: 12,
    },
    marginBottom: {
      marginBottom: 11,
    },
    noMargin: {
      marginBottom: 0,
    },
    clearButton: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    clearButtonLabel: {
      color: colors.primary,
    },
    listViewContainer: {
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
  });

export default CartSheet;
