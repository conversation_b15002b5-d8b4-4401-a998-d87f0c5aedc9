import {
  Assignment,
  KeyboardArrowDown,
  KeyboardArrowUp,
} from "@mui/icons-material"
import { styled, useTheme } from "@mui/material"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Divider from "@mui/material/Divider"
import Modal from "@mui/material/Modal"
import Paper from "@mui/material/Paper"
import Typography from "@mui/material/Typography"
import "moment-duration-format"
import { useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import { useLocation } from "react-router-dom"
import DPANA from "../../../../../assets/DPANA.svg"
import useActionButtonClick from "../../../../../hooks/useActionButtonClick"
import useFormatNumber from "../../../hooks/useFormatNumber"
import useFulfilment from "../../../hooks/useFulfilment"
import useInitItems from "../../../hooks/useInitItems"
import useSelect from "../../../hooks/useSelect"
import Header from "../provider/components/Header"
import ApplyCoupon from "./components/ApplyCoupon"
import CouponAppliedModal from "./components/CouponAppliedModal"
import DashedLine from "./components/DashedLine"
import DeliveryCharges from "./components/DeliveryCharges"
import FulfillmentSelection from "./components/FulfilmentSelection"
import Items from "./components/Items"
import SellerFee from "./components/SellerFee"
import Taxes from "./components/Taxes"

const StyledContainer = styled(Box)({
  backgroundColor: "white",
  flex: 1,
})

const StyledSheetContainer = styled(Paper)(({ theme }) => ({
  borderTopLeftRadius: 15,
  borderTopRightRadius: 15,
  backgroundColor: theme.palette.grey[100],
  flex: 1,
}))

const StyledDeliveryAppliedCoupon = styled(Box)(({ theme }) => ({
  margin: theme.spacing(0, 2),
  padding: theme.spacing(2),
  borderRadius: 8,
  backgroundColor: "#419E6A1A",
  marginTop: theme.spacing(2.5),
  marginBottom: theme.spacing(2.5),
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: theme.spacing(1.25),
}))

const StyledSummaryContainer = styled(Paper)(({ theme }) => ({
  backgroundColor: "white",
  margin: theme.spacing(0, 2),
  marginBottom: theme.spacing(1.5),
  marginTop: theme.spacing(1.5),
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: 12,
}))

const StyledBillSummaryTitle = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1.25, 2),
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  gap: theme.spacing(1.25),
}))

const StyledBillSummaryItems = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1, 2),
  gap: 1,
}))

const StyledBillSummaryTotal = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2, 2),
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
}))

const StyledProceedButtonContainer = styled(Box)(() => ({
  padding: 2,
  backgroundColor: "white",
  width: "100%",
  bottom: 0,
}))

const Fulfillment = () => {
  const location = useLocation()
  const { cartId, items, cartItems, deliveryAddress, appliedCoupon } =
    location.state || {}
  const scrollViewRef = useRef<any>(null)
  const expandableRef = useRef<any>(null)
  const { formatNumber } = useFormatNumber()
  const { t } = useTranslation()
  const theme = useTheme()
  const onButtonPressAction = useActionButtonClick()
  const { setAddressAndCartId, onGetQuote } = useSelect()
  const [selectedFulfillmentList, setSelectedFulfillmentList] = useState<any[]>(
    []
  )
  const [uniqueFulfilment, setUniqueFulfilment] = useState<any[]>([])
  const [chargeRequested, setChargeRequested] = useState<boolean>(false)
  const [modalDetails, setModalDetails] = useState<any>({
    isVisible: false,
    children: <Box />,
  })
  const { selectedItems, setSelectedItems, updateSelectedItemsForInit } =
    useFulfilment()
  const { setPreInitInfo, handleInitializeOrder, initializeOrderLoading } =
    useInitItems(deliveryAddress, deliveryAddress)
  const [toShowHidePay, setToShowHidePay] = useState(false)
  const [selectedCouponDetails, setSelectedCouponDetails] = useState<any>({})
  const [isApplyingCouponModal, setIsApplyingCouponModal] =
    useState<boolean>(false)
  const showPaymentOption = () => {
    updateSelectedItemsForInit()
    const totalQuantity = charges?.itemsList?.reduce(
      (sum: number, item: any) => sum + item.quantity,
      0
    )

    setPreInitInfo(
      cartId,
      items,
      selectedItems,
      selectedFulfillmentList,
      Number(charges.totalOrderValueAfterSubsidy),
      charges?.offer ?? null,
      charges?.itemCost ?? 0,
      totalQuantity
    )
    handleInitializeOrder()
  }

  const requestCharge = async (fulfilmentId: any) => {
    setAddressAndCartId(deliveryAddress, cartId)
    try {
      setChargeRequested(true)
      await onGetQuote(cartItems[0].context.message_id, items, fulfilmentId)
    } catch (error: any) {
      // crashlytics().recordError(error);
    } finally {
      setChargeRequested(false)
    }
  }

  useEffect(() => {
    setSelectedItems(cartItems)
    const fulfilmentIds = Array.from(
      new Set(
        cartItems[0]?.message?.quote?.items?.map(
          (item: any) => item?.fulfillment_id
        )
      )
    )
    setUniqueFulfilment(fulfilmentIds)
  }, [cartItems])

  const charges = useMemo(() => {
    return cartItems?.length > 0 ? cartItems[0].charges : null
  }, [cartItems])

  const savedCharges = useMemo(() => {
    const response: any = {}
    for (const key in charges.combinedDeliveryItemList) {
      response[key] = Number(charges.combinedDeliveryItemList[key])
    }
    response.saved = response?.digiHaatSubsidy || 0 + response?.snpDiscount || 0
    return response
  }, [cartItems])

  const { orderTotal, orderTotalBeforeCoupon, discount } = useMemo(() => {
    const total = Number(charges?.totalOrderValueAfterSubsidy)
    let totalDiscount = Math.abs(charges?.totalProductDiscount) ?? 0
    if (charges?.offer?.benefit) {
      totalDiscount += Math.abs(charges?.offer?.benefit)
    }
    return {
      orderTotal: total.toFixed(2),
      orderTotalBeforeCoupon: charges?.totalOrderValueAfterSubsidyBeforeCoupon
        ? Number(charges?.totalOrderValueAfterSubsidyBeforeCoupon).toFixed(2)
        : total.toFixed(2),
      discount: totalDiscount,
    }
  }, [charges])

  useEffect(() => {
    try {
      if (selectedItems.length > 0) {
        selectedItems?.forEach((singleItem) => {
          let { message } = singleItem

          if (message) {
            let fulfilmentIds = Object.assign([], selectedFulfillmentList)

            if (fulfilmentIds.length === 0) {
              fulfilmentIds = selectedItems[0]?.message?.quote.items.map(
                (item: any) => item.fulfillment_id
              )
              setSelectedFulfillmentList(fulfilmentIds)
            }
          }
        })
      }
    } catch (err) {
      console.log("error", err)
    }
  }, [selectedItems, selectedFulfillmentList, items])

  const handleSelectedCouponDetails = (data: any) => {
    setSelectedCouponDetails(data)
    setTimeout(() => {
      setIsApplyingCouponModal(true)
    }, 400)
  }
  const handleCouponConfirmModal = async () => {
    setIsApplyingCouponModal(false)
  }
  const handleToPayBlock = () => {
    setToShowHidePay((prev) => !prev)
  }

  const disabledButton =
    selectedFulfillmentList.length === 0 ||
    initializeOrderLoading ||
    chargeRequested

  return (
    <StyledContainer>
      <StyledSheetContainer>
        <Box
          sx={{
            position: "sticky",
            top: 0,
            zIndex: theme.zIndex.appBar,
            backgroundColor: "white",
          }}
        >
          <Header label={t("Fulfillment.Order Details")} />
        </Box>

        <Box
          component="div"
          ref={scrollViewRef}
          sx={{ flex: 1, overflowY: "auto", overflowX: "hidden" }}
        >
          <FulfillmentSelection
            uniqueFulfilment={uniqueFulfilment}
            selectedFulfillmentList={selectedFulfillmentList}
            setSelectedFulfillmentList={setSelectedFulfillmentList}
            setSelectedItems={setSelectedItems}
            items={items}
            cartItems={cartItems}
            quote={cartItems[0]?.message?.quote}
            requestCharge={requestCharge}
            deliveryAddress={deliveryAddress}
            disableSelection={chargeRequested || initializeOrderLoading}
          />
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              paddingLeft: theme.spacing(2),
              paddingRight: theme.spacing(2),
            }}
          >
            <ApplyCoupon
              appliedCoupon={appliedCoupon}
              charges={charges}
              cartTotal={orderTotalBeforeCoupon}
              cartId={cartId}
              deliveryAddress={deliveryAddress}
              items={items}
              messageId={cartItems[0].context.message_id}
              bppId={cartItems[0].context.bpp_id}
              domain={cartItems[0].context.domain}
              providerId={items[0].item.provider.id}
              subcategory={items[0].item.product.category_id}
              selectedFulfilmentId={
                selectedFulfillmentList.length > 0
                  ? selectedFulfillmentList[0]
                  : null
              }
              setSelectedCouponDetails={handleSelectedCouponDetails}
            />
          </Box>
          {savedCharges.saved > 0 && (
            <StyledDeliveryAppliedCoupon>
              <img src={DPANA} width={24} height={24} />
              <Typography variant="body1" sx={{ color: "#419E6A" }}>
                {t("Fulfillment.You saved on delivery", {
                  amount: formatNumber(Number(savedCharges.saved).toFixed(2)),
                })}
              </Typography>
            </StyledDeliveryAppliedCoupon>
          )}
          {!!charges && (
            <StyledSummaryContainer>
              <StyledBillSummaryTitle onClick={handleToPayBlock}>
                <Assignment color="primary" />
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body1">
                    {t("Fulfillment.To Pay")}{" "}
                    {discount > 0 && (
                      <Typography
                        variant="labelSmall"
                        component="span"
                        sx={{
                          textDecoration: "line-through",
                          color: "text.secondary",
                        }}
                      >
                        ₹
                        {formatNumber(
                          (Number(orderTotal) + Number(discount)).toFixed(2)
                        )}
                      </Typography>
                    )}{" "}
                    <Typography variant="body1" component="span">
                      ₹{formatNumber(Number(orderTotal).toFixed(2))}
                    </Typography>
                  </Typography>
                  {discount > 0 && (
                    <Typography
                      variant="caption"
                      sx={{ color: "success.main", fontSize: 12 }}
                    >
                      ₹{formatNumber(discount.toFixed(2))}{" "}
                      {t("Fulfillment.saved on the total")}
                    </Typography>
                  )}
                </Box>

                {toShowHidePay ? (
                  <KeyboardArrowUp color="primary" />
                ) : (
                  <KeyboardArrowDown color="primary" />
                )}
              </StyledBillSummaryTitle>
              <Box
                sx={{ display: toShowHidePay ? "block" : "none" }}
                ref={expandableRef}
              >
                <Divider />
                <StyledBillSummaryItems>
                  <Items
                    itemsList={charges.itemsList}
                    setModalDetails={setModalDetails}
                  />
                  <DeliveryCharges
                    deliveryCharges={charges.combinedDeliveryItemList}
                    setModalDetails={setModalDetails}
                  />
                  <SellerFee
                    sellerFees={charges.sellerFees}
                    setModalDetails={setModalDetails}
                  />
                  {Number(charges?.platformFees) > 0 && (
                    <Box
                      sx={{ display: "flex", justifyContent: "space-between" }}
                    >
                      <Typography variant="labelSmall" color="black">
                        {t("Fulfillment.Platform Fee")}
                      </Typography>
                      <Typography variant="labelSmall" color="black">
                        ₹
                        {formatNumber(Number(charges?.platformFees).toFixed(2))}
                      </Typography>
                    </Box>
                  )}
                  <Taxes
                    taxes={charges.taxes}
                    setModalDetails={setModalDetails}
                  />
                  {discount > 0 && (
                    <Box
                      sx={{ display: "flex", justifyContent: "space-between" }}
                    >
                      <Typography variant="labelSmall" color="black">
                        {t("Fulfillment.Discount")}
                      </Typography>
                      <Typography variant="labelSmall" color="success.main">
                        -₹{formatNumber(discount.toFixed(2))}
                      </Typography>
                    </Box>
                  )}
                </StyledBillSummaryItems>
                <DashedLine height={2} />
                <StyledBillSummaryTotal>
                  <Typography variant="body1" color="primary">
                    {t("Fulfillment.To Pay")}
                  </Typography>
                  <Typography variant="body1" color="primary">
                    ₹{formatNumber(Number(orderTotal).toFixed(2))}
                  </Typography>
                </StyledBillSummaryTotal>
              </Box>
            </StyledSummaryContainer>
          )}
          <StyledProceedButtonContainer>
            <Box sx={{ p: 2 }}>
              <Button
                disabled={disabledButton}
                variant="contained"
                sx={{ width: "100%", height: "48px" }}
                onClick={() => onButtonPressAction(showPaymentOption)}
              >
                {initializeOrderLoading ? null : (
                  <Typography variant="bodyLarge" color="white">
                    {t("Fulfillment.Proceed to Pay")} | ₹
                    {formatNumber(Number(orderTotal).toFixed(2))}
                  </Typography>
                )}
              </Button>
            </Box>
          </StyledProceedButtonContainer>
        </Box>
      </StyledSheetContainer>
      <Modal
        open={modalDetails.isVisible}
        onClose={() => setModalDetails({ isVisible: false, children: <Box /> })}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {modalDetails.children}
      </Modal>
      <CouponAppliedModal
        discount={discount}
        onConfirm={handleCouponConfirmModal}
        selectedCouponDetails={selectedCouponDetails}
        visible={isApplyingCouponModal}
      />
    </StyledContainer>
  )
}

export default Fulfillment
