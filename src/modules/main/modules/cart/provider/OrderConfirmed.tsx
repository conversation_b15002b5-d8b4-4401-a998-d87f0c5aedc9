import React from "react";
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
import {useLocation} from "react-router-dom";
import <PERSON><PERSON> from "lottie-react";
import animationData from "../../../../../assets/order/order_animation.json";
import "./OrderConfirmed.css";
import Box from "@mui/material/Box";

interface OrderConfirmedProps {
  orderId?: string;
}

const OrderConfirmed = () => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  //
  // useEffect(() => {
  //   setTimeout(() => {
  //     navigate(`/order/${orderId}`);
  //   }, 1000);
  // }, [orderId, navigate]);

  const lottieOptions: any = {
    loop: true,
    autoplay: true,
    animationData: animationData,
    rendererSettings: {
      preserveAspectRatio: "xMidYMid slice",
    },
  };

  return (
    <Box className="container">
      <Box className="sub-container">
        <Lottie animationData={animationData} loop={true} autoplay={true} height={200} width={200}/>
        <Typography variant="h4" className="title">
          {t("Payment.Order Confirmed")}
        </Typography>
        <Typography variant="labelSmall" className="message">
          {t("Payment.Your order has been placed successfully")}
        </Typography>
      </Box>
    </Box>
  );
};

export default OrderConfirmed;
