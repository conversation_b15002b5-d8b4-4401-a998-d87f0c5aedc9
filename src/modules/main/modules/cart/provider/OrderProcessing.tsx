import React, {useEffect, useState} from "react";
import <PERSON><PERSON> from "lottie-react";
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import orderProcessingAnimation from "../../../../../assets/order/order_processing.json"; // Adjust the path as needed
import "./OrderProcessing.css";
import {useLocation} from "react-router-dom";
import {emptyAlertCallback} from "../../../../../utils/utils";
import useConfirmItems from "../../../hooks/useConfirmItems";
import Box from "@mui/material/Box";

const OrderProcessing = () => {
  const location = useLocation();
  const {
    activePaymentMethod,
    cartItems,
    updatedCartItemsData,
    cartId,
    itemsTotal,
    itemCount,
    orderTotal,
    deliveryAddress
  } = location.state || {};
  const {t} = useTranslation();
  const [message, setMessage] = useState<string>(t("Payment.Heading to Cart"));

  const updateMessage = () => {
    setMessage(t("Payment.We’re placing your order"));
  };

  const {handleConfirmOrder} = useConfirmItems(updateMessage);

  useEffect(() => {
    handleConfirmOrder(
      cartId,
      deliveryAddress,
      activePaymentMethod,
      cartItems,
      updatedCartItemsData,
      orderTotal,
      itemsTotal,
      itemCount,
    ).finally(emptyAlertCallback);
  }, []);

  return (
    <Box className="order-processing">
      <Box className="order-container">
        <Lottie animationData={orderProcessingAnimation} className="animation"/>
        <Typography variant="body1" className="message">
          {message}
        </Typography>
      </Box>
    </Box>
  );
};

export default OrderProcessing;
