import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import List from "@mui/material/List"
import TextField from "@mui/material/TextField"
import { useEffect, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { useLocation, useNavigate } from "react-router-dom"
import HeaderWithActions from "../../../components/header/HeaderWithActions"
import SafeAreaPage from "../../../components/page/SafeAreaPage"
import useCoupons from "../../../hooks/useCoupons"
import useSelect from "../../../hooks/useSelect"
import Coupon from "./components/Coupon"
import CouponAppliedModal from "./components/CouponAppliedModal"
import CouponSkeleton from "./components/CouponSkeleton"

const CouponList = () => {
  const location = useLocation()
  const {
    cartId,
    deliveryCharge,
    sellerDiscount,
    deliveryAddress,
    items,
    selectedFulfilmentId,
    messageId,
    bppId,
    domain,
    providerId,
    subcategory,
    mov,
    appliedCoupon,
    applySubsidy,
  } = location.state || {}
  const { t } = useTranslation()
  const { getCoupons } = useCoupons()
  const navigation = useNavigate()
  const { setAddressAndCartId, onGetQuote, onGetQuoteForCouponList } =
    useSelect()
  const [coupons, setCoupons] = useState<any[]>([])
  const [couponRequested, setCouponRequested] = useState<boolean>(true)
  const [applyingCoupon, setApplyingCoupon] = useState<boolean>(false)
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [errorCode, setErrorCode] = useState<string>("")
  const [selectingCoupon, setSelectingCoupon] = useState<string>("")
  const [selectedCouponDetails, setSelectedCouponDetails] = useState<any>({})
  const [appliedCouponNavigationDetails, setAppliedCouponNavigationDetails] =
    useState<any>({})
  const [isApplyingCouponModal, setIsApplyingCouponModal] =
    useState<boolean>(false)
  console.log("🚀 ~ CouponList ~ isApplyingCouponModal:", isApplyingCouponModal)

  const isPresentInList = (query: string, list: any[]) => {
    return list?.find((coupon: any) => coupon.offerId.toLowerCase() === query)
  }

  const discount = useMemo(() => {
    if (
      !appliedCouponNavigationDetails ||
      !appliedCouponNavigationDetails.hasOwnProperty("cartItems")
    ) {
      return 0
    }

    const { charges } = appliedCouponNavigationDetails?.cartItems[0]
    let totalDiscount = Math.abs(charges?.totalProductDiscount) ?? 0
    if (charges?.offer?.benefit) {
      totalDiscount += Math.abs(charges?.offer?.benefit)
    }
    return totalDiscount
  }, [appliedCouponNavigationDetails])

  const applySelectedCoupon = async (query: string) => {
    setApplyingCoupon(true)
    let availableCoupon = isPresentInList(query, coupons)
    if (!availableCoupon) {
      const hiddenList = await getCoupons(
        bppId,
        domain,
        providerId,
        subcategory,
        true,
        query,
        applySubsidy,
        String(sellerDiscount),
        String(mov),
        String(deliveryCharge)
      )
      availableCoupon = isPresentInList(query, hiddenList)
      if (!availableCoupon) {
        setApplyingCoupon(false)
        setErrorCode("Invalid code")
        return false
      }
    }
    if (
      availableCoupon.offerQualifier.minValue &&
      Number(availableCoupon.offerQualifier.minValue) > Number(mov)
    ) {
      setApplyingCoupon(false)
      return false
    }
    setSelectingCoupon(availableCoupon.id)
    setErrorCode("")
    setAddressAndCartId(deliveryAddress, cartId)
    const benefitAvailable = Number(availableCoupon?.benefit ?? 0) > 0
    if (benefitAvailable) {
      setSelectedCouponDetails(availableCoupon)
    }
    try {
      const result = await onGetQuoteForCouponList(
        messageId,
        items,
        selectedFulfilmentId,
        availableCoupon
      )

      setAppliedCouponNavigationDetails(result)

      setIsApplyingCouponModal(true)
    } catch (error: any) {
    } finally {
      setSelectingCoupon("")
      setApplyingCoupon(false)
    }
  }

  const updateSearchQuery = (text: string) => {
    setSearchQuery(text.toUpperCase())
    setErrorCode("")
  }

  useEffect(() => {
    setCouponRequested(true)
    getCoupons(
      bppId,
      domain,
      providerId,
      subcategory,
      false,
      "",
      applySubsidy,
      String(sellerDiscount),
      String(mov),
      String(deliveryCharge)
    )
      .then((list) => {
        if (appliedCoupon && appliedCoupon.hidden) {
          const index = list.findIndex(
            (one: any) =>
              one.offerId.toLowerCase() === appliedCoupon.offerId.toLowerCase()
          )
          if (index === -1) {
            list.unshift(appliedCoupon)
            setCoupons(list)
          } else {
            setCoupons(list)
          }
        } else {
          setCoupons(list)
        }
        setCouponRequested(false)
      })
      .catch(() => {
        setCouponRequested(false)
      })
  }, [])
  const handleCouponConfirmModal = async () => {
    const { cartItems } = appliedCouponNavigationDetails
    navigation("/fulfillment", {
      replace: true,
      state: {
        cartId: appliedCouponNavigationDetails.cartId,
        items: appliedCouponNavigationDetails.items,
        deliveryAddress: appliedCouponNavigationDetails.deliveryAddress,
        appliedCoupon: appliedCouponNavigationDetails.appliedCoupon,
        cartItems,
      },
    })
  }
  return (
    <SafeAreaPage>
      <Box>
        <HeaderWithActions label={t("Coupon.Apply Coupon")} />
        <Box
          p={2}
          gap={2}
          display="flex"
          alignItems="flex-start"
          justifyContent="flex-start"
          bgcolor="white"
        >
          <TextField
            fullWidth
            size="small"
            variant="outlined"
            placeholder={t("Coupon.Please enter valid coupon code")}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value.toUpperCase())}
            error={!!errorCode}
            helperText={errorCode && t(`Coupon.${errorCode}`)}
          />
          <Button
            variant="contained"
            color="primary"
            onClick={() => applySelectedCoupon(searchQuery)}
            disabled={applyingCoupon}
          >
            {applyingCoupon ? (
              <CircularProgress size={20} />
            ) : (
              t("Coupon.Apply")
            )}
          </Button>
        </Box>
        <List sx={{ height: "calc(100dvh - 154px)", overflow: "scroll" }}>
          {couponRequested
            ? Array(5)
                .fill(null)
                .map((_, i) => (
                  <Box px={2} py={1} key={i}>
                    <CouponSkeleton />
                  </Box>
                ))
            : coupons.map((coupon) => (
                <Box px={2} py={1} key={coupon.id}>
                  <Coupon
                    cartTotal={mov}
                    applySelectedCoupon={applySelectedCoupon}
                    selectingCoupon={selectingCoupon}
                    appliedCoupon={appliedCoupon}
                    coupon={coupon}
                  />
                </Box>
              ))}
        </List>
      </Box>
      <CouponAppliedModal
        discount={discount}
        onConfirm={handleCouponConfirmModal}
        selectedCouponDetails={selectedCouponDetails}
        visible={isApplyingCouponModal}
      />
    </SafeAreaPage>
  )
}

export default CouponList
