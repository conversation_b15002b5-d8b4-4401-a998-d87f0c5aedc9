import Box from "@mui/material/Box"
import CircularProgress from "@mui/material/CircularProgress"
import Modal from "@mui/material/Modal"
import Typography from "@mui/material/Typography"
import { useCallback, useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate, useParams } from "react-router-dom" // React Router for navigation

import axios from "axios"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import { updateCartItems } from "../../../../../toolkit/reducer/cart"
import { API_BASE_URL, CART } from "../../../../../utils/apiActions"
import {
  emptyAlertCallback,
  getPriceWithCustomisations,
  parseBrandOutletId,
} from "../../../../../utils/utils"
import useBackHandler from "../../../hooks/useBackHandler"
import useGetAddressesOfUser from "../../../hooks/useGetAddressesOfUser"
import useSelect from "../../../hooks/useSelect"
import SkeletonLoader from "../all/components/SkeletonLoader"
import CartItems from "./components/CartItems"
import CartSummary from "./components/CartSummary"
import EmptyCart from "./components/EmptyCart"
import Header from "./components/Header"
import ProfilePending from "./components/ProfilePending"
import "./ProviderCart.css"
const CancelToken = axios.CancelToken
const ProviderCart = () => {
  const routeParams = useParams()
  const location = useLocation()
  const parts = location.pathname.split("/")
  const lastValue = parts[parts.length - 1]
  const cartLocationId = lastValue.replace(/-/g, "/")

  const { t } = useTranslation()
  const dispatch = useDispatch()

  const { name, uid } = useSelector((state: any) => state.auth)
  const { address } = useSelector((state: any) => state.address)
  const [deliveryAddress, setDeliveryAddress] = useState<any>(null)
  const [cartTotal, setCartTotal] = useState<string>("0")
  const [cartId, setCartId] = useState<string>("")
  const [providerWiseItems, setProviderWiseItems] = useState<any[]>([])
  const getAddresses = useGetAddressesOfUser()
  const cartData = useSelector((state: any) => state.cart)
  const source = useRef<any>(null)
  const { goBack } = useBackHandler()
  const navigation = useNavigate()
  const [apiInProgress, setApiInProgress] = useState(true)
  const { getDataWithAuth } = useNetworkHandling()

  const getAllCartList = async () => {
    try {
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/all`,
        { signal: source.current.signal }
      )
      dispatch(updateCartItems(data))
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    setApiInProgress(true)
    dispatch(updateCartItems([]))
    getAllCartList().finally(() => {
      setApiInProgress(false)
    })
    return () => {
      if (source.current) {
        source.current.cancel()
      }
    }
  }, [])

  const {
    checkoutLoading,
    onCheckoutFromCart,
    haveDistinctProviders,
    isProductAvailableQuantityIsZero,
    isProductCategoryIsDifferent,
    cartItems,
    setCartItems,
  } = useSelect()

  useEffect(() => {
    const cart = cartData?.cartItems?.find(
      (one: any) =>
        one.location_id === cartLocationId || one.location_id === lastValue
    )
    if (cart) {
      setCartId(cart?._id)
      setCartItems(cart?.items)
    }
  }, [cartData])

  const updateCartDetailItems = (items: any[]) => {
    dispatch(updateCartItems(items))
    const newItems = items?.find(
      (one: any) => one.location_id === cartLocationId
    )?.items
    setCartItems(newItems)
  }

  useEffect(() => {
    if (address) {
      setDeliveryAddress(address)
    }
  }, [address])

  useEffect(() => {
    const getCartSubtotal = () => {
      let subtotal = 0
      cartItems.map((cartItem: any) => {
        if (cartItem.item.hasCustomisations) {
          subtotal +=
            getPriceWithCustomisations(cartItem) *
            cartItem?.item?.quantity?.count
        } else {
          subtotal +=
            cartItem?.item?.product?.subtotal * cartItem?.item?.quantity?.count
        }
      })
      return subtotal.toFixed(2)
    }

    const getProviderById = (providers: any[], providerId: string) => {
      return providers.find((provider) => provider.provider.id === providerId)
    }

    const addProviderItem = (providers: any[], item: any) => {
      const providerId = item.item.provider.id
      const existingProvider = getProviderById(providers, providerId)

      if (existingProvider) {
        existingProvider.items.push(item)
      } else {
        providers.push({
          provider: item.item.provider,
          items: [item],
        })
      }
    }

    const getProviderWiseItems = (): any[] => {
      const providers: any[] = []
      cartItems.forEach((item: any) => addProviderItem(providers, item))
      return providers
    }

    setProviderWiseItems(getProviderWiseItems())
    setCartTotal(getCartSubtotal())
  }, [cartItems])

  useEffect(() => {
    setDeliveryAddress(address)
    getAddresses().then(emptyAlertCallback)
  }, [])

  const updateDetailCartItems = (items: any[]) => {
    dispatch(updateCartItems(items))
    const newItems = items?.find(
      (one: any) => one.location_id === cartLocationId
    ).items
    setCartItems(newItems)
  }

  const updateSpecificCartItems = (items: any[]) => {
    setCartItems(items)
    if (items.length === 0) {
      goBack()
    }
  }

  const navigateToHome = () => {
    if (providerWiseItems?.length > 0) {
      const routeParams: any = {
        brandId: providerWiseItems[0]?.items[0]?.item?.provider?.id,
      }
      routeParams.outletId =
        providerWiseItems[0]?.items[0]?.item?.location_details?.id

      const result = parseBrandOutletId(routeParams.outletId)
      navigation(
        `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${routeParams.outletId}`
      )
    }
  }

  const addNewAddress = useCallback(
    () =>
      navigation("/add-default-address", {
        state: { setDefaultAddress: true },
      }),
    [navigation]
  )

  const { addressPresent, deliverTo, formattedDeliveryAddress } =
    useMemo(() => {
      let present: boolean = false
      let delivery = ""
      let formattedAddress: string = ""

      const { address, descriptor } = deliveryAddress || {}
      if (descriptor) {
        present = true
        delivery = `${descriptor?.name} (${descriptor?.phone})`
      }

      formattedAddress = `${address?.building}, ${address?.street}, ${
        address?.landmark ? `${address?.landmark},` : ""
      } ${address?.city}, ${address?.state}, ${address?.areaCode}`

      return {
        addressPresent: present,
        deliverTo: delivery,
        formattedDeliveryAddress: formattedAddress,
      }
    }, [deliveryAddress])

  const disableDeliveredTo =
    isProductAvailableQuantityIsZero ||
    isProductCategoryIsDifferent ||
    haveDistinctProviders ||
    checkoutLoading ||
    !addressPresent

  const cartLength = cartItems.length

  const openAddressList = useCallback(
    () => navigation("/address-sheet"),
    [navigation]
  )

  return (
    <Box className="provider-cart">
      <Header label={t("Cart.Store Cart")} />
      {apiInProgress ? (
        <SkeletonLoader count={1} />
      ) : cartLength === 0 ? (
        <EmptyCart />
      ) : (
        <>
          <Box
            className="cart-container"
            style={{ pointerEvents: checkoutLoading ? "none" : "auto" }}
          >
            <CartItems
              fullCartItems={cartData?.cartItems}
              providerWiseItems={providerWiseItems}
              cartId={cartId}
              cartItems={cartItems}
              setCartItems={updateDetailCartItems}
              updateSpecificCartItems={updateSpecificCartItems}
              haveDistinctProviders={haveDistinctProviders}
              isProductCategoryIsDifferent={isProductCategoryIsDifferent}
              navigateToHome={navigateToHome}
            />
          </Box>
          {!name ? (
            <ProfilePending />
          ) : (
            <CartSummary
              cartLength={cartLength}
              disabled={
                isProductAvailableQuantityIsZero ||
                isProductCategoryIsDifferent ||
                haveDistinctProviders ||
                checkoutLoading
              }
              cartTotal={cartTotal}
              formattedDeliveryAddress={formattedDeliveryAddress}
              addressPresent={addressPresent}
              addNewAddress={addNewAddress}
              disableDeliveredTo={disableDeliveredTo}
              deliverTo={deliverTo}
              openAddressList={openAddressList}
              checkOut={() => onCheckoutFromCart(deliveryAddress, cartId)}
            />
          )}
          <Modal
            open={checkoutLoading}
            onClose={emptyAlertCallback}
            className="checkout-modal"
          >
            <Box className="modal-content">
              <CircularProgress color="primary" />
              <Typography variant="headlineMedium">Almost done</Typography>
              <Typography variant="labelSmall">
                Checking with the store for any price updates
              </Typography>
            </Box>
          </Modal>
        </>
      )}
    </Box>
  )
}

export default ProviderCart
