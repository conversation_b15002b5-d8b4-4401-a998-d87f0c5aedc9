import CloseIcon from "@mui/icons-material/Close"
import { Box, Button, IconButton, Paper, Typography } from "@mui/material"
import { useCallback, useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import DPANA from "../../../../../assets/DPANA.svg"
import INF from "../../../../../assets/INF.svg"
import LSE from "../../../../../assets/LSE.svg"
import MIA from "../../../../../assets/MIA.svg"
import MOQE from "../../../../../assets/MOQE.svg"
import PNS from "../../../../../assets/PNS.svg"
import Warning from "../../../../../assets/warning.svg"
import { setDomain } from "../../../../../toolkit/reducer/homeDomain"
import { parseBrandOutletId } from "../../../../../utils/utils"

const FulfillmentError = () => {
  const location = useLocation()
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const cartData = useSelector(({ cart }) => cart)
  const { cartLocationId } = location.state || {}
  const { t } = useTranslation()
  const errorCode = location.state?.errorCode
  const errorDetails = useMemo(() => {
    const error = {
      title: t(`Cart.OnSelectWarnings.${errorCode}.title`),
      message: t(`Cart.OnSelectWarnings.${errorCode}.message`),
      firstButtonText: t(`Cart.OnSelectWarnings.${errorCode}.firstButtonText`),
      secondaryButtonText: t(
        `Cart.OnSelectWarnings.${errorCode}.secondButtonText`
      ),
      ModalIcon: Warning,
      firstButtonAction: "",
      secondButtonAction: "",
    }

    switch (errorCode) {
      case "30011":
      case "40006":
        error.ModalIcon = DPANA
        error.firstButtonAction = "Shop With Another Store"
        break
      case "40013":
      case "40009":
        error.ModalIcon = MOQE
        error.firstButtonAction = "Back To Cart"
        break
      case "40004":
        error.ModalIcon = PNS
        error.firstButtonAction = "Retry Payment"
        error.secondButtonAction = "Back To Cart"
        break
      case "40012":
      case "30023":
        error.ModalIcon = MOQE
        error.firstButtonAction = "Proceed With Quantity Update"
        error.secondButtonAction = "Add More Item"
        break
      case "40012":
        error.ModalIcon = INF
        error.firstButtonAction = "Explore The Store"
        error.secondButtonAction = "Back To Cart"
        break
      case "40002":
        error.ModalIcon = INF
        error.firstButtonAction = "Proceed Without Product"
        error.secondButtonAction = "Explore The Store"
        break
      case "30016":
      case "40000":
      case "30022":
        error.ModalIcon = Warning
        error.firstButtonAction = "Explore The Store"
        error.secondButtonAction = "Back To Home"
        break
      case "30008":
        error.ModalIcon = LSE
        error.firstButtonAction = "Update Address"
        error.secondButtonAction = "Back To Home"
        break
      case "30017":
      case "30021":
        error.ModalIcon = MIA
        error.firstButtonAction = "Explore Other Stores"
        error.secondButtonAction = "Back To Home"
        break
      case "30010":
      case "30009":
      case "10002":
        error.ModalIcon = LSE
        error.firstButtonAction = "Update Address"
        error.secondButtonAction = "Back To Home"
        break
      case "31002":
      case "30001":
      case "30000":
      case "31001":
        error.ModalIcon = Warning
        error.firstButtonAction = "Explore The Stores"
        error.secondButtonAction = "Back To Home"
        break
    }

    return error
  }, [errorCode, t])

  const handleNavigation = useCallback(
    (action) => {
      switch (action) {
        case "Shop With Another Store":
          if (cartData?.cartItems?.[0]?.location?.domain) {
            dispatch(
              setDomain({ domain: cartData.cartItems[0].location.domain })
            )
            navigate("/dashboard")
          }
          break
        case "Back To Cart":
          navigate(-1)
          break
        case "Explore The Store":
        case "Add More Item": {
          const cart = cartData?.cartItems?.find(
            (item) => item._id === cartLocationId
          )

          const result = parseBrandOutletId(cart?.items[0]?.item?.provider?.id)

          if (cart) {
            navigate(
              `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${cart.location_id}`,
              {
                state: {
                  brandId: cart.location_id.split("_").slice(0, -1).join("_"),
                  outletId: cart.location_id,
                },
              }
            )
          }
          break
        }
        case "Back To Home":
          navigate("/dashboard")
          break
        case "Update Address":
          navigate("/address-sheet")
          break
        default:
          navigate(-1)
          break
      }
    },
    [cartData, dispatch, navigate, cartLocationId]
  )

  return (
    <Box
      sx={{
        height: "100vh",
        bgcolor: "rgba(47, 47, 47, 0.75)",
        display: "flex",
        flexDirection: "column",
        justifyContent: "flex-end",
      }}
    >
      <Box display="flex" justifyContent="center" mb={2}>
        <IconButton
          onClick={() => navigate(-1)}
          sx={{ bgcolor: "#aaa", "&:hover": { bgcolor: "#888" } }}
        >
          <CloseIcon sx={{ color: "#fff" }} />
        </IconButton>
      </Box>
      <Paper
        elevation={3}
        sx={{
          p: 4,
          borderTopLeftRadius: 16,
          borderTopRightRadius: 16,
          textAlign: "center",
          flexDirection: "column",
        }}
      >
        <img src={errorDetails.ModalIcon} width={60} height={60} />
        <Box sx={{ mb: 2, mt: 2 }}>
          <Typography
            variant="titleLarge"
            sx={{ mb: 2, color: "primary.main" }}
          >
            {errorDetails.title}
          </Typography>
        </Box>
        <Box>
          <Typography variant="titleSmall" sx={{ color: "text.secondary" }}>
            {errorDetails.message}
          </Typography>
        </Box>
        <Button
          variant="contained"
          fullWidth
          sx={{ mb: 2, mt: 2, borderRadius: 2 }}
          onClick={() => handleNavigation(errorDetails.firstButtonAction)}
        >
          {errorDetails.firstButtonText}
        </Button>
        {errorDetails.secondaryButtonText && (
          <Button
            variant="outlined"
            fullWidth
            sx={{ borderRadius: 2 }}
            onClick={() => handleNavigation(errorDetails.secondButtonAction)}
          >
            {errorDetails.secondaryButtonText}
          </Button>
        )}
      </Paper>
    </Box>
  )
}

export default FulfillmentError
