import React from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";
import "./DeliveryAddress.css";

interface DeliveryAddressProps {
  formattedDeliveryAddress: string;
  deliverTo?: string;
  addressPresent: boolean;
  disabled: boolean;
  openAddressList: () => void;
  addNewAddress: () => void;
}

const DeliveryAddress: React.FC<DeliveryAddressProps> = ({
                                                           formattedDeliveryAddress,
                                                           deliverTo,
                                                           addressPresent,
                                                           disabled,
                                                           openAddressList,
                                                           addNewAddress,
                                                         }) => {
  const {t} = useTranslation();
  const {addressListPresent} = useSelector((state: any) => state.address);

  return (
    <Box className="summary-row">
      <Box className="delivery-address">
        <Typography variant="bodyLarge" component="div" className="address-title">
          {t("Cart.Delivery Address")}
        </Typography>
        <Typography variant="labelSmall" className="address-label">
          {formattedDeliveryAddress}
        </Typography>
        {deliverTo && (
          <Typography variant="labelSmall" className="address-label">
            {deliverTo}
          </Typography>
        )}
      </Box>
      {addressPresent ? (
        <Button
          disabled={disabled}
          className="change-button"
          variant="outlined"
          onClick={openAddressList}
        >
          {t("Cart.Change")}
        </Button>
      ) : addressListPresent ? (
        <Button
          disabled={disabled}
          className="change-button"
          variant="outlined"
          onClick={openAddressList}
        >
          {t("Cart.Select Address")}
        </Button>
      ) : (
        <Button
          disabled={disabled}
          className="change-button"
          variant="outlined"
          onClick={addNewAddress}
        >
          {t("Address List.Add Address")}
        </Button>
      )}
    </Box>
  );
};

export default DeliveryAddress;
