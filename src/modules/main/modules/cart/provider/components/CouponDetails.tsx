import React from "react";
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import moment from "moment";
import "./CouponDetails.css";
import Box from "@mui/material/Box";

interface CouponDetailsProps {
  coupon: any;
  detailsVisible: boolean;
}

const CouponDetails: React.FC<CouponDetailsProps> = ({coupon, detailsVisible}) => {
  const {t} = useTranslation();

  if (!detailsVisible) {
    return null;
  }

  return (
    <Box className="details-container">
      <Box className="line-view"/>
      {coupon?.offerQualifier?.minValue && (
        <Box className="coupon-detail-row">
          <Box className="dot-view"/>
          <Typography variant="labelSmall" className="text-neutral">
            {t("Coupon.Applicable on minimum order value", {
              total: coupon?.offerQualifier?.minValue,
            })}
          </Typography>
        </Box>
      )}
      {coupon?.offerQualifier?.usageDurationInDays && (
        <Box className="coupon-detail-row">
          <Box className="dot-view"/>
          <Typography variant="labelSmall" className="text-neutral">
            {t("Coupon.Applicable maximum times in a day", {
              time: coupon?.offerQualifier?.usageDurationInDays,
              frequency: coupon?.offerQualifier?.usageFrequency?.toLowerCase() ?? "",
            })}
          </Typography>
        </Box>
      )}
      {coupon?.offerBenefit?.valueCap && (
        <Box className="coupon-detail-row">
          <Box className="dot-view"/>
          <Typography variant="labelSmall" className="text-neutral">
            {t("Coupon.Maximum discount", {
              amount: coupon?.offerBenefit?.valueCap,
            })}
          </Typography>
        </Box>
      )}
      {coupon?.validTo && (
        <Box className="coupon-detail-row">
          <Box className="dot-view"/>
          <Typography variant="labelSmall" className="text-neutral">
            {t("Coupon.Coupon valid till", {
              date: moment(Number(coupon?.validTo)).locale('en').format("Do MMM YYYY"),
            })}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default CouponDetails;
