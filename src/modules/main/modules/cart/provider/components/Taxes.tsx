import React, {useMemo} from "react"
import {useTranslation} from "react-i18next"
import {useTheme} from "@mui/material/styles"
import {Box, Button, Divider, Typography,} from "@mui/material"
import InformationRow from "./InformationRow"
// import DashedLine from './DashedLine';
import useFormatNumber from "../../../../hooks/useFormatNumber"
import DashedLine from "./DashedLine"
import {styled} from "@mui/material/node/styles"

interface TaxCharges {
  itemTaxes?: number
  platformFeesTax?: number

  [key: string]: number | undefined
}

interface TaxesProps {
  taxes: TaxCharges
  setModalDetails: (details: {
    isVisible: boolean
    children: React.ReactNode
  }) => void
}

const BillModalContainer = styled(Box)(({theme}) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
  width: "80%",
  maxWidth: 400,
  maxHeight: "80dvh",
  overflow: "auto",
  margin: "auto",
}))

const BillTitleContainer = styled(Box)({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 8,
})
const Taxes: React.FC<TaxesProps> = ({taxes, setModalDetails}) => {
  const theme = useTheme()
  const {t} = useTranslation()
  const {formatNumber} = useFormatNumber()

  const charges = useMemo(() => {
    const response: TaxCharges & { total: number } = {
      itemTaxes: 0,
      platformFeesTax: 0,
      total: 0,
    }

    let total = 0
    for (const key in taxes) {
      const value = Number(taxes[key] || 0)
      response[key] = value
      total += value
    }
    response.total = total
    return response
  }, [taxes])

  const openMenu = () => {
    const total = formatNumber(
      ((charges.itemTaxes || 0) + (charges.platformFeesTax || 0)).toFixed(2)
    )

    setModalDetails({
      isVisible: true,
      children: (
        <BillModalContainer>
          <BillTitleContainer>
            <Typography variant="titleLarge">{t("Fulfillment.Item Taxes")}</Typography>
            <Typography variant="titleLarge">₹{total}</Typography>
          </BillTitleContainer>
          <Divider sx={{my: 1}}/>

          <InformationRow
            label={t("Fulfillment.Item Taxes")}
            value={charges.itemTaxes || 0}
          />
          <InformationRow
            label={t("Fulfillment.Platform Fees Tax")}
            value={charges.platformFeesTax || 0}
          />
        </BillModalContainer>
      ),
    })
  }

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        py: 1,
      }}
    >
      <Button
        onClick={openMenu}
        sx={{
          textTransform: "none",
          p: 0,
          minWidth: "auto",
          color: "text.primary",
          flexDirection: "column",
        }}
      >
        <Typography variant="labelSmall" sx={{color: "black"}}>
          {t("Fulfillment.Taxes")}
        </Typography>
        <DashedLine height={1} primary={true}/>
      </Button>

      <Box sx={{textAlign: "right"}}>
        <Typography
          variant="labelSmall"
          sx={{
            color: charges.total === 0 ? "success.main" : "text.primary",
            fontWeight: "medium",
          }}
        >
          ₹{formatNumber(charges.total.toFixed(2))}
        </Typography>
      </Box>
    </Box>
  )
}

export default Taxes
