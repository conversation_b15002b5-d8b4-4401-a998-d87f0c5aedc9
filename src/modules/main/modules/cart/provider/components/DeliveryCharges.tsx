import React, {useMemo} from "react"
import {useTranslation} from "react-i18next"
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import Typography from "@mui/material/Typography";
import InformationRow from "./InformationRow"
import useFormatNumber from "../../../../hooks/useFormatNumber"
import DashedLine from "./DashedLine"
import {styled} from "@mui/material/node/styles"

// import DashedLine from './DashedLine';

interface DeliveryChargesProps {
  deliveryCharges: any
  setModalDetails: (details: {
    isVisible: boolean
    children: React.ReactNode
  }) => void
}

const BillModalContainer = styled(Box)(({theme}) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
  width: "80%",
  maxWidth: 400,
  maxHeight: "80dvh",
  overflow: "auto",
  margin: "auto",
}))

const BillTitleContainer = styled(Box)({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 8,
})

const DeliveryCharges: React.FC<DeliveryChargesProps> = ({
                                                           deliveryCharges,
                                                           setModalDetails,
                                                         }) => {
  const {t} = useTranslation()
  const {formatNumber} = useFormatNumber()

  const charges = useMemo(() => {
    const response: any = {}
    for (const key in deliveryCharges) {
      response[key] = Number(deliveryCharges[key])
    }
    response.saved =
      (response?.digiHaatSubsidy || 0) + (response?.snpDiscount || 0)
    return response
  }, [deliveryCharges])

  const openMenu = () => {
    const total = formatNumber(
      (
        charges?.baseCharge -
        (charges?.snpDiscount + charges?.digiHaatSubsidy)
      ).toFixed(2)
    )

    setModalDetails({
      isVisible: true,
      children: (
        <BillModalContainer>
          <BillTitleContainer>
            <Typography variant="titleLarge">
              {t("Fulfillment.Delivery Fee")}
            </Typography>
            <Typography variant="titleLarge">₹{total}</Typography>
          </BillTitleContainer>
          <Divider sx={{my: 1}}/>

          <InformationRow
            label={t("Fulfillment.Base Fee")}
            value={charges?.baseCharge}
          />
          <InformationRow
            label={t("Fulfillment.SNP Delivery Fees Discount")}
            value={charges?.snpDiscount}
          />
          <InformationRow
            label={t("Fulfillment.DigiHaat Discount")}
            value={charges?.digiHaatSubsidy}
          />
        </BillModalContainer>
      ),
    })
  }

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        py: 1,
      }}
    >
      <Button
        onClick={openMenu}
        sx={{
          textTransform: "none",
          p: 0,
          minWidth: "auto",
          flexDirection: "column",
        }}
      >
        <Typography variant="labelSmall" sx={{color: "black"}}>
          {t("Fulfillment.Delivery Fee")}
        </Typography>
        <DashedLine height={1} primary={true}/>
      </Button>
      <Box sx={{textAlign: "right"}}>
        {charges?.baseCharge !== charges?.deliveryCharge && (
          <Typography
            variant="labelLarge"
            sx={{
              textDecoration: "line-through",
              color: "text.secondary",
            }}
          >
            ₹{formatNumber(charges?.baseCharge?.toFixed(2))}
          </Typography>
        )}
        <Typography
          variant="labelSmall"
          sx={{
            color:
              charges?.deliveryCharge === 0 ? "success.main" : "text.primary",
          }}
        >
          ₹{formatNumber(charges?.deliveryCharge?.toFixed(2))}
        </Typography>
      </Box>
    </Box>
  )
}

export default DeliveryCharges
