import React, {useCallback, useRef, useState} from "react";
import {useDispatch, useSelector} from 'react-redux';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import DeleteOutline from '@mui/icons-material/DeleteOutline';
import Edit from '@mui/icons-material/Edit';
import {useTranslation} from "react-i18next";
import axios from "axios";
import NoImageAvailable from "../../../../../../assets/noImage.png";
import "./CartItems.css";
import Customizations from '../../../../components/customization/Customizations';
import ManageQuantity from '../../../../components/customization/ManageQuantity';
import useUpdateSpecificItemCount from "../../../../hooks/useUpdateSpecificItemCount";
import useNetworkHandling from "../../../../../../hooks/useNetworkHandling";
import {API_BASE_URL, CART, ITEM_DETAILS} from "../../../../../../utils/apiActions";
import {removeProductFromCart} from '../../../../../../toolkit/reducer/cart';
import ItemTotal from "./ItemTotal";
import {CURRENCY_SYMBOLS, FB_DOMAIN} from "../../../../../../utils/constants";
import useCustomizationStateHelper from "../../../../hooks/useCustomizationStateHelper";
import {setProduct} from '../../../../../../toolkit/reducer/product';
import useNetworkErrorHandling from "../../../../../../hooks/useNetworkErrorHandling";
import AddMissingItems from "./AddMissingItems";
import CloseIcon from "@mui/icons-material/Close";
import useFormatNumber from "../../../../hooks/useFormatNumber";
import FBProductCustomization from "../../../../components/provider/components/FBProductCustomization";
import CustomizationFooterButtons from "../../../../components/provider/components/CustomizationFooterButtons";
import {getCustomizations} from '../../../../../../utils/utils';
import {theme} from "../../../../../../utils/theme";
import Image from "../../../../components/image/Image";
import ModalProductDetails from "../../../product/details/ModalProductDetails";

interface CartItemsProps {
  fullCartItems: any[];
  allowScroll?: boolean;
  providerWiseItems: any[];
  cartId: string;
  cartItems: any[];
  setCartItems: (items: any[]) => void;
  navigateToHome: () => void;
  updateSpecificCartItems: (items: any[]) => void;
  haveDistinctProviders: boolean;
  isProductCategoryIsDifferent: boolean;
}

const CancelToken = axios.CancelToken;

const CartItems: React.FC<CartItemsProps> = ({
                                               fullCartItems,
                                               haveDistinctProviders,
                                               isProductCategoryIsDifferent,
                                               providerWiseItems,
                                               cartId,
                                               cartItems,
                                               setCartItems,
                                               updateSpecificCartItems,
                                               navigateToHome,
                                             }) => {
  const {t} = useTranslation();
  const [deleteItemModalVisible, setDeleteItemModalVisible] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<any>(null);
  const source = useRef<any>(null);
  const {updatingCartItem, updateSpecificCartItem} =
    useUpdateSpecificItemCount();
  const itemIdToDelete = useRef<any>(null);
  const {deleteDataWithAuth, getDataWithAuth, putDataWithAuth} =
    useNetworkHandling();
  const dispatch = useDispatch();
  const {uid}: any = useSelector(({auth}: any) => auth);
  const [requestedProduct, setRequestedProduct] = useState<any>(null);
  const {customizationState, setCustomizationState, customizationPrices} =
    useCustomizationStateHelper();
  const [productPayload, setProductPayload] = useState<any>(null);
  const {handleApiError} = useNetworkErrorHandling();
  const [currentCartItem, setCurrentCartItem] = useState<any>(null);
  const [itemQty, setItemQty] = useState<number>(1);
  const [customizationSheet, setCustomizationSheet] = useState(false);
  const {gestureEnabled}: any = useSelector(({gesture}: any) => gesture);
  const {formatNumber} = useFormatNumber();
  const [itemOutOfStock, setItemOutOfStock] = useState<boolean>(false);
  const [updatingProduct, setUpdatingProduct] = useState<boolean>(false);
  const [productModalDetails, setProductModalDetails] = useState<any>(null);

  const updateCartItem = async (
    locationId: any,
    itemId: any,
    increment: boolean,
    uniqueId: any,
  ) => {
    await updateSpecificCartItem(
      locationId,
      itemId,
      increment,
      uniqueId,
      fullCartItems,
      setCartItems,
    );
  };

  const closeDeleteItemModal = () => {
    setDeleteItemModalVisible(false);
    setItemToDelete(null);
  };

  const deleteCartItem = async () => {
    try {
      setItemToDelete(itemIdToDelete.current);
      source.current = CancelToken.source();
      await deleteDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/${itemIdToDelete.current}`,
        source.current.token,
      );
      const list = cartItems.filter(
        (item: any) => item._id !== itemIdToDelete.current,
      );
      if (list.length === 0) {
        await deleteDataWithAuth(
          `${API_BASE_URL}${CART}/${uid}/${cartId}/clear`,
          source.current.token,
        );
      }
      updateSpecificCartItems(list);
      dispatch(
        removeProductFromCart({cartId, productId: itemIdToDelete.current}),
      );
    } catch (error) {
    } finally {
      itemIdToDelete.current = null;
      setItemToDelete(null);
      closeDeleteItemConfirmationModal();
    }
  };

  const closeDeleteItemConfirmationModal = useCallback(() => {
    setDeleteItemModalVisible(false);
    itemIdToDelete.current = null;
  }, []);

  const openDeleteItemConfirmationModal = (itemId: any) => {
    itemIdToDelete.current = itemId;
    setDeleteItemModalVisible(true);
  };

  const getProductDetails = async (productId: any) => {
    try {
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${ITEM_DETAILS}?id=${productId}`,
        source.current.token,
      );
      dispatch(setProduct(data));
      setProductPayload(data);
    } catch (error) {
      handleApiError(error);
    }
  };

  const showCustomization = () => {
    setCustomizationSheet(true)
  };

  const hideCustomization = () => setCustomizationSheet(false);

  const handleCustomiseClick = async (cartItem: any) => {
    try {
      setRequestedProduct(cartItem._id);
      setCustomizationState(cartItem.item.customisationState);
      await getProductDetails(cartItem.item.id);
      setCurrentCartItem(cartItem);
      setItemQty(cartItem?.item?.quantity?.count);
      showCustomization();
    } catch (error: any) {
      // crashlytics().recordError(error);
    } finally {
      setRequestedProduct(null);
    }
  };

  const handleEditClick = async (cartItem: any) => {
    await getProductDetails(cartItem.item.id);
    setProductModalDetails({
      inStock: Number(cartItem?.item?.product?.quantity?.available?.count) >= 1,
      qty: cartItem?.item?.quantity?.count,
      isOpen: true
    });
  };

  const updateCustomizations = async () => {
    try {
      setUpdatingProduct(true);
      const url = `${API_BASE_URL}${CART}/${uid}/${currentCartItem._id}`;

      let customisations = await getCustomizations(
        productPayload,
        customizationState,
      );

      const subtotal =
        productPayload?.item_details?.price?.value + customizationPrices;

      const payload: any = {
        id: productPayload.id,
        local_id: productPayload.local_id,
        bpp_id: productPayload.bpp_details.bpp_id,
        bpp_uri: productPayload.context.bpp_uri,
        contextCity: productPayload.context.city,
        domain: productPayload.context.domain,
        tags: productPayload.item_details.tags,
        customisationState: customizationState,
        quantity: {
          count: itemQty,
        },
        provider: {
          id: productPayload.bpp_details.bpp_id,
          locations: productPayload.locations,
          ...productPayload.provider_details,
        },
        location_details: productPayload.location_details,
        product: {
          id: productPayload.id,
          subtotal,
          ...productPayload.item_details,
        },
        customisations,
        hasCustomisations: true,
      };

      const items = cartItems.concat([]);
      const itemIndex = items.findIndex(
        item => item._id === currentCartItem._id,
      );
      if (itemIndex !== -1) {
        source.current = CancelToken.source();
        await putDataWithAuth(url, payload, source.current.token);
        hideCustomization();

        items[itemIndex] = {...items[itemIndex], item: payload};
        updateSpecificCartItems(items);
      }
      hideCustomization();
    } catch (error: any) {
      // crashlytics().recordError(error);
    } finally {
      setUpdatingProduct(false);
    }
  };

  return (
    <>
      <div className="cart-container">
        {providerWiseItems.map((provider: any) => (
          <div key={provider?.provider?.id} className="cart-provider">
            <Box className="provider">
              <Box className="providerHeader">
                <Image source={provider?.provider?.descriptor?.symbol || NoImageAvailable}
                       imageStyle={"providerImage"}/>
                <Box className="providerMeta">
                  <Typography variant="titleLarge" className="providerName" component="div">
                    {provider?.provider?.descriptor?.name}
                  </Typography>
                  {provider?.provider?.locations?.length > 0 && (
                    <Typography variant="labelSmall" color="textSecondary" component="div" className="providerAddress">
                      {provider.items[0]?.item?.location_details?.address
                        ?.locality || 'NA'}
                    </Typography>
                  )}
                </Box>
              </Box>
            </Box>
            <Box className="productsContainer">
              {provider?.items.map((cartItem: any) => {
                let imageSource: any = NoImageAvailable;
                if (cartItem?.item?.product?.descriptor?.symbol) {
                  imageSource = cartItem?.item?.product?.descriptor?.symbol;
                } else if (
                  cartItem?.item?.product?.descriptor?.images?.length > 0
                ) {
                  imageSource = cartItem?.item?.product?.descriptor?.images[0];
                }
                return (
                  <Box key={cartItem._id}>
                    <Box className="product">
                      <Image source={imageSource || NoImageAvailable} imageStyle={"productImage"}/>
                      <Box className="cartFlex">
                        <Box className="productMeta">
                          <Box className="cartFlex">
                            <Typography variant="body1">{cartItem?.item?.product?.descriptor?.name}</Typography>
                            <Customizations cartItem={cartItem}/>
                            {
                              !cartItem.item.hasCustomisations &&
                              cartItem.item.product?.quantity?.unitized &&
                              Object.keys(
                                cartItem.item.product?.quantity?.unitized,
                              ).map(one => (
                                <Typography variant="labelSmall" color="textSecondary"
                                            key={
                                              cartItem.item.product?.quantity?.unitized[
                                                one
                                                ].value
                                            }>
                                  {
                                    cartItem.item.product?.quantity?.unitized[
                                      one
                                      ].value
                                  }{' '}
                                  {
                                    cartItem.item.product?.quantity?.unitized[
                                      one
                                      ].unit
                                  }
                                </Typography>
                              ))
                            }
                          </Box>
                          <Box className="quantityContainer">
                            <ManageQuantity
                              allowDelete
                              cartItem={cartItem}
                              updatingCartItem={updatingCartItem}
                              updateCartItem={updateCartItem}
                              deleteCartItem={openDeleteItemConfirmationModal}
                            />
                          </Box>
                        </Box>
                        <ItemTotal cartItem={cartItem}/>
                      </Box>
                    </Box>
                    <Box className="productActionContainer">
                      {cartItem.item.domain === FB_DOMAIN ? (
                        cartItem.item.hasCustomisations ? (
                          <Box
                            className="customiseContainer"
                            onClick={() => handleCustomiseClick(cartItem)}
                          >
                            {cartItem._id === requestedProduct ? (
                              <CircularProgress size={20} color="primary"/>
                            ) : (
                              <Edit fontSize="small" color="primary"/>
                            )}
                            <Typography variant="bodyLarge" color={theme.palette.primary.main}>
                              {t('Cart.Edit')}
                            </Typography>
                          </Box>
                        ) : (
                          <Box/>
                        )
                      ) : (
                        <Box
                          className="customiseContainer"
                          onClick={() => handleEditClick(cartItem)}
                        >
                          {cartItem._id === requestedProduct ? (
                            <CircularProgress size={20} color="primary"/>
                          ) : (
                            <Edit fontSize="small" color="primary"/>
                          )}
                          <Typography variant="bodyLarge" color="primary">
                            {t('Cart.Edit')}
                          </Typography>
                        </Box>
                      )}
                      <Box>
                        {itemToDelete === cartItem._id ? (
                          <CircularProgress size={20} color="primary"/>
                        ) : (
                          <IconButton color="error" onClick={() => openDeleteItemConfirmationModal(cartItem._id)}>
                            <DeleteOutline color="error"/>
                          </IconButton>
                        )}
                      </Box>
                    </Box>
                  </Box>
                )
              })}
            </Box>
          </div>
        ))}
        {haveDistinctProviders && (
          <Box className="errorBox">
            <Typography variant="body1" className="errorText">
              {t(
                'Cart Items.You are ordering from different store. Please check your order again',
              )}
            </Typography>
          </Box>
        )}
        {isProductCategoryIsDifferent && (
          <Box className="errorBox">
            <Typography variant="body1" className="errorText">
              {t(
                'Cart Items.You are ordering from different category. Please check your order again',
              )}
            </Typography>
          </Box>
        )}
        <AddMissingItems navigateToHome={navigateToHome}/>
      </div>


      <Box
        sx={{
          position: "fixed",
          bottom: 0,
          width: "100%",
          zIndex: 1301,
        }}
      >
        {/* Floating Close Button (Now Outside Dialog) */}
        {customizationSheet && (
          <Box
            sx={{
              position: "fixed",
              top: "28%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              backgroundColor: "#000",
              color: "#fff",
              borderRadius: "50%",
              width: 40,
              height: 40,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              zIndex: 1302, // Ensure it's above the dialog
              boxShadow: "0px 4px 10px rgba(0,0,0,0.2)",
            }}
            onClick={() => hideCustomization()}
          >
            <CloseIcon sx={{fontSize: 26, color: '#fff'}}/>
          </Box>
        )}
        <Dialog
          open={customizationSheet} fullWidth maxWidth="sm"
          sx={{
            "& .MuiPaper-root": {
              borderRadius: "20px 20px 0 0",
              position: "fixed",
              bottom: 0,
              width: "100%",
              margin: 0,
              paddingBottom: "15px",
              boxShadow: "0px -5px 20px rgba(0, 0, 0, 0.2)",
            },
          }}
        >
          <Box
            className={`sheetContainer ${gestureEnabled ? "gestureContainer" : "nonGestureContainer"}`}
          >
            <Box className="header">
              <img
                src={productPayload?.item_details?.descriptor?.symbol}
                alt={productPayload?.item_details?.descriptor?.symbol}
                className="sheetProductSymbol"
              />
              <Box className="titleContainer">
                <Typography variant="titleSmall" className="title">
                  {productPayload?.item_details?.descriptor?.name}
                </Typography>
                <Typography variant="labelMedium" color="primary">
                  {
                    CURRENCY_SYMBOLS[
                      productPayload?.item_details?.price?.currency
                      ]
                  }
                  {formatNumber(productPayload?.item_details?.price?.value)}
                </Typography>
              </Box>
            </Box>
            {currentCartItem?.item?.provider?.locations?.length > 0 && (
              <Typography variant="labelMedium" className="address">
                {currentCartItem?.item?.provider?.locations[0]?.address
                  ?.street || '-'}
                ,{' '}
                {currentCartItem?.item?.provider?.locations[0]?.address?.city ||
                  '-'}
              </Typography>
            )}
            <Box sx={{maxHeight: 300, overflowY: "auto", padding: 2}} className="customizationContainer">
              <FBProductCustomization
                product={productPayload}
                customizationState={customizationState}
                setCustomizationState={setCustomizationState}
                setItemOutOfStock={setItemOutOfStock}
                disabled={itemOutOfStock}
              />
            </Box>
            <CustomizationFooterButtons
              update
              productLoading={updatingProduct}
              itemQty={itemQty}
              setItemQty={setItemQty}
              itemOutOfStock={itemOutOfStock}
              addDetailsToCart={updateCustomizations}
              product={productPayload}
              customizationPrices={customizationPrices}
            />
          </Box>
        </Dialog>
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteItemModalVisible} //onClose={closeDeleteItemModal}
        sx={{
          "& .MuiPaper-root": {
            borderRadius: "20px",
            position: "fixed",
            paddingBottom: "15px",
            boxShadow: "0px -5px 20px rgba(0, 0, 0, 0.2)",
          },
        }}
      >
        <DialogTitle sx={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
          {t("Cart.Remove from Cart")}
          <CloseIcon sx={{fontSize: 24}} onClick={closeDeleteItemModal}/>
        </DialogTitle>
        <DialogContent>
          <Typography>{t("Cart.Are you sure you want to remove this item from your cart?")}</Typography>
        </DialogContent>
        <DialogActions sx={{padding: '24px', paddingTop: '0', paddingBottom: '8px'}}>
          <Button sx={{borderRadius: '10px'}} fullWidth onClick={closeDeleteItemModal} variant="outlined"
                  color="primary">
            {t('WishList.No')}
          </Button>
          <Button sx={{borderRadius: '10px'}} fullWidth onClick={deleteCartItem} color="primary" variant="contained">
            {itemToDelete ? <CircularProgress size={20}/> : t('WishList.Yes')}
          </Button>
        </DialogActions>
      </Dialog>

      {productModalDetails && (
        <ModalProductDetails closeModal={() => setProductModalDetails(null)} wishlistProductId={''} wishlistId={''}
                             qty={productModalDetails.qty} isOpen={productModalDetails.isOpen}
                             inStock={productModalDetails.inStock}/>
      )}
    </>
  );
};

export default CartItems;
