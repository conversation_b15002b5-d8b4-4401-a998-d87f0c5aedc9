import React from "react";
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import useFormatNumber from "../../../../hooks/useFormatNumber";
import "./TotalSummary.css";
import Box from "@mui/material/Box"; // Importing CSS styles

interface TotalSummaryProps {
  cartTotal: string;
  cartLength: number;
}

const TotalSummary: React.FC<TotalSummaryProps> = ({cartTotal, cartLength}) => {
  const {t} = useTranslation();
  const {formatNumber} = useFormatNumber();

  return (
    <Box>
      <Typography variant="titleLarge" className="cart-total">
        ₹{formatNumber(cartTotal)}
      </Typography>
      <Typography variant="body1" className="cart-item-count">
        {cartLength === 1
          ? `${formatNumber(cartLength)} ${t("Cart.item")}`
          : `${formatNumber(cartLength)} ${t("Cart.items")}`}
      </Typography>
    </Box>
  );
};

export default TotalSummary;
