import { Box, Button, Modal, styled, Typography, useTheme } from "@mui/material"
import React, { useEffect } from "react"
import { useTranslation } from "react-i18next"
import CouponIcon from "../../../../../../assets/coupon.svg"

interface CouponAppliedModalProps {
  visible: boolean
  discount?: number
  selectedCouponDetails: {
    offerId: string
    benefit: number
  }
  onConfirm: () => void
}

const AnimationContainer = styled(Box)({
  width: "100%",
  height: 400,
  position: "absolute",
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
})

const BottomAnimation = styled(Box)({
  transform: "rotate(180deg)",
})

const CouponContainer = styled(Box)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  padding: theme.spacing(2.5),
  margin: "0 15%",
  display: "flex",
  flexDirection: "column",
  gap: theme.spacing(2),
  alignItems: "center",
  borderRadius: 15,
  position: "relative",
  zIndex: 1,
}))

const CouponAppliedModal: React.FC<CouponAppliedModalProps> = ({
  discount = 0,
  visible,
  selectedCouponDetails,
  onConfirm,
}) => {
  const { t } = useTranslation()
  const theme = useTheme()

  useEffect(() => {
    let timeoutId: any

    if (visible) {
      timeoutId = setTimeout(() => {
        if (visible) {
          onConfirm()
        }
      }, 5000)
    }

    return () => clearTimeout(timeoutId)
  }, [visible])

  return (
    <Modal open={visible} onClose={onConfirm}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "100%",
        }}
      >
        <AnimationContainer>
          <img
            src={require("../../../../../../assets/coupon/couponAppliedPopup.gif")}
            alt="DigiHaat"
            width="300"
            height="300"
          />

          <BottomAnimation>
            <img
              src={require("../../../../../../assets/coupon/couponAppliedPopup.gif")}
              alt="DigiHaat"
              width="300"
              height="300"
            />
          </BottomAnimation>
        </AnimationContainer>

        <CouponContainer>
          <img src={CouponIcon} alt="Coupon" height={48} width={48} />
          <Typography variant="bodyLarge">
            {`'${selectedCouponDetails.offerId}' `}
            {t("Coupon.Applied")}
          </Typography>
          <Typography
            variant="labelLarge"
            sx={{
              color: "success.main",
              fontSize: 22,
              lineHeight: "25px",
            }}
          >
            {t("Coupon.You saved")} ₹{discount.toFixed(2)}
          </Typography>
          <Typography
            variant="titleSmall"
            sx={{
              fontSize: 11,
              alignSelf: "center",
            }}
          >
            {t("Coupon.with this coupon code")}
          </Typography>
          <Button
            variant="text"
            onClick={onConfirm}
            sx={{
              py: 0.75,
              px: 2.5,
              color: "primary.main",
            }}
          >
            <Typography variant="titleSmall">
              {t("Coupon.Continue with Savings")}
            </Typography>
          </Button>
        </CouponContainer>
      </Box>
    </Modal>
  )
}

export default CouponAppliedModal
