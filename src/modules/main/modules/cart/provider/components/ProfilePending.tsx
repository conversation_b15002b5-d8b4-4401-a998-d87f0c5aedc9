import React, {useCallback, useState} from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import "./ProfilePending.css";
import UpdateProfile from "../../../profile/UpdateProfile";

const ProfilePending: React.FC = () => {
  const {t} = useTranslation();
  const [openProfile, setOpenProfile] = useState(false);

  const handleCloseProfileModal = () => setOpenProfile(false);

  const navigateToProfile = useCallback(() => {
    setOpenProfile(true);
  }, []);

  return (
    <>
      <Box className="profile-pending-container">
        <Typography variant="bodyLarge" component="div" className="profile-title">
          {t("Cart.Almost There")}
        </Typography>
        <Typography variant="labelMedium" component="div" className="profile-message">
          {t("Cart.Update your profile quickly to place order")}
        </Typography>
        <Divider className="profile-divider"/>
        <Button
          variant="contained"
          color="primary"
          className="profile-button"
          onClick={navigateToProfile}
        >
          {t("Cart.Update Your Profile")}
        </Button>
      </Box>
      <UpdateProfile updateName={false} open={openProfile} handleClose={handleCloseProfileModal}/>
    </>
  );
};

export default ProfilePending;
