import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import Typography from '@mui/material/Typography';
import React, {useMemo} from "react"
import {useTranslation} from "react-i18next"
import {styled} from "@mui/system"
import useFormatNumber from "../../../../hooks/useFormatNumber"
import DashedLine from "./DashedLine"

const BillModalContainer = styled(Box)(({theme}) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
  width: "80%",
  maxWidth: 400,
  maxHeight: "80dvh",
  overflow: "auto",
  margin: "auto",
}))

const BillTitleContainer = styled(Box)({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 8,
})

const BillContentTextContainer = styled(Box)({
  display: "flex",
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  gap: 8,
})

const SummaryRow = styled(Box)({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
})

const SectionContainer = styled(Box)({
  display: "flex",
  justifyContent: "flex-end",
})

const Items = ({
                 itemsList,
                 setModalDetails,
               }: {
  itemsList: any[]
  setModalDetails: (value: any) => void
}) => {
  const {t} = useTranslation()
  const {formatNumber} = useFormatNumber()

  const openMenu = (total: number) => {
    setModalDetails({
      isVisible: true,
      children: (
        <BillModalContainer>
          <BillTitleContainer>
            <Typography variant="titleLarge">{t("Fulfillment.Item Total")}</Typography>
            <Typography variant="titleLarge">
              ₹{formatNumber(total?.toFixed(2))}
            </Typography>
          </BillTitleContainer>

          <Divider sx={{my: 1}}/>
          <BillContentTextContainer>
            <Typography variant="labelLarge" sx={{flex: 2}}>
              {t("Fulfillment.Item")}
            </Typography>
            <Typography
              variant="labelLarge"
              sx={{flex: 1}}
            >
              {t("Fulfillment.price")}
            </Typography>
            <Typography
              variant="labelLarge"
              sx={{flex: 1}}
            >
              {t("Fulfillment.Discount")}
            </Typography>
            <Typography
              variant="labelLarge"
              sx={{flex: 1, textAlign: "right"}}
            >
              {t("Fulfillment.Total")}
            </Typography>
          </BillContentTextContainer>
          <List>
            {itemsList.map((item, index) => {
              const discount =
                item.sellerDiscount +
                (item.hasOwnProperty("discount") ? item?.discount : 0)
              const itemCost = item?.basePriceWithoutTax
                ? item?.basePriceWithoutTax
                : item?.basePrice

              return (
                <React.Fragment key={item.name}>
                  <ListItem sx={{px: 0}}>
                    <BillContentTextContainer sx={{width: "100%"}}>
                      <Typography variant="labelSmall" sx={{flex: 2}}>
                        {item.name}
                      </Typography>
                      <Typography
                        variant="labelSmall"
                        sx={{flex: 1}}
                      >
                        ₹{formatNumber(itemCost?.toFixed(2))}
                      </Typography>
                      <Typography
                        variant="labelSmall"
                        sx={{
                          flex: 1,
                          color: discount > 0 ? "success.main" : "inherit",
                        }}
                      >
                        -₹{formatNumber(discount.toFixed(2))}
                      </Typography>
                      <Typography
                        variant="labelSmall"
                        sx={{flex: 1, textAlign: "right"}}
                      >
                        ₹{formatNumber((itemCost - discount)?.toFixed(2))}
                      </Typography>
                    </BillContentTextContainer>
                  </ListItem>
                  {index < itemsList.length - 1 && <DashedLine height={1}/>}
                </React.Fragment>
              )
            })}
          </List>
        </BillModalContainer>
      ),
    })
  }

  const total = useMemo(() => {
    let itemsTotal = 0
    itemsList?.forEach((item) => {
      const itemCost = item?.basePriceWithoutTax
        ? item?.basePriceWithoutTax
        : item?.basePrice
      itemsTotal += itemCost
    })
    return itemsTotal
  }, [itemsList])

  return (
    <SummaryRow>
      <Button
        onClick={() => openMenu(total)}
        sx={{textTransform: "none", padding: 0, flexDirection: "column"}}
      >
        <Typography variant="labelSmall" color="black">
          {t("Fulfillment.Items Total")}
        </Typography>
        <DashedLine height={1} primary={true}/>
      </Button>
      <SectionContainer>
        <Typography
          variant="labelSmall"
          sx={{color: total <= 0 ? "success.main" : "inherit"}}
        >
          ₹{formatNumber(total?.toFixed(2))}
        </Typography>
      </SectionContainer>
    </SummaryRow>
  )
}

export default Items
