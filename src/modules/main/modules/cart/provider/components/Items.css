.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-container {
    display: flex;
    align-items: center;
    gap: 4px;
}

.info-button {
    cursor: pointer;
    color: #9e9e9e !important;

}

.menu-content {
    padding: 12px;
    width: 300px;
}

.menu-title {
    color: #424242;
}

.item-row {
    display: flex;
    justify-content: space-between;
    gap: 8px;
    padding: 8px 0;
}

.header-row {
    font-weight: bold;
}

.item-column {
    width: 80px;
    color: #757575;
}

.positive {
    color: #008817;
}

.menu-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}
