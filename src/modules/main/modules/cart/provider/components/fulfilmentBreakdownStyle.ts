export const makeStyles = () => ({
  summaryRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  detailRow: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: "32px",
  },
  itemRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    gap: "8px",
  },
  sectionContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: "4px",
  },
  subTotal: {
    color: "#757575", // Adjust color to match neutral400
  },
  itemColumn: {
    color: "#757575",
    width: "64px",
  },
  infoButton: {
    width: "16px",
    height: "16px",
  },
  menu: {
    marginLeft: "20px",
  },
  menuContent: {
    padding: "8px 12px",
    gap: "4px",
    maxWidth: "280px",
  },
  title: {
    color: "#757575",
  },
  positive: {
    color: "#008817",
  },
  discountContainer: {
    borderRadius: "4px",
    backgroundColor: "#C1DEF278",
    padding: "5px 9px",
  },
  amountStrike: {
    color: "#BDBDBD",
    textDecoration: "line-through",
  },
});
