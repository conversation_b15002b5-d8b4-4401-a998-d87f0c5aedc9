import {styled} from "@mui/material"
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import {useMemo, useRef, useState} from "react"
import {useTranslation} from "react-i18next"
import "moment-duration-format"
import {KeyboardArrowLeft, KeyboardArrowRight, LocationOn,} from "@mui/icons-material"

import NoImageAvailable from "../../../../../../assets/noImage.png"
import useFormatNumber from "../../../../hooks/useFormatNumber"

import Image from "../../../../components/image/Image"
import SingleFulfilment from "./SingleFulfilment"

import DPANA from "../../../../../../assets/DPANA.svg"
import userSquare from "../../../../../../assets/userSquare.svg"
import {useTheme} from "@mui/material/styles"

const StyledPaper = styled(Paper)(({theme}) => ({
  borderRadius: 12,
  border: `1px solid ${theme.palette.divider}`,
  overflow: "hidden",
}))

const ProductDivider = styled(Divider)(({theme}) => ({
  borderStyle: "dashed",
  margin: "10px 0",
}))

const DeliveryDivider = styled(Divider)(({theme}) => ({
  margin: "15px 0",
}))

const FulfillmentSelection = ({
                                uniqueFulfilment,
                                quote,
                                cartItems,
                                items,
                                selectedFulfillmentList,
                                setSelectedItems,
                                setSelectedFulfillmentList,
                                requestCharge,
                                disableSelection,
                                deliveryAddress,
                              }: {
  uniqueFulfilment: any
  quote: any
  cartItems: any[]
  selectedFulfillmentList: any[]
  setSelectedItems: (value: any[]) => void
  setSelectedFulfillmentList: (value: any[]) => void
  requestCharge: (fulfilmentId: any) => void
  items: any
  disableSelection: boolean
  deliveryAddress: any
}) => {
  const {formatNumber} = useFormatNumber()
  const {t} = useTranslation()
  const theme = useTheme()
  const pagerRef = useRef<any>()
  const [page, setPage] = useState<number>(0)

  const showPreviousFulfilment = () => {
    const nextPage = page - 1
    setPage(nextPage)
    pagerRef.current?.setPage(nextPage)
  }

  const showNextFulfilment = () => {
    const nextPage = page + 1
    setPage(nextPage)
    pagerRef.current?.setPage(nextPage)
  }

  const {updateUniqueFulfilmentList} = useMemo(() => {
    let maxFulfilmentCount = 1
    const list = uniqueFulfilment.map((fulfillmentId: any) => {
      const filteredProducts = quote?.items?.filter((item: any) => {
        let isItem = false
        if (item.hasOwnProperty("tags") && item.tags.length > 0) {
          const findTag = item?.tags.find((tag: any) => tag.code === "type")
          if (findTag) {
            isItem =
              findTag.list.findIndex(
                (listItem: any) => listItem.value === "item"
              ) > -1
          }
        } else {
          isItem = true
        }
        return isItem && item?.fulfillment_id === fulfillmentId
      })
      const provider = items[0]?.item?.provider
      const fulfilmentList: any[] = quote?.fulfillments.filter(
        (fulfillment: any) =>
          fulfillment.id === fulfillmentId ||
          !fulfillment.id.includes(fulfillmentId)
      )

      const fulfilmentLength = fulfilmentList.length
      if (maxFulfilmentCount < fulfilmentLength) {
        maxFulfilmentCount = fulfilmentLength
      }

      return {filteredProducts, fulfilmentList, fulfillmentId, provider}
    })
    const actualHeight = maxFulfilmentCount * 40 + 270
    //const maxAllowedHeight = screenHeight - 480
    const maxAllowedHeight = 480
    return {
      updateUniqueFulfilmentList: list,
      height: actualHeight > maxAllowedHeight ? maxAllowedHeight : actualHeight,
    }
  }, [uniqueFulfilment, quote])

  const receiverDetails =
    deliveryAddress?.descriptor?.name +
    ", +91-" +
    deliveryAddress?.descriptor?.phone

  return (
    <>
      {uniqueFulfilment?.length > 0 &&
        updateUniqueFulfilmentList.map(
          (
            {fulfillmentId, filteredProducts, fulfilmentList, provider}: any,
            index: number
          ) => {
            let fulfillmentTotal = 0
            const formattedAddress = `${deliveryAddress?.address?.building}, ${
              deliveryAddress?.address?.street
            }, ${
              deliveryAddress?.address?.landmark
                ? `${deliveryAddress?.address?.landmark},`
                : ""
            } ${deliveryAddress?.address?.city}, ${
              deliveryAddress?.address?.state
            }, ${deliveryAddress?.address?.areaCode}`
            return (
              <Box key={fulfillmentId}>
                <Box sx={{px: 2}}>
                  <Box
                    sx={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      pt: 2,
                    }}
                  >
                    {uniqueFulfilment.length > 1 && (
                      <Box sx={{display: "flex", alignItems: "center"}}>
                        <IconButton onClick={showPreviousFulfilment}>
                          <KeyboardArrowLeft
                            color="primary"
                            fontSize="medium"
                          />
                        </IconButton>
                        <Box sx={{width: 20}}/>
                        <IconButton onClick={showNextFulfilment}>
                          <KeyboardArrowRight
                            color="primary"
                            fontSize="medium"
                          />
                        </IconButton>
                      </Box>
                    )}
                  </Box>

                  <StyledPaper>
                    <Box
                      sx={{
                        display: "flex",
                        p: 1.25,
                        backgroundColor: "#EBF2F8",
                      }}
                    >
                      <img
                        src={provider?.descriptor?.images[0]}
                        width={48}
                        height={48}
                        alt={provider?.descriptor?.name}/>
                      <Box sx={{ml: 1.25, display: 'block'}}>
                        <Typography variant="titleLarge" component="div" noWrap>
                          {provider?.descriptor.name}
                        </Typography>
                        <Typography variant="labelSmall" component="div" color="textSecondary">
                          {provider?.locations[0]?.address?.locality}
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{px: 2.5, py: 1.875}}>
                      <Typography
                        variant="bodySmall"
                        color={theme.palette.neutral.light}
                      >
                        {t("Fulfillment.Total items")} -{" "}
                        {filteredProducts.length}
                      </Typography>
                      <ProductDivider/>

                      {filteredProducts?.map((item: any, index) => {
                        const singleProduct = items?.find(
                          (one: any) => one.item.product.id === item.id
                        )

                        let itemTotal =
                          singleProduct?.item?.product?.price?.value *
                          singleProduct?.item?.quantity?.count
                        singleProduct?.item?.customisations?.forEach(
                          (customization: any) => {
                            itemTotal +=
                              customization.quantity.count *
                              customization.item_details.price.value
                          }
                        )
                        fulfillmentTotal += itemTotal

                        let imageSource: any = NoImageAvailable
                        if (singleProduct?.item?.product?.descriptor?.symbol) {
                          imageSource = {
                            uri: singleProduct?.item?.product?.descriptor
                              ?.symbol,
                          }
                        } else if (
                          singleProduct?.item?.product?.descriptor?.images
                            ?.length > 0
                        ) {
                          imageSource = {
                            uri: singleProduct?.item?.product?.descriptor
                              ?.images[0],
                          }
                        }
                        return (
                          <Box key={`${item.id}${item.parent_item_id}`}>
                            <Box sx={{display: "flex", my: 1}}>
                              <Image
                                source={imageSource?.uri}
                                imageStyle="image"
                              />
                              <Box sx={{ml: 1.25, flex: 1}}>
                                <Typography
                                  variant="labelLarge"
                                  sx={{mb: 0.5}}
                                  component="div"
                                  noWrap
                                >
                                  {singleProduct?.item?.product?.descriptor?.name}
                                </Typography>
                                <Typography variant="labelSmall" component="div" color="black">
                                  {singleProduct?.item?.quantity?.count}{" "}
                                  {
                                    singleProduct?.item?.product?.quantity
                                      ?.unitized?.measure?.unit
                                  }
                                </Typography>
                              </Box>
                              <Typography variant="labelSmall" component="div">
                                ₹
                                {formatNumber(
                                  singleProduct?.item?.product?.price?.value.toFixed(
                                    2
                                  )
                                )}
                              </Typography>
                            </Box>
                            {index !== filteredProducts.length - 1 && (
                              <ProductDivider/>
                            )}
                          </Box>
                        )
                      })}
                    </Box>
                  </StyledPaper>

                  {uniqueFulfilment.length > 1 && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        py: 2,
                      }}
                    >
                      <Typography variant="body1">
                        {t("Fulfillment.Items Total")}
                      </Typography>
                      <Typography variant="body1">
                        ₹{formatNumber(fulfillmentTotal.toFixed(2))}
                      </Typography>
                    </Box>
                  )}
                </Box>

                <Box
                  sx={{height: 4, backgroundColor: theme.palette.grey[100]}}
                />

                <Box sx={{p: 2}}>
                  <StyledPaper>
                    <Box sx={{py: 1.875}}>
                      {fulfilmentList.length > 1 && (
                        <Box
                          sx={{
                            px: 1.875,
                            display: "flex",
                            alignItems: "center",
                          }}
                        >
                          <img src={DPANA} width={24} height={24}/>
                          <Typography variant="bodySmall" sx={{ml: 1.25}}>
                            {t("Fulfillment.Delivery")}{" "}
                            <Typography component="span" variant="body1">
                              {t("Fulfillment.Method")}
                            </Typography>
                          </Typography>
                        </Box>
                      )}

                      {fulfilmentList.map((fulfilment: any) => (
                        <SingleFulfilment
                          key={`${fulfilment.id}${fulfilment.type}`}
                          cartItems={cartItems}
                          fulfilment={fulfilment}
                          fulfilmentList={fulfilmentList}
                          selectedFulfillmentList={selectedFulfillmentList}
                          setSelectedItems={setSelectedItems}
                          setSelectedFulfillmentList={
                            setSelectedFulfillmentList
                          }
                          requestCharge={requestCharge}
                          disableSelection={disableSelection}
                          deliveryAddress={deliveryAddress}
                        />
                      ))}

                      <DeliveryDivider/>

                      <Box
                        sx={{
                          px: 1.875,
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <LocationOn color="primary" fontSize="medium"/>
                        <Typography variant="bodySmall" sx={{ml: 1.25}}>
                          {t("Fulfillment.Delivery")}{" "}
                          <Typography component="span" variant="body1">
                            {t("Fulfillment.Address")}
                          </Typography>
                        </Typography>
                      </Box>

                      <Box sx={{px: 1.875, ml: 4.375}}>
                        <Typography variant="bodySmall">
                          {formattedAddress}
                        </Typography>
                      </Box>

                      <DeliveryDivider/>

                      <Box
                        sx={{
                          px: 1.875,
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <img src={userSquare} width={24} height={24}/>
                        <Typography variant="bodySmall" sx={{ml: 1.25}}>
                          {t("Fulfillment.Receiver")}
                          <Typography component="span" variant="bodyMedium">
                            {" "}
                            {t("Fulfillment.Details")}
                          </Typography>
                        </Typography>
                      </Box>

                      <Box sx={{px: 1.875, ml: 4.375}}>
                        <Typography variant="bodySmall">
                          {receiverDetails}
                        </Typography>
                      </Box>
                    </Box>
                  </StyledPaper>
                </Box>
              </Box>
            )
          }
        )}
    </>
  )
}

export default FulfillmentSelection
