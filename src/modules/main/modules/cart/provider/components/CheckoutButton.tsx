import React from "react";
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import useActionButtonClick from "../../../../../../hooks/useActionButtonClick";
import "./CartSummary.css";

interface CartSummaryProps {
  disableDeliveredTo: boolean;
  checkOut: () => void;
}

const CartSummary: React.FC<CartSummaryProps> = ({
                                                   disableDeliveredTo,
                                                   checkOut,
                                                 }) => {
  const {t} = useTranslation();
  const onButtonPressAction = useActionButtonClick();

  return (
    <Button
      className={`delivery-button ${disableDeliveredTo ? "disabled" : ""}`}
      disabled={disableDeliveredTo}
      onClick={() => onButtonPressAction(checkOut)}
      variant="contained"
    >
      <Typography component="span" variant="body1">{t("Cart.View Delivery Options")}</Typography>
    </Button>
  );
};

export default CartSummary;
