import React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {useTranslation} from 'react-i18next';

interface AddMissingItemsProps {
  navigateToHome: () => void;
}

const AddMissingItems: React.FC<AddMissingItemsProps> = ({navigateToHome}) => {
  const {t} = useTranslation();

  return (
    <Box
      display="flex"
      justifyContent="center"
      alignItems="center"
      flexDirection="row"
      border={1}
      borderColor="grey.300"
      borderRadius={2}
      height={62}
      gap={1}
      mx={2}
      my={1.5}
    >
      <Typography variant="body1" color="textPrimary">
        {t('Cart.Missing something?')}
      </Typography>
      <Button onClick={navigateToHome} color="primary">
        {t('Cart.Add More Items')}
      </Button>
    </Box>
  );
};

export default AddMissingItems;
