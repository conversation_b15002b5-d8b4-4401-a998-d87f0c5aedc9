import React from "react";
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import {getPriceWithCustomisations} from "../../../../../../utils/utils";
import useFormatNumber from "../../../../hooks/useFormatNumber";
import {CURRENCY_SYMBOLS} from "../../../../../../utils/constants";
import "./ItemTotal.css";
import Box from "@mui/material/Box";

interface ItemTotalProps {
  cartItem: any;
}

const ItemTotal: React.FC<ItemTotalProps> = ({cartItem}) => {
  const {t} = useTranslation();
  const {formatNumber} = useFormatNumber();

  const maxValue = Number(cartItem?.item?.product?.price?.maximum_value);
  const quantity = Number(cartItem?.item?.quantity?.count);

  let percentage = 0;
  if (maxValue !== 0 && maxValue !== Number(cartItem?.item?.product?.price?.value)) {
    percentage = 100 - Math.floor((100 * cartItem?.item?.product?.price?.value) / maxValue);
  }

  const cartAmount = cartItem.item.hasCustomisations
    ? formatNumber(Number(getPriceWithCustomisations(cartItem) * quantity).toFixed(2))
    : formatNumber(Number(cartItem?.item?.product?.subtotal * quantity).toFixed(2));

  const currency = CURRENCY_SYMBOLS[cartItem?.item?.product?.price?.currency];
  const maxProductPrice = maxValue * quantity;

  return (
    <Box className="item-total-container">
      <Typography variant="body1" className="amount">
        {currency}
        {cartAmount}
      </Typography>
      {percentage !== 0 && (
        <Box className="item-total-container">
          <Typography variant="labelSmall" className="amount-strike">
            {currency}
            {formatNumber(Number(maxProductPrice).toFixed(2))}
          </Typography>
          <Typography variant="body1" className="discount">
            {t("Product.discount", {discount: percentage})}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ItemTotal;
