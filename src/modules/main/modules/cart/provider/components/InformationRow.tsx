import React from "react"
import Typography from "@mui/material/Typography"
import "./InformationRow.css"
import Box from "@mui/material/Box"

interface InformationRowProps {
  label: string
  value: number
}

const InformationRow: React.FC<InformationRowProps> = ({label, value}) => {
  if (value > 0) {
    return (
      <Box className="detail-row flex">
        <Typography variant="labelSmall" className="sub-total">
          {label}
        </Typography>
        <Typography variant="labelSmall" className="sub-total">
          ₹{value.toFixed(2)}
        </Typography>
      </Box>
    )
  }

  return null
}

export default InformationRow
