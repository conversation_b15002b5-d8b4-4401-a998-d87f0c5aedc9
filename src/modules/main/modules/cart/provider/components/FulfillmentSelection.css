.scroll-container {
    overflow-y: auto;
}

.fulfillment-container {
    display: flex;
    flex-direction: column;
}

.fulfillment-wrapper {
    padding: 16px;
}

.fulfillment-summary {
    padding: 16px;
    background: white;
    border-radius: 8px;
}

.fulfillment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.fulfillment-count {
    font-weight: bold;
}

.item-count {
    color: gray;
}

.nav-buttons {
    display: flex;
    gap: 8px;
}

.product-list {
    display: flex;
    overflow-x: auto;
    gap: 12px;
    padding: 12px 0;
}

.product-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.product-image {
    width: 44px;
    height: 44px;
    border-radius: 8px;
}

.product-name {
    margin-top: 4px;
    width: 85px;
    text-align: center;
}

.product-quantity {
    color: gray;
}

.list-container {
    overflow: scroll;
    height: 100dvh;
}

.delivery-method {
    font-weight: bold;
}
