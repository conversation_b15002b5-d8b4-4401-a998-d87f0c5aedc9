import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {useTranslation} from 'react-i18next';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

const EmptyCart: React.FC = () => {
  const {t} = useTranslation();

  return (
    <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" height="100dvh">
      <ShoppingCartIcon style={{fontSize: 128, color: '#B0B0B0'}}/>
      <Typography variant="headlineSmall" sx={{mt: 2, color: 'text.secondary'}}>
        {t('Empty Cart.Your Cart is Empty')}
      </Typography>
      <Typography variant="labelSmall" sx={{mt: 1, textAlign: 'center', color: 'text.secondary'}}>
        {t('Empty Cart.It seems you haven’t added any products in your cart')}
      </Typography>
    </Box>
  );
};

export default EmptyCart;
