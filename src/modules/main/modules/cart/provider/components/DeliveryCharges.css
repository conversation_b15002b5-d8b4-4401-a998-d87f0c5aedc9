.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.section-container {
    display: flex;
    align-items: center;
    gap: 8px;
}

.amount-strike {
    text-decoration: line-through;
    color: #9e9e9e;
}

.positive {
    color: #008817;
}

.info-button {
    min-width: 0 !important;
    padding: 4px !important;
}

.info-icon {
    color: #9e9e9e;
}

.discount-container {
    background-color: #e8f5e9;
    padding: 4px 8px;
    border-radius: 4px;
}

.menu-content {
    padding: 16px;
}
