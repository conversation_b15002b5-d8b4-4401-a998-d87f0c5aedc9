.container {
    flex: 1;
    max-height: 400px;
    overflow-y: auto;
    padding: 20px;
    border: 1px solid #ddd;
    background: #f9f9f9;
}

.cart-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.cart-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    background: #fff;
}

.cart-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    object-fit: cover;
}

.cart-details {
    flex: 1;
    margin-left: 16px;
}

.cart-actions {
    display: flex;
    gap: 8px;
}

.dialog-container {
    padding: 16px;
}

.dialog-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 16px;
}

.quantityContainer {
    margin-top: 10px;
    margin-bottom: 10px;
}

.provider {
    background-color: #fff;
    padding: 0 16px;
}

.providerHeader {
    display: flex;
    height: 72px;
    flex-direction: row;
    align-items: center;
    gap: 12px;
}

.providerMeta {
    flex: 1;
    height: 42px;
    justify-content: space-between;
}

.providerImage {
    width: 48px;
    height: 48px;
    border-radius: 8px;
}

.providerName {
    color: #1A1A1A;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.providerAddress {
    color: #686868;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.productsContainer {
    padding: 16px 16px 0;
    background-color: #fff;
}

.product {
    display: flex;
    flex-direction: row;
    gap: 12px;
}

.productImage {
    width: 80px;
    height: 80px;
    border-radius: 8px;
}

.cartFlex {
    flex: 1;
}

.productMeta {
    display: flex;
    flex-direction: row;
}

.productActionContainer {
    display: flex;
    margin-top: 12px;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    margin-bottom: 16px;
}

.customiseContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
}

.errorBox {
    background-color: #FFEBEB;
    border-radius: 8px;
    padding: 8px;
    margin-top: 12px;
    margin-left: 16px;
    margin-right: 16px;
}

.errorText {
    color: #D83232;
}

.sheetContainer {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    background-color: #fff;
}

.gestureContainer {
    padding-bottom: 36px;
}

.nonGestureContainer {
    padding-bottom: 24px;
}

.header {
    padding: 12px 16px;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.sheetProductSymbol {
    width: 36px;
    height: 36px;
}

.titleContainer {
    margin-left: 8px;
}

.title {
    color: #1A1A1A;
    flex-shrink: 1;
}

.address {
    padding-bottom: 8px;
    padding-left: 16px;
    padding-right: 16px;
}

.customizationContainer {
    padding: 16px;
    background-color: #FAFAFA;
}
