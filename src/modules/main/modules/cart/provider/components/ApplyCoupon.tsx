import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos"
import CheckCircleIcon from "@mui/icons-material/CheckCircle"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import Typography from "@mui/material/Typography"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import CouponIcon from "../../../../../../assets/coupon.svg"
import { theme } from "../../../../../../utils/theme"
import { formatNumber } from "../../../../../../utils/utils"
import useCoupons from "../../../../hooks/useCoupons"
import useSelect from "../../../../hooks/useSelect"

const ApplyCoupon = ({
  cartTotal,
  cartId,
  deliveryAddress,
  items,
  selectedFulfilmentId,
  messageId,
  charges,
  bppId,
  domain,
  providerId,
  subcategory,
  appliedCoupon,
  setSelectedCouponDetails,
}: {
  cartTotal: string
  cartId: string
  deliveryAddress: any
  messageId: string
  items: any
  selectedFulfilmentId: any
  charges: any
  bppId: string
  domain: string
  providerId: string
  subcategory: string
  appliedCoupon: any
  setSelectedCouponDetails: any
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { setAddressAndCartId, onGetQuote } = useSelect()
  const [applyRequested, setApplyRequested] = useState<boolean>(false)
  const { getCoupons } = useCoupons()
  const [coupons, setCoupons] = useState([])
  const [applyingCoupon, setApplyingCoupon] = useState<boolean>(false)
  const [selectingCoupon, setSelectingCoupon] = useState<string>("")
  const [loadingCoupons, setLoadingCoupons] = useState<boolean>(true)

  useEffect(() => {
    setLoadingCoupons(true)
    getCoupons(
      bppId,
      domain,
      providerId,
      subcategory,
      false,
      "",
      charges?.applySubsidy ?? false,
      String(charges?.ONDC_FA ?? 0),
      String(charges?.mov),
      String(charges?.combinedDeliveryItemList?.deliveryCharge ?? 0),
      "true"
    )
      .then((list) => {
        if (appliedCoupon && appliedCoupon.hidden) {
          const index = list.findIndex(
            (one: any) =>
              one.offerId.toLowerCase() === appliedCoupon.offerId.toLowerCase()
          )
          if (index === -1) {
            list.unshift(appliedCoupon)
            setCoupons(list)
          } else {
            setCoupons(list)
          }
        } else {
          setCoupons(list)
        }
        setLoadingCoupons(false)
      })
      .catch(() => {
        setLoadingCoupons(false)
      })
  }, [])

  const isPresentInList = (query: string, list: any[]) => {
    return list?.find((coupon: any) => coupon.offerId.toLowerCase() === query)
  }

  const applySelectedCoupon = async (query: string) => {
    setApplyingCoupon(true)
    let availableCoupon = isPresentInList(query, coupons)
    if (!availableCoupon) {
      const hiddenList = await getCoupons(
        bppId,
        domain,
        providerId,
        subcategory,
        true,
        query,
        charges?.applySubsidy ?? false
      )
      availableCoupon = isPresentInList(query, hiddenList)
      if (!availableCoupon) {
        setApplyingCoupon(false)
        return false
      }
    }
    if (
      availableCoupon.offerQualifier.minValue &&
      Number(availableCoupon.offerQualifier.minValue) > Number(charges?.mov)
    ) {
      setApplyingCoupon(false)
      return false
    }

    setSelectingCoupon(availableCoupon.id)
    setAddressAndCartId(deliveryAddress, cartId)
    const benefitAvailable = Number(availableCoupon?.benefit ?? 0) > 0
    if (benefitAvailable) {
      setSelectedCouponDetails(availableCoupon)
    }
    try {
      await onGetQuote(messageId, items, selectedFulfilmentId, availableCoupon)
    } catch (error: any) {
      console.error(error)
    } finally {
      setSelectingCoupon("")
      setApplyingCoupon(false)
    }
  }

  const navigateToCouponList = () => {
    navigate("/coupons", {
      state: {
        sellerDiscount: charges?.ONDC_FA ?? 0,
        deliveryCharge: charges?.combinedDeliveryItemList?.deliveryCharge ?? 0,
        cartTotal,
        cartId,
        deliveryAddress,
        items,
        selectedFulfilmentId,
        messageId,
        bppId,
        domain,
        providerId,
        subcategory,
        mov: charges?.mov,
        applySubsidy: charges?.applySubsidy ?? false,
        appliedCoupon,
      },
    })
  }

  const removeCoupon = async () => {
    setAddressAndCartId(deliveryAddress, cartId)
    try {
      setApplyRequested(true)
      await onGetQuote(messageId, items, selectedFulfilmentId)
    } catch (error: any) {
      console.error(error)
    } finally {
      setApplyRequested(false)
    }
  }

  if (loadingCoupons) {
    return <></>
  }

  if (charges?.offer && Object.keys(charges?.offer).length > 0) {
    return (
      <Box
        p={2}
        border={1}
        borderRadius={2}
        borderColor="grey.300"
        sx={{ width: "100%" }}
        bgcolor={theme.palette.primary50}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" gap={1}>
            <CheckCircleIcon color="success" fontSize="small" />
            {charges?.offer?.benefit > 0 ? (
              <Typography
                variant="bodyMedium"
                sx={{ color: theme.palette.primary.main }}
              >
                {`${t("Fulfillment.Saved")} ₹${formatNumber(
                  Number(charges?.offer.benefit).toFixed(2)
                )} ${t("Fulfillment.with")} '${charges?.offer.coupon}'`}
              </Typography>
            ) : (
              <Typography
                variant="bodyMedium"
                sx={{ color: theme.palette.primary.main }}
              >
                {t("Coupon.Free delivery")}
              </Typography>
            )}
          </Box>
          {applyRequested ? (
            <CircularProgress size={16} />
          ) : (
            <Button
              color="error"
              onClick={removeCoupon}
              disabled={applyRequested}
            >
              {t("Coupon.remove")}
            </Button>
          )}
        </Box>
        <Button
          size="small"
          sx={{ textTransform: "none", color: theme.palette.neutral.main }}
          onClick={navigateToCouponList}
          endIcon={<ArrowForwardIosIcon fontSize="small" />}
        >
          {t("Coupon.View all coupons")}
        </Button>
      </Box>
    )
  } else if (coupons.length !== 0) {
    return (
      <Box
        p={2}
        border={1}
        borderRadius={2}
        borderColor="grey.300"
        bgcolor={theme.palette.primary50}
        sx={{
          width: "100%",
          mb: 2,
          gap: 1,
          display: "flex",
          flexDirection: "column",
        }}
      >
        {coupons.map((item: any) => (
          <Box key={String(item?.id)} display="flex" alignItems="center">
            <img src={CouponIcon} width="24" height="24" alt="CouponIcon" />
            <Typography
              variant="bodyMedium"
              sx={{ color: theme.palette.primary.main, ml: 1, flex: 1 }}
            >
              {`${t("Fulfillment.Save")} ₹${formatNumber(
                Number(item?.benefit).toFixed(2)
              )} ${t("Fulfillment.with")} '${item?.offerId}'`}
            </Typography>
            {applyingCoupon ? (
              <CircularProgress size={20} />
            ) : (
              <Button
                variant="contained"
                color="primary"
                disabled={selectingCoupon.length > 0}
                onClick={() => applySelectedCoupon(item?.offerId.toLowerCase())}
                size="small"
              >
                {t("Coupon.Apply")}
              </Button>
            )}
          </Box>
        ))}
        <Button
          size="small"
          sx={{
            textTransform: "none",
            color: theme.palette.neutral.main,
            alignSelf: "flex-start",
          }}
          onClick={navigateToCouponList}
          endIcon={<ArrowForwardIosIcon fontSize="small" />}
        >
          {t("Coupon.View all coupons")}
        </Button>
      </Box>
    )
  } else {
    return (
      <Box
        display="flex"
        alignItems="center"
        gap={2}
        onClick={navigateToCouponList}
        sx={{
          backgroundColor: theme.palette.primary50,
          width: "100%",
          borderWidth: 1,
          borderColor: "grey.300",
          borderStyle: "solid",
          borderRadius: 2,
          padding: 2,
          mb: 2,
          cursor: "pointer",
        }}
      >
        <img src={CouponIcon} width="24" height="24" alt="CouponIcon" />
        <Typography
          variant="bodyLarge"
          sx={{ flex: 1, color: theme.palette.primary.main }}
        >
          {t("Coupon.Apply Coupon")}
        </Typography>
        <ArrowForwardIosIcon fontSize="small" color="primary" />
      </Box>
    )
  }
}

export default ApplyCoupon
