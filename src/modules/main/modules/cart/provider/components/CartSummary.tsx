import React from 'react';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Paper from '@mui/material/Paper';
import DeliveryAddress from './DeliveryAddress';
import TotalSummary from './TotalSummary';
import CheckoutButton from './CheckoutButton';
import './CartSummary.css';

interface CartSummaryProps {
  formattedDeliveryAddress: string;
  deliverTo?: string;
  addressPresent: boolean;
  disabled: boolean;
  openAddressList: () => void;
  addNewAddress: () => void;
  cartTotal: string;
  cartLength: number;
  disableDeliveredTo: boolean;
  checkOut: () => void;
}

const CartSummary: React.FC<any> = ({
                                      formattedDeliveryAddress,
                                      deliverTo,
                                      addressPresent,
                                      disabled,
                                      openAddressList,
                                      addNewAddress,
                                      cartTotal,
                                      cartLength,
                                      disableDeliveredTo,
                                      checkOut,
                                    }) => {
  return (
    <Box className="cart-summary-container">
      <Paper className="summary-card" elevation={3}>
        <DeliveryAddress
          formattedDeliveryAddress={formattedDeliveryAddress}
          deliverTo={deliverTo}
          addressPresent={addressPresent}
          disabled={disabled}
          openAddressList={openAddressList}
          addNewAddress={addNewAddress}
        />
        <Divider className="summary-divider"/>
        <Box className="delivery-row">
          <TotalSummary cartTotal={cartTotal} cartLength={cartLength}/>
          <CheckoutButton disableDeliveredTo={disableDeliveredTo} checkOut={checkOut}/>
        </Box>
      </Paper>
    </Box>
  );
};

export default CartSummary;
