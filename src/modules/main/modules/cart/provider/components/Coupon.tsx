import React, {useMemo, useState} from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import {useTranslation} from "react-i18next";
import CouponDetails from "./CouponDetails";
import CouponIcon from "../../../../../../assets/coupon.svg";
import DisabledCouponIcon from "../../../../../../assets/coupon_disabled.svg";
import {checkIfFloat} from "../../../../../../utils/utils";
import "./Coupon.css";

const Coupon: React.FC<any> = ({
                                 coupon,
                                 cartTotal,
                                 applySelectedCoupon,
                                 selectingCoupon,
                                 appliedCoupon,
                               }) => {
  const {t} = useTranslation();
  const [detailsVisible, setDetailsVisible] = useState(false);

  const toggleDetailsView = () => {
    setDetailsVisible(!detailsVisible);
  };

  const {enabled, remainingValue} = useMemo(() => {
    if (coupon?.offerQualifier?.minValue) {
      const minValue = Number(coupon?.offerQualifier?.minValue);
      const total = Number(cartTotal);
      return {
        enabled: total >= minValue,
        remainingValue: minValue - total,
      };
    } else {
      return {enabled: true, remainingValue: 0};
    }
  }, [cartTotal, coupon]);

  const getDescription = () => {
    if (coupon.type === "discount") {
      if (coupon.offerBenefit.valueType === "percentage") {
        return t("Coupon.Percent message", {
          percent: coupon.offerBenefit.value,
          maximum: coupon.offerBenefit.valueCap,
        });
      } else if (coupon.offerBenefit.valueType === "amount") {
        return t("Coupon.Amount message", {
          amount: coupon.offerBenefit.value,
        });
      }
    } else {
      if (coupon.offerBenefit.valueType === "percentage") {
        return coupon.offerBenefit.value === 100
          ? t("Coupon.Free Delivery")
          : t("Coupon.Percent delivery message", {
            percent: coupon.offerBenefit.value,
            maximum: coupon.offerBenefit.valueCap,
          });
      } else if (coupon.offerBenefit.valueType === "amount") {
        return t("Coupon.Amount delivery message", {
          amount: coupon.offerBenefit.value,
        });
      }
    }
    return "";
  };

  const applyCoupon = () => {
    applySelectedCoupon(coupon.offerId.toLowerCase());
  };

  const currentlySelected = selectingCoupon === coupon?.id;

  return (
    <Box className="coupon-container">
      <Box className="coupon-header">
        <img
          src={enabled ? CouponIcon : DisabledCouponIcon}
          width="24"
          height="24"
          alt="Coupon Icon"
        />
        <Box className="coupon-details">
          <Typography variant="titleLarge" component="div" color="text.primary">
            {coupon.shortDescription}
          </Typography>
          {enabled ? (
            <Typography variant="labelSmall" component="div" color="primary">
              {getDescription()}
            </Typography>
          ) : (
            <Typography variant="labelSmall" color="error">
              Add items worth more to unlock {
              checkIfFloat(remainingValue)
                ? remainingValue.toFixed(2)
                : remainingValue
            }
            </Typography>
          )}
          <Box className="coupon-meta">
            <Typography variant="labelLarge" className="coupon-code">{coupon?.offerId}</Typography>
            <Button
              size="small"
              onClick={toggleDetailsView}
              endIcon={detailsVisible ? <ExpandLessIcon/> : <ExpandMoreIcon/>}>
              {detailsVisible ? "Hide Details" : "View Details"}
            </Button>
          </Box>
        </Box>
      </Box>
      <CouponDetails coupon={coupon} detailsVisible={detailsVisible}/>
      {appliedCoupon?.offerId === coupon.offerId ? (
        <Box className="coupon-applied">
          <Typography variant="body1" color="success.main">
            Applied
          </Typography>
        </Box>
      ) : enabled && coupon?.allowedUsage && coupon?.status && selectingCoupon?.length === 0 ? (
        <Box onClick={applyCoupon} className="coupon-button">
          <Typography variant="body1" color="primary" ml={1}>
            Tap to Apply
          </Typography>
        </Box>
      ) : (
        <Box className="coupon-inactive">
          {currentlySelected && <CircularProgress size={16} color="primary"/>}
          <Typography variant="body1" color="text.secondary" ml={1}>
            Tap to Apply
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default Coupon;
