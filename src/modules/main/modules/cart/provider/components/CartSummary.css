.cart-summary-container {
    background-color: #f9f9f9;
}

.summary-card {
    border-radius: 16px;
    padding: 16px;
    background-color: white;
    box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.summary-divider {
    margin: 16px 0 !important;
}

.delivery-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 15px;
    padding-top: 8px;
}


/* Below Checkout Button CSS */
.delivery-button {
    border-radius: 8px;
    background-color: #1976d2;
    height: 48px;
    width: 230px;
    color: white;
    text-align: center;
    font-weight: bold;
    transition: background-color 0.3s;
}

.delivery-button:hover {
    background-color: #115293; /* Darker shade for hover effect */
}

.disabled {
    background-color: #e0e0e0 !important;
    color: #9e9e9e !important;
    cursor: not-allowed !important;
}
