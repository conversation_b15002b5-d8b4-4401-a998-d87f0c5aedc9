import ArrowBackIcon from "@mui/icons-material/ArrowBack"
import {useTheme} from "@mui/material"
import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import React from "react"
import {useNavigate} from "react-router-dom"
import "./Header.css"

interface HeaderProps {
  label: string
}

const Header: React.FC<HeaderProps> = ({label}) => {
  const navigate = useNavigate()
  const theme = useTheme()
  const goBack = () => {
    navigate(-1) // Navigates back one step in history
  }

  return (
    <Box className="page-header">
      <IconButton onClick={goBack}>
        <ArrowBackIcon className="back-icon"/>
      </IconButton>
      <Typography
        variant="titleLarge"
        color={theme.palette.primary.dark}
        className="page-title"
      >
        {label}
      </Typography>
    </Box>
  )
}

export default Header
