import {Box, Radio, styled, Typography} from "@mui/material"
import moment from "moment"
import {useTranslation} from "react-i18next"
import "moment-duration-format"
import {useMemo} from "react"
import useFormatDate from "../../../../hooks/useFormatDate"
import useFormatNumber from "../../../../hooks/useFormatNumber"
import {useTheme} from "@mui/material/styles"
import DPANA from "../../../../../../assets/DPANA.svg"

const StyledRadioContainer = styled(Box, {
  shouldForwardProp: (prop) =>
    prop !== "selected" && prop !== "multipleOptions",
})<{ selected?: boolean; multipleOptions?: boolean }>(
  ({theme, selected, multipleOptions}) => ({
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    padding: theme.spacing(0, 2),

    ...(multipleOptions && {
      border: `1px solid ${theme.palette.divider}`,

      borderRadius: 8,
      margin: theme.spacing(1, 3, 0, 6.25),
      padding: theme.spacing(0, 1.25),
      ...(selected && {
        backgroundColor: "#ECF3F8",
      }),
    }),
  })
)

const SingleFulfilment = ({
                            cartItems,
                            fulfilment,
                            fulfilmentList,
                            selectedFulfillmentList,
                            setSelectedItems,
                            setSelectedFulfillmentList,
                            requestCharge,
                            disableSelection,
                          }: {
  cartItems: any
  fulfilment: any
  fulfilmentList: any[]
  selectedFulfillmentList: any[]
  setSelectedItems: (value: any[]) => void
  setSelectedFulfillmentList: (value: any[]) => void
  requestCharge: (fulfilmentId: any) => void
  disableSelection: boolean
}) => {
  const theme = useTheme()
  const {t} = useTranslation()
  const {formatDate} = useFormatDate()
  const {formatNumber} = useFormatNumber()

  const fulfilmentPresent: boolean = selectedFulfillmentList.includes(
    fulfilment.id
  )

  const formatPickupOrDeliveryMessage = (
    expectedTime: moment.Moment,
    label: string,
    futureLabel: string,
    hourLabel: string,
    timeFormat: string,
    dayFormat: string
  ) => {
    const current = moment()
    if (expectedTime.isSame(current, "day")) {
      if (expectedTime.diff(current, "minute") > 60) {
        return {time: formatDate(expectedTime, timeFormat), label: label}
      } else {
        return {
          time: `${formatNumber(
            moment.duration(fulfilment["@ondc/org/TAT"]).locale('en').format("m")
          )} ${t("Fulfillment.minutes")}`,
          label: hourLabel,
        }
      }
    } else {
      return {time: formatDate(expectedTime, dayFormat), label: futureLabel}
    }
  }

  const tatMessage = useMemo(() => {
    if (!fulfilment.hasOwnProperty("@ondc/org/TAT")) {
      return {time: "", label: ""}
    }
    let expectedTime = moment().add(
      moment.duration(fulfilment["@ondc/org/TAT"])
    )

    const isTakeaway = fulfilment["@ondc/org/category"] === "Takeaway"
    let labelKey = "Fulfillment.Delivered Today by"
    let futureLabelKey = "Fulfillment.Delivered by"
    let hourKey = "Fulfillment.Delivered In"

    if (isTakeaway) {
      labelKey = "Fulfillment.Self pickup by"
      futureLabelKey = "Fulfillment.Self pickup by"
      hourKey = "Fulfillment.Self pickup by"
    }

    return formatPickupOrDeliveryMessage(
      expectedTime,
      labelKey,
      futureLabelKey,
      hourKey,
      "hh:mm a",
      "dddd D MMM"
    )
  }, [fulfilment])

  return (
    <StyledRadioContainer
      selected={fulfilmentPresent}
      multipleOptions={fulfilmentList.length > 1}
    >
      {fulfilmentList.length === 1 ? (
        <img src={DPANA} width={24} height={24}/>
      ) : (
        <Radio
          disabled={disableSelection}
          checked={fulfilmentPresent}
          onChange={() => {
            if (!fulfilmentPresent) {
              const list = selectedFulfillmentList.filter(
                (one) =>
                  fulfilmentList.findIndex((object) => object.id === one) < 0
              )
              setSelectedFulfillmentList(list.concat([fulfilment.id]))
              const updateItems = JSON.parse(JSON.stringify(cartItems))
              updateItems[0]?.message?.quote?.items?.forEach((item: any) => {
                item.fulfillment_id = fulfilment.id
              })
              setSelectedItems(updateItems)
            }
            requestCharge(fulfilment.id)
          }}
          color="primary"
        />
      )}
      <Typography
        variant="labelSmall"
        sx={{
          color:
            fulfilmentPresent && fulfilmentList.length > 1
              ? theme.palette.black
              : theme.palette.black,
          ml: fulfilmentList.length === 1 ? 1.25 : 0,
        }}
      >
        {t(tatMessage.label)}{" "}
        <Typography
          component="span"
          variant="body1"
          sx={{
            color:
              fulfilmentPresent && fulfilmentList.length > 1
                ? theme.palette.black
                : theme.palette.black,
          }}
        >
          {tatMessage.time}
        </Typography>
      </Typography>
    </StyledRadioContainer>
  )
}

export default SingleFulfilment
