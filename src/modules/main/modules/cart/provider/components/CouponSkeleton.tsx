import React from "react";
import Box from '@mui/material/Box';
import Skeleton from "@mui/material/Skeleton";
import "./CouponSkeleton.css";

const CouponSkeleton: React.FC = () => {
  return (
    <Box className="card">
      <Box className="meta">
        <Skeleton variant="circular" width={32} height={32}/>
        <Box>
          <Skeleton variant="text" className="name"/>
          <Skeleton variant="text" className="name"/>
          <Box className="code-container">
            <Skeleton variant="text" className="code"/>
            <Skeleton variant="text" className="code"/>
          </Box>
        </Box>
      </Box>
      <Box className="divider"/>
      <Skeleton variant="text" className="item"/>
      <Skeleton variant="text" className="item"/>
      <Skeleton variant="text" className="item"/>
      <Skeleton variant="text" className="item"/>
    </Box>
  );
};

export default CouponSkeleton;
