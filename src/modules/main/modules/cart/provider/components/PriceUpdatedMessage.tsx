import React from "react";
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import MoneyIcon from "../../../../../../assets/cart/money.svg";
import "./PriceUpdatedMessage.css";
import Box from "@mui/material/Box";

const PriceUpdatedMessage: React.FC = () => {
  const {t} = useTranslation();

  return (
    <Box className="price-updated-container">
      <img src={MoneyIcon} alt="Money Icon" className="money-icon"/>
      <Typography variant="body1" className="price-updated-text">
        {t("Fulfillment.Cart price updated")}
      </Typography>
    </Box>
  );
};

export default PriceUpdatedMessage;
