import React from "react"
import {styled} from "@mui/system"

interface DashedLineProps {
  width?: string | number
  dashLength?: number
  dashGap?: number
  height?: number
  primary?: boolean
}

const DashedDiv = styled("div")<DashedLineProps>(
  ({theme, width, height = 2, dashLength = 4, dashGap = 4, primary}) => ({
    width: width || "100%",
    height: height,
    borderBottom: `${height}px dashed ${
      primary ? theme.palette.primary.main : theme.palette.divider
    }`,
  })
)

const DashedLine = ({
                      width = "100%",
                      dashLength = 4,
                      dashGap = 4,
                      height = 2,
                      primary = false,
                    }: DashedLineProps) => {
  return (
    <DashedDiv
      width={width}
      height={height}
      dashLength={dashLength}
      dashGap={dashGap}
      primary={primary}
    />
  )
}

export default DashedLine
