import React, {useMemo} from "react"
import {useTranslation} from "react-i18next"
import Button from "@mui/material/Button";
import {styled} from "@mui/material/styles";
import Box from "@mui/material/Box";
import Divider from "@mui/material/Divider";
import Typography from "@mui/material/Typography";
import InformationRow from "./InformationRow"
import useFormatNumber from "../../../../hooks/useFormatNumber"
import DashedLine from "./DashedLine"

interface SellerFees {
  miscCharge?: number
  sellerDiscount?: number
  packingCharge?: number
}

interface DeliveryChargesProps {
  sellerFees: SellerFees;
  setModalDetails: (details: {
    isVisible: boolean
    children: React.ReactNode
  }) => void
}

interface CalculatedFees {
  miscCharge: number
  sellerDiscount: number
  packingCharge: number
  total: number
  positiveTotal: number
}

const BillModalContainer = styled(Box)(({theme}) => ({
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(2),
  width: "80%",
  maxWidth: 400,
  maxHeight: "80dvh",
  overflow: "auto",
  margin: "auto",
}))

const BillTitleContainer = styled(Box)({
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: 8,
})

const DeliveryCharges: React.FC<DeliveryChargesProps> = ({
                                                           sellerFees,
                                                           setModalDetails,
                                                         }) => {
  const {t} = useTranslation()
  const {formatNumber} = useFormatNumber()

  const sellerTotal = useMemo<CalculatedFees>(() => {
    const defaultFees = {
      miscCharge: 0,
      sellerDiscount: 0,
      packingCharge: 0,
    }

    const normalizedFees = {...defaultFees, ...sellerFees}

    let total = 0
    let positiveTotal = 0

    Object.entries(normalizedFees).forEach(([key, value]) => {
      const numValue = Number(value || 0)
      total += numValue
      if (numValue >= 0) {
        positiveTotal += numValue
      }
    })

    return {...normalizedFees, total, positiveTotal}
  }, [sellerFees])

  if (sellerTotal.total <= 0) {
    return null
  }

  const openMenu = () => {
    setModalDetails({
      isVisible: true,
      children: (
        <BillModalContainer>
          <BillTitleContainer>
            <Typography variant="titleLarge">
              {t("Fulfillment.Seller Fee")}
            </Typography>
            <Typography variant="titleLarge">₹{sellerTotal.total}</Typography>
          </BillTitleContainer>
          <Divider sx={{my: 1}}/>

          <InformationRow
            label={t("Fulfillment.Convenience Fee")}
            value={sellerTotal.miscCharge}
          />
          <InformationRow
            label={t("Fulfillment.Seller Discount")}
            value={sellerTotal.sellerDiscount}
          />
          <InformationRow
            label={t("Fulfillment.Packaging Charges")}
            value={sellerTotal.packingCharge}
          />
        </BillModalContainer>
      ),
    })
  }

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        py: 1,
      }}
    >
      <Button
        onClick={openMenu}
        sx={{
          textTransform: "none",
          p: 0,
          minWidth: "auto",
          flexDirection: "column",
        }}
      >
        <Typography variant="labelSmall" sx={{color: "black"}}>
          {t("Fulfillment.Seller Fee")}
        </Typography>
        <DashedLine height={1} primary={true}/>
      </Button>
      <Box sx={{textAlign: "right"}}>
        {sellerTotal.total < sellerTotal.positiveTotal && (
          <Typography
            variant="labelLarge"
            sx={{
              textDecoration: "line-through",
              color: "text.secondary",
            }}
          >
            ₹{formatNumber(sellerTotal.positiveTotal)}
          </Typography>
        )}
        <Typography
          variant="labelSmall"
          sx={{
            color: sellerTotal.total <= 0 ? "success.main" : "text.primary",
          }}
        >
          ₹{formatNumber(sellerTotal.total.toFixed(2))}
        </Typography>
      </Box>
    </Box>
  )
}

export default DeliveryCharges
