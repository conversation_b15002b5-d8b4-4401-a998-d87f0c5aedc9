import React, { useCallback } from "react"
import { useNavigate, useLocation } from "react-router-dom"
import { useTranslation } from "react-i18next"
import { useTheme } from "@mui/material/styles"
import { makeStyles } from "@mui/styles"
import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import Paper from "@mui/material/Paper"
import ArrowBackIcon from "@mui/icons-material/ArrowBack"
import FullscreenIcon from "@mui/icons-material/Fullscreen"
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit"
import ShareIcon from "@mui/icons-material/Share"

import RouteMap from "../components/RouteMap"

interface LocationState {
  fromLocation?: {
    lat: number
    lng: number
    name?: string
  }
  toLocation?: {
    lat: number
    lng: number
    name?: string
  }
  title?: string
}

const FullScreenRouteMap: React.FC = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()
  const theme = useTheme()
  const styles = useStyles()

  // Get route data from navigation state or use defaults
  const state = (location.state as LocationState) || {}
  const {
    fromLocation = {
      lat: 28.6139,
      lng: 77.209,
      name: "New Delhi Railway Station",
    },
    toLocation = {
      lat: 28.5355,
      lng: 77.391,
      name: "Noida Sector 18",
    },
    title = "Route Navigation",
  } = state

  const handleBack = useCallback(() => {
    navigate(-1)
  }, [navigate])

  const handleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }, [])

  const handleShare = useCallback(async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: `Route from ${fromLocation.name} to ${toLocation.name}`,
          url: window.location.href,
        })
      } catch (error) {
        console.log("Error sharing:", error)
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href)
        // You could show a toast notification here
        console.log("Link copied to clipboard")
      } catch (error) {
        console.log("Error copying to clipboard:", error)
      }
    }
  }, [title, fromLocation.name, toLocation.name])

  return (
    <Box className={styles.container}>
      {/* Header */}
      <Paper className={styles.header} elevation={2}>
        <Box className={styles.headerLeft}>
          <IconButton
            onClick={handleBack}
            className={styles.backButton}
            size="large"
          >
            <ArrowBackIcon />
          </IconButton>
          <Box className={styles.titleContainer}>
            <Typography variant="h6" className={styles.title}>
              {title}
            </Typography>
            <Typography variant="body2" className={styles.subtitle}>
              {fromLocation.name} → {toLocation.name}
            </Typography>
          </Box>
        </Box>

        <Box className={styles.headerActions}>
          <IconButton
            onClick={handleShare}
            className={styles.actionButton}
            title={t("Share Route")}
          >
            <ShareIcon />
          </IconButton>
          <IconButton
            onClick={handleFullscreen}
            className={styles.actionButton}
            title={t("Toggle Fullscreen")}
          >
            <FullscreenIcon />
          </IconButton>
        </Box>
      </Paper>

      {/* Full Screen Map */}
      <Box className={styles.mapContainer}>
        <RouteMap
          fromLocation={fromLocation}
          toLocation={toLocation}
          height="100%"
          showCloseButton={false}
        />
      </Box>
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    height: "100vh",
    width: "100vw",
    backgroundColor: theme.palette.background.default,
    overflow: "hidden",
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "12px 16px",
    backgroundColor: theme.palette.background.paper,
    borderRadius: 0,
    zIndex: 1000,
    minHeight: 64,
    boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
  },
  headerLeft: {
    display: "flex",
    alignItems: "center",
    flex: 1,
    minWidth: 0,
  },
  backButton: {
    marginRight: 8,
    color: theme.palette.text.primary,
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  titleContainer: {
    flex: 1,
    minWidth: 0,
  },
  title: {
    fontWeight: 600,
    color: theme.palette.text.primary,
    lineHeight: 1.2,
  },
  subtitle: {
    color: theme.palette.text.secondary,
    marginTop: 2,
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
  },
  headerActions: {
    display: "flex",
    alignItems: "center",
    gap: 4,
  },
  actionButton: {
    color: theme.palette.text.secondary,
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
      color: theme.palette.primary.main,
    },
  },
  mapContainer: {
    flex: 1,
    width: "100%",
    height: "calc(100vh - 64px)",
    position: "relative",
    overflow: "hidden",
  },
}))

export default FullScreenRouteMap
