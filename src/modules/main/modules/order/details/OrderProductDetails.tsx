import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import ArrowBackIcon from "@mui/icons-material/ArrowBackRounded";
import {useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {useParams} from "react-router-dom";
import ProviderDetails from "./components/ProviderDetails";
import {CURRENCY_SYMBOLS} from "../../../../../utils/constants";
import ShippingDetails from "./components/ShippingDetails";
import ProductSummary from "./components/ProductSummary";
import OrderMeta from "./components/OrderMeta";
import useFormatNumber from "../../../hooks/useFormatNumber";
import DeliveryPartnerDetails from "./components/DeliveryPartnerDetails";
import {makeStyles} from '@mui/styles';

const OrderProductDetails = () => {
  const {formatNumber} = useFormatNumber();
  const classes = useStyles();
  const navigate = useNavigate();
  const {orderDetails} = useSelector((state: any) => state.order);
  const {fulfillmentId = ""} = useParams();

  return (
    <Box maxWidth="md" className={classes.orderDetails}>
      <Box className={classes.header}>
        <IconButton size="small" onClick={() => navigate(-1)}>
          <ArrowBackIcon fontSize="medium"/>
        </IconButton>
        <Box className={classes.headerTitleContainer}>
          <Typography variant="titleLarge" className={classes.orderDetailsTitle}>
            {orderDetails?.id}
          </Typography>
          <Typography variant="labelSmall" component="div" className={classes.orderStatus}>
            {orderDetails?.state} -{" "}
            {CURRENCY_SYMBOLS[orderDetails?.payment?.params?.currency]}
            {formatNumber(
              Number(
                orderDetails?.charges?.quote?.totalOrderValueAfterSubsidy ??
                orderDetails?.payment?.params?.amount
              ).toFixed(2)
            )}
          </Typography>
        </Box>
      </Box>

      <Box className={classes.pageContainer}>
        <ProviderDetails
          provider={orderDetails?.provider}
          bppId={orderDetails?.bppId}
          domain={orderDetails?.domain}
          documents={[]}
          fulfillments={orderDetails?.fulfillments}
        />
        <DeliveryPartnerDetails
          orderState={orderDetails?.state}
          fulfilmentId={fulfillmentId}
          fulfilments={orderDetails?.fulfillments}
        />
        <ShippingDetails fullfillmentId={fulfillmentId}/>
        <Box className={classes.productSummary}>
          <ProductSummary
            items={orderDetails?.items.filter(
              (one: any) => one.fulfillment_id === fulfillmentId
            )}
            quote={orderDetails?.updatedQuote ?? orderDetails?.quote}
            fulfilment={orderDetails?.fulfillments.find(
              (one: any) => one.id === fulfillmentId
            )}
            itemCharges={orderDetails?.charges?.quote?.itemsList ?? []}
          />
        </Box>
        <OrderMeta/>
      </Box>
    </Box>
  );
};

// MUI Styling
const useStyles = makeStyles<any>((theme) => ({
  orderDetails: {
    flex: 1,
  },
  header: {
    backgroundColor: theme.palette.white,
    padding: "8px 16px",
    display: "flex",
    alignItems: "center",
  },
  headerTitleContainer: {
    marginLeft: "20px",
  },
  orderDetailsTitle: {
    color: theme.palette.neutral400,
    marginBottom: "4px",
  },
  orderStatus: {
    color: theme.palette.neutral300,
  },
  productSummary: {
    padding: 16
  },
  pageContainer: {
    flex: 1,
    backgroundColor: theme.palette.neutral50,
  },
}));

export default OrderProductDetails;
