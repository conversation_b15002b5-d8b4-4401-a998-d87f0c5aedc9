import React from 'react';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import {useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';
import {makeStyles} from '@mui/styles';
import useBackHandler from '../../../hooks/useBackHandler';
import SafeAreaPage from '../../../components/page/SafeAreaPage';
import AddressString from './components/AddressString';

const PaymentMethods = () => {
  const {t} = useTranslation();
  const styles = useStyles();
  const {goBack} = useBackHandler();
  const {orderDetails} = useSelector((state: any) => state.order);

  const {location = {}, contact = {}} =
  orderDetails?.fulfillments[0]?.end || {};

  return (
    <SafeAreaPage>
      <Box className={styles.pageContainer}>
        <Box className={styles.header}>
          <IconButton onClick={goBack}>
            <ArrowBackIcon/>
          </IconButton>
          <Typography variant="titleLarge" className={styles.pageTitle}>
            {t('Address List.Delivery Address')}
          </Typography>
        </Box>

        <Box className={styles.pageContent}>
          {/* Delivery Address */}
          <Box className={styles.container}>
            <Typography variant="titleLarge" component="div" className={styles.title}>
              {t('Address List.Delivery Address')}
            </Typography>
            {location?.address?.name && (
              <Typography variant="bodyLarge" component="div" className={styles.name}>
                {location.address.name}
              </Typography>
            )}
            <Typography variant="bodySmall" component="div" className={styles.normalText}>
              {contact?.email ? `${contact.email}, ` : ''}
              {contact?.phone}
            </Typography>
            <AddressString address={location?.address}/>
          </Box>

          {/* Payment Method */}
          <Box className={styles.container}>
            <Typography variant="titleLarge" component="div" className={styles.title}>
              {t('Profile.Payment Methods')}
            </Typography>
            <Typography variant="bodySmall" component="div" className={styles.normalText}>
              {orderDetails?.payment?.type === 'ON-FULFILLMENT'
                ? t('Payment Methods.Cash On Delivery')
                : t('Payment Methods.Prepaid')}
            </Typography>
            <Divider className={styles.divider}/>
            <Typography variant="titleLarge" component="div" className={styles.title}>
              {t('Payment Methods.Billed Address')}
            </Typography>
            {orderDetails?.billing?.name && (
              <Typography variant="bodyLarge" component="div" className={styles.name}>
                {orderDetails.billing.name}
              </Typography>
            )}
            <Typography variant="bodySmall" component="div" className={styles.normalText}>
              {orderDetails?.billing?.email
                ? `${orderDetails.billing.email}, `
                : ''}
              {orderDetails?.billing?.phone}
            </Typography>
            <AddressString address={orderDetails?.billing?.address}/>
          </Box>

          {/* Customer Details */}
          <Box className={styles.container}>
            <Typography variant="titleLarge" component="div" className={styles.title}>
              {t('Payment Methods.Customer Details')}
            </Typography>

            <Box className={styles.customerDetails}>
              <Typography variant="bodyLarge" component="div" className={styles.name}>
                {t('Payment Methods.Order Number')}
              </Typography>
              <Typography variant="bodySmall" component="div" className={styles.normalText}>
                {orderDetails?.id}
              </Typography>
            </Box>

            <Box className={styles.customerDetails}>
              <Typography variant="bodyLarge" component="div" className={styles.name}>
                {t('Payment Methods.Customer Name')}
              </Typography>
              <Typography variant="bodySmall" component="div" className={styles.normalText}>
                {orderDetails?.billing?.name}
              </Typography>
            </Box>

            <Typography variant="bodyLarge" component="div" className={styles.name}>
              {t('Payment Methods.Phone Number')}
            </Typography>
            <Typography variant="bodySmall" component="div" className={styles.normalText}>
              {orderDetails?.billing?.phone}
            </Typography>
          </Box>
        </Box>
      </Box>
    </SafeAreaPage>
  );
};

const useStyles = makeStyles((theme: any) => ({
  pageContainer: {
    backgroundColor: theme.palette.white,
    height: '100%',
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    padding: '12px 16px',
  },
  pageTitle: {
    marginLeft: 20,
    fontWeight: 'bold'
  },
  pageContent: {
    color: theme.palette.neutral300,
    padding: 16,
    height: 'calc(100dvh - 64px)',
    overflow: 'scroll'
  },
  container: {
    borderRadius: 8,
    backgroundColor: theme.palette.white,
    border: `1px solid ${theme.palette.neutral100}`,
    padding: 16,
    marginBottom: 12,
  },
  title: {
    marginBottom: 12,
    color: theme.palette.neutral400,
  },
  divider: {
    margin: '12px 0',
  },
  name: {
    color: theme.palette.neutral400,
    marginBottom: 4,
  },
  normalText: {
    color: theme.palette.neutral300,
    marginBottom: 4,
  },
  customerDetails: {
    marginBottom: 12,
  },
}));
export default PaymentMethods;
