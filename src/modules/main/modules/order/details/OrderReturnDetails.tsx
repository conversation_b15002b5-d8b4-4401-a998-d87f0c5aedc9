import React from 'react';
import {useSelector} from 'react-redux';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import ProviderDetails from './components/ProviderDetails';
import {CURRENCY_SYMBOLS} from '../../../../../utils/constants';
import OrderMeta from './components/OrderMeta';
import ReturnDetails from './components/ReturnDetails';
import ReturnSummary from './components/ReturnSummary';
import useFormatNumber from '../../../hooks/useFormatNumber';
import SafeAreaPage from '../../../components/page/SafeAreaPage';
import useBackHandler from '../../../hooks/useBackHandler';
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import {makeStyles} from '@mui/styles';
import {useParams} from "react-router-dom";

const OrderReturnDetails = () => {
  const {fulfillmentId = ''} = useParams();
  const {formatNumber} = useFormatNumber();
  const styles = useStyles();
  const {orderDetails} = useSelector((state: any) => state.order);
  const {goBack} = useBackHandler();

  return (
    <SafeAreaPage>
      <Box className={styles.orderDetails}>
        <Box className={styles.header}>
          <IconButton onClick={goBack}>
            <ArrowBackIcon/>
          </IconButton>
          <Box className={styles.headerTitleContainer}>
            <Typography variant="titleLarge" className={styles.orderDetailsTitle}>
              {orderDetails?.id}
            </Typography>
            <Typography variant="labelSmall" className={styles.orderStatus}>
              {orderDetails?.state} -{' '}
              {CURRENCY_SYMBOLS[orderDetails?.payment?.params?.currency]}
              {formatNumber(
                Number(
                  orderDetails?.charges?.quote?.totalOrderValueAfterSubsidy ??
                  orderDetails?.payment?.params?.amount,
                ).toFixed(2),
              )}
            </Typography>
          </Box>
        </Box>
        <Box className={styles.pageContainer}>
          <ProviderDetails
            provider={orderDetails?.provider}
            bppId={orderDetails?.bppId}
            domain={orderDetails?.domain}
            documents={[]}
            fulfillments={orderDetails?.fulfillments}
          />
          <ReturnDetails fulfilmentId={fulfillmentId}/>
          <ReturnSummary fulfilmentId={fulfillmentId}/>
          <OrderMeta/>
        </Box>
      </Box>
    </SafeAreaPage>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  orderDetails: {
    flex: 1,
    overflowY: 'auto',
    height: '100dvh'
  },
  header: {
    display: 'flex',
    backgroundColor: theme.palette.white,
    padding: 6,
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10
  },
  headerTitleContainer: {
    marginLeft: 20,
  },
  orderDetailsTitle: {
    color: theme.palette.neutral400,
    marginBottom: 2,
  },
  orderStatus: {
    color: theme.palette.neutral300,
  },
  pageContainer: {
    flex: 1,
    backgroundColor: theme.palette.neutral50,
  },
}));

export default OrderReturnDetails;
