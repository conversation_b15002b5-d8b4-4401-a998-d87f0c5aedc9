import React, {useMemo, useRef, useState} from "react";
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import ClearIcon from "@mui/icons-material/Clear";
import {useTranslation} from "react-i18next";
import {useLocation} from "react-router-dom";
import {makeStyles} from "@mui/styles";
import {API_BASE_URL, GET_SIGN_URL, RETURN_ORDER} from "../../../../../utils/apiActions";
import {CURRENCY_SYMBOLS, RETURN_REASONS} from "../../../../../utils/constants";
import useNetworkHandling from "../../../../../hooks/useNetworkHandling";
import useUploadFile from "../../../hooks/useUploadFile";
import useFormatNumber from "../../../hooks/useFormatNumber";
import useBackHandler from "../../../hooks/useBackHandler";
import {theme} from "../../../../../utils/theme";
import {useToast} from "../../../../../hooks/toastProvider";

const ReturnItem: React.FC = () => {
  const {showToast} = useToast();
  const classes = useStyles();
  const {t} = useTranslation();
  const location = useLocation(); // Use location to get passed state
  const {formatNumber} = useFormatNumber();
  const {postDataWithAuth} = useNetworkHandling();
  const {uploadFile} = useUploadFile();
  const {goBack} = useBackHandler();

  const {
    maxReturnCount,
    item,
    providerId,
    orderId,
    bppId,
    bppUrl,
    transactionId,
    itemBasePrice,
  } = location.state || {};

  const [quantity, setQuantity] = useState<number>(maxReturnCount);
  const [apiRequested, setApiRequested] = useState<boolean>(false);
  const [selectedReason, setSelectedReason] = useState(RETURN_REASONS[0]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [photos, setPhotos] = useState<File[]>([]);
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false);
  const source = useRef<any>(null);

  const openMenu = (event: React.MouseEvent<HTMLButtonElement>) => setAnchorEl(event.currentTarget);
  const closeMenu = () => setAnchorEl(null);
  const updateSelectedReason = (reason: any) => {
    setSelectedReason(reason);
    closeMenu();
  };

  const handleChoosePhoto = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);

      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
      const validFiles = files.filter(file => allowedTypes.includes(file.type));

      if (validFiles.length !== files.length) {
        showToast(t('Only image files are allowed'), 'error');
      }

      if (validFiles.length > 0) {
        const processedFiles = validFiles.map(file => {
          return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (event) => {
              resolve({
                name: file.name,
                type: file.type,
                size: file.size,
                base64: (event.target?.result as string).split(',')[1],
              });
            };
            reader.readAsDataURL(file);
          });
        });

        Promise.all(processedFiles).then((results: any) => {
          setPhotos(results);
        });
      }
    }
  };

  const removePhoto = (photoIndex: number) => {
    setPhotos(prev => prev.filter((_, index) => photoIndex !== index));
  };

  const returnOrder = async () => {
    setApiRequested(true);
    try {
      const signedUrlResponse = await postDataWithAuth(
        `${API_BASE_URL}${GET_SIGN_URL}/${orderId}`,
        {
          fileType: photos[0]?.type.split('/')[1],
        },
        source.current.token,
      );


      await uploadFile(signedUrlResponse.data.urls, URL.createObjectURL(photos[0]));

      const items = [
        {
          id: item.id,
          quantity: {count: quantity},
          tags: {
            update_type: "return",
            reason_code: selectedReason.key,
            ttl_approval: item.product["@ondc/org/return_window"] || "",
            ttl_reverseqc: "P3D",
            image: signedUrlResponse.data.publicUrl,
          },
        },
      ];

      const {data} = await postDataWithAuth(
        `${API_BASE_URL}${RETURN_ORDER}`,
        [
          {
            context: {bpp_id: bppId, bpp_uri: bppUrl, transaction_id: transactionId},
            message: {update_target: "item", order: {id: orderId, provider: {id: providerId}, items}},
          },
        ],
        source.current.token,
      );

      if (data[0].message.ack.status === "NACK") {
        showToast(t("Global.Something went wrong"), 'error');
      } else {
        goBack();
      }
    } catch (err: any) {
      showToast(err.message, 'error');
    } finally {
      setApiRequested(false);
    }
  };
  const {cancellable, returnable} = useMemo(() => {
    return {
      cancellable: item.product['@ondc/org/cancellable'],
      returnable: item.product['@ondc/org/returnable'],
    };
  }, [item.product]);

  return (
    <Box className={classes.page}>
      <Box className={classes.header}>
        <IconButton onClick={goBack}>
          <CloseIcon/>
        </IconButton>
        <Typography className={classes.heading} variant="titleLarge">{t("Return Items.Return Items")}</Typography>
      </Box>

      <Box className={classes.container}>
        <Box className={classes.itemMeta}>
          <Box className={classes.productInfo}>
            <img src={item?.product?.descriptor?.symbol} alt="Item" className={classes.itemImage}/>
            <Box sx={{width: '100%'}}>
              <Box className={classes.quantityContainer}>
                <Typography className={classes.heading}>{item?.product?.descriptor?.name}</Typography>
                <Box className={classes.priceContainer}>
                  <Box className={classes.quantity}>
                    <IconButton disabled={quantity <= 1} onClick={() => setQuantity(quantity - 1)}>
                      <RemoveIcon/>
                    </IconButton>
                    <Typography>{formatNumber(quantity)}</Typography>
                    <IconButton disabled={quantity >= maxReturnCount} onClick={() => setQuantity(quantity + 1)}>
                      <AddIcon/>
                    </IconButton>
                  </Box>
                  <Typography variant="headlineMedium">
                    {CURRENCY_SYMBOLS[item?.product?.price?.currency]}
                    {formatNumber(
                      Number(quantity * itemBasePrice).toFixed(2),
                    )}
                  </Typography>
                </Box>
              </Box>
              <Box className={classes.chipContainer}>
                {cancellable ? (
                  <Box className={classes.chip}>
                    <Typography variant={'labelMedium'}>
                      {t('Profile.Cancellable')}
                    </Typography>
                  </Box>
                ) : (
                  <Box className={classes.chip}>
                    <Typography variant={'labelMedium'}>
                      {t('Profile.Non-cancellable')}
                    </Typography>
                  </Box>
                )}
                {returnable ? (
                  <Box className={classes.chip}>
                    <Typography variant={'labelMedium'}>
                      {t('Profile.Returnable')}
                    </Typography>
                  </Box>
                ) : (
                  <Box className={classes.chip}>
                    <Typography variant={'labelMedium'}>
                      {t('Profile.Non-returnable')}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Box>
          </Box>

        </Box>
        <Typography variant={'titleLarge'} className={classes.message}>
          {t('Return Item.Select reason')}*
        </Typography>
        <Button className={classes.selectDropdown} variant="outlined" sx={{textTransform: 'none'}} onClick={openMenu}
                endIcon={<KeyboardArrowDownIcon/>}>
          {selectedReason.value}
        </Button>
        <Menu anchorEl={anchorEl} open={Boolean(anchorEl)} onClose={closeMenu}>
          {RETURN_REASONS.map((reason: any) => (
            <MenuItem key={reason.key} onClick={() => updateSelectedReason(reason)}>
              {reason.value}
            </MenuItem>
          ))}
        </Menu>
        <Typography variant="titleLarge" className={classes.message}>
          {t("Return Item.Upload Images")}*
        </Typography>

        <Button className={classes.selectDropdown} variant="outlined">
          <input
            type="file"
            id="fileInput"
            accept="image/*"
            multiple
            style={{display: "none"}}
            onChange={handleChoosePhoto}
          />

          <label htmlFor="fileInput" className={classes.uploadBox}>
            <Button variant="outlined" component="span" className={classes.browseButton}>
              <Typography variant="labelMedium">{t("Return Item.Browse")}</Typography>
            </Button>
          </label>
        </Button>

        <Box className={classes.imageContainer}>
          {photos?.map((photo: any, index: number) => (
            <Box key={`Image${index}`} className={classes.photoPreview}>
              <Avatar
                variant="rounded"
                src={`data:${photo.type};base64,${photo.base64}`}
                style={{width: 56, height: 56}}
              />
              <IconButton
                size="small"
                className={classes.removeImage}
                onClick={() => removePhoto(index)}
                sx={{'position': 'absolute !important'}}
              >
                <ClearIcon fontSize="small"/>
              </IconButton>
            </Box>
          ))}
        </Box>

        <Box className={classes.buttonContainer}>
          <Button className={classes.confirmButton} sx={{textTransform: 'none'}} variant="contained"
                  disabled={apiRequested} onClick={() => setShowConfirmation(true)}>
            {t("Return Items.Confirm")}
          </Button>
        </Box>

        <Dialog open={showConfirmation} onClose={() => setShowConfirmation(false)}>
          <DialogTitle>{t("Return Items.Return Items")}</DialogTitle>
          <DialogContent>{t("Return Items.Are you sure you would like to return the items?")}</DialogContent>
          <DialogActions>
            <Button onClick={() => setShowConfirmation(false)}>No</Button>
            <Button onClick={returnOrder} disabled={apiRequested}>
              {apiRequested ? <CircularProgress size={24}/> : "Yes"}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Box>
  );
};
const useStyles = makeStyles({
  page: {
    padding: 16, height: "100dvh",
    display: "flex",
    flexDirection: "column",
  },
  header: {display: "flex", alignItems: "center", gap: 10, marginBottom: 20},
  heading: {
    fontWeight: 'bold !important'
  },
  container: {
    flexGrow: 1,
    display: "flex",
    flexDirection: "column",
    gap: 20,
    overflowY: "auto"
  },
  itemMeta: {display: "flex", gap: 12, flexDirection: 'column'},
  itemImage: {width: 44, height: 44, borderRadius: 8},
  quantityContainer: {display: "flex", alignItems: "center", gap: 6, justifyContent: 'space-between', width: '100%'},
  productInfo: {
    display: "flex", alignItems: "center", gap: 6
  },
  quantity: {
    display: 'flex',
    alignItems: 'center',
    border: `1px solid ${theme.palette.neutral100} !important`,
    borderRadius: '8px !important',
    marginRight: 5,
    height: 35
  },
  chipContainer: {
    display: 'flex',
    flexDirection: 'row',
    marginTop: 12,
  },
  message: {
    marginTop: '8px !important',
    fontWeight: 'bold !important'
  },
  uploadContainer: {
    display: "flex",
    alignItems: "center",
    border: "1px solid #ccc",
    borderRadius: 4,
    padding: "4px 8px",
    width: "100%"
  },
  uploadBox: {flexGrow: 1, display: "flex", justifyContent: "flex-start"},
  fileName: {
    color: theme.palette.neutral400,
    flex: 1,
  },
  imageContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
    gap: 6
  },
  image: {
    width: 40,
    height: 40,
    marginRight: 8,
    borderRadius: 6,
    borderColor: theme.palette.neutral200,
    borderWidth: 1,
    objectFit: 'contain',
  },
  browseButton: {
    backgroundColor: theme.palette.neutral100 + '!important',
    borderColor: theme.palette.neutral300 + '!important',
    color: theme.palette.neutral300 + '!important',
    padding: '8px 6px !important',
    borderRadius: '4px !important',
    borderWidth: '1px !important',
    width: '85px !important',
    height: '35px !important',
  },
  chip: {
    marginRight: 4,
    backgroundColor: theme.palette.neutral100,
    padding: '2px 8px',
    borderRadius: 22,
  },
  photoPreview: {
    position: 'relative'
  },
  selectDropdown: {
    display: 'flex !important',
    borderRadius: '8px !important',
    borderWidth: '1px !important',
    borderColor: theme.palette.neutral200 + '!important',
    color: theme.palette.text.primary + '!important',
    padding: '10px 9px !important',
    flexDirection: 'row',
    alignItems: 'center !important',
    justifyContent: 'space-between !important'
  },
  buttonContainer: {display: "flex", marginTop: 'auto', justifyContent: "center", paddingBottom: 40},
  priceContainer: {
    display: 'flex',
    gap: 6
  },
  imagePreview: {display: "flex", alignItems: "center", gap: 12, marginTop: 10},
  removeImage: {
    top: '-8px' + '!important',
    right: '-8px' + '!important',
    backgroundColor: `${theme.palette.error.main} !important`,
    color: `${theme.palette.common.white} !important`,
    '&:hover': {
      backgroundColor: `${theme.palette.error.dark} !important`,
    },
  },
  confirmButton: {
    width: "100%",
    padding: '10px !important',
    borderRadius: '8px !important',
    backgroundColor: theme.palette.primary.main + '!important'
  },
  fileInput: {display: "none"},
});

export default ReturnItem;
