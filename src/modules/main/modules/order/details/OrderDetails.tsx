import React, { useEffect, useRef, useState } from "react"
import axios from "axios"
import { useDispatch, useSelector } from "react-redux"
import { useLocation } from "react-router-dom"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import {
  API_BASE_URL,
  GET_ISSUE,
  ORDERS,
} from "../../../../../utils/apiActions"
import NonCancelledOrder from "./components/NonCancelledOrder"
import CancelledOrder from "./components/CancelledOrder"
import {
  updateOrderDetails,
  updateRequestingStatus,
  updateRequestingTracker,
} from "../../../../../toolkit/reducer/order"
import {
  CANCEL_ORDER_STATUS,
  DRAFT_ORDER_STATUS,
  FAILED_ORDER_STATUS,
  PROCESSING_ORDER_STATUS,
} from "../../../../../utils/constants"
import useNetworkErrorHandling from "../../../../../hooks/useNetworkErrorHandling"
import { emptyAlertCallback } from "../../../../../utils/utils"
import OrderDetailsSkeleton from "./components/OrderDetailsSkeleton"
import RefreshContainer from "./components/RefreshContainer"
import PendingOrder from "./components/PendingOrder"
import SafeAreaPage from "../../../components/page/SafeAreaPage"
import PrimarySafeAreaPage from "../../../components/page/PrimarySafeAreaPage"

const CancelToken = axios.CancelToken

const OrderDetails: React.FC = () => {
  const dispatch = useDispatch()
  const location = useLocation()
  const params = new URLSearchParams(location.search)
  const orderId = params.get("id")

  const source = useRef<any>(null)
  const firstRequest = useRef<boolean>(true)
  const { getDataWithAuth } = useNetworkHandling()
  const { orderDetails } = useSelector((state: any) => state.order)
  const [apiInProgress, setApiInProgress] = useState<boolean>(true)
  const { handleApiError } = useNetworkErrorHandling()

  /**
   * Function to fetch complaints for a specific order.
   */
  const getComplaint = async (orderId: string): Promise<any[]> => {
    try {
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${GET_ISSUE}?orderId=${orderId}`,
        source.current.token
      )
      return data?.issues ?? []
    } catch (error: any) {
      handleApiError(error)
      return []
    }
  }

  /**
   * Function to fetch order details.
   */
  const getOrderDetails = async (selfUpdate: boolean = false) => {
    try {
      if (!selfUpdate) {
        setApiInProgress(true)
      }
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${ORDERS}/${orderId}`,
        source.current.token
      )
      const state = data[0].state
      if (state !== "Draft" && state !== "Processing" && state !== "Failed") {
        data[0].issues = await getComplaint(data[0].id)
      }
      dispatch(updateRequestingTracker(false))
      dispatch(updateOrderDetails(data[0]))
    } catch (err: any) {
      handleApiError(err)
    } finally {
      setApiInProgress(false)
    }
  }

  /**
   * Fetch order details when the page loads or the order ID changes.
   */
  useEffect(() => {
    dispatch(updateRequestingStatus(false))
    dispatch(updateRequestingTracker(false))
    dispatch(updateOrderDetails(null))
    getOrderDetails().then(emptyAlertCallback)

    return () => {
      if (source.current) {
        source.current.cancel()
      }
    }
  }, [orderId])

  /**
   * Fetch order details again when the user refocuses the page.
   */
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && !firstRequest.current) {
        getOrderDetails(true).then(emptyAlertCallback)
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [])

  if (apiInProgress) {
    return <OrderDetailsSkeleton />
  }

  if (orderDetails) {
    switch (orderDetails?.state) {
      case CANCEL_ORDER_STATUS:
        return (
          <SafeAreaPage>
            <CancelledOrder failed={false} getOrderDetails={getOrderDetails} />
          </SafeAreaPage>
        )

      case FAILED_ORDER_STATUS:
        return (
          <SafeAreaPage>
            <CancelledOrder failed={true} getOrderDetails={getOrderDetails} />
          </SafeAreaPage>
        )

      case PROCESSING_ORDER_STATUS:
        return (
          <PrimarySafeAreaPage>
            <PendingOrder draft={false} getOrderDetails={getOrderDetails} />
          </PrimarySafeAreaPage>
        )

      case DRAFT_ORDER_STATUS:
        return (
          <PrimarySafeAreaPage>
            <PendingOrder draft={true} getOrderDetails={getOrderDetails} />
          </PrimarySafeAreaPage>
        )

      default:
        return (
          <PrimarySafeAreaPage>
            <NonCancelledOrder getOrderDetails={getOrderDetails} />
          </PrimarySafeAreaPage>
        )
    }
  } else {
    return <RefreshContainer onRefreshPress={getOrderDetails} />
  }
}

export default OrderDetails
