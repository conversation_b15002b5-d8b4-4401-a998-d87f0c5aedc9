import React, {useRef, useState} from "react"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import Dialog from "@mui/material/Dialog"
import DialogActions from "@mui/material/DialogActions"
import DialogContent from "@mui/material/DialogContent"
import DialogTitle from "@mui/material/DialogTitle"
import FormControl from "@mui/material/FormControl"
import FormControlLabel from "@mui/material/FormControlLabel"
import IconButton from "@mui/material/IconButton"
import MenuItem from "@mui/material/MenuItem"
import Radio from "@mui/material/Radio"
import RadioGroup from "@mui/material/RadioGroup"
import Select from "@mui/material/Select"
import Typography from "@mui/material/Typography"
import CloseIcon from "@mui/icons-material/Close"
import ExpandMoreIcon from "@mui/icons-material/ExpandMore"
import {makeStyles} from "@mui/styles"
import axios from "axios"
import {useTranslation} from "react-i18next"
import {useSelector} from "react-redux"
import {useNavigate} from "react-router-dom"
import {useLocation} from "react-router-dom";
import {BUYER_CANCELLATION_REASONS, SSE_TIMEOUT,} from "../../../../../utils/constants"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import {API_BASE_URL, CANCEL_ORDER, ON_CANCEL, ORDER_EVENT,} from "../../../../../utils/apiActions"
import {EventSourcePolyfill} from "event-source-polyfill"
import SafeAreaPage from "../../../components/page/SafeAreaPage"
import {useToast} from "../../../../../hooks/toastProvider"
import CancelIcon from "../../../../../assets/cancel.svg"

const CancelOrder = () => {
  const location = useLocation();
  const {showToast} = useToast()
  const {t} = useTranslation()
  const classes = useStyles()
  const navigate = useNavigate()
  const eventTimeOutRef = useRef<any>(null)
  const cancelEventSourceResponseRef = useRef<any[]>([])
  const source = useRef<any>(null)
  const {token} = useSelector(({auth}: any) => auth)
  const [loading, setLoading] = useState(false)
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false)
  const [selectedReason, setSelectedReason] = useState(
    BUYER_CANCELLATION_REASONS[0]
  )
  const {getDataWithAuth, postDataWithAuth} = useNetworkHandling()

  const closeModal = () => setShowConfirmation(false)

  const getCancelOrderDetails = async (messageId: any) => {
    try {
      source.current = axios.CancelToken.source()
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${ON_CANCEL}?messageId=${messageId}`,
        source.current.token
      )
      cancelEventSourceResponseRef.current = [
        ...cancelEventSourceResponseRef.current,
        data[0],
      ]
      if (data?.message) {
        setShowConfirmation(false)
        navigate(-1)
      } else {
        showToast(
          t(
            "Return Items.Something went wrong, product status cannot be updated"
          ),
          "error"
        )
        setLoading(false)
      }
      setLoading(false)
    } catch (err: any) {
      setLoading(false)
      showToast(err?.message, "error")
      eventTimeOutRef.current.eventSource.close()
      clearTimeout(eventTimeOutRef.current.timer)
    }
  }

  const fetchCancelOrderDataThroughEvents = (messageId: any) => {
    const eventSource = new EventSourcePolyfill(
      `${API_BASE_URL}${ORDER_EVENT}${messageId}`,
      {
        headers: {Authorization: `Bearer ${token}`},
      }
    )

    eventSource.addEventListener("on_cancel", (event: any) => {
      const data = JSON.parse(event?.data)
      getCancelOrderDetails(data.messageId).catch(console.error)
    })

    const timer = setTimeout(() => {
      eventSource.close()
      clearTimeout(timer)

      if (cancelEventSourceResponseRef.current.length <= 0) {
        showToast(
          t("Global.Unable to fetch details. Please try again"),
          "error"
        )
        setLoading(false)
      }
    }, SSE_TIMEOUT)

    eventTimeOutRef.current = {eventSource, timer}
  }

  const cancelOrder = async () => {
    cancelEventSourceResponseRef.current = []
    setLoading(true)
    source.current = axios.CancelToken.source()
    try {
      const {data} = await postDataWithAuth(
        `${API_BASE_URL}${CANCEL_ORDER}`,
        {
          context: {
            domain: location.state.domain,
            bpp_id: location.state.bppId,
            bpp_uri: location.state.bppUrl,
            transaction_id: location.state.transactionId,
          },
          message: {
            order_id: location.state.orderId,
            cancellation_reason_id: selectedReason?.key,
          },
        },
        source.current.token
      )

      if (data.message.ack.status === "NACK") {
        setLoading(false)
        showToast(
          t("Return Items.Something went wrong, please try again"),
          "error"
        )
      } else {
        fetchCancelOrderDataThroughEvents(data.context.message_id)
      }
    } catch (error: any) {
      showToast(error?.message, "error")
      setLoading(false)
    }
  }

  return (
    <SafeAreaPage>
      <Box className={classes.page}>
        <Box className={classes.header}>
          <IconButton size="small" onClick={() => navigate(-1)}>
            <CloseIcon/>
          </IconButton>
          <Typography variant="titleLarge" className={classes.title}>
            {t("Cancel Order.Cancel Order")}
          </Typography>
        </Box>
        <Box className={classes.container}>
          <Box className={classes.formContainer}>
            <Box className={classes.radioContainer}>
              <RadioGroup value="complete_order">
                <FormControlLabel
                  value="complete_order"
                  control={<Radio/>}
                  label={t("Cancel Order.Complete order")}
                />
              </RadioGroup>
            </Box>
            <Typography variant="body1" className={classes.message}>
              {t("Cancel Order.Select reason")}*
            </Typography>
            <FormControl className={classes.formControl}>
              <Select
                value={selectedReason.key}
                onChange={(e) => {
                  const reason = BUYER_CANCELLATION_REASONS.find(
                    (r) => r.key === e.target.value
                  )
                  setSelectedReason(reason!)
                }}
                displayEmpty
                IconComponent={ExpandMoreIcon}
                variant="outlined">
                {BUYER_CANCELLATION_REASONS.filter((one) => !one.isApplicableForCancellation).map((one) => (
                  <MenuItem key={one.key} value={one.key} sx={{
                    whiteSpace: 'normal',
                    wordWrap: 'break-word',
                    maxWidth: '100%',
                  }}>
                    {one.value}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          <Box className={classes.bottomButtonContainer}>
            <Button
              className={classes.actionButton}
              variant="contained"
              onClick={() => setShowConfirmation(true)}
            >
              {t("Cancel Order.Cancel")}
            </Button>
          </Box>
        </Box>

        <Dialog open={showConfirmation} onClose={closeModal} fullWidth>
          <DialogTitle className={classes.modalTitle}>
            {t("Cancel Order.Cancel Order")}
            <IconButton onClick={closeModal}>
              <CloseIcon/>
            </IconButton>
          </DialogTitle>
          <DialogContent className={classes.modalContent}>
            <Typography>
              {t(
                "Cancel Order.Are You sure you would like to cancel this order?"
              )}
            </Typography>
          </DialogContent>
          <DialogActions className={classes.actionContainer}>
            <Button onClick={closeModal} color="primary">
              {t("Cancel Order.No")}
            </Button>
            <Button
              className={classes.yesButton}
              onClick={cancelOrder}
              variant="contained"
              disabled={loading}
            >
              {loading ? <CircularProgress size={20}/> : t("Cancel Order.Yes")}
            </Button>
          </DialogActions>
        </Dialog>

        <Dialog open={showConfirmation} onClose={closeModal}>
          <DialogTitle sx={{display: "flex", justifyContent: "flex-end"}}>
            <IconButton onClick={closeModal}>
              <CloseIcon/>
            </IconButton>
          </DialogTitle>
          <DialogContent>
            <Box display="flex" flexDirection="column" alignItems="center">
              <img src={CancelIcon} width={100} height={100}/>
              <Typography variant="headlineMedium" textAlign="center">
                {t("Cancel Order.Cancel Order")}
              </Typography>
              <Typography
                variant="bodySmall"
                textAlign="center"
                sx={{width: "80%", marginTop: 1}}
              >
                {t(
                  "Cancel Order.Are You sure you would like to cancel this order?"
                )}
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button
              variant="outlined"
              fullWidth
              onClick={closeModal}
              disabled={loading}
              sx={{borderRadius: 1}}
            >
              {t("Cancel Order.No")}
            </Button>
            <Button
              variant="contained"
              fullWidth
              onClick={cancelOrder}
              disabled={loading}
              sx={{borderRadius: 1}}
            >
              {loading ? <CircularProgress size={20}/> : t("Cancel Order.Yes")}
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </SafeAreaPage>
  )
}

const useStyles = makeStyles((theme: any) => ({
  title: {
    fontWeight: "bold !important",
  },
  page: {
    display: "flex",
    flexDirection: "column",
    padding: "16px !important",
    minHeight: "100dvh",
  },
  header: {
    display: "flex !important",
    alignItems: "center !important",
    marginBottom: "20px !important",
    gap: '20px',
  },
  pageTitle: {
    marginLeft: "16px !important",
    color: theme.palette.text.primary,
  },
  container: {
    flex: 1,
    display: "flex !important",
    flexDirection: "column",
    gap: "20px !important",
  },
  message: {
    color: theme.palette.text.primary,
    fontWeight: "bold !important",
    marginTop: "20px !important",
  },
  formControl: {
    width: "100% !important",
  },
  selectDropdown: {
    width: "100% !important",
  },
  actionButton: {
    borderRadius: "8px !important",
    marginTop: "10px !important",
    width: "100% !important",
    backgroundColor: theme.palette.text.primary,
    "&:hover": {
      backgroundColor: theme.palette.text.primary,
    },
  },
  modalContent: {
    padding: "20px !important",
  },
  modalTitle: {
    display: "flex !important",
    justifyContent: "space-between !important",
    alignItems: "center !important",
  },
  actionContainer: {
    display: "flex !important",
    justifyContent: "space-between !important",
    gap: "10px !important",
  },
  formContainer: {
    flex: 1,
  },
  radioContainer: {
    flexDirection: "row",
    alignItems: "center !important",
  },
  radioTitle: {
    marginLeft: 8,
    color: theme.palette.neutral300,
  },
  bottomButtonContainer: {
    marginTop: "auto",
    paddingBottom: "40px",
  },
  yesButton: {
    backgroundColor: theme.palette.primary.main,
    "&:hover": {
      backgroundColor: theme.palette.primary.dark,
    },
  },
}))
export default CancelOrder
