import {makeStyles} from '@mui/styles';
import {useMemo} from 'react';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import PhoneIcon from '@mui/icons-material/PhoneRounded';
import {useTranslation} from 'react-i18next';
import {useTheme} from '@mui/material/styles';
import DeliveryPartner from '../../../../../../assets/order/post/delivery_partner.svg';

interface DeliveryPartnerDetailsProps {
  fulfilmentId: string;
  fulfilments: any[];
  orderState: string;
}

const DeliveryPartnerDetails: React.FC<DeliveryPartnerDetailsProps> = ({
                                                                         fulfilmentId,
                                                                         fulfilments,
                                                                         orderState,
                                                                       }) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);

  const fulfillment = useMemo(() => {
    const currentFulfillment = fulfilments?.find(
      (one) => one.id === fulfilmentId
    );
    if (
      currentFulfillment &&
      currentFulfillment.type === 'Delivery' &&
      currentFulfillment.hasOwnProperty('agent')
    ) {
      return currentFulfillment;
    } else {
      return null;
    }
  }, [fulfilments, fulfilmentId]);

  if (orderState === 'Completed' || !fulfillment || !fulfillment?.agent?.name) {
    return null;
  }

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Box className={styles.partnerNameContainer}>
          <img src={DeliveryPartner} alt="Delivery Partner" width={24}/>
          <Typography variant="body1" className={styles.partnerName}>
            {fulfillment?.agent?.name}
          </Typography>
        </Box>
        <IconButton
          className={styles.callButton}
          onClick={() => window.open(`tel:${fulfillment?.agent?.phone}`, '_self')}
        >
          <PhoneIcon sx={{fontSize: 16}}/>
        </IconButton>
      </Box>

      {fulfillment?.end?.authorization?.type === 'OTP' && (
        <>
          <Divider className={styles.divider}/>
          <Box className={styles.optContainer}>
            <Typography variant="labelSmall" className={styles.partnerName}>
              {t('Order Details.OTP')}:
            </Typography>
            <Typography variant="body1" className={styles.partnerName}>
              {fulfillment?.end?.authorization?.token}
            </Typography>
          </Box>
        </>
      )}
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 8,
    backgroundColor: theme.palette.white,
    border: `1px solid ${theme.palette.neutral100}`,
    margin: '16px',
    padding: '16px',
    marginTop: '20px',
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  partnerNameContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
  },
  partnerName: {
    color: theme.palette.neutral400,
  },
  callButton: {
    border: `1px solid ${theme.palette.primary.main}`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '24px',
    height: '24px',
    borderRadius: '50%',
  },
  optContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '4px',
  },
  divider: {
    margin: '10px 0',
    backgroundColor: theme.palette.neutral100,
    height: '1px',
    width: '100%',
  },
}));

export default DeliveryPartnerDetails;
