import moment from 'moment';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import {useTranslation} from 'react-i18next';
import {useTheme} from '@mui/material/styles';
import useFormatDate from '../../../../hooks/useFormatDate';

const FulfilmentDateMessage = ({
                                 fulfillment,
                                 fulfillmentHistory,
                                 createdAt,
                               }: {
  fulfillment: any;
  fulfillmentHistory: any[];
  createdAt: string;
}) => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);
  const {formatDate} = useFormatDate();
  const today = moment();

  const getFulfilmentEndDate = () => {
    if (fulfillment?.end?.time?.timestamp) {
      return moment(fulfillment?.end?.time?.timestamp);
    }

    if (fulfillment?.end?.time?.range?.end) {
      return moment(fulfillment?.end?.time?.range?.end);
    }

    if (fulfillment.hasOwnProperty('@ondc/org/TAT')) {
      const tatDuration = moment.duration(fulfillment['@ondc/org/TAT']);
      if (fulfillment?.start?.time?.timestamp) {
        return moment(fulfillment?.start?.time?.timestamp).add(tatDuration);
      }

      if (fulfillment?.start?.time?.range?.end) {
        return moment(fulfillment?.start?.time?.range?.end).add(tatDuration);
      }

      return moment(createdAt).add(tatDuration);
    }

    return null;
  };

  if (fulfillment?.state?.descriptor?.code === 'Order-delivered') {
    if (fulfillment?.end?.time?.timestamp) {
      const deliveryTime = moment(fulfillment?.end?.time?.timestamp);
      return (
        <Box className={styles.dateContainer}>
          <Typography variant="labelSmall" className={styles.deliveryDate}>
            {t('Shipment Details.Delivered On')}{' '}
          </Typography>
          <Typography variant="caption" className={styles.deliveryDate}>
            {formatDate(deliveryTime, 'Do MMM hh:mm a')}
          </Typography>
        </Box>
      );
    }

    const singleHistory = fulfillmentHistory.find(
      (history) =>
        history.id === fulfillment.id && history.state === 'Order-delivered'
    );

    if (singleHistory) {
      const deliveryTime = moment(singleHistory.updatedAt);
      return (
        <Box className={styles.dateContainer}>
          <Typography variant="labelSmall" className={styles.deliveryDate}>
            {t('Shipment Details.Delivered On')}{' '}
          </Typography>
          <Typography variant="labelSmall" className={styles.deliveryDate}>
            {formatDate(deliveryTime, 'Do MMM hh:mm a')}
          </Typography>
        </Box>
      );
    }
    return <></>;
  } else {
    let endDate = getFulfilmentEndDate();

    if (endDate) {
      return (
        <Box className={styles.dateContainer}>
          <Typography variant="labelSmall" className={styles.deliveryDate}>
            {t('Item Details.Items will be delivered by')}{' '}
          </Typography>
          <Typography variant="labelSmall" className={styles.deliveryDate}>
            {today.isSame(endDate, 'day')
              ? formatDate(endDate, 'hh:mm a')
              : formatDate(endDate, 'Do MMM')}
          </Typography>
        </Box>
      );
    }
    return <></>;
  }
};

const useStyles = makeStyles<any>((theme) => ({
  dateContainer: {
    width: '50%'
  },
  deliveryDate: {
    color: theme.palette.neutral300,
    flex: 1,
    flexWrap: 'wrap',
  },
}));

export default FulfilmentDateMessage;
