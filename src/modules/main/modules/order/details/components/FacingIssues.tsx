import {makeStyles} from '@mui/styles';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {useTranslation} from 'react-i18next';
import {useTheme} from '@mui/material/styles';

const HELP_EMAIL = process.env.REACT_APP_HELP_EMAIL;

const FacingIssues = () => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);

  const sendMail = async () => {
    try {
      window.open(`mailto:${HELP_EMAIL}`, '_self');
    } catch (error: any) {
      console.error('Error opening email:', error);
    }
  };

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="titleLarge" className={styles.sectionTitle}>
          {t('Order Details.Still facing Issue')}
        </Typography>
      </Box>
      <Typography variant="labelSmall" className={styles.message}>
        {t('Order Details.For further assistance, please drop an email to')}{' '}
        <Typography
          component="span"
          variant="labelSmall"
          className={styles.link}
          onClick={sendMail}
        >
          {HELP_EMAIL}
        </Typography>
      </Typography>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 8,
    backgroundColor: theme.palette.white,
    border: `1px solid ${theme.palette.neutral100}`,
    margin: '16px',
    padding: '16px',
    marginTop: '20px',
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '4px',
  },
  sectionTitle: {
    color: theme.palette.neutral400,
  },
  link: {
    color: theme.palette.primary,
    cursor: 'pointer',
    textDecoration: 'underline',
  },
  message: {
    color: theme.palette.neutral300,
    marginTop: '4px',
  },
}));

export default FacingIssues;
