import React, { useEffect, useMemo, useRef, useState } from "react"
import { useFormik } from "formik"
import * as yup from "yup"
import axios from "axios"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import uuid from "react-uuid"
import { toast } from "react-toastify"
import { makeStyles } from "@mui/styles"
import Avatar from "@mui/material/Avatar"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Checkbox from "@mui/material/Checkbox"
import CircularProgress from "@mui/material/CircularProgress"
import Dialog from "@mui/material/Dialog"
import DialogActions from "@mui/material/DialogActions"
import DialogContent from "@mui/material/DialogContent"
import DialogTitle from "@mui/material/DialogTitle"
import FormControl from "@mui/material/FormControl"
import Grid from "@mui/material/Grid2"
import IconButton from "@mui/material/IconButton"
import MenuItem from "@mui/material/MenuItem"
import Select from "@mui/material/Select"
import TextField from "@mui/material/TextField"
import Typography from "@mui/material/Typography"
import Add from "@mui/icons-material/Add"
import Clear from "@mui/icons-material/Clear"
import CloudUpload from "@mui/icons-material/CloudUpload"
import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight"
import EmailIcon from "@mui/icons-material/Email"
import Remove from "@mui/icons-material/Remove"
import OrderComplaint from "./OrderComplaint"
import { CURRENCY_SYMBOLS } from "../../../../../../utils/constants"
import { ISSUE_TYPES } from "../../../../../../utils/issueTypes"
import { API_BASE_URL, RAISE_ISSUE } from "../../../../../../utils/apiActions"
import { isItemCustomization } from "../../../../../../utils/utils"
import useFormatNumber from "../../../../hooks/useFormatNumber"
import RaiseComplaint from "../../../../../../assets/raise_complaint.svg"
import useNetworkHandling from "../../../../../../hooks/useNetworkHandling"
import { useToast } from "../../../../../../hooks/toastProvider"

interface ProductItem {
  id: string
  product: {
    descriptor: {
      name: string
      symbol: string
    }
    price: {
      currency: string
      value: number
    }
  }
  quantity: {
    count: number
  }
  tags?: any[]
}

const validationSchema = yup.object({
  subcategory: yup.string().required("Subcategory is required"),
  shortDescription: yup
    .string()
    .max(200)
    .required("Short Description is required"),
  longDescription: yup
    .string()
    .max(1000)
    .required("Long Description is required"),
})

const categoriesForImages = ["ITM02", "ITM03", "ITM04", "ITM05", "FLM04"]

const categories = ISSUE_TYPES.map((item) => {
  return item.subCategory.map((subcategoryItem) => {
    return {
      ...subcategoryItem,
      category: item.value,
      label: subcategoryItem.value,
    }
  })
}).flat()

const CancelToken = axios.CancelToken

const RaiseIssueButton = ({
  getOrderDetails,
}: {
  getOrderDetails: (selfUpdate?: boolean) => void
}) => {
  const { showToast } = useToast()
  const classes = useStyles()
  const { formatNumber } = useFormatNumber()
  const { t } = useTranslation()
  const source = useRef<any>(null)
  const { orderDetails } = useSelector(({ order }: any) => order)
  const [open, setOpen] = useState<boolean>(false)
  const [selectedItems, setSelectedItems] = useState<string[]>([])
  const [productsWithIssue, setProductsWithIssue] = useState<ProductItem[]>([])
  const [photos, setPhotos] = useState<any[]>([])
  const [raiseInProgress, setRaiseInProgress] = useState<boolean>(false)
  const { postDataWithAuth } = useNetworkHandling()

  const formik = useFormik({
    initialValues: {
      subcategory: "",
      shortDescription: "",
      longDescription: "",
      email: orderDetails?.billing?.email || "",
    },
    validationSchema,
    onSubmit: async (values: any) => {
      await raiseIssue(values)
    },
  })

  const handleOpen = () => {
    setOpen(true)
    setRaiseInProgress(false)
  }

  const handleClose = () => {
    setOpen(false)
    setRaiseInProgress(false)
    setPhotos([])
    setSelectedItems([])
    formik.resetForm()
  }

  const reduceItemQuantity = (itemId: string) => {
    setProductsWithIssue((prev) =>
      prev.map((item) =>
        item.id === itemId && item.quantity.count > 1
          ? {
              ...item,
              quantity: {
                ...item.quantity,
                count: item.quantity.count - 1,
              },
            }
          : item
      )
    )
  }

  const increaseItemQuantity = (itemId: string) => {
    setProductsWithIssue((prev) =>
      prev.map((item) =>
        item.id === itemId
          ? {
              ...item,
              quantity: {
                ...item.quantity,
                count: item.quantity.count + 1,
              },
            }
          : item
      )
    )
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files)
      if (files.length > 4) {
        showToast(t("Maximum 4 images allowed"), "error")
        return
      }

      const allowedTypes = ["image/jpeg", "image/png", "image/gif"]
      const validFiles = files.filter((file) =>
        allowedTypes.includes(file.type)
      )

      if (validFiles.length !== files.length) {
        showToast(t("Only image files are allowed"), "error")
      }

      if (validFiles.length > 0) {
        const processedFiles = validFiles.map((file) => {
          return new Promise((resolve) => {
            const reader = new FileReader()
            reader.onload = (event) => {
              resolve({
                name: file.name,
                type: file.type,
                size: file.size,
                base64: (event.target?.result as string).split(",")[1],
              })
            }
            reader.readAsDataURL(file)
          })
        })

        Promise.all(processedFiles).then((results) => {
          setPhotos(results)
        })
      }
    }
  }

  const removePhoto = (photoIndex: number) => {
    setPhotos((prev) => prev.filter((_, index) => photoIndex !== index))
  }

  const raiseIssue = async (values: any) => {
    try {
      const selectedCategory = categories.find(
        (one) => one.value === values.subcategory
      )

      if (
        selectedCategory &&
        categoriesForImages.includes(selectedCategory.enums) &&
        photos.length === 0
      ) {
        showToast(t("Please attach image"), "error")
        return
      }

      setRaiseInProgress(true)
      const createdDateTime = new Date().toISOString()
      const items = productsWithIssue.filter((item) =>
        selectedItems.includes(item.id)
      )

      const params = {
        context: {
          city: orderDetails?.fulfillments[0]?.end?.location?.address?.city,
          state: orderDetails?.fulfillments[0]?.end?.location?.address?.state,
          transaction_id: uuid(),
          domain: orderDetails?.domain,
        },
        message: {
          issue: {
            category: selectedCategory?.category.toUpperCase(),
            sub_category: selectedCategory?.enums,
            bppId: orderDetails?.bppId,
            bpp_uri: orderDetails?.bpp_uri,
            created_at: createdDateTime,
            updated_at: createdDateTime,
            complainant_info: {
              person: {
                name: orderDetails?.billing?.name,
              },
              contact: {
                phone: orderDetails?.billing?.phone,
                email: orderDetails?.billing?.email,
              },
            },
            description: {
              short_desc: values.shortDescription,
              long_desc: values.longDescription,
              additional_desc: {
                url: "https://buyerapp.com/additonal-details/desc.txt",
                content_type: "text/plain",
              },
              images: photos?.map(
                (photo: any) => `data:${photo.type};base64,${photo.base64}`
              ),
            },
            order_details: {
              id: orderDetails?.id,
              state: orderDetails?.state,
              items,
              fulfillments: orderDetails?.fulfillments,
              provider_id: orderDetails?.provider?.id,
            },
            issue_actions: {
              complainant_actions: [],
              respondent_actions: [],
            },
          },
        },
      }

      source.current = CancelToken.source()
      source.current = CancelToken.source()
      await postDataWithAuth(
        `${API_BASE_URL}${RAISE_ISSUE}`,
        params,
        source.current.token
      )

      onSuccess()
    } catch (err: any) {
      toast.error(err?.message || "Failed to raise issue")
      setRaiseInProgress(false)
    }
  }

  const onSuccess = () => {
    handleClose()
    showToast(t("Complaint raised successfully"), "info")
    getOrderDetails()
  }

  useEffect(() => {
    if (orderDetails) {
      const items = orderDetails.items
        ?.filter((item: ProductItem) => item.quantity.count > 0)
        .map((item: ProductItem) => ({
          ...item,
          maxQuantity: item.quantity.count,
        }))
      setProductsWithIssue(items || [])
    }
  }, [orderDetails])

  const availableCategories = useMemo(() => {
    if (
      orderDetails?.state === "Accepted" ||
      orderDetails?.state === "Created" ||
      orderDetails?.state === "In-progress"
    ) {
      return categories.filter((one) => one.enums === "FLM02")
    }
    return categories
  }, [orderDetails?.state])

  const showCancelButton = useMemo(() => {
    return (
      orderDetails?.state !== "Draft" &&
      orderDetails?.state !== "Processing" &&
      orderDetails?.state !== "Failed"
    )
  }, [orderDetails?.state])

  const disableConfirmButton = raiseInProgress || selectedItems.length === 0

  const issuesLength = orderDetails?.issues?.length || 0

  return (
    <>
      {showCancelButton ? (
        <Box className={classes.container}>
          <Button className={classes.raiseIssueButton} onClick={handleOpen}>
            <Box
              display="flex"
              alignItems="center"
              justifyContent="space-between"
              width="100%"
              className={classes.issueBox}
            >
              <Box display="flex" alignItems="center">
                <img
                  alt="Raise issue"
                  src={RaiseComplaint}
                  width={42}
                  height={42}
                />
                <Typography
                  variant="titleLarge"
                  style={{ marginLeft: 8, flex: 1, textAlign: "left" }}
                >
                  {t("Profile.Raise Issue")}
                </Typography>
              </Box>
              <KeyboardArrowRight />
            </Box>
          </Button>

          {issuesLength > 0 && (
            <Box className={classes.issuesContainer}>
              <Typography
                variant="labelSmall"
                className={classes.issuesLabel}
                gutterBottom
              >
                {t(
                  issuesLength > 1 ? "Raise Issue.Issues" : "Raise Issue.Issue"
                )}{" "}
                ({issuesLength})
              </Typography>
              {orderDetails?.issues?.map((issue: any, index: number) => (
                <OrderComplaint
                  key={issue.issueId}
                  complaint={issue}
                  isLast={index + 1 === issuesLength}
                />
              ))}
            </Box>
          )}
        </Box>
      ) : (
        <Box className={classes.container}>
          <Box className={classes.showEmailContainer}>
            <Box className={classes.iconContainer}>
              <EmailIcon color="primary" sx={{ fontSize: 30 }} />
            </Box>
            <Box>
              <Typography variant="labelSmall" className={classes.emailMessage}>
                {t("Raise Issue.For any issues, kindly reach out to us at")}
              </Typography>
              <Typography
                variant="labelLarge"
                className={classes.emailMessage}
                component="a"
                href={`mailto:${process.env.REACT_APP_HELP_EMAIL}`}
                sx={{
                  textDecoration: "none",
                  color: "primary.main",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
              >
                {` `}
                {process.env.REACT_APP_HELP_EMAIL}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}
      <Dialog open={open} onClose={handleClose} fullWidth maxWidth="md">
        <DialogTitle className={classes.dialogTitle}>
          <Typography variant="titleLarge">
            {t("Raise Issue.Raise an Issue")}
          </Typography>
          <IconButton onClick={handleClose} disabled={raiseInProgress}>
            <Clear />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Box mb={2}>
            <Typography variant="body1" gutterBottom>
              {t("Raise Issue.Choose items that had a problem")}
              <span className={classes.requiredField}>*</span>
            </Typography>

            <Grid container spacing={2}>
              {productsWithIssue?.map((item: any, index: number) => {
                const itemSelected = selectedItems.includes(item.id)
                const itemCustomization = isItemCustomization(item.tags)
                if (itemCustomization) {
                  return null
                }

                return (
                  <Grid item size={12} key={index}>
                    <Box className={classes.productItem}>
                      <Checkbox
                        checked={itemSelected}
                        onChange={() => {
                          if (itemSelected) {
                            setSelectedItems((prev) =>
                              prev.filter((id) => id !== item.id)
                            )
                          } else {
                            setSelectedItems((prev) => [...prev, item.id])
                          }
                        }}
                      />
                      <Avatar
                        src={item.product.descriptor.symbol}
                        variant="rounded"
                        className={classes.productImage}
                      />
                      <Typography variant="labelSmall" style={{ flex: 1 }}>
                        {item.product.descriptor.name}
                      </Typography>
                      <Box className={classes.quantityControls}>
                        <IconButton
                          size="small"
                          disabled={!itemSelected || item.quantity.count <= 1}
                          onClick={() => reduceItemQuantity(item.id)}
                        >
                          <Remove fontSize="small" />
                        </IconButton>
                        <Typography
                          variant="labelSmall"
                          style={{ margin: "0 8px" }}
                        >
                          {formatNumber(item.quantity.count)}
                        </Typography>
                        <IconButton
                          size="small"
                          disabled={
                            !itemSelected ||
                            item.maxQuantity === item.quantity.count
                          }
                          onClick={() => increaseItemQuantity(item.id)}
                        >
                          <Add fontSize="small" />
                        </IconButton>
                      </Box>
                      <Typography
                        variant="labelSmall"
                        style={{ minWidth: 40, textAlign: "right" }}
                      >
                        {CURRENCY_SYMBOLS[item.product.price.currency]}
                        {formatNumber(
                          item.quantity.count * item.product.price.value
                        )}
                      </Typography>
                    </Box>
                  </Grid>
                )
              })}
            </Grid>
          </Box>

          <Box component="form" onSubmit={formik.handleSubmit}>
            <Grid container spacing={2}>
              <Grid size={12}>
                <FormControl
                  fullWidth
                  error={Boolean(
                    formik.touched.subcategory && formik.errors.subcategory
                  )}
                >
                  <Typography
                    variant="bodySmall"
                    component="div"
                    className={classes.inputLabel}
                  >
                    Select Issue Subcategory
                    <Typography component="span" className={classes.required}>
                      *
                    </Typography>
                  </Typography>
                  <Select
                    name="subcategory"
                    value={formik.values.subcategory}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    disabled={raiseInProgress}
                    label=""
                    variant="outlined"
                  >
                    {availableCategories.map((category) => (
                      <MenuItem key={category.value} value={category.value}>
                        {category.label}
                      </MenuItem>
                    ))}
                  </Select>
                  {/* {formik.touched.subcategory && formik.errors.subcategory && (
                    <FormHelperText>{formik.errors.subcategory}</FormHelperText>
                  )} */}
                </FormControl>
              </Grid>

              <Grid size={12}>
                <Typography
                  variant="bodySmall"
                  component="div"
                  className={classes.inputLabel}
                >
                  Short Description
                  <Typography component="span" className={classes.required}>
                    *
                  </Typography>
                </Typography>
                <TextField
                  fullWidth
                  name="shortDescription"
                  label=""
                  value={formik.values.shortDescription}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={Boolean(
                    formik.touched.shortDescription &&
                      formik.errors.shortDescription
                  )}
                  // helperText={formik.touched.shortDescription && formik.errors.shortDescription}
                  disabled={raiseInProgress}
                  multiline
                  rows={2}
                  sx={{
                    "& .MuiInputBase-root": {
                      height: "auto", // ensures it doesn't have a fixed height
                    },
                    "& .MuiInputBase-inputMultiline": {
                      height: "auto",
                    },
                  }}
                />
              </Grid>

              <Grid size={12}>
                <Typography
                  variant="bodySmall"
                  component="div"
                  className={classes.inputLabel}
                >
                  Long Description
                  <Typography component="span" className={classes.required}>
                    *
                  </Typography>
                </Typography>
                <TextField
                  fullWidth
                  name="longDescription"
                  label=""
                  value={formik.values.longDescription}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={Boolean(
                    formik.touched.longDescription &&
                      formik.errors.longDescription
                  )}
                  // helperText={formik.touched.longDescription && formik.errors.longDescription}
                  disabled={raiseInProgress}
                  multiline
                  rows={4}
                  sx={{
                    "& .MuiInputBase-root": {
                      height: "auto", // ensures it doesn't have a fixed height
                    },
                    "& .MuiInputBase-inputMultiline": {
                      height: "auto",
                    },
                  }}
                />
              </Grid>

              <Grid size={12}>
                <Typography
                  variant="bodySmall"
                  component="div"
                  className={classes.inputLabel}
                >
                  Email
                </Typography>
                <TextField
                  fullWidth
                  name="email"
                  label=""
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={Boolean(formik.touched.email && formik.errors.email)}
                  // helperText={formik.touched.email && formik.errors.email}
                  disabled
                />
              </Grid>

              <Grid size={12}>
                <Typography variant="bodySmall" component="div" gutterBottom>
                  {t("Raise Issue.Images (Maximum 4)")}
                </Typography>
                <Button
                  variant="outlined"
                  component="label"
                  startIcon={<CloudUpload />}
                  disabled={raiseInProgress}
                  style={{ marginBottom: 16 }}
                >
                  Browse
                  <input
                    type="file"
                    hidden
                    multiple
                    accept="image/*"
                    onChange={handleFileChange}
                  />
                </Button>

                <Box className={classes.photoPreviewContainer}>
                  {photos.map((photo, index) => (
                    <Box key={index} className={classes.photoPreview}>
                      <Avatar
                        variant="rounded"
                        src={`data:${photo.type};base64,${photo.base64}`}
                        style={{ width: 56, height: 56 }}
                      />
                      <IconButton
                        size="small"
                        className={classes.removePhotoButton}
                        onClick={() => removePhoto(index)}
                        sx={{ position: "absolute !important" }}
                      >
                        <Clear fontSize="small" />
                      </IconButton>
                    </Box>
                  ))}
                </Box>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions className={classes.dialogActions}>
          <Button
            variant="outlined"
            onClick={handleClose}
            disabled={raiseInProgress}
            className={classes.actionButton}
          >
            {t("Raise Issue.Cancel")}
          </Button>
          <Button
            variant="contained"
            onClick={() => formik.handleSubmit()}
            disabled={disableConfirmButton}
            className={classes.actionButton}
          >
            {raiseInProgress ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              "Confirm"
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}
const useStyles = makeStyles<any>((theme) => ({
  container: {
    margin: theme.spacing(2),
    borderRadius: 8,
    border: "1px solid #e0e0e0",
  },
  raiseIssueButton: {
    padding: theme.spacing(2),
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    textTransform: "none",
    width: "100%",
  },
  showEmailContainer: {
    padding: theme.spacing(2),
    display: "flex",
    alignItems: "center",
    textTransform: "none",
    width: "100%",
  },
  issuesContainer: {
    padding: theme.spacing(2),
    borderTop: `1px solid ${theme.palette.divider}`,
  },
  issuesLabel: {
    color: theme.palette.text.primary,
    marginBottom: theme.spacing(1),
  },
  dialogTitle: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  productItem: {
    display: "flex",
    alignItems: "center",
    gap: 6,
  },
  productImage: {
    width: 40,
    height: 40,
  },
  required: {
    color: theme.palette.error.main,
  },
  iconContainer: {
    backgroundColor: "#ECF3F8",
    padding: 6,
    borderRadius: 24,
    marginRight: 8,
  },
  emailMessage: {
    color: theme.palette.neutral400,
  },
  quantityControls: {
    display: "flex",
    alignItems: "center",
    borderWidth: 1,
    borderColor: theme.palette.neutral100,
    borderStyle: "solid",
    borderRadius: 4,
  },
  photoPreviewContainer: {
    display: "flex",
    flexWrap: "wrap",
    gap: theme.spacing(1),
    marginTop: theme.spacing(1),
  },
  photoPreview: {
    position: "relative",
  },
  removePhotoButton: {
    top: "-8px" + "!important",
    right: "-8px" + "!important",
    backgroundColor: `${theme.palette.error.main} !important`,
    color: `${theme.palette.common.white} !important`,
    "&:hover": {
      backgroundColor: `${theme.palette.error.dark} !important`,
    },
  },
  dialogActions: {
    padding: theme.spacing(2),
  },
  actionButton: {
    flex: 1,
  },
  requiredField: {
    color: theme.palette.error.main,
  },
  issueBox: {
    color: "#686868",
  },
}))
export default RaiseIssueButton
