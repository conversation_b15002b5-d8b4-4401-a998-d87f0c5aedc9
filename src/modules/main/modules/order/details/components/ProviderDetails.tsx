import CancelIcon from "@mui/icons-material/Cancel"
import PhoneOutlinedIcon from "@mui/icons-material/PhoneOutlined"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import { useCallback, useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import { FB_DOMAIN } from "../../../../../../utils/constants"
import { parseBrandOutletId } from "../../../../../../utils/utils"
import PaymentRefund from "./PaymentRefund"

const ProviderDetails = ({
  bppId,
  domain,
  provider,
  cancelled = false,
  failed = false,
  documents,
  fulfillments,
}: {
  bppId: any
  domain: any
  provider: any
  cancelled?: boolean
  failed?: boolean
  documents: any[]
  fulfillments: any[]
}) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const styles = useStyles()

  const { phoneNumber, locality } = useMemo(() => {
    if (fulfillments?.length > 0) {
      const fulfilment = fulfillments.find((one) => one.hasOwnProperty("start"))
      if (fulfilment) {
        return {
          phoneNumber: fulfilment?.start?.contact?.phone ?? null,
          locality: fulfilment?.start?.location?.address?.locality ?? null,
        }
      }
    }
    return { phoneNumber: null, locality: null }
  }, [fulfillments])

  const navigateToProvider = useCallback(() => {
    const routeParams: any = {
      brandId: `${bppId}_${domain}_${provider?.id}`,
    }

    if (domain === FB_DOMAIN) {
      routeParams.outletId = `${bppId}_${domain}_${provider?.id}_${provider?.locations[0]?.id}`

      const result = parseBrandOutletId(routeParams.outletId)
      navigate(
        `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${routeParams.outletId}`
      )
      return
    }

    navigate(
      `/store?domain=${domain}&provider_id=${provider?.id}&bpp_id=${bppId}&locationId=${provider?.locations[0]?.id}`
    )
  }, [bppId, domain, navigate, provider])

  const callProvider = useCallback(() => {
    window.open(`tel:${phoneNumber}`, "_self")
  }, [phoneNumber])

  const downloadInvoice = useCallback(() => {
    window.open(documents[0]?.url, "_blank")
  }, [documents])

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Button className={styles.providerMeta} onClick={navigateToProvider}>
          <img
            src={provider?.descriptor?.symbol}
            alt="Provider"
            className={styles.providerImage}
          />
          <Typography variant="headlineSmall" className={styles.providerName}>
            {provider?.descriptor?.name}
          </Typography>
        </Button>
        {phoneNumber && (
          <IconButton
            size="small"
            className={styles.callButton}
            onClick={callProvider}
          >
            <PhoneOutlinedIcon
              className="material-icons"
              style={{ color: "#1976d2" }}
            />
          </IconButton>
        )}
      </Box>
      {locality && <Typography variant="labelSmall">{locality}</Typography>}
      {cancelled && (
        <>
          <Box className={styles.cancelContainer}>
            <CancelIcon className={styles.cancelIcon} />
            <Typography
              variant="labelSmall"
              component="div"
              className={styles.cancelledLabel}
            >
              {failed
                ? t("Provider Details.Payment failed")
                : t("Provider Details.Order Cancelled")}
            </Typography>
          </Box>
          <PaymentRefund />
          {!!documents && (
            <Button
              variant="outlined"
              className={styles.downloadButton}
              onClick={downloadInvoice}
            >
              {t("Provider Details.Download Invoice")}
            </Button>
          )}
        </>
      )}
    </Box>
  )
}

const useStyles = makeStyles((theme: any) => ({
  container: {
    borderRadius: 8,
    backgroundColor: theme.palette.background.paper,
    border: `1px solid ${theme.palette.grey[300]}`,
    margin: "20px 16px",
    padding: 16,
  },
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  providerMeta: {
    display: "flex",
    alignItems: "center",
    flex: 1,
    textTransform: "none",
  },
  providerName: {
    flex: 1,
    color: theme.palette.text.primary,
    fontWeight: "bold",
    textAlign: "left",
  },
  providerImage: {
    width: 30,
    height: 30,
    marginRight: 8,
  },
  callButton: {
    width: 32,
    height: 32,
    border: `1px solid ${theme.palette.primary.main} !important`,
    borderRadius: "50%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  cancelContainer: {
    border: "1px solid" + theme.palette.error.dark + "!important",
    borderRadius: 8,
    borderWidth: 1,
    padding: 10,
    display: "flex",
    alignItems: "center",
    backgroundColor: " #ffebeb !important",
    margin: "16px 0",
  },
  cancelIcon: {
    color: theme.palette.error.dark,
  },
  cancelledLabel: {
    color: theme.palette.error.dark + "!important",
    paddingLeft: 8,
  },
  downloadButton: {
    marginTop: 16,
    borderRadius: 8,
    padding: "9px 16px",
    borderColor: theme.palette.primary.main,
    color: theme.palette.primary.main,
  },
}))

export default ProviderDetails
