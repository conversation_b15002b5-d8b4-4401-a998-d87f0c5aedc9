import {useState} from 'react';
import {useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';
import moment from 'moment';
import {makeStyles} from '@mui/styles';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import ExpandMoreIcon from '@mui/icons-material/ExpandMoreRounded';
import ExpandLessIcon from '@mui/icons-material/ExpandLessRounded';
import CheckCircleIcon from '@mui/icons-material/CheckCircleRounded';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import Box from '@mui/material/Box';
import useFormatDate from '../../../../hooks/useFormatDate';
import Button from '@mui/material/Button';

const useStyles = makeStyles<any>((theme) => ({
  shippingContainer: {
    borderRadius: 8,
    backgroundColor: '#FFF',
    border: `1px solid ${theme.palette.neutral100}`,
    margin: '24px 16px 0',
    padding: '16px',
  },
  shippingTitle: {
    marginBottom: 12,
    fontWeight: 600,
  },
  accordion: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    cursor: 'pointer',
  },
  accordionTitle: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: 12,
  },
  arrivalLabel: {
    color: theme.palette.neutral300,
  },
  arrivalDate: {
    color: theme.palette.neutral400,
    marginLeft: 8,
  },
  statusContainer: {
    display: 'flex',
    justifyContent: 'space-between',
  },
  status: {
    display: 'flex',
  },
  link: {
    height: 30,
    borderLeft: `2px solid #419E6B`,
    marginLeft: 9,
  },
  state: {
    fontWeight: 700,
    color: '#1D1D1D',
    paddingTop: 4,
    paddingLeft: 8,
  },
  timestamp: {
    paddingTop: 4,
    color: '#8A8A8A',
  },
  divider: {
    marginBottom: 15
  }
}));

const ReturnDetails = ({fulfilmentId}: { fulfilmentId: string }) => {
  const {formatDate} = useFormatDate();
  const {t} = useTranslation();
  const classes = useStyles();
  const [showDetails, setShowDetails] = useState<boolean>(true);
  const {orderDetails} = useSelector(({order}: any) => order);

  const fulfillmentHistory = orderDetails?.fulfillmentHistory.filter(
    (one: any) => one.id === fulfilmentId
  );

  const returnInitiated = fulfillmentHistory.find(
    (one: any) => one.state === 'Return_Initiated'
  );

  const fulfilment = orderDetails?.fulfillments.find(
    (one: any) => one.id === fulfilmentId
  );
  const returnTag = fulfilment?.tags.find(
    (tag: any) => tag.code === 'return_request'
  );
  const itemTag = returnTag?.list.find((tag: any) => tag.code === 'item_quantity');

  return (
    <Paper className={classes.shippingContainer} elevation={2}>
      <Typography variant="titleLarge" className={classes.shippingTitle}>
        {t('Item Details.Return Details')}
      </Typography>
      <Typography variant="body1" className={classes.shippingTitle}>
        {t('Return Details.Items Returned', {count: itemTag?.value || 0})}
      </Typography>
      <Button className={classes.accordion} onClick={() => setShowDetails(!showDetails)}>
        <Box className={classes.accordionTitle}>
          <Typography variant="labelSmall" className={classes.arrivalLabel}>
            {t('Return Details.Return On')}:
          </Typography>
          <Typography variant="labelSmall" className={classes.arrivalDate}>
            {formatDate(moment(returnInitiated?.createdAt), 'DD-MM-YYYY')}
          </Typography>
        </Box>
        <IconButton>
          {showDetails ? <ExpandLessIcon/> : <ExpandMoreIcon/>}
        </IconButton>
      </Button>
      <Divider className={classes.divider}/>
      {showDetails &&
        fulfillmentHistory.map((history: any, index: number) => (
          <Box key={history?._id} className={classes.statusContainer}>
            <Box className={classes.status}>
              <Box>
                <CheckCircleIcon style={{color: '#419E6B'}}/>
                {index < fulfillmentHistory.length - 1 && <Box className={classes.link}/>}
              </Box>
              <Typography variant="labelSmall" className={classes.state}>
                {t(`Fulfilment Status.${history?.state.replace(/-/g, ' ')}`)}
              </Typography>
            </Box>
            <Typography variant="labelSmall" className={classes.timestamp}>
              {formatDate(moment(history?.createdAt), 'ddd, DD MMM hh:mm A')}
            </Typography>
          </Box>
        ))}
    </Paper>
  );
};

export default ReturnDetails;
