import {useEffect, useState} from "react";
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import Close from "@mui/icons-material/Close";
import {useSelector} from "react-redux";
import {useTranslation} from "react-i18next";
import useFormatNumber from "../../../../hooks/useFormatNumber";
import {CURRENCY_SYMBOLS, RETURN_REASONS} from "../../../../../../utils/constants";
import {makeStyles} from "@mui/styles";

const ReturnSummary = ({fulfilmentId}: { fulfilmentId: any }) => {
  const {formatNumber} = useFormatNumber();
  const {t} = useTranslation();
  const styles = useStyles();
  const {orderDetails} = useSelector(({order}: any) => order);

  const [images, setImages] = useState<string[]>([]);
  const [quantity, setQuantity] = useState<number>(1);
  const [items, setItems] = useState<any[]>([]);
  const [reasonId, setReasonId] = useState<any>(null);
  const [open, setOpen] = useState<boolean>(false);

  useEffect(() => {
    if (orderDetails) {
      const fulfillment = orderDetails.fulfillments.find(
        (one: any) => one.id === fulfilmentId
      );

      const returnTag = fulfillment?.tags.find((tag: any) => tag.code === "return_request");
      const reasonIdTag = returnTag?.list.find((tag: any) => tag.code === "reason_id");
      setReasonId(reasonIdTag?.value);

      const imagesTag = returnTag?.list.find((tag: any) => tag.code === "images");
      setImages(imagesTag?.value.split(",") || []);

      const itemTag = returnTag?.list.find((tag: any) => tag.code === "item_id");
      const itemQuantityTag = returnTag?.list.find((tag: any) => tag.code === "item_quantity");
      setQuantity(itemQuantityTag?.value || 1);

      setItems(orderDetails.items.filter((item: any) => item.id === itemTag?.value));
    }
  }, [orderDetails]);

  let total = 0;

  return (
    <>
      <Box className={styles.container}>
        <Box className={styles.header}>
          <Typography variant="titleMedium">{t("Return Summary.Items")}</Typography>
        </Box>

        {items.map((item) => {
          const cancellable = item?.product["@ondc/org/cancellable"];
          const returnable = item?.product["@ondc/org/returnable"];
          const itemTotal = Number(quantity * item?.product?.price?.value);
          total += itemTotal;

          return (
            <Box key={item.id} className={styles.item}>
              <Box className={styles.itemMeta}>
                <Avatar src={item?.product?.descriptor?.symbol} className={styles.itemImage}/>
                <Box className={styles.itemDetails}>
                  <Box className={styles.itemHeader}>
                    <Typography variant="body1" className={styles.itemName}>
                      {item?.product?.descriptor?.name}
                    </Typography>
                    <Typography variant="labelSmall" className={styles.quantity}>
                      {t("Return Summary.Qty")} {quantity}
                    </Typography>
                    <Typography variant="labelSmall" className={styles.quantity}>
                      {CURRENCY_SYMBOLS[item?.product?.price?.currency]}
                      {formatNumber(itemTotal.toFixed(2))}
                    </Typography>
                  </Box>

                  <Box className={styles.chipContainer}>
                    <Box className={styles.chip}>
                      <Typography variant="caption">
                        {t(cancellable ? "Profile.Cancellable" : "Profile.Non-cancellable")}
                      </Typography>
                    </Box>
                    <Box className={styles.chip}>
                      <Typography variant="caption">
                        {t(returnable ? "Profile.Returnable" : "Profile.Non-returnable")}
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </Box>
          );
        })}

        <Box className={styles.footer}>
          <Typography variant="labelSmall" className={styles.reason}>
            {t("Return Summary.Reason")}:{" "}
            {t(`Return Reason.${RETURN_REASONS.find((one) => one.key === reasonId)?.value}`)}
          </Typography>
          <Button className={styles.viewImageButton}
                  onClick={() => setOpen(true)}>{t("Return Summary.Click to View Images")}</Button>
        </Box>

        <Box className={styles.divider}/>
        <Box className={styles.grossTotal}>
          <Typography variant="titleSmall">{t("Cart.Total")}</Typography>
          <Typography variant="titleSmall" className={styles.grossTotalValue}>
            {CURRENCY_SYMBOLS[orderDetails?.quote?.price?.currency]}
            {formatNumber(total)}
          </Typography>
        </Box>
      </Box>

      {/* Modal for Images */}
      <Dialog open={open} onClose={() => setOpen(false)} maxWidth="md">
        <DialogContent>
          <Box className={styles.closeContainer}>
            <IconButton onClick={() => setOpen(false)}>
              <Close/>
            </IconButton>
          </Box>
          <Box className={styles.imageContainer}>
            {images.map((image) => (
              <Avatar key={image} src={image} className={styles.image}/>
            ))}
          </Box>
        </DialogContent>
      </Dialog>
    </>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 8,
    backgroundColor: theme.palette.background.paper,
    border: `1px solid ${theme.palette.grey[200]}`,
    margin: 16,
    padding: 16,
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  item: {
    padding: "12px 0",
    borderBottom: `1px solid ${theme.palette.grey[200]}`,
  },
  itemMeta: {
    display: "flex",
    alignItems: "center",
  },
  itemImage: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 10,
    backgroundColor: theme.palette.grey[200],
  },
  itemDetails: {
    flex: 1,
  },
  itemHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    paddingBottom: 5
  },
  chipContainer: {
    display: "flex",
    gap: 4,
  },
  chip: {
    backgroundColor: theme.palette.grey[200],
    padding: "4px 8px",
    borderRadius: 12,
  },
  quantity: {
    color: theme.palette.neutral300,
  },
  reason: {
    color: theme.palette.neutral300,
  },
  divider: {
    margin: "20px 0",
    height: 1,
    backgroundColor: theme.palette.grey[200],
  },
  grossTotal: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  grossTotalValue: {
    color: theme.palette.primary.main,
  },
  closeContainer: {
    display: "flex",
    justifyContent: "flex-end",
  },
  imageContainer: {
    display: "flex",
    gap: 10,
    justifyContent: "center",
    flexWrap: "wrap",
  },
  image: {
    width: 100,
    height: 100,
    borderRadius: 8,
  },
  footer: {
    paddingTop: 10,
    width: '100%'
  },
  viewImageButton: {
    textAlign: 'center',
    width: '100%',
    paddingTop: 10,
    textTransform: 'none',
    color: theme.palette.primary.main
  }
}));

export default ReturnSummary;
