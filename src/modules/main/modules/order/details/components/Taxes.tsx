import Typography from '@mui/material/Typography';
import {CURRENCY_SYMBOLS} from "../../../../../../utils/constants";
import useFormatNumber from "../../../../hooks/useFormatNumber";
import useQuoteSummary from "../../../../hooks/useQuoteSummary";
import {makeStyles} from "@mui/styles";
import Box from "@mui/material/Box";

const Taxes = ({quote}: { quote: any }) => {
  const styles = useStyles();
  const {formatNumber} = useFormatNumber();
  const {getQuoteSummary} = useQuoteSummary();
  const summedData = getQuoteSummary(quote);

  if (!summedData) return null;

  return (
    <Box className={styles.summaryContainer}>
      {Object.values(summedData)?.map((one: any) => (
        <Box key={one?.title} className={styles.summaryRow}>
          <Typography variant="labelSmall" className={styles.taxName}>
            {one?.title}
          </Typography>
          <Typography variant="labelSmall" className={styles.taxValue}>
            {CURRENCY_SYMBOLS[one?.price?.currency]}
            {formatNumber(one?.price?.value.toFixed(2))}
          </Typography>
        </Box>
      ))}
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  summaryContainer: {
    marginTop: 12,
  },
  summaryRow: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  taxName: {
    color: theme.palette.text.secondary,
  },
  taxValue: {
    color: theme.palette.text.secondary,
  },
}));

export default Taxes;
