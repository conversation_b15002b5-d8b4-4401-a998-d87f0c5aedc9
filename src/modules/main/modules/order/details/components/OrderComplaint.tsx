import React, {useMemo} from "react";
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";
import moment from "moment";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {useNavigate} from "react-router-dom";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";

import {issueCategories} from "../../../../../../utils/issueTypes";
import ComplaintStatus from "../../../complaint/components/ComplaintStatus";
import DashLine from "../../../cart/all/components/DashLine";
import {updateComplaint} from "../../../../../../toolkit/reducer/complaint";

interface OrderComplaintProps {
  complaint: any;
  isLast: boolean;
}

const OrderComplaint: React.FC<OrderComplaintProps> = ({complaint, isLast}) => {
  const styles = useStyles();
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const navigateToDetails = () => {
    dispatch(updateComplaint(complaint));
    navigate("/complaint-details");
  };

  const categoryName = useMemo(() => {
    return (
      issueCategories.find((one) => one.enums === complaint?.sub_category)
        ?.value ?? "NA"
    );
  }, [complaint]);

  const closeDate = useMemo(() => {
    if (complaint?.issue_status === "Close") {
      const closedAction = complaint?.issue_actions?.complainant_actions?.find(
        (action: any) => action.complainant_action === "CLOSE"
      );
      if (closedAction) {
        return moment(closedAction.updated_at).locale('en').format("DD MMM, hh:mm a");
      }
      return moment(complaint.updated_at).locale('en').format("DD MMM, hh:mm a");
    }
    return "";
  }, [complaint]);

  return (
    <>
      <Box onClick={navigateToDetails} className={styles.container}>
        <Box className={styles.header}>
          <Box className={styles.meta}>
            <Typography variant="labelSmall" className={styles.categoryName}>
              {categoryName}
            </Typography>
            <ComplaintStatus status={complaint.issue_status}/>
          </Box>
          <IconButton>
            <KeyboardArrowRightIcon color="disabled"/>
          </IconButton>
        </Box>

        <Box className={styles.details}>
          <Typography variant="labelSmall" className={styles.text}>
            {t("Complaint Details.Issue Id")}:
          </Typography>
          <Typography variant="labelSmall" className={styles.text}>
            {complaint?.issueId}
          </Typography>
        </Box>

        <Box className={styles.details}>
          <Typography variant="labelSmall" className={styles.text}>
            {t("Complaint Details.Opened on")}:
          </Typography>
          <Typography variant="labelSmall" className={styles.text}>
            {moment(complaint?.created_at).locale('en').format("DD MMM, hh:mm a")}
          </Typography>
        </Box>

        {complaint?.issue_status === "Close" && (
          <Box className={styles.details}>
            <Typography variant="labelSmall" className={styles.text}>
              {t("Complaint Details.Closed on")}:
            </Typography>
            <Typography variant="labelSmall" className={styles.text}>
              {closeDate}
            </Typography>
          </Box>
        )}
      </Box>
      {!isLast && <DashLine/>}
    </>
  );
};

const useStyles = makeStyles((theme: any) => ({
  container: {
    borderRadius: theme.shape.borderRadius,
    backgroundColor: theme.palette.background.paper,
    cursor: "pointer",
    "&:hover": {
      backgroundColor: theme.palette.action.hover,
    },
  },
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: theme.spacing(1),
  },
  meta: {
    display: "flex",
    alignItems: "center",
    gap: theme.spacing(1),
  },
  categoryName: {
    color: theme.palette.text.primary,
  },
  details: {
    display: "flex",
    alignItems: "center",
    gap: theme.spacing(1),
  },
  text: {
    color: theme.palette.text.primary,
  },
}));
export default OrderComplaint;
