import {makeStyles} from "@mui/styles";

export const makeButtonStyles = makeStyles<any>((theme) => ({
  container: {
    width: 28,
    height: 28,
    backgroundColor: "rgba(255, 255, 255, 0.15)",
    // padding: 4,
    borderRadius: 4,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    // marginBottom: '12px'
  },
  trackButton: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 4,
    padding: "4px 8px",
    borderRadius: 39,
    border: `1px solid ${theme.palette.primary.main}`,
    backgroundColor: theme.palette.primary.light,
  },
  refreshContainer: {
    padding: "16px 0",
    alignItems: "center",
    marginBottom: 24,
  },
  refresh: {
    border: `1px solid ${theme.palette.primary.main}`,
    borderRadius: 8,
    padding: "7px 8px",
    gap: 8,
    display: "flex",
    flexDirection: "row",
  },
  refreshButton: {
    color: theme.palette.background.paper,
  },
  refreshAnimation: {
    width: 24,
    height: 24,
  }
}));
