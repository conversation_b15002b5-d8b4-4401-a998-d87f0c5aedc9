import React, {useMemo} from "react";
import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import {useSelector} from "react-redux";
import moment from "moment";
import {useTranslation} from "react-i18next";

import CancelOrderButton from "./CancelOrderButton";
import useFormatDate from "../../../../hooks/useFormatDate";
import {makeStyles} from "@mui/styles";

const OrderMeta: React.FC = () => {
  const {formatDate} = useFormatDate();
  const {t} = useTranslation();
  const {orderDetails} = useSelector(({order}: any) => order);
  const styles = useStyles();

  const address = useMemo(() => {
    return (
      orderDetails?.fulfillments?.[0]?.end?.location?.address ??
      orderDetails?.billing?.address
    );
  }, [orderDetails]);

  return (
    <Paper elevation={1} className={styles.container}>
      <Typography variant="headlineSmall" component="div" className={styles.title}>
        {t("Order Meta.Order Details")}
      </Typography>

      <Box className={styles.metaContainer}>
        <Typography variant="bodyLarge" component="div" className={styles.label}>
          {t("Order Meta.Order Number")}
        </Typography>
        <Typography variant="bodyMedium" className={styles.value}>
          {orderDetails?.id}
        </Typography>
      </Box>

      <Box className={styles.metaContainer}>
        <Typography variant="bodyLarge" component="div" className={styles.label}>
          {t("Order Meta.Payment mode")}
        </Typography>
        <Typography variant="bodySmall" component="div" className={styles.value}>
          {orderDetails?.payment?.type === "ON-FULFILLMENT"
            ? t("Payment Methods.Cash on delivery")
            : t("Payment Methods.Prepaid")}
        </Typography>
      </Box>

      <Box className={styles.metaContainer}>
        <Typography variant="bodyLarge" component="div" className={styles.label}>
          {t("Order Meta.Date")}
        </Typography>
        <Typography variant="bodySmall" component="div" className={styles.value}>
          {formatDate(moment(orderDetails?.createdAt), "DD/MM/YY hh:mm a")}
        </Typography>
      </Box>

      <Box className={styles.metaContainer}>
        <Typography variant="bodyLarge" component="div" className={styles.label}>
          {t("Order Meta.Phone Number")}
        </Typography>
        <Typography variant="bodySmall" component="div" className={styles.value}>
          {orderDetails?.billing?.phone}
        </Typography>
      </Box>

      <Box>
        <Typography variant="bodyLarge" component="div" className={styles.label}>
          {t("Order Meta.Delivery Address")}
        </Typography>
        <Typography variant="bodySmall" component="div" className={styles.value}>
          {address?.locality}, {address?.building}, {address?.city},{" "}
          {address?.state}, {address?.country} -{" "}
          {address?.area_code ?? address?.areaCode}
        </Typography>
      </Box>
      <CancelOrderButton/>
    </Paper>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 8,
    backgroundColor: "#fff",
    border: "1px solid" + theme.palette.neutral100,
    marginLeft: 16,
    marginRight: 16,
    paddingLeft: 16,
    paddingRight: 16,
    paddingTop: 16,
    paddingBottom: 8,
    marginBottom: 12,
    marginTop: 12,
  },
  title: {
    marginBottom: 12,
    fontWeight: 'bold'
  },
  metaContainer: {
    marginBottom: 12,
  },
  label: {
    marginBottom: 12,
    fontWeight: 'bold'
  },
  value: {},
}));

export default OrderMeta;
