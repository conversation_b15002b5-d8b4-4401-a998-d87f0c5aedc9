import React, {useCallback} from "react";
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIosRounded";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
import {makeStyles} from "@mui/styles";

interface PaymentMethodProps {
  payment: any;
  address: any;
  contact: any;
}

const PaymentMethod: React.FC<PaymentMethodProps> = ({payment, address, contact}) => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const styles = useStyles();
  const navigateToPaymentMethods = useCallback(() => navigate("/payment-methods"), [navigate]);

  return (
    <Box className={styles.container}>
      <Typography variant="titleLarge" className={styles.title}>
        {t("Profile.Payment Methods")}
      </Typography>

      <Box className={styles.modeContainer} onClick={navigateToPaymentMethods}>
        <Typography variant="titleSmall" className={styles.mode}>
          {payment?.type === "ON-FULFILLMENT"
            ? t("Payment Methods.Cash On Delivery")
            : t("Payment Methods.Prepaid")}
        </Typography>
        <IconButton>
          <ArrowForwardIosIcon sx={{fontSize: 20, color: "#686868"}}/>
        </IconButton>
      </Box>

      <Divider className={styles.divider}/>

      <Typography variant="titleLarge" component="div" className={styles.addressTitle}>
        {t("Profile.Shipping Address")}
      </Typography>

      <Box className={styles.modeContainer} onClick={() => navigate("/payment-methods")}>
        <Typography variant="bodyLarge" className={styles.mode}>
          {address?.name}
          <br/>
          {address?.building}, {address?.locality}, {address?.city}, {address?.state}, {address?.country} -{" "}
          {address?.area_code ?? address?.areaCode}
          <br/>
          {contact?.phone}
        </Typography>
        <IconButton>
          <ArrowForwardIosIcon sx={{fontSize: 20, color: "#686868"}}/>
        </IconButton>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 8,
    backgroundColor: "#fff",
    border: "1px solid #e0e0e0",
    margin: 16,
    padding: 16,
  },
  title: {
    marginBottom: 1,
  },
  addressTitle: {
    marginBottom: 12,
  },
  modeContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    cursor: "pointer",
  },
  mode: {
    color: "#686868",
    flex: 1,
  },
  divider: {
    marginTop: 10,
    marginBottom: 10,
  },
}));

export default PaymentMethod;
