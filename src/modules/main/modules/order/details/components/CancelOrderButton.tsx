import Button from "@mui/material/Button"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"

const CancelOrderButton = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { orderDetails, requestingStatus, requestingTracker } = useSelector(
    ({ order }: any) => order
  )
  const showCancelButton =
    orderDetails?.hasOwnProperty("isETABreached") &&
    orderDetails?.hasOwnProperty("isCancellable")
      ? orderDetails?.isETABreached || orderDetails?.isCancellable
      : false
  const navigateToCancelOrder = () => {
    navigate("/cancel-order", {
      state: {
        domain: orderDetails.domain,
        bppId: orderDetails.bppId,
        bppUrl: orderDetails.bpp_uri,
        transactionId: orderDetails.transactionId,
        orderId: orderDetails?.id,
      },
    })
  }

  const hasTypeTagWithItem = (tags: any[]) => {
    return tags?.some(
      (tag: any) =>
        tag?.code === "type" &&
        tag?.list?.some(
          (item: any) => item?.code === "type" && item?.value === "item"
        )
    )
  }

  const allCancellable = useMemo(() => {
    const items = orderDetails?.items?.filter(
      (obj: any) => !obj.tags || hasTypeTagWithItem(obj.tags)
    )
    return items?.every((item: any) => item.product["@ondc/org/cancellable"])
  }, [orderDetails])

  if (
    ((orderDetails?.state === "Accepted" ||
      orderDetails?.state === "Created") &&
      allCancellable) ||
    (showCancelButton &&
      orderDetails.state !== "Completed" &&
      orderDetails.state !== "Cancelled" &&
      orderDetails.state !== "Draft" &&
      orderDetails.state !== "Failed")
  ) {
    return (
      <Button
        variant="outlined"
        color="error"
        fullWidth
        sx={{ mt: 3, mb: 2, height: 44 }}
        onClick={navigateToCancelOrder}
        disabled={requestingStatus || requestingTracker}
      >
        {t("Cancel Order.Cancel Order")}
      </Button>
    )
  }

  return null
}

export default CancelOrderButton
