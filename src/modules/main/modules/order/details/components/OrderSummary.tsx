import React, {useMemo} from "react"
import Box from "@mui/material/Box"
import Divider from "@mui/material/Divider"
import Paper from "@mui/material/Paper"
import Typography from "@mui/material/Typography"
import {useTranslation} from "react-i18next"
import {CURRENCY_SYMBOLS} from "../../../../../../utils/constants"
import useFormatNumber from "../../../../hooks/useFormatNumber"
import useQuoteSummary from "../../../../hooks/useQuoteSummary"

interface OrderSummaryProps {
  quote: any
  coupon: any
}

const OrderSummary: React.FC<OrderSummaryProps> = ({quote, coupon}) => {
  const {formatNumber} = useFormatNumber()
  const {t} = useTranslation()
  const {getQuoteSummary} = useQuoteSummary()

  let orderTotal = Number(quote?.price?.value) || 0

  if (coupon?.benefit) {
    orderTotal -= Number(coupon.benefit) || 0
  }

  const orderSubTotal = useMemo(() => {
    let total = 0
    quote?.breakup?.forEach((item: any) => {
      if (item["@ondc/org/title_type"] === "item") {
        total += Number(item.price.value)
      }
    })
    return total
  }, [quote])

  const summedData = getQuoteSummary(quote)

  return (
    <Paper elevation={1} sx={styles.container}>
      <Box sx={styles.header}>
        <Typography variant="titleLarge" sx={styles.sectionTitle}>
          {t("Product Summary.Order Summary")}
        </Typography>
      </Box>

      <Box sx={styles.summaryRow}>
        <Typography variant="body1" sx={styles.taxName}>
          {t("Order Details.Items Total")}
        </Typography>
        <Typography variant="body1" sx={styles.taxValue}>
          ₹{formatNumber(orderSubTotal.toFixed(2))}
        </Typography>
      </Box>

      {!!summedData &&
        Object.values(summedData).map((one: any) => (
          <Box key={one?.title} sx={styles.summaryRow}>
            <Typography variant="body1" sx={styles.taxName}>
              {one?.title}
            </Typography>
            <Typography variant="body1" sx={styles.taxValue}>
              {CURRENCY_SYMBOLS[one?.price?.currency]}
              {formatNumber(one?.price?.value.toFixed(2))}
            </Typography>
          </Box>
        ))}

      {!!coupon && (
        <Box sx={styles.summaryRow}>
          <Typography variant="body1" sx={styles.taxName}>
            {coupon?.offerId}
          </Typography>
          <Typography variant="body1" sx={styles.benefit}>
            - ₹{formatNumber(coupon?.benefit.toFixed(2))}
          </Typography>
        </Box>
      )}

      <Divider sx={styles.divider}/>

      <Box sx={styles.grossTotal}>
        <Typography variant="titleMedium" sx={styles.grossTotalLabel}>
          {t("Product Summary.Order Total")}
        </Typography>
        <Typography variant="headlineSmall" sx={styles.grossTotalValue}>
          {CURRENCY_SYMBOLS[quote?.price?.currency]}
          {formatNumber(Number(orderTotal).toFixed(2))}
        </Typography>
      </Box>
    </Paper>
  )
}

const styles = {
  container: {
    borderRadius: 2,
    backgroundColor: "#fff",
    border: "1px solid #e0e0e0",
    mx: 2,
    p: 2,
    mt: 2.5,
  },
  header: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    mb: 1,
  },
  sectionTitle: {
    fontWeight: "bold",
  },
  summaryRow: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    mb: 1,
  },
  taxName: {},
  taxValue: {},
  benefit: {
    color: "#d32f2f",
  },
  divider: {
    my: 2,
  },
  grossTotal: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  grossTotalLabel: {},
  grossTotalValue: {
    color: "#1976d2",
  },
}

export default OrderSummary
