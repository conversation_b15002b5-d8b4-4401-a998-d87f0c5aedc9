import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Icon from "@mui/icons-material/KeyboardArrowRightRounded";
import {useTranslation} from "react-i18next";
import {makeStyles} from "@mui/styles";
import FeedbackIcon from "../../../../../../assets/order/post/feedback.svg";

const ShareExperienceButton = ({url}: { url: string }) => {
  const {t} = useTranslation();
  const styles = useStyles();

  const openLink = () => {
    window.open(url, "_blank");
  };

  if (process.env.REACT_APP_ENV === "PRODUCTION") {
    return (
      <Box className={styles.container} onClick={openLink}>
        <Box className={styles.icon}>
          <img alt="Feedback Icon" src={FeedbackIcon} width={42} height={42}/>
        </Box>
        <Typography variant="titleLarge" className={styles.title}>
          {t("Profile.Share Your Experience")}
        </Typography>
        <Icon fontSize="small" className={styles.arrowIcon}/>
      </Box>
    );
  }

  return null;
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    padding: "8px 16px",
    borderRadius: 8,
    border: `1px solid ${theme.palette.grey[300]}`,
    display: "flex",
    alignItems: "center",
    marginTop: 20,
    marginLeft: 16,
    marginRight: 16,
    cursor: "pointer",
    transition: "background 0.3s",
    "&:hover": {
      background: theme.palette.grey[100],
    },
  },
  icon: {
    display: "flex",
    alignItems: "center",
  },
  title: {
    marginLeft: 8,
    flexGrow: 1,
  },
  arrowIcon: {
    color: theme.palette.grey[500],
  },
}));

export default ShareExperienceButton;
