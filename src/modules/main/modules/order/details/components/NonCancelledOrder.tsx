import React, {useCallback, useMemo} from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import moment from 'moment';
import {useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';
import ArrowBackIcon from '@mui/icons-material/ArrowBackRounded';
import ShoppingBagOutlinedIcon from '@mui/icons-material/ShoppingBagOutlined';
import GetAppOutlinedIcon from '@mui/icons-material/GetAppRounded';
import IconButton from "@mui/material/IconButton";
import ProviderDetails from './ProviderDetails';
import ItemDetails from './ItemDetails';
import PaymentMethod from './PaymentMethod';
import RaiseIssueButton from './RaiseIssueButton';
import ShareExperienceButton from './ShareExperienceButton';
import CancelOrderButton from './CancelOrderButton';
import useFormatDate from '../../../../hooks/useFormatDate';
import OrderSummary from './OrderSummary';
import {FEEDBACK_FORM} from '../../../../../../utils/constants';
import DeliveryPartnerDetails from './DeliveryPartnerDetails';
import GetStatusButton from './GetStatusButton';
import TrackOrderButton from './TrackOrderButton';
import OrderSummaryWithSubsidy from './OrderSummaryWithSubsidy';
import useBackHandler from '../../../../hooks/useBackHandler';

const NonCancelledOrder: React.FC<{ getOrderDetails: (selfUpdate?: boolean) => void }> = ({getOrderDetails}) => {
  const {formatDate} = useFormatDate();
  const {t} = useTranslation();
  const {orderDetails} = useSelector((state: any) => state.order);
  const styles = useStyles();
  const {goBack} = useBackHandler();
  const invoiceAvailable = useMemo(() => {
    return orderDetails?.documents?.length > 0 && orderDetails.documents[0]?.url;
  }, [orderDetails]);

  const downloadInvoice = useCallback(() => {
    if (orderDetails?.documents[0]?.url) {
      window.open(orderDetails?.documents[0]?.url, '_blank');
    }
  }, [orderDetails]);

  const renderOrderDate = () => {
    if (orderDetails?.state === 'Completed') {
      const history = orderDetails?.orderHistory?.find((one: any) => one.state === 'Completed');
      return (
        <Box className={styles.orderDate}>
          <ShoppingBagOutlinedIcon/>
          <Typography variant='labelMedium' component="div">
            Order completed on {formatDate(moment(history?.updatedAt || orderDetails?.updatedAt), 'DD MMM hh:mm a')}
          </Typography>
        </Box>
      );
    }
    return (
      <Box className={styles.trackContainer}>
        <Box display="flex" alignItems="center">
          <ShoppingBagOutlinedIcon/>
          <Typography variant='labelMedium' component="div">
            Order placed on {formatDate(moment(orderDetails?.createdAt), 'DD MMM hh:mm a')}
          </Typography>
        </Box>
        <TrackOrderButton/>
      </Box>
    );
  };

  return (
    <Box className={styles.orderDetails}>
      <Box className={styles.header}>
        <Box className={styles.headerRow}>
          <Button onClick={goBack} className={styles.backButton}>
            <ArrowBackIcon fontSize="small" sx={{color: '#fff'}}/>
          </Button>
          <Typography
            variant="titleLarge"
            className={styles.orderDetailsTitle}
          >
            {orderDetails?.provider?.descriptor?.name}
          </Typography>
          <Box className={styles.empty}/>
        </Box>
        <Box className={styles.statusContainer}>
          <Typography variant="headlineSmall" component="div" className={styles.orderStatus}>
            {t(`Order Details.Order ${orderDetails?.state}`)}
          </Typography>
          <GetStatusButton onUpdateOrder={getOrderDetails}/>
        </Box>
      </Box>
      {renderOrderDate()}

      <Box sx={{display: 'flex', alignItems: 'center', justifyContent: 'space-between', px: 2}}>
        <Typography variant="titleLarge">
          {orderDetails?.id}
        </Typography>
        {invoiceAvailable ? (
          <IconButton size="small" onClick={downloadInvoice}><GetAppOutlinedIcon/></IconButton>
        ) : (
          <IconButton size="small" disabled={true}><GetAppOutlinedIcon/></IconButton>
        )}
      </Box>
      <DeliveryPartnerDetails
        fulfilmentId={orderDetails?.fulfillments?.[0]?.id}
        fulfilments={orderDetails?.fulfillments}
        orderState={orderDetails?.state}
      />
      <ProviderDetails
        provider={orderDetails?.provider}
        bppId={orderDetails?.bppId}
        domain={orderDetails?.domain}
        documents={[]}
        fulfillments={orderDetails?.fulfillments}
      />
      <ItemDetails
        items={orderDetails?.items}
        fulfillments={orderDetails?.fulfillments}
        quoteBreakup={
          orderDetails?.updatedQuote
            ? orderDetails?.updatedQuote.breakup
            : orderDetails?.quote?.breakup
        }
        itemCharges={orderDetails?.charges?.quote?.itemsList ?? []}
      /> <PaymentMethod payment={orderDetails?.payment} address={orderDetails?.billing?.address}
                        contact={orderDetails?.billing?.contact}/>
      {orderDetails?.charges ? (
        <OrderSummaryWithSubsidy charges={orderDetails?.charges?.quote}/>
      ) : (
        <OrderSummary
          quote={orderDetails?.updatedQuote ?? orderDetails?.quote}
          coupon={orderDetails?.coupon}
        />)}
      <ShareExperienceButton
        url={orderDetails?.state === 'Completed' ? FEEDBACK_FORM.COMPLETED_ORDER : FEEDBACK_FORM.NON_COMPLETED_ORDER}/>
      <RaiseIssueButton getOrderDetails={getOrderDetails}/>
      <Box className={styles.cancelButton}>
        <CancelOrderButton/>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  orderDetails: {
    flex: 1,
  },
  header: {
    backgroundColor: theme.palette.primary.main,
  },
  headerRow: {
    display: 'flex',
    padding: '12px 16px !important',
    gap: 12
  },
  backButton: {
    color: theme.palette.background.paper,
    padding: '8px 0 !important',
    justifyContent: 'left !important',
    minWidth: '20px'
  },
  cancelButton: {
    marginLeft: 16,
    marginRight: 16
  },
  orderDetailsTitle: {
    color: theme.palette.background.paper,
    textAlign: 'center',
    width: '100%',
    textTransform: 'none'

  },
  statusContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    gap: 12,
    paddingBottom: 12,
  },
  orderStatus: {
    color: theme.palette.background.paper,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  empty: {
    width: 24,
    height: 24,
  },
  pageContainer: {
    flex: 1,
    backgroundColor: theme.palette.neutral50,
  },
  scrollView: {
    paddingBottom: 16,
  },
  creationHeader: {
    flexDirection: 'row',
    marginVertical: 20,
    alignItems: 'center',
    marginHorizontal: 16,
  },
  orderIdContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginHorizontal: 16,
  },
  orderId: {
    color: theme.palette.neutral300,
  },
  creationDate: {
    marginLeft: 8,
    color: theme.palette.neutral300,
  },
  cancelContainer: {
    marginHorizontal: 16,
  },
  deliveryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
  },
  orderDate: {
    display: 'flex',
    gap: 8,
    padding: '16px !important',
    alignItems: 'center',
  },
  trackContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    padding: '16px !important',
    alignItems: 'center',
  }
}));
export default NonCancelledOrder;
