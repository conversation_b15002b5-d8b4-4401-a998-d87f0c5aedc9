import {useMemo, useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import axios from 'axios';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import RefreshIcon from '@mui/icons-material/RefreshRounded';
import DirectionsBikeIcon from '@mui/icons-material/DirectionsBikeRounded';
import CloseIcon from '@mui/icons-material/CloseRounded';
import {useTranslation} from 'react-i18next';
import {makeStyles} from '@mui/styles';
import {EventSourcePolyfill} from 'event-source-polyfill';

import {API_BASE_URL, ORDER_EVENT, TRACK_ORDER} from '../../../../../../utils/apiActions';
import {updateRequestingTracker} from '../../../../../../toolkit/reducer/order';
import useNetworkHandling from '../../../../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../../../../hooks/useNetworkErrorHandling';

const CancelToken = axios.CancelToken;

const TrackOrderButton = () => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const classes = useStyles();

  const {orderDetails, requestingTracker, requestingStatus} = useSelector((state: any) => state.order);
  const {token} = useSelector((state: any) => state.auth);

  const {getDataWithAuth, postDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();

  const [trackingUrl, setTrackingUrl] = useState<string>('');
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [route, setRoute] = useState<any>(null);
  const [currentLocation, setCurrentLocation] = useState<any>(null);

  const source = useRef<any>(null);

  const handleFetchTrackOrderDetails = async () => {
    dispatch(updateRequestingTracker(true));
    const transaction_id = orderDetails?.transactionId;
    const bpp_id = orderDetails?.bppId;
    const order_id = orderDetails?.id;

    try {
      source.current = CancelToken.source();
      const {data} = await postDataWithAuth(
        `${API_BASE_URL}${TRACK_ORDER}`,
        [
          {
            context: {transaction_id, bpp_id},
            message: {order_id},
          },
        ],
        source.current.token
      );

      if (data[0]?.message.ack.status === 'ACK') {
        fetchTrackingDataThroughEvents(data[0]?.context?.message_id);
      } else {
        alert(t('Something went wrong, please try again later'));
        dispatch(updateRequestingTracker(false));
      }
    } catch (err) {
      dispatch(updateRequestingTracker(false));
      handleApiError(err);
    }
  };

  const fetchTrackingDataThroughEvents = (messageId: string) => {
    const eventSource = new EventSourcePolyfill(`${API_BASE_URL}${ORDER_EVENT}${messageId}`, {
      headers: {Authorization: `Bearer ${token}`},
    });
    eventSource.addEventListener('on_track', async (event: any) => {
      const data = JSON.parse(event?.data);
      await getTrackOrderDetails(data.messageId);
    });

    setTimeout(() => {
      eventSource.close();
      if (!trackingUrl && !route) {
        alert(t('Unable to fetch details. Please try again.'));
        dispatch(updateRequestingTracker(false));
      }
    }, 10000);
  };

  const getTrackOrderDetails = async (messageId: any) => {
    try {
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(`${API_BASE_URL}${ORDER_EVENT}${messageId}`, source.current.token);

      const {message} = data[0];

      if (message.tracking.status === 'active' && message.tracking.url) {
        setTrackingUrl(message.tracking.url);
        setOpenDialog(true);
      } else if (message.tracking.status === 'active' && message.tracking.location?.gps) {
        const [lat, lon] = message.tracking.location.gps.split(',').map(Number);
        setCurrentLocation({lat, lon});
        setOpenDialog(true);
      } else {
        alert(t('Tracking information is not provided by the provider.'));
      }
    } catch (err) {
      handleApiError(err);
    } finally {
      dispatch(updateRequestingTracker(false));
    }
  };

  const refreshTracking = () => {
    handleFetchTrackOrderDetails().then(r => {});
  };

  const showTrackingButton = useMemo(() => {
    if (orderDetails) {
      const filtered = orderDetails.fulfillments?.filter((f: any) => f.type === 'Delivery');
      return filtered.length > 0 && filtered[0].tracking;
    }
    return false;
  }, [orderDetails]);

  return (
    <>
      {showTrackingButton && (
        <Button className={classes.trackButton} onClick={handleFetchTrackOrderDetails}
                disabled={requestingTracker || requestingStatus}>
          {requestingTracker ? <CircularProgress size={16}/> : <DirectionsBikeIcon/>}
          <Typography variant="labelSmall">{t('Profile.Track')}</Typography>
        </Button>
      )}

      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} className={classes.dialog}>
        <DialogContent>
          <IconButton onClick={() => setOpenDialog(false)} style={{position: 'absolute', right: 10, top: 10}}>
            <CloseIcon/>
          </IconButton>
          {trackingUrl ? (
            <iframe src={trackingUrl} className={classes.iframe}/>
          ) : (
            <Typography variant="bodyLarge">Coming Soon</Typography>
          )}
          <Button className={classes.refreshButton} onClick={refreshTracking} disabled={requestingTracker}>
            {requestingTracker ? <CircularProgress size={16}/> : <RefreshIcon/>}
            <Typography>{t('Profile.Refresh')}</Typography>
          </Button>
        </DialogContent>
      </Dialog>
    </>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  trackButton: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
    padding: '4px 8px',
    borderRadius: 8,
    backgroundColor: theme.palette.background.paper,
    border: `1px solid ${theme.palette.primary.main}`,
    cursor: 'pointer',
  },
  dialog: {
    '& .MuiDialog-paper': {
      width: '90vw',
      maxWidth: 600,
      height: '80dvh',
    },
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
  },
  refreshButton: {
    marginTop: 8,
    display: 'flex',
    alignItems: 'center',
    gap: 4,
  },
  mapContainer: {
    width: '100%',
    height: '400px',
  },
}));

export default TrackOrderButton;
