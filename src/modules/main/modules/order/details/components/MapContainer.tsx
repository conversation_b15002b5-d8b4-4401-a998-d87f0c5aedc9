import React, { useEffect, useRef } from 'react';
import useMapMeta from "../../../../hooks/useMapMeta";
import { mappls, mappls_plugin } from 'mappls-web-maps';
// import 'mappls-web-maps/dist/mappls-web-maps.css';
// import 'mappls-web-maps/dist/mappls-ui.css'; // optional but recommended for UI widgets

const mapplsClassObject = new mappls();
const mapplsPluginObject = new mappls_plugin();

const MapContainer = ({ driverPosition, sellerPoint, destinationPoint }: any) => {
  const mapRef = useRef(null);
  const mapInstance = useRef(null);
  const directionPluginRef = useRef(null);
  const {getMapMeta, loading} = useMapMeta();
  const mapplsToken = useRef<any>(null);

  useEffect(() => {
    if (!driverPosition && !destinationPoint) return;

    // // Initialize SDK tokens (ensure they're set once)
    // mappls.setToken({
    //   mapSDKKey: process.env.REACT_APP_MMI_API_KEY,
    //   atlasClientId: process.env.REACT_APP_MMI_CLIENT_ID,
    //   atlasClientSecret: process.env.REACT_APP_MMI_CLIENT_SECRET,
    //   restAPIKey: process.env.REACT_APP_MMI_REST_KEY,
    // });

    // const initializeMap = () => {
    //   // Create a map instance
    //   mapInstance.current = new mappls.Map(mapRef.current, {
    //     center: driverPosition || { lat: 28.61, lng: 77.23 },
    //     zoom: 12,
    //     traffic: false,
    //   });

    //   // Add markers
    //   if (sellerPoint) {
    //     new mappls.Marker({
    //       map: mapInstance.current,
    //       position: sellerPoint,
    //       icon_url: '/icons/seller-icon.png',
    //       title: 'Seller',
    //     });
    //   }

    //   if (driverPosition) {
    //     new mappls.Marker({
    //       map: mapInstance.current,
    //       position: driverPosition,
    //       icon_url: '/icons/driver-icon.png',
    //       title: 'Delivery Agent',
    //     });
    //   }

    //   if (destinationPoint) {
    //     new mappls.Marker({
    //       map: mapInstance.current,
    //       position: destinationPoint,
    //       icon_url: '/icons/destination-icon.png',
    //       title: 'Delivery Location',
    //     });
    //   }

    //   // Show direction if both points are present
    //   if (driverPosition && destinationPoint) {
    //     mapInstance.current.on('load', () => {
    //       directionPluginRef.current = new mappls_plugin.direction({
    //         map: mapInstance.current,
    //         start: `${driverPosition.lat},${driverPosition.lng}`,
    //         end: `${destinationPoint.lat},${destinationPoint.lng}`,
    //         via: [],
    //         alternatives: false,
    //         steps: false,
    //         draggable: false,
    //         routeColor: '#007AFF',
    //       });
    //     });
    //   }
    // };

    return () => {
    //   if (mapInstance.current) {
    //     mapInstance.current.remove();
    //   }
      directionPluginRef.current = null;
    };
  }, [driverPosition, sellerPoint, destinationPoint]);

  const loadObject = {map: true, plugins: ["direction"]};

    const initializeMap = (accessToken: string) => {
        mapplsClassObject.initialize(accessToken, loadObject, async () => {
            let location = null;
            const newMap = mapplsClassObject.Map({
                id: "map",
                properties: {
                    center: driverPosition || { lat: 28.61, lng: 77.23 },
                    zoom: 12,
                    traffic: false,
                },
            });
        
            // Add markers
            if (sellerPoint) {
                mapplsClassObject.Marker({
                    map: mapInstance.current,
                    position: sellerPoint,
                    icon_url: '/icons/seller-icon.png',
                    title: 'Seller',
                });
            }

            if (driverPosition) {
                mapplsClassObject.Marker({
                    map: mapInstance.current,
                    position: driverPosition,
                    icon_url: '/icons/driver-icon.png',
                    title: 'Delivery Agent',
                });
            }

            if (destinationPoint) {
                mapplsClassObject.Marker({
                    map: mapInstance.current,
                    position: destinationPoint,
                    icon_url: '/icons/destination-icon.png',
                    title: 'Delivery Location',
                });
            }

            // Show direction if both points are present
            if (driverPosition && destinationPoint) {
                newMap.on('load', () => {
                directionPluginRef.current = newMap.direction({
                    map: mapInstance.current,
                    start: `${driverPosition.lat},${driverPosition.lng}`,
                    end: `${destinationPoint.lat},${destinationPoint.lng}`,
                    via: [],
                    alternatives: false,
                    steps: false,
                    draggable: false,
                    routeColor: '#007AFF',
                });
                });
            }
            // newMap.on("load", async () => {
            //     setIsMapLoaded(true);
            // });
            // mapRef.current = newMap;
        });
    }

    // 1. Generate Token on Component Mount
    useEffect(() => {
        getMapMeta().then((data: any) => {
            if (data?.access_token) {
                    mapplsToken.current = data.access_token;
                    setTimeout(() => {
                    initializeMap(data.access_token);
                }, 300);
            }
        });
    }, []);

  return <div ref={mapRef} style={{ width: '100%', height: '500px' }} />;
};

export default MapContainer;
