import React, {useEffect, useRef} from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';
import CachedIcon from '@mui/icons-material/Cached';
import axios from 'axios';
import {useDispatch, useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';
import {useLocation} from 'react-router-dom';
import {API_BASE_URL, ORDER_EVENT, ORDER_STATUS,} from '../../../../../../utils/apiActions';
import {emptyAlertCallback,} from '../../../../../../utils/utils';
import useNetworkHandling from '../../../../../../hooks/useNetworkHandling';
import {SSE_TIMEOUT} from '../../../../../../utils/constants';
import {makeButtonStyles} from './buttonStyles';
import {updateRequestingStatus} from '../../../../../../toolkit/reducer/order';
import useNetworkErrorHandling from '../../../../../../hooks/useNetworkErrorHandling';
import {useToast} from "../../../../../../hooks/toastProvider";
import {EventSourcePolyfill} from "event-source-polyfill";
import Lottie from 'lottie-react';

const getStatusAnimation = require('../../../../../../assets/order/post/get_status.json');

interface GetStatusButtonProps {
  onUpdateOrder: (value: any, selfUpdate: boolean) => void;
}

const GetStatusButton: React.FC<GetStatusButtonProps> = ({onUpdateOrder}) => {
  const {showToast} = useToast();
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {orderDetails, requestingStatus} = useSelector((state: any) => state.order);
  const backTriggered = useRef<boolean>(false);
  const source = useRef<any>(null);
  const {token} = useSelector(({auth}: any) => auth);
  const eventTimeOutRef = useRef<{ eventSource: EventSource; timer: NodeJS.Timeout } | null>(null);
  const statusEventSourceResponseRef = useRef<any[]>([]);
  const {getDataWithAuth, postDataWithAuth} = useNetworkHandling();
  const styles = makeButtonStyles();
  const {handleApiError} = useNetworkErrorHandling();
  const location = useLocation();

  const clearEventTimer = () => {
    if (eventTimeOutRef.current) {
      eventTimeOutRef.current.eventSource.close();
      clearTimeout(eventTimeOutRef.current.timer);
      eventTimeOutRef.current = null;
    }
  };

  const handleFetchUpdatedStatus = async (selfUpdate = false) => {
    statusEventSourceResponseRef.current = [];
    if (!selfUpdate) {
      dispatch(updateRequestingStatus(true));
    }
    source.current = axios.CancelToken.source();
    const transaction_id = orderDetails?.transactionId;
    const bpp_id = orderDetails?.bppId;
    const order_id = orderDetails?.id;
    try {
      const {data} = await postDataWithAuth(
        `${API_BASE_URL}${ORDER_STATUS}`,
        [
          {
            context: {transaction_id, bpp_id},
            message: {order_id},
          },
        ],
        source.current.token
      );
      if (data[0]?.error && data[0]?.message?.ack?.status === 'NACK') {
        dispatch(updateRequestingStatus(false));
        showToast(data[0].error.message, 'error');
      } else {
        fetchStatusDataThroughEvents(data[0]?.context?.message_id, selfUpdate);
      }
    } catch (err) {
      console.log(err);
      handleApiError(err);
      dispatch(updateRequestingStatus(false));
    }
  };

  const getUpdatedStatus = async (messageId: string, selfUpdate: boolean) => {
    try {
      source.current = axios.CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}/clientApis/v2/on_order_status?messageIds=${messageId}`,
        source.current.token
      );
      statusEventSourceResponseRef.current.push(data[0]);
      const {message, error} = data[0] || {};
      if (error?.message) {
        showToast(t('Orders.Cannot get status for this product'), 'error');
        dispatch(updateRequestingStatus(false));
        return;
      }
      if (message?.order) {
        onUpdateOrder(message?.order, selfUpdate);
        if (!selfUpdate) {
          showToast(t('Profile.Order status updated successfully'), 'info');
        }
      }
      dispatch(updateRequestingStatus(false));
    } catch (err) {
      dispatch(updateRequestingStatus(false));
      handleApiError(err);
      clearEventTimer();
    }
  };

  const fetchStatusDataThroughEvents = (messageId: string, selfUpdate: boolean) => {
    const eventSource = new EventSourcePolyfill(`${API_BASE_URL}${ORDER_EVENT}${messageId}`, {
      headers: {Authorization: `Bearer ${token}`},
    });

    eventSource.addEventListener('on_status', (event: any) => {
      if (!backTriggered.current) {
        const data = JSON.parse(event.data);
        getUpdatedStatus(data.messageId, selfUpdate).then(emptyAlertCallback);
      }
    });

    const timer = setTimeout(() => {
      eventSource.close();
      if (statusEventSourceResponseRef.current.length <= 0 && !selfUpdate) {
        showToast(t('Global.Unable to fetch details. Please try again'), 'error');
        dispatch(updateRequestingStatus(false));
      }
    }, SSE_TIMEOUT);

    eventTimeOutRef.current = {eventSource, timer};
  };

  useEffect(() => {
    const interval = setInterval(() => {
      handleFetchUpdatedStatus(true).then(emptyAlertCallback);
    }, SSE_TIMEOUT);

    return () => clearInterval(interval);
  }, [location.pathname]);

  useEffect(() => {
    return () => {
      backTriggered.current = true;
      clearEventTimer();
      source.current?.cancel();
    };
  }, []);

  if(requestingStatus){
    return (
      <Box className={styles.container}>
        <Lottie
          animationData={getStatusAnimation}
          autoPlay
          loop
          style={{width: 24, height: 24}}
        />
      </Box>
    )
  }

  return (
    <IconButton
      sx={{
        width: 28,
        height: 28,
        backgroundColor: "rgba(255, 255, 255, 0.15)",
        padding: 0,
        borderRadius: 1,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
      }}
      disabled={requestingStatus}
      onClick={() => handleFetchUpdatedStatus()}
    >
      {requestingStatus ? (
        <CircularProgress size={20}/>
      ) : (
        <CachedIcon className={styles.refreshButton}/>
      )}
    </IconButton>
  );
};

export default GetStatusButton;
