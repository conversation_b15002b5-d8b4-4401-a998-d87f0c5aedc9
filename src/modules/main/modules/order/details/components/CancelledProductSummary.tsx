import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import {useTranslation} from 'react-i18next';
import {CURRENCY_SYMBOLS} from '../../../../../../utils/constants';
import {isItemCustomization} from '../../../../../../utils/utils';
import useFormatNumber from '../../../../hooks/useFormatNumber';

const CancelledProductSummary = ({items}: { items: any[] }) => {
  const {formatNumber} = useFormatNumber();
  const {t} = useTranslation();
  const styles = useStyles();

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="headlineSmall" className={styles.sectionTitle}>
          {t('Product Summary.Your Items')}
        </Typography>
      </Box>
      {items.map((item, index) => {
        const itemCustomization = isItemCustomization(item.tags);

        if (itemCustomization || item?.quantity?.count === 0) {
          return <Box key={`${item.id}${item?.fulfillment_id}${index}`}/>;
        }

        const currency = CURRENCY_SYMBOLS[item?.product?.price?.currency || ''];
        return (
          <Box
            key={`${item.id}${item?.fulfillment_id}${index}`}
            className={`${styles.item} ${index > 0 ? styles.itemBorderTop : ''}`}
          >
            <Typography variant="body1" className={styles.itemName}>
              {item?.product?.descriptor?.name}
            </Typography>
            <Box className={styles.itemQuantityContainer}>
              <Typography variant="body1" className={styles.quantity}>
                {item?.quantity?.count} X {currency}
                {formatNumber(Number(item?.product?.subtotal).toFixed(2))}
              </Typography>
              <Typography variant="labelSmall" className={styles.quantity}>
                {currency}
                {formatNumber(
                  Number(item?.quantity?.count * (item?.product?.subtotal || 0)).toFixed(2)
                )}
              </Typography>
            </Box>
          </Box>
        );
      })}
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 8,
    backgroundColor: theme.palette.common.white,
    border: `1px solid ${theme.palette.grey[300]}`,
    margin: '12px 16px',
    padding: '16px',
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  sectionTitle: {
    fontWeight: 'bold'
  },
  item: {
    padding: '12px 0',
  },
  itemBorderTop: {
    borderTop: `1px solid ${theme.palette.grey[200]}`,
  },
  itemQuantityContainer: {
    marginTop: 4,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemName: {
    flex: 1,
  },
  quantity: {
    color: theme.palette.grey[800],
  },
}));

export default CancelledProductSummary;
