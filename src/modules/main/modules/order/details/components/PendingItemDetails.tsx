import React, {useEffect, useState} from "react";
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import {SingleItem} from "./ItemDetails";
import Box from "@mui/material/Box";

const PendingItemDetails = ({
                              fulfillments,
                              items,
                              quoteBreakup,
                              itemCharges,
                            }: {
  fulfillments: any[];
  items: any[];
  quoteBreakup: any;
  itemCharges: any[];
}) => {
  const {t} = useTranslation();
  const [shipmentFulfillmentList, setShipmentFulfillmentList] = useState<any[]>(
    []
  );

  useEffect(() => {
    setShipmentFulfillmentList(
      fulfillments.filter((fulfillment: any) => fulfillment.type === "Delivery")
    );
  }, [fulfillments]);

  const filterCustomizations = (tags: any) =>
    tags?.some(
      (tag: any) =>
        tag?.code === "type" &&
        tag?.list?.some(
          (item: any) => item?.code === "type" && item?.value === "customization"
        )
    );

  return (
    <>
      {shipmentFulfillmentList.length > 0 && (
        <Box>
          <Typography variant="titleLarge" sx={styles.fulfilmentTitle}>
            {t("Profile.Shipment Details")}
          </Typography>
          {shipmentFulfillmentList.map((fulfillment: any) => {
            const filteredItems = items.filter(
              (item) => item.fulfillment_id === fulfillment.id
            );
            const customizations = filteredItems.filter((obj) =>
              filterCustomizations(obj?.tags)
            );

            return (
              <Box key={fulfillment.id} sx={styles.container}>
                {filteredItems.map((item, index) =>
                  item?.quantity?.count > 0 ? (
                    <SingleItem
                      key={`${item.id}${index}ShipmentFulfillment`}
                      item={item}
                      customizations={customizations}
                      quoteBreakup={quoteBreakup}
                      itemCharges={itemCharges}
                    />
                  ) : (
                    <Box key={`${item.id}${index}ShipmentFulfillment`}/>
                  )
                )}
              </Box>
            );
          })}
        </Box>
      )}
    </>
  );
};

const styles = {
  container: {
    borderRadius: "8px",
    backgroundColor: "#FFFFFF",
    border: "1px solid #E0E0E0",
    marginX: "16px",
    paddingX: "16px",
    paddingTop: "16px",
    marginTop: "12px",
    paddingBottom: "8px",
  },
  fulfilmentTitle: {
    marginTop: "20px",
    color: "#757575",
    paddingX: "16px",
  },
};

export default PendingItemDetails;
