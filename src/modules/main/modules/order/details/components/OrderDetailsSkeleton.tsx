import React from "react";
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';

const OrderDetailsSkeleton: React.FC = () => {
  return (
    <Box>
      <Skeleton variant="rectangular" width="100%" height={100} sx={{mb: 2}}/>
      <Skeleton variant="text" width={200} height={24} sx={{mb: 1.5, mx: 2}}/>
      <Skeleton variant="rectangular" width="calc(100% - 32px)" height={86} sx={{mb: 2, mx: 2}}/>
      <Skeleton variant="text" width={200} height={24} sx={{mb: 1.5, mx: 2}}/>
      <Skeleton variant="rectangular" width="calc(100% - 32px)" height={230} sx={{mb: 2, mx: 2}}/>
      <Skeleton variant="rectangular" width="calc(100% - 32px)" height={230} sx={{mb: 2, mx: 2}}/>
      <Skeleton variant="rectangular" width="calc(100% - 32px)" height={230} sx={{mb: 2, mx: 2}}/>
    </Box>
  );
};

export default OrderDetailsSkeleton;
