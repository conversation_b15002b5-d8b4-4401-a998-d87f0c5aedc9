import React from "react"
import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import ArrowBackIcon from "@mui/icons-material/ArrowBack"
import { useSelector } from "react-redux"
import { useTranslation } from "react-i18next"
import ProviderDetails from "./ProviderDetails"
import PaymentMethod from "./PaymentMethod"
import PendingPaymentConfirmation from "./PendingPaymentConfirmation"
import PendingItemDetails from "./PendingItemDetails"
import OrderSummary from "./OrderSummary"
import FacingIssues from "./FacingIssues"
import OrderSummaryWithSubsidy from "./OrderSummaryWithSubsidy"
import useBackHandler from "../../../../hooks/useBackHandler"
import RaiseIssueButton from "./RaiseIssueButton"

const PendingOrder = ({
  draft,
  getOrderDetails,
}: {
  draft: boolean
  getOrderDetails: (selfUpdate?: boolean) => void
}) => {
  const { t } = useTranslation()
  const { orderDetails } = useSelector(({ order }: any) => order)
  const { goBack } = useBackHandler()

  return (
    <Box sx={styles.orderDetails}>
      {/* Header */}
      <Box sx={styles.header}>
        <Box sx={styles.headerRow}>
          <IconButton onClick={goBack} sx={{ color: "white" }}>
            <ArrowBackIcon />
          </IconButton>
          <Typography variant="titleLarge" sx={styles.orderDetailsTitle} noWrap>
            {orderDetails?.provider?.descriptor?.name}
          </Typography>
          <Box sx={styles.empty} />
        </Box>
        <Typography variant="labelLarge" sx={styles.orderStatus}>
          {draft
            ? t("Order Details.Order Draft")
            : t("Order Details.Order Confirmation is Pending")}
        </Typography>
      </Box>

      {/* Content */}
      <Box maxWidth="md" sx={styles.pageContainer}>
        <PendingPaymentConfirmation draft={draft} />
        <Typography variant="headlineSmall" sx={styles.orderId}>
          {draft
            ? orderDetails?.transactionId?.split("-")?.pop()
            : orderDetails?.id}
        </Typography>

        <ProviderDetails
          provider={orderDetails?.provider}
          bppId={orderDetails?.bppId}
          domain={orderDetails?.domain}
          documents={[]}
          fulfillments={orderDetails?.fulfillments}
        />

        <PendingItemDetails
          items={orderDetails?.items}
          fulfillments={orderDetails?.fulfillments}
          quoteBreakup={
            orderDetails?.updatedQuote
              ? orderDetails?.updatedQuote.breakup
              : orderDetails?.quote?.breakup
          }
          itemCharges={orderDetails?.charges?.quote?.itemsList ?? []}
        />

        <PaymentMethod
          payment={orderDetails?.payment}
          address={
            orderDetails?.fulfillments[0]?.end?.location?.address ??
            orderDetails?.billing?.address
          }
          contact={orderDetails?.fulfillments[0]?.end?.contact}
        />

        {orderDetails?.charges ? (
          <OrderSummaryWithSubsidy charges={orderDetails?.charges?.quote} />
        ) : (
          <OrderSummary
            quote={orderDetails?.updatedQuote ?? orderDetails?.quote}
            coupon={orderDetails?.coupon}
          />
        )}
        <FacingIssues />
        <RaiseIssueButton getOrderDetails={getOrderDetails} />
      </Box>
    </Box>
  )
}

const styles = {
  orderDetails: {
    flex: 1,
  },
  header: {
    backgroundColor: "#1976D2",
    paddingBottom: "14px",
  },
  headerRow: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingX: "16px",
    paddingY: "12px",
  },
  orderDetailsTitle: {
    color: "white",
    textAlign: "center",
    flexShrink: 1,
  },
  orderStatus: {
    color: "white",
    textAlign: "center",
  },
  empty: {
    width: "24px",
    height: "24px",
  },
  pageContainer: {
    flex: 1,
    backgroundColor: "#FAFAFA",
    paddingBottom: "16px",
  },
  orderId: {
    marginTop: "20px",
    color: "#757575",
    paddingLeft: "16px",
  },
}

export default PendingOrder
