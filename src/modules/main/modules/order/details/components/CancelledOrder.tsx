import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import { useSelector } from "react-redux"
import ProviderDetails from "./ProviderDetails"
import OrderMeta from "./OrderMeta"
import useBackHandler from "../../../../hooks/useBackHandler"
import CancelledProductSummary from "./CancelledProductSummary"
import OrderSummaryWithSubsidy from "./OrderSummaryWithSubsidy"
import ArrowBackIcon from "@mui/icons-material/ArrowBackRounded"
import RaiseIssueButton from "./RaiseIssueButton"

const CancelledOrder = ({
  failed,
  getOrderDetails,
}: {
  failed: boolean
  getOrderDetails: (selfUpdate?: boolean) => void
}) => {
  const { goBack } = useBackHandler()
  const { orderDetails } = useSelector((state: any) => state.order)

  const styles = useStyles()

  return (
    <Box className={styles.orderDetails}>
      <Box className={styles.header}>
        <Button onClick={goBack} className={styles.backButton}>
          <ArrowBackIcon fontSize="small" />
        </Button>
        <Typography variant="titleMedium" className={styles.orderDetailsTitle}>
          {orderDetails?.id
            ? orderDetails?.id
            : orderDetails?.transactionId?.split("-")?.pop()}
        </Typography>
      </Box>
      <Box className={styles.pageContainer}>
        <ProviderDetails
          provider={orderDetails?.provider}
          bppId={orderDetails?.bppId}
          domain={orderDetails?.domain}
          cancelled
          failed={failed}
          documents={orderDetails?.documents}
          fulfillments={orderDetails?.fulfillments}
        />
        <CancelledProductSummary items={orderDetails?.items} />
        {orderDetails?.charges && (
          <OrderSummaryWithSubsidy charges={orderDetails?.charges?.quote} />
        )}
        <OrderMeta />
        <RaiseIssueButton getOrderDetails={getOrderDetails} />
      </Box>
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  orderDetails: {
    display: "flex",
    flexDirection: "column",
    height: "100%",
  },
  header: {
    display: "flex",
    alignItems: "center",
    backgroundColor: theme.palette.background.paper,
    padding: "16px 8px",
  },
  backButton: {
    minWidth: "auto",
  },
  orderDetailsTitle: {
    marginLeft: theme.spacing(2.5),
    textTransform: "none",
  },
  pageContainer: {
    flex: 1,
    height: "100dvh",
    overflow: "scroll",
  },
}))

export default CancelledOrder
