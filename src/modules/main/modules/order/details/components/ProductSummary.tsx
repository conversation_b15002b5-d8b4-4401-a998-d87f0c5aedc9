import {useMemo} from "react";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";
import {useNavigate} from "react-router-dom";
import {CURRENCY_SYMBOLS} from "../../../../../../utils/constants";
import {isItemCustomization} from "../../../../../../utils/utils";
import useFormatNumber from "../../../../hooks/useFormatNumber";
import {makeStyles} from '@mui/styles';

const ProductSummary = ({items, quote, fulfilment, itemCharges}: {
  items: any[];
  quote: any;
  fulfilment: any;
  itemCharges: any[]
}) => {
  const {formatNumber} = useFormatNumber();
  const {t} = useTranslation();
  const navigate = useNavigate();
  const {orderDetails} = useSelector(({order}: any) => order);
  const styles = useStyles();
  const filterCustomizations = (tags: any) =>
    tags?.some(
      (tag: any) =>
        tag?.code === 'type' &&
        tag?.list?.some(
          (childItem: any) =>
            childItem?.code === 'type' && childItem?.value === 'customization',
        ),
    );
  const filteredItems = useMemo(() => items?.filter((one) => one?.quantity?.count > 0) || [], [items]);

  let itemsTotal = 0;

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="headlineSmall" className={styles.sectionTitle}>
          {t("Product Summary.Your Items")}
        </Typography>
      </Box>

      {filteredItems.map((item, index) => {
        const cancellable = item?.product['@ondc/org/cancellable'];
        const returnable = item?.product['@ondc/org/returnable'];
        const itemCustomization = isItemCustomization(item.tags);

        if (itemCustomization) {
          return <Box key={item.id}/>;
        }

        const customizations = items?.filter(obj =>
          filterCustomizations(obj?.tags),
        );

        const associatedItems = items.filter((one: any) => {
          return (
            one?.product?.parent_item_id === item?.product?.parent_item_id &&
            one.id !== item.id
          );
        });

        let returnedCount = 0;
        const returnFulfilmentList = orderDetails?.fulfillments?.filter(
          (one: any) =>
            one.type === 'Return' &&
            one.state?.descriptor?.code === 'Return_Initiated',
        );
        returnFulfilmentList.forEach((fulfillment: any) => {
          const returnTag = fulfillment.tags.find(
            (tag: any) => tag.code === 'return_request',
          );
          const itemTag = returnTag?.list?.find(
            (tag: any) => tag.code === 'item_id',
          );
          const itemQuantityTag = returnTag?.list?.find(
            (tag: any) => tag.code === 'item_quantity',
          );
          if (itemTag && itemQuantityTag) {
            if (itemTag.value === item.id) {
              returnedCount += Number(itemQuantityTag.value);
            }
          }
        });

        let itemPrice = item?.quantity?.count * item?.product?.subtotal;
        let itemBasePrice = item?.product?.subtotal;
        if (quote?.breakup) {
          const filteredQuote = quote?.breakup?.filter(
            (one: any) => one['@ondc/org/item_id'] === item.id,
          );
          if (filteredQuote) {
            itemPrice = filteredQuote.reduce((sum: number, one: any) => {
              const priceValue = parseFloat(one.price.value);
              return sum + priceValue;
            }, 0);
          }
        }
        if (itemCharges?.length > 0) {
          const chargedItem = itemCharges.find(one => one.itemId === item.id);
          itemBasePrice = chargedItem.discountedBasePrice;
          itemPrice = Number(
            item?.quantity?.count * chargedItem.discountedBasePrice,
          );
        }
        itemsTotal += itemPrice;

        return (
          <Box key={item.id} className={`${styles.item} ${index > 0 ? styles.itemBorderTop : ""}`}>
            <Box className={styles.itemMeta}>
              <img src={item?.product?.descriptor?.symbol} alt="Product" className={styles.itemImage}/>
              <Box className={styles.itemDetails}>
                <Box className={styles.itemHeader}>
                  <Typography variant="bodyLarge" component="div" className={styles.itemName}>
                    {item?.product?.descriptor?.name}
                  </Typography>
                  <Box className={styles.itemQuantityContainer}>
                    <Typography variant="labelSmall" className={styles.quantity}>
                      {t("Fulfillment.Qty")} {item?.quantity?.count}
                    </Typography>
                    <Typography variant="labelLarge" component="div" className={styles.itemPrice}>
                      {CURRENCY_SYMBOLS[item?.product?.price?.currency]}
                      {formatNumber(Number(itemPrice).toFixed(2))}
                    </Typography>
                  </Box>
                </Box>

                {item.product?.quantity?.unitized &&
                  Object.keys(item.product?.quantity?.unitized).map((one) => (
                    <Typography variant="labelSmall" className={styles.label} key={one}>
                      {item.product?.quantity?.unitized[one].value} {item.product?.quantity?.unitized[one].unit}
                    </Typography>
                  ))}

                <Box className={styles.chipContainer}>
                  <Box className={styles.chip}>
                    <Typography variant="labelSmall" className={styles.chipLabel}>
                      {cancellable ? t("Profile.Cancellable") : t("Profile.Non-cancellable")}
                    </Typography>
                  </Box>
                  <Box className={styles.chip}>
                    <Typography variant="labelSmall" className={styles.chipLabel}>
                      {returnable ? t("Profile.Returnable") : t("Non-returnable")}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Box>

            {returnable &&
              (fulfilment?.state?.descriptor?.code === "Order-delivered" || fulfilment?.state?.descriptor?.code === "Completed") &&
              item.quantity?.count > 0 && (
                <Button
                  variant="outlined"
                  className={styles.returnItem}
                  onClick={() =>
                    navigate("/return-item", {
                      state: {
                        item,
                        maxReturnCount: item.quantity?.count,
                        associatedItems,
                        providerId: orderDetails?.provider?.id,
                        state: orderDetails?.state,
                        orderId: orderDetails?.id,
                        id: orderDetails?._id,
                        bppId: orderDetails?.bppId,
                        bppUrl: orderDetails?.bpp_uri,
                        transactionId: orderDetails?.transactionId,
                        itemBasePrice: itemBasePrice,
                      },
                    })
                  }
                >
                  {t("Return Item.Return Item")}
                </Button>
              )}
          </Box>
        );
      })}

      <Divider className={styles.divider}/>
      <Box className={styles.grossTotal}>
        <Typography variant="headlineSmall" className={styles.grossTotalLabel}>
          {t("Fulfillment.Item Total")}
        </Typography>
        <Typography variant="headlineSmall" className={styles.grossTotalValue}>
          {CURRENCY_SYMBOLS[quote?.price?.currency]}
          {formatNumber(Number(itemsTotal).toFixed(2))}
        </Typography>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 2,
    backgroundColor: "#fff",
    border: "1px solid #E0E0E0",
    padding: "16px",
    marginTop: "12px",
  },
  header: {
    display: "flex",
    alignItems: "center",
    justifyContent: "space-between",
  },
  sectionTitle: {
    color: theme.palette.text.primary,
  },
  item: {
    padding: "12px 0 0",
  },
  itemBorderTop: {
    borderTop: "1px solid #E0E0E0",
  },
  itemImage: {
    width: "32px",
    height: "32px",
    borderRadius: "8px",
    marginRight: "10px",
  },
  itemMeta: {
    display: "flex",
    alignItems: "center",
    marginBottom: "8px",
  },
  itemDetails: {
    flex: 1,
  },
  itemHeader: {
    display: "flex",
    justifyContent: "space-between",
  },
  chipContainer: {
    display: "flex",
    marginTop: "4px",
  },
  returnItem: {
    borderRadius: "8px !important",
    marginTop: "12px !important",
    borderColor: theme.palette.primary.main + '!important',
    color: theme.palette.primary.main + '!important',
    width: "100% !important"
  },
  chip: {
    marginRight: "4px",
    backgroundColor: "#E0E0E0",
    padding: "2px 8px",
    borderRadius: "22px",
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: theme.palette.neutral.main,
  },
  chipLabel: {
    color: theme.palette.neutral.main,
  },
  itemQuantityContainer: {
    display: "flex",
    alignItems: "center",
  },
  itemName: {
    color: "#424242",
  },
  quantity: {
    color: theme.palette.text.primary,
    paddingRight: '10px'
  },
  itemPrice: {
    color: theme.palette.text.primary,
    marginLeft: "12px",
  },
  divider: {
    margin: "20px 0 !important",
  },
  grossTotal: {
    display: "flex",
    justifyContent: "space-between",
    marginBottom: "12px",
    marginTop: "16px",
  },
  grossTotalLabel: {
    color: theme.palette.text.primary,
  },
  grossTotalValue: {
    color: theme.palette.primary.main,
  },
  label: {
    color: theme.palette.text.primary,
  },
}));

export default ProductSummary;
