import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import moment from 'moment';
import {useTranslation} from 'react-i18next';
import {useTheme} from '@mui/material/styles';
import useFormatDate from '../../../../hooks/useFormatDate';

const return_end_states = [
  'Return_Delivered',
  'Liquidated',
  'Return_Rejected',
  'Return_Failed',
];

const FulfilmentStatus = ({
                            code,
                            fulfilment,
                          }: {
  code: string;
  fulfilment?: any;
}) => {
  const {formatDate} = useFormatDate();
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);

  const returnPresent = return_end_states.includes(code);

  return (
    <Box className={styles.statusChip}>
      <Typography variant="labelSmall" className={styles.statusText}>
        {code ? t(`${code}`) : ''}{' '}
        {returnPresent
          ? t('on time', {
            time: formatDate(moment(fulfilment?.updatedAt), 'Do MMM'),
          })
          : ''}
      </Typography>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  statusChip: {
    borderRadius: 26,
    padding: '2px 8px',
    backgroundColor: theme.palette.primary50,
  },
  statusText: {
    color: theme.palette.primary.main,
    textTransform: 'none'
  },
}));

export default FulfilmentStatus;
