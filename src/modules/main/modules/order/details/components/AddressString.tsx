import React from 'react';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';

const AddressString = ({address}: { address: any }) => {
  const styles = useStyles();

  return (
    <Typography variant='body2' className={styles.normalText}>
      {address?.building}, {address?.locality}, {address?.city}, {address?.state}, {address?.country} - {address?.area_code}
    </Typography>
  );
};

const useStyles = makeStyles((theme: any) => ({
  normalText: {
    color: theme.palette.neutral300,
    marginBottom: '4px',
  },
}));

export default AddressString;
