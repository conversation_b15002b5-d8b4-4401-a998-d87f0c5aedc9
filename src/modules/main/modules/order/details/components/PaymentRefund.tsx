import Typography from '@mui/material/Typography';
import CurrencyExchangeIcon from "@mui/icons-material/CurrencyExchangeRounded";
import {useTranslation} from "react-i18next";
import {useSelector} from "react-redux";
import {makeStyles} from '@mui/styles';
import Box from "@mui/material/Box";

const PaymentRefund = () => {
  const {t} = useTranslation();
  const {orderDetails} = useSelector((state: any) => state.order);
  const styles = useStyles()
  return (
    <Box className={styles.container}>
      <CurrencyExchangeIcon className={styles.icon}/>
      <Box className={styles.meta}>
        <Typography variant="body1" className={styles.title}>
          {t("Order Details.Payment Refund")}
        </Typography>
        <Typography variant="caption" className={styles.message}>
          {orderDetails?.technicalCancellation
            ? t("Order Details.We could not place your order")
            : t("Order Details.Your order has been cancelled")}
        </Typography>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    backgroundColor: theme.palette.primary50,
    padding: "10px",
    borderRadius: "8px",
    display: "flex",
    flexDirection: "row",
    gap: "8px",
  },
  meta: {
    flex: 1,
  },
  icon: {
    marginTop: 4
  },
  message: {
    marginTop: "4px",
  },
}));

export default PaymentRefund;
