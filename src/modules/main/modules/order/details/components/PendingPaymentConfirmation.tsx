import React from "react";
import Typography from '@mui/material/Typography';
import WarningAmberIcon from "@mui/icons-material/WarningAmber";
import {useTranslation} from "react-i18next";
import Box from "@mui/material/Box";

const PendingPaymentConfirmation = ({draft}: { draft: boolean }) => {
  const {t} = useTranslation();

  return (
    <Box sx={styles.container}>
      <WarningAmberIcon sx={{color: "#FFA726", fontSize: 24}}/>
      <Typography variant="body1" sx={styles.label}>
        {draft
          ? t("Orders.Processing Payment Message")
          : t("Order Details.Please wait, we are confirming your order")}
      </Typography>
    </Box>
  );
};

const styles = {
  container: {
    backgroundColor: "#FFF3E0", // Light warning background
    border: "1px solid #E0E0E0",
    borderRadius: "8px",
    padding: "10px 16px",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: "8px",
    marginX: "16px",
    marginTop: "20px",
  },
  label: {
    color: "#757575", // Neutral gray color
    flex: 1,
  },
};

export default PendingPaymentConfirmation;
