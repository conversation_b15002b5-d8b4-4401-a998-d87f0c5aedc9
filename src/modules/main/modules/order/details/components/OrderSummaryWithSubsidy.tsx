import React, {useMemo, useState} from "react"
import Box from "@mui/material/Box"
import Divider from "@mui/material/Divider"
import Paper from "@mui/material/Paper"
import Typography from "@mui/material/Typography"
import {useTranslation} from "react-i18next"
import useFormatNumber from "../../../../hooks/useFormatNumber"
import SellerFee from "../../../cart/provider/components/SellerFee"
import DeliveryCharges from "../../../cart/provider/components/DeliveryCharges"
import Taxes from "../../../cart/provider/components/Taxes"
import Items from "../../../cart/provider/components/Items"
import {makeStyles} from "@mui/styles"
import Modal from "@mui/material/Modal"

interface OrderSummaryWithSubsidyProps {
  charges: any
}

const OrderSummaryWithSubsidy: React.FC<OrderSummaryWithSubsidyProps> = ({
                                                                           charges,
                                                                         }) => {
  const {formatNumber} = useFormatNumber()
  const {t} = useTranslation()
  const styles = useStyles()

  const {orderTotal, discount} = useMemo(() => {
    let totalDiscount = Math.abs(charges?.totalProductDiscount) ?? 0;
    if (charges?.offer?.benefit) {
      totalDiscount += Math.abs(charges?.offer?.benefit);
    }
    return {
      orderTotal: Number(charges?.totalOrderValueAfterSubsidy),
      discount: totalDiscount,
    };
  }, [charges]);

  const [modalDetails, setModalDetails] = useState<any>({
    isVisible: false,
    children: <Box/>,
  });

  return (
    <Paper elevation={1} className={styles.container}>
      <Box className={styles.header}>
        <Typography variant="titleMedium" className={styles.sectionTitle}>
          {t("Product Summary.Order Summary")}
        </Typography>
      </Box>
      <Items
        itemsList={charges.itemsList}
        setModalDetails={setModalDetails}
      />
      <SellerFee
        sellerFees={charges.sellerFees}
        setModalDetails={setModalDetails}
      />
      <DeliveryCharges
        deliveryCharges={charges.combinedDeliveryItemList}
        setModalDetails={setModalDetails}
      />

      {Number(charges?.platformFees) > 0 && (
        <Box
          sx={{display: "flex", justifyContent: "space-between"}}
        >
          <Typography variant="labelSmall" color="black">
            {t("Fulfillment.Platform Fee")}
          </Typography>
          <Typography variant="labelSmall" color="black">
            ₹
            {formatNumber(Number(charges?.platformFees).toFixed(2))}
          </Typography>
        </Box>
      )}
      <Taxes taxes={charges.taxes} setModalDetails={setModalDetails}/>

      {discount > 0 && (
        <Box
          sx={{display: "flex", justifyContent: "space-between"}}
        >
          <Typography variant="labelSmall" color="black">
            {t("Fulfillment.Discount")}
          </Typography>
          <Typography variant="labelSmall" color="success.main">
            -₹{formatNumber(discount.toFixed(2))}
          </Typography>
        </Box>
      )}

      <Divider className={styles.divider}/>

      <Box className={styles.grossTotal}>
        <Typography variant="titleMedium" className={styles.grossTotalLabel}>
          {t("Fulfillment.Order Total")}
        </Typography>
        <Typography variant="headlineSmall" className={styles.grossTotalValue}>
          ₹{formatNumber(Number(orderTotal).toFixed(2))}
        </Typography>
      </Box>
      <Modal
        open={modalDetails.isVisible}
        onClose={() => setModalDetails({isVisible: false, children: <Box/>})}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        {modalDetails.children}
      </Modal>
    </Paper>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    borderRadius: 8,
    backgroundColor: "#fff",
    border: `1px solid ${theme.palette.grey[300]}`,
    margin: "12px 16px",
    padding: "16px",
  },
  header: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    mb: 1,
  },
  summaryRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    mb: 1,
  },

  divider: {
    marginTop: 12,
    marginBottom: 12,
  },
  grossTotal: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginTop: 10,
  },
  grossTotalValue: {
    color: theme.palette.primary.main,
    fontWeight: "bold",
  },
  sectionTitle: {
    fontWeight: "bold",
  },
}))

export default OrderSummaryWithSubsidy
