import React from 'react';
import {useNavigate} from 'react-router-dom';
import IconButton from '@mui/material/IconButton';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import {makeStyles} from '@mui/styles';
import Box from '@mui/material/Box';
import SafeAreaPage from '../../../../components/page/SafeAreaPage';
import GlobalError from '../../../../components/globalError/GlobalError';

const RefreshContainer = ({onRefreshPress}: { onRefreshPress: () => void }) => {
  const navigate = useNavigate();
  const classes = useStyles();

  return (
    <SafeAreaPage>
      <Box className={classes.header}>
        <IconButton onClick={() => navigate(-1)}>
          <ArrowBackIcon className={classes.icon}/>
        </IconButton>
      </Box>
      <GlobalError onRefreshPress={onRefreshPress}/>
    </SafeAreaPage>
  );
};

const useStyles = makeStyles({
  header: {
    padding: '14px 16px',
  },
  icon: {
    color: '#9e9e9e',
  },
});

export default RefreshContainer;
