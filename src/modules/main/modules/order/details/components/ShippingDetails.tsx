import CheckCircleIcon from "@mui/icons-material/CheckCircleRounded"
import ExpandLessIcon from "@mui/icons-material/ExpandLessRounded"
import ExpandMoreIcon from "@mui/icons-material/ExpandMoreRounded"
import Box from "@mui/material/Box"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import axios from "axios"
import moment from "moment"
import { useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { useSearchParams } from "react-router-dom"
import useNetworkErrorHandling from "../../../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../../../hooks/useNetworkHandling"
import {
  updateOrderDetails,
  updateRequestingStatus,
  updateRequestingTracker,
} from "../../../../../../toolkit/reducer/order"
import {
  API_BASE_URL,
  GET_ISSUE,
  ORDERS,
} from "../../../../../../utils/apiActions"
import { CANCEL_ORDER_STATUS } from "../../../../../../utils/constants"
import { emptyAlertCallback } from "../../../../../../utils/utils"
import useFormatDate from "../../../../hooks/useFormatDate"
const CancelToken = axios.CancelToken
const ShippingDetails = ({ fullfillmentId }: { fullfillmentId: string }) => {
  const { formatDate } = useFormatDate()
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const styles = useStyles()
  const [showDetails, setShowDetails] = useState<boolean>(true)
  const { orderDetails } = useSelector(({ order }: any) => order)
  const [urlParams] = useSearchParams()
  const { getDataWithAuth } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const source = useRef<any>(null)
  const orderId = urlParams.get("id")
  const { deliveredFulfilmentLength, fulfilmentIndex, fulfilment } =
    useMemo(() => {
      const filteredFulfillment = orderDetails?.fulfillments?.filter(
        (one: any) => one.type === "Delivery"
      )
      const index = filteredFulfillment?.findIndex(
        (one: any) => one.id === fullfillmentId
      )

      return {
        deliveredFulfilmentLength: filteredFulfillment?.length,
        fulfilmentIndex: index,
        fulfilment: filteredFulfillment ? filteredFulfillment[index] : null,
      }
    }, [orderDetails?.fulfillments, fullfillmentId])

  useEffect(() => {
    dispatch(updateRequestingStatus(false))
    dispatch(updateRequestingTracker(false))
    dispatch(updateOrderDetails(null))
    getOrderDetails().then(emptyAlertCallback)

    return () => {
      if (source.current) {
        source.current.cancel()
      }
    }
  }, [orderId])

  const getComplaint = async (orderId: string): Promise<any[]> => {
    try {
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${GET_ISSUE}?orderId=${orderId}`,
        source.current.token
      )
      return data?.issues ?? []
    } catch (error: any) {
      handleApiError(error)
      return []
    }
  }

  const getOrderDetails = async (selfUpdate: boolean = false) => {
    try {
      if (!selfUpdate) {
        // setApiInProgress(true)
      }
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${ORDERS}/${orderId}`,
        source.current.token
      )
      const state = data[0].state
      if (state !== "Draft" && state !== "Processing" && state !== "Failed") {
        data[0].issues = await getComplaint(data[0].id)
      }
      dispatch(updateRequestingTracker(false))
      dispatch(updateOrderDetails(data[0]))
    } catch (err: any) {
      handleApiError(err)
    } finally {
      // setApiInProgress(false)
    }
  }

  const fulfillmentHistory = useMemo(() => {
    return orderDetails?.fulfillmentHistory.filter(
      (one: any) => one.id === fullfillmentId
    )
  }, [orderDetails?.fulfillmentHistory, fullfillmentId])

  const hasTypeTagWithItemValue = (tags: any[]) => {
    return tags?.some(
      (tag: any) =>
        tag?.code === "type" &&
        tag?.list?.some(
          (item: any) => item.code === "type" && item.value === "item"
        )
    )
  }

  const items = useMemo(() => {
    return orderDetails?.items
      ?.filter((obj: any) =>
        obj.hasOwnProperty("tags") ? hasTypeTagWithItemValue(obj.tags) : true
      )
      .filter(
        (one: any) =>
          one?.quantity?.count > 0 &&
          one?.cancellation_status !== CANCEL_ORDER_STATUS
      )
  }, [orderDetails?.items])

  return (
    <Box className={styles.shippingContainer}>
      <Typography
        variant="titleLarge"
        component="div"
        className={styles.shippingTitle}
      >
        {t("Shipment Details.Shipment Details")} ({fulfilmentIndex + 1}/
        {deliveredFulfilmentLength})
      </Typography>
      <Typography
        variant="bodyLarge"
        component="div"
        className={styles.shippingTitle}
      >
        {orderDetails?.state !== "Completed"
          ? t("Shipment Details.Items Arriving", {
              count: items?.length,
            })
          : t("Shipment Details.Items Delivered", {
              count: items?.length,
            })}
      </Typography>
      <Box
        className={styles.accordion}
        onClick={() => setShowDetails(!showDetails)}
      >
        <Box className={styles.accordionTitle}>
          <Typography variant="labelSmall" className={styles.arrivalLabel}>
            {orderDetails?.state !== "Completed"
              ? t("Shipment Details.Arriving On")
              : t("Shipment Details.Delivered On")}
            :
          </Typography>
          <Typography variant="labelSmall" className={styles.arrivalDate}>
            {formatDate(
              moment(
                orderDetails?.state !== "Completed"
                  ? fulfilment?.end?.time?.range?.end
                  : orderDetails?.updatedAt
              ),
              "DD-MM-YYYY"
            )}
          </Typography>
        </Box>
        <IconButton size="small">
          {showDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
        </IconButton>
      </Box>
      {showDetails &&
        fulfillmentHistory?.map((history: any, index: number) => (
          <Box key={history?._id} className={styles.statusContainer}>
            <Box className={styles.status}>
              <Box>
                <CheckCircleIcon
                  className={styles.successIcon}
                  fontSize="small"
                />
                {index < fulfillmentHistory?.length - 1 && (
                  <Box className={styles.link} />
                )}
              </Box>
              <Typography variant="labelSmall" className={styles.state}>
                {history?.state.replace(/-/g, " ")}
              </Typography>
            </Box>
            <Typography variant="labelSmall" className={styles.timestamp}>
              {formatDate(moment(history?.createdAt), "ddd, DD MMM hh:mm A")}
            </Typography>
          </Box>
        ))}
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  shippingContainer: {
    borderRadius: "8px !important",
    backgroundColor: "#FFF !important",
    border: `1px solid ${theme.palette.grey[200]} !important`,
    marginInline: "16px !important",
    padding: "16px 16px 8px !important",
    marginTop: "24px !important",
  },
  shippingTitle: {
    marginBottom: "12px !important",
    color: theme.palette.text.primary,
  },
  accordion: {
    display: "flex !important",
    alignItems: "center !important",
    justifyContent: "space-between !important",
    cursor: "pointer !important",
  },
  accordionTitle: {
    display: "flex !important",
    alignItems: "center !important",
    marginBottom: "12px !important",
  },
  arrivalLabel: {
    color: theme.palette.text.primary,
  },
  arrivalDate: {
    marginLeft: "8px !important",
    color: theme.palette.text.primary,
    fontWeight: "bold !important",
  },
  statusContainer: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  status: {
    display: "flex",
  },
  link: {
    height: 28,
    borderLeft: `2px solid ${theme.palette.success.main}`,
    marginLeft: 9,
  },
  state: {
    color: theme.palette.text.primary,
    paddingTop: 3,
    paddingLeft: 8,
    fontWeight: "bold !important",
  },
  timestamp: {
    paddingTop: 4,
    color: theme.palette.text.primary,
  },
  successIcon: {
    color: theme.palette.success.main,
  },
}))

export default ShippingDetails
