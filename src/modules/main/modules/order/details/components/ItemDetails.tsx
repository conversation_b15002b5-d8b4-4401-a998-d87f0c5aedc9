import Icon from "@mui/icons-material/KeyboardArrowRight"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CardMedia from "@mui/material/CardMedia"
import Divider from "@mui/material/Divider"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import moment from "moment"
import React, { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import { useNavigate, useSearchParams } from "react-router-dom"
import {
  CANCELLATION_REASONS,
  CANCELLATION_REASONS_SELLER,
  CURRENCY_SYMBOLS,
  RETURN_REASONS,
} from "../../../../../../utils/constants"
import { isItemCustomization } from "../../../../../../utils/utils"
import useFormatDate from "../../../../hooks/useFormatDate"
import useFormatNumber from "../../../../hooks/useFormatNumber"
import FulfilmentDateMessage from "./FulfilmentDateMessage"
import FulfilmentStatus from "./FulfilmentStatus"
import ItemCustomization from "./ItemCustomization"

const today = moment()

export const SingleItem: React.FC<{
  item: any
  customizations?: any[]
  itemQuantityTag?: any
  quoteBreakup: any
  itemCharges: any[]
}> = ({
  item,
  customizations = [],
  itemQuantityTag = null,
  quoteBreakup,
  itemCharges,
}) => {
  const { formatNumber } = useFormatNumber()
  const { t } = useTranslation()
  const styles = useStyles()

  const itemCustomization = isItemCustomization(item.tags)

  if (itemCustomization) {
    return <Box key={item.id} />
  }

  const itemCustomizationList = customizations?.filter(
    (obj: any) => obj.parent_item_id === item.parent_item_id
  )

  let itemQuantity = itemQuantityTag ? itemQuantityTag : item?.quantity?.count
  let itemTotal = Number(itemQuantity * item?.product?.subtotal)

  if (quoteBreakup && !itemQuantityTag) {
    const filteredQuote = quoteBreakup.filter(
      (one: any) => one["@ondc/org/item_id"] === item.id
    )

    if (filteredQuote) {
      const itemQuote = filteredQuote.find(
        (one: any) => one["@ondc/org/title_type"] === "item"
      )
      if (itemQuote && itemQuote["@ondc/org/item_quantity"]) {
        itemQuantity = itemQuote["@ondc/org/item_quantity"].count
      }
      itemTotal = filteredQuote.reduce((sum: number, one: any) => {
        return sum + parseFloat(one.price.value)
      }, 0)
    }
  }

  if (itemCharges?.length > 0) {
    const chargedItem = itemCharges.find((one) => one.itemId === item.id)
    itemTotal = Number(itemQuantity * chargedItem.discountedBasePrice)
  }

  return (
    <Box key={item.id} className={styles.itemContainer}>
      <CardMedia
        component="img"
        src={item?.product?.descriptor?.symbol}
        sx={{ width: 44, height: 44, marginRight: 1.5 }}
      />
      <Box className={styles.itemInfo}>
        <Box className={styles.rowBetween}>
          <Typography variant="labelLarge" className={styles.productName}>
            {item?.product?.descriptor?.name}
          </Typography>
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography variant="labelLarge">
              {t("Fulfillment.Qty")} {itemQuantity}
            </Typography>
            <Typography variant="labelLarge" className={styles.priceText}>
              {CURRENCY_SYMBOLS[item?.product?.price?.currency]}
              {formatNumber(itemTotal.toFixed(2))}
            </Typography>
          </Box>
        </Box>
        {itemCustomizationList?.length > 0 ? (
          <ItemCustomization itemCustomizationList={itemCustomizationList} />
        ) : (
          item.product?.quantity?.unitized &&
          Object.keys(item.product?.quantity?.unitized).map((one) => (
            <Typography
              variant="labelSmall"
              key={item.product?.quantity?.unitized[one].value}
            >
              {item.product?.quantity?.unitized[one].value}{" "}
              {item.product?.quantity?.unitized[one].unit}
            </Typography>
          ))
        )}
        <Box className={styles.itemTags}>
          {item?.product["@ondc/org/cancellable"] ? (
            <Box className={styles.chip}>
              <Typography variant={"labelSmall"} className={styles.chipText}>
                {t("Profile.Cancellable")}
              </Typography>
            </Box>
          ) : (
            <Box className={styles.chip}>
              <Typography variant={"labelSmall"} className={styles.chipText}>
                {t("Profile.Non-cancellable")}
              </Typography>
            </Box>
          )}
          {item?.product["@ondc/org/returnable"] ? (
            <Box className={styles.chip}>
              <Typography variant={"labelSmall"} className={styles.chipText}>
                {t("Profile.Returnable")}
              </Typography>
            </Box>
          ) : (
            <Box className={styles.chip}>
              <Typography variant={"labelSmall"} className={styles.chipText}>
                {t("Profile.Non-returnable")}
              </Typography>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  )
}

const ItemDetails: React.FC<{
  fulfillments: any[]
  items: any[]
  quoteBreakup: any
  itemCharges: any[]
}> = ({ fulfillments, items, quoteBreakup, itemCharges }) => {
  const [urlParams] = useSearchParams()
  const orderId = urlParams.get("id")
  const { formatDate } = useFormatDate()
  const { t } = useTranslation()
  const { orderDetails } = useSelector(({ order }: any) => order)
  const navigate = useNavigate()
  const [shipmentFulfillmentList, setShipmentFulfillmentList] = useState<any[]>(
    []
  )
  const [returnFulfillmentList, setReturnFulfillmentList] = useState<any[]>([])
  const [cancelFulfillmentList, setCancelFulfillmentList] = useState<any[]>([])
  const styles = useStyles()

  const renderReason = (reasonId: string) => {
    const value = RETURN_REASONS.find((one) => one.key === reasonId)?.value

    return (
      <Typography variant={"labelSmall"} className={styles.reason}>
        {t(`Return Reason.${value}`)}
      </Typography>
    )
  }

  const renderCancelReason = (reasonId: string) => {
    let value = CANCELLATION_REASONS.find((one) => one.key === reasonId)?.value

    if (value) {
      return (
        <Typography variant={"labelSmall"} className={styles.reason}>
          {t(`Cancellation Reason.${value}`)}
        </Typography>
      )
    } else {
      value = CANCELLATION_REASONS_SELLER.find(
        (one) => one.key === reasonId
      )?.value
      return (
        <Typography variant={"labelSmall"} className={styles.reason}>
          {t(`Cancellation Reason.${value}`)}
        </Typography>
      )
    }
  }

  useEffect(() => {
    const shipments: any[] = []
    const returns: any[] = []
    const cancels: any[] = []

    fulfillments?.forEach((fulfillment: any) => {
      switch (fulfillment.type) {
        case "Delivery":
          const filteredItems = items.filter(
            (item) =>
              item.fulfillment_id === fulfillment.id &&
              item?.quantity?.count > 0
          )
          if (filteredItems.length > 0) {
            shipments.push({ ...fulfillment, filteredItems })
          }
          break

        case "Return":
          returns.push(fulfillment)
          break

        case "Cancel":
          cancels.push(fulfillment)
          break
      }
    })
    setShipmentFulfillmentList(shipments)
    setReturnFulfillmentList(returns)
    setCancelFulfillmentList(cancels)
  }, [fulfillments])

  const hasCustomizationTypeTag = (tags: any[]) => {
    return tags?.some(
      (tag: any) =>
        tag?.code === "type" &&
        tag?.list?.some(
          (item: any) =>
            item?.code === "type" && item?.value === "customization"
        )
    )
  }
  return (
    <>
      {shipmentFulfillmentList.length > 0 && (
        <Box className={styles.container}>
          <Typography variant="titleLarge" className={styles.shipmentHeading}>
            {t("Profile.Shipment Details")}
          </Typography>
          {shipmentFulfillmentList.map((fulfillment: any) => {
            if (fulfillment.filteredItems?.length > 0) {
              const customizations = fulfillment.filteredItems?.filter(
                (obj: any) => hasCustomizationTypeTag(obj?.tags)
              )
              return (
                <Box key={fulfillment.id} className={styles.cardContainer}>
                  <Box className={styles.rowBetween}>
                    <FulfilmentDateMessage
                      fulfillment={fulfillment}
                      fulfillmentHistory={orderDetails.fulfillmentHistory}
                      createdAt={orderDetails.createdAt}
                    />
                    <Button
                      onClick={() =>
                        navigate(
                          `/order-product-details/${fulfillment.id}?id=${orderId}`
                        )
                      }
                    >
                      <FulfilmentStatus
                        code={fulfillment?.state?.descriptor?.code}
                      />
                      <Icon />
                    </Button>
                  </Box>
                  <Divider className={styles.divider} />
                  {fulfillment.filteredItems.map((item: any, index: number) => (
                    <SingleItem
                      key={`${item.id}${index}ShipmentFulfillment`}
                      item={item}
                      customizations={customizations}
                      quoteBreakup={quoteBreakup}
                      itemCharges={itemCharges}
                    />
                  ))}
                </Box>
              )
            } else {
              return <></>
            }
          })}
        </Box>
      )}
      {returnFulfillmentList.length > 0 && (
        <Box className={styles.container}>
          <Typography variant="titleLarge" className={styles.shipmentHeading}>
            {t("Item Details.Return Details")}
          </Typography>
          {returnFulfillmentList?.map((fulfillment: any) => {
            const fulfillmentHistory = orderDetails?.fulfillmentHistory?.filter(
              (one: any) => fulfillment.id === one.id
            )
            const returnInitiated = fulfillmentHistory.find(
              (one: any) => one.state === "Return_Initiated"
            )

            let itemId: any = null
            let reasonId: any = null
            const returnTag = fulfillment.tags.find(
              (tag: any) => tag.code === "return_request"
            )
            const itemTag = returnTag?.list?.find(
              (tag: any) => tag.code === "item_id"
            )
            itemId = itemTag.value

            const reasonIdTag = returnTag?.list?.find(
              (tag: any) => tag.code === "reason_id"
            )
            const itemQuantityTag = returnTag?.list?.find(
              (tag: any) => tag.code === "item_quantity"
            )
            reasonId = reasonIdTag.value

            const filteredItems =
              fulfillment.state.descriptor.code === "Return_Initiated"
                ? items.filter(
                    (item) =>
                      item.id === itemId &&
                      item.quantity.count > 0 &&
                      item.fulfillment_status === "Order-delivered"
                  )
                : items.filter(
                    (item) =>
                      item.fulfillment_id === fulfillment.id &&
                      item.quantity.count > 0
                  )

            if (filteredItems.length > 0) {
              return (
                <Box key={fulfillment.id} className={styles.cardContainer}>
                  <Box className={styles.header}>
                    <Box className={styles.deliveryBox}>
                      <Typography
                        variant={"titleSmall"}
                        className={styles.deliveryDate}
                      >
                        {t("Item Details.Return initiated on")}{" "}
                      </Typography>
                      <Typography
                        variant={"titleSmall"}
                        className={styles.deliveryDate}
                      >
                        {returnInitiated
                          ? today.isSame(
                              moment(returnInitiated.createdAt),
                              "day"
                            )
                            ? formatDate(
                                moment(returnInitiated.createdAt),
                                "hh:mm a"
                              )
                            : formatDate(
                                moment(returnInitiated.createdAt),
                                "Do MMM"
                              )
                          : ""}
                      </Typography>
                    </Box>
                    <Button
                      className={styles.statusContainer}
                      onClick={() =>
                        navigate(`/order-return-details/${fulfillment.id}`)
                      }
                    >
                      <FulfilmentStatus
                        code={fulfillment?.state?.descriptor?.code}
                        fulfilment={fulfillmentHistory.find(
                          (one: any) =>
                            one.state === fulfillment?.state?.descriptor?.code
                        )}
                      />
                      <Icon />
                    </Button>
                  </Box>
                  <Divider className={styles.divider} />
                  {filteredItems.map((item, index) => (
                    <SingleItem
                      key={`${item.id}${index}ReturnFulfillment`}
                      item={item}
                      itemQuantityTag={itemQuantityTag?.value ?? 1}
                      quoteBreakup={quoteBreakup}
                      itemCharges={itemCharges}
                    />
                  ))}
                  <Divider className={styles.divider} />
                  <Box className={styles.footer}>{renderReason(reasonId)}</Box>
                </Box>
              )
            } else {
              return <Box key={fulfillment.id} />
            }
          })}
        </Box>
      )}

      {cancelFulfillmentList.length > 0 && (
        <Box>
          <Typography variant="titleLarge" className={styles.fulfilmentTitle}>
            {t("Item Details.Cancel Details")}
          </Typography>
          {cancelFulfillmentList?.map((fulfillment: any) => {
            const isReturnInitiated =
              fulfillment?.state?.descriptor?.code === "Cancelled"

            const fulfillmentHistory = orderDetails?.fulfillmentHistory?.filter(
              (one: any) => fulfillment.id === one.id
            )
            const cancelInitiated = fulfillmentHistory.find(
              (one: any) => one.state === "Cancelled"
            )

            let itemId: any = null
            let cancelId: any = null
            if (isReturnInitiated) {
              const cancelTag = fulfillment?.tags?.find(
                (tag: any) => tag.code === "cancel_request"
              )
              const returnTag = fulfillment?.tags?.find(
                (tag: any) => tag.code === "quote_trail"
              )
              const reasonIdTag = cancelTag?.list?.find(
                (tag: any) => tag.code === "reason_id"
              )
              const itemTag = returnTag?.list?.find(
                (tag: any) => tag.code === "id"
              )
              itemId = itemTag?.value
              cancelId = reasonIdTag?.value
            }

            if (cancelInitiated) {
              return (
                <Box key={fulfillment.id} className={styles.container}>
                  <Box className={styles.header}>
                    <Box className={styles.deliveryBox}>
                      <Typography
                        variant={"labelSmall"}
                        className={styles.deliveryDate}
                      >
                        {t("Item Details.Cancelled on")}
                      </Typography>
                      <Typography
                        variant={"labelSmall"}
                        className={styles.deliveryDate}
                      >
                        {today.isSame(moment(cancelInitiated.createdAt), "day")
                          ? formatDate(
                              moment(cancelInitiated.createdAt),
                              "hh:mm a"
                            )
                          : formatDate(
                              moment(cancelInitiated.createdAt),
                              "Do MMM"
                            )}
                      </Typography>
                    </Box>
                    <Box className={styles.statusContainer}>
                      <FulfilmentStatus
                        code={fulfillment?.state?.descriptor?.code}
                      />
                    </Box>
                  </Box>
                  {fulfillment?.state?.descriptor?.code === "Cancelled"
                    ? items
                        .filter(
                          (item) =>
                            item.id === itemId &&
                            item.fulfillment_id === fulfillment.id
                        )
                        .map((item, index) => (
                          <SingleItem
                            key={`${item.id}${index}CancelFulfillment`}
                            item={item}
                            quoteBreakup={quoteBreakup}
                            itemCharges={itemCharges}
                          />
                        ))
                    : items
                        .filter(
                          (item) => item.fulfillment_id === fulfillment.id
                        )
                        .map((item, index) => (
                          <SingleItem
                            key={`${item.id}${index}CancelFulfillment`}
                            item={item}
                            quoteBreakup={quoteBreakup}
                            itemCharges={itemCharges}
                          />
                        ))}
                  <Box className={styles.footer}>
                    {renderCancelReason(cancelId)}
                  </Box>
                </Box>
              )
            } else {
              return <></>
            }
          })}
        </Box>
      )}
    </>
  )
}

const useStyles = makeStyles((theme: any) => ({
  container: {
    padding: 16,
  },
  header: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingBottom: 12,
    marginBottom: 12,
    borderBottomColor: theme.palette.neutral100,
    borderBottomWidth: 1,
  },
  footer: {
    paddingTop: 12,
    borderTopColor: theme.palette.neutral100,
    borderTopWidth: 1,
  },
  reason: {
    color: theme.palette.neutral300,
  },
  statusContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "flex-end",
    gap: 8,
  },
  shipmentHeading: {
    fontWeight: "bold",
  },
  fulfilmentTitle: {
    marginTop: 20,
    color: theme.palette.neutral400,
    paddingHorizontal: 16,
  },
  productName: {
    width: "50%",
  },
  divider: {
    marginTop: 16,
    marginBottom: 16,
  },
  itemContainer: {
    display: "flex",
    marginBottom: theme.spacing(2),
    marginTop: theme.spacing(2),
  },
  itemInfo: {
    flex: 1,
  },
  itemTags: {
    display: "flex",
    flexDirection: "row",
  },
  chip: {
    marginRight: 4,
    backgroundColor: theme.palette.neutral100,
    paddingRight: 8,
    paddingLeft: 8,
    borderRadius: 22,
  },
  chipText: {
    color: theme.palette.neutral300,
  },
  rowBetween: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "flex-start",
    gap: 5,
  },
  priceText: {
    marginLeft: theme.spacing(2),
  },
  cardContainer: {
    padding: theme.spacing(2),
    marginTop: "12px !important",
    border: `1px solid ${theme.palette.grey[300]}`,
    borderRadius: 8,
  },
  deliveryDate: {
    color: theme.palette.neutral300,
    flex: 1,
    flexWrap: "wrap",
  },
  deliveryBox: {
    width: "50%",
  },
}))

export default ItemDetails
