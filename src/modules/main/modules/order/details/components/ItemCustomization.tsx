import React from 'react';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import useFormatNumber from '../../../../hooks/useFormatNumber';

interface ItemCustomizationProps {
  itemCustomizationList: any[];
}

const ItemCustomization: React.FC<ItemCustomizationProps> = ({itemCustomizationList}) => {
  const {formatNumber} = useFormatNumber();
  const styles = useStyles();

  if (itemCustomizationList?.length > 0) {
    return (
      <Typography variant="labelSmall" className={styles.label}>
        {itemCustomizationList.map((customization, index) => {
          const isLastItem = index === itemCustomizationList.length - 1;
          return `${customization?.product?.item_details?.descriptor?.name} (₹${formatNumber(
            customization?.product?.item_details?.price?.value
          )})${isLastItem ? '' : ' + '}`;
        })}
      </Typography>
    );
  } else {
    return null;
  }
};

const useStyles = makeStyles<any>((theme) => ({
  label: {
    color: theme.palette.text.secondary,
  },
}));

export default ItemCustomization;
