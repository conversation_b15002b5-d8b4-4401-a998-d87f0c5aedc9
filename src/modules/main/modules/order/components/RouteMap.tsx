import React, { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useTheme } from "@mui/material/styles"
import { makeStyles } from "@mui/styles"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Typography from "@mui/material/Typography"
import IconButton from "@mui/material/IconButton"
import Paper from "@mui/material/Paper"
import CircularProgress from "@mui/material/CircularProgress"
import CloseIcon from "@mui/icons-material/Close"
import MyLocationIcon from "@mui/icons-material/MyLocation"
import DirectionsIcon from "@mui/icons-material/Directions"
import { mappls, mappls_plugin } from "mappls-web-maps"
import useMapMeta from "../../../hooks/useMapMeta"

// Initialize Mappls objects
const mapplsClassObject = new mappls()
const mapplsPluginObject = new mappls_plugin()

interface RouteMapProps {
  fromLocation?: {
    lat: number
    lng: number
    name?: string
  }
  toLocation?: {
    lat: number
    lng: number
    name?: string
  }
  onClose?: () => void
  showCloseButton?: boolean
  height?: string | number
}

const RouteMap: React.FC<RouteMapProps> = ({
  fromLocation,
  toLocation,
  onClose,
  showCloseButton = true,
  height = "400px",
}) => {
  const { t } = useTranslation()
  const theme = useTheme()
  const styles = useStyles()
  const { getMapMeta } = useMapMeta()

  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstance = useRef<any>(null)
  const directionPluginRef = useRef<any>(null)
  const markersRef = useRef<any[]>([])

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [routeInfo, setRouteInfo] = useState<{
    distance?: string
    duration?: string
  }>({})

  // Default locations (Delhi area)
  const defaultFrom = { lat: 28.6139, lng: 77.209, name: "New Delhi" }
  const defaultTo = { lat: 28.5355, lng: 77.391, name: "Noida" }

  const from = fromLocation || defaultFrom
  const to = toLocation || defaultTo

  useEffect(() => {
    initializeMap()

    return () => {
      cleanup()
    }
  }, [from, to])

  const cleanup = () => {
    if (mapInstance.current) {
      try {
        mapInstance.current.remove()
      } catch (error) {
        console.warn("Error removing map:", error)
      }
    }
    if (directionPluginRef.current) {
      directionPluginRef.current = null
    }
    markersRef.current = []
  }

  const initializeMap = async () => {
    if (!mapRef.current) return

    setIsLoading(true)
    setError(null)

    try {
      const mapMeta = await getMapMeta()
      const token = mapMeta.access_token

      if (!token) {
        throw new Error("No valid map token available")
      }

      const loadObject = { map: true, plugins: ["direction"] }

      mapplsClassObject.initialize(token, loadObject, () => {
        try {
          // Create map instance
          const newMap = mapplsClassObject.Map({
            id: mapRef.current,
            properties: {
              center: from,
              zoom: 10,
              traffic: true,
              geolocation: true,
            },
          })

          mapInstance.current = newMap

          // Add markers
          addMarkers()

          // Add route
          addRoute()

          newMap.on("load", () => {
            setIsLoading(false)
          })
        } catch (err) {
          console.error("Error initializing map:", err)
          setError("Failed to load map")
          setIsLoading(false)
        }
      })
    } catch (err) {
      console.error("Error getting map meta:", err)
      setError("Failed to get map credentials")
      setIsLoading(false)
    }
  }

  const addMarkers = () => {
    if (!mapInstance.current) return

    // Clear existing markers
    markersRef.current.forEach((marker) => {
      try {
        marker.remove()
      } catch (error) {
        console.warn("Error removing marker:", error)
      }
    })
    markersRef.current = []

    // Add from marker (green)
    const fromMarker = mapplsClassObject.Marker({
      map: mapInstance.current,
      position: from,
      icon_url: "https://apis.mapmyindia.com/map_v3/1.png",
      title: from.name || "Start Location",
      popup: {
        content: `<div><strong>${from.name || "Start Location"}</strong></div>`,
        openPopup: false,
      },
    })
    markersRef.current.push(fromMarker)

    // Add to marker (red)
    const toMarker = mapplsClassObject.Marker({
      map: mapInstance.current,
      position: to,
      icon_url: "https://apis.mapmyindia.com/map_v3/2.png",
      title: to.name || "Destination",
      popup: {
        content: `<div><strong>${to.name || "Destination"}</strong></div>`,
        openPopup: false,
      },
    })
    markersRef.current.push(toMarker)
  }

  const addRoute = () => {
    if (!mapInstance.current) return

    try {
      // Remove existing direction
      if (directionPluginRef.current) {
        directionPluginRef.current.remove()
      }

      // Add new direction
      directionPluginRef.current = mapplsPluginObject.direction({
        map: mapInstance.current,
        start: `${from.lat},${from.lng}`,
        end: `${to.lat},${to.lng}`,
        via: [],
        alternatives: false,
        steps: true,
        draggable: false,
        routeColor: theme.palette.primary.main,
        routeWidth: 4,
        callback: (data: any) => {
          if (data && data.routes && data.routes.length > 0) {
            const route = data.routes[0]
            setRouteInfo({
              distance: route.distance || "N/A",
              duration: route.duration || "N/A",
            })
          }
        },
      })
    } catch (err) {
      console.error("Error adding route:", err)
    }
  }

  const centerMap = () => {
    if (!mapInstance.current) return

    try {
      // Fit bounds to show both markers
      const bounds = mapplsClassObject.LatLngBounds()
      bounds.extend(from)
      bounds.extend(to)
      mapInstance.current.fitBounds(bounds, { padding: 50 })
    } catch (error) {
      console.warn("Error centering map:", error)
    }
  }

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords
          if (mapInstance.current) {
            mapInstance.current.setCenter({ lat: latitude, lng: longitude })
            mapInstance.current.setZoom(15)
          }
        },
        (error) => {
          console.warn("Error getting location:", error)
        }
      )
    }
  }

  return (
    <Paper className={styles.container} sx={{ height }}>
      {/* Header */}
      <Box className={styles.header}>
        <Box className={styles.headerContent}>
          <DirectionsIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
          <Typography variant="h6" className={styles.title}>
            {t("Route Map")}
          </Typography>
        </Box>

        {showCloseButton && onClose && (
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        )}
      </Box>

      {/* Route Info */}
      {(routeInfo.distance || routeInfo.duration) && (
        <Box className={styles.routeInfo}>
          {routeInfo.distance && (
            <Typography variant="body2" className={styles.routeText}>
              Distance: {routeInfo.distance}
            </Typography>
          )}
          {routeInfo.duration && (
            <Typography variant="body2" className={styles.routeText}>
              Duration: {routeInfo.duration}
            </Typography>
          )}
        </Box>
      )}

      {/* Map Container */}
      <Box className={styles.mapContainer} sx={{ position: "relative" }}>
        <div
          ref={mapRef}
          id="route-map"
          style={{
            width: "100%",
            height: "100%",
            borderRadius: "0 0 8px 8px",
          }}
        />

        {/* Loading Overlay */}
        {isLoading && (
          <Box className={styles.loadingOverlay}>
            <CircularProgress size={40} />
            <Typography variant="body2" sx={{ mt: 1 }}>
              {t("Loading map...")}
            </Typography>
          </Box>
        )}

        {/* Error Overlay */}
        {error && (
          <Box className={styles.errorOverlay}>
            <Typography variant="body2" color="error">
              {error}
            </Typography>
            <Button size="small" onClick={initializeMap} sx={{ mt: 1 }}>
              {t("Retry")}
            </Button>
          </Box>
        )}

        {/* Map Controls */}
        <Box className={styles.mapControls}>
          <IconButton
            onClick={centerMap}
            size="small"
            className={styles.controlButton}
            title="Center Map"
          >
            <DirectionsIcon fontSize="small" />
          </IconButton>

          <IconButton
            onClick={getCurrentLocation}
            size="small"
            className={styles.controlButton}
            title="My Location"
          >
            <MyLocationIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>
    </Paper>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    width: "100%",
    borderRadius: 8,
    overflow: "hidden",
    boxShadow: theme.shadows[3],
  },
  header: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    padding: "12px 16px",
    backgroundColor: theme.palette.grey[50],
    borderBottom: `1px solid ${theme.palette.divider}`,
  },
  headerContent: {
    display: "flex",
    alignItems: "center",
  },
  title: {
    fontWeight: 600,
    color: theme.palette.text.primary,
  },
  routeInfo: {
    display: "flex",
    gap: 16,
    padding: "8px 16px",
    backgroundColor: theme.palette.primary.light,
  },
  routeText: {
    color: theme.palette.primary.contrastText,
    fontWeight: 500,
  },
  mapContainer: {
    flex: 1,
    position: "relative",
    minHeight: 300,
  },
  loadingOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    zIndex: 1000,
  },
  errorOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    zIndex: 1000,
  },
  mapControls: {
    position: "absolute",
    top: 16,
    right: 16,
    display: "flex",
    flexDirection: "column",
    gap: 8,
    zIndex: 999,
  },
  controlButton: {
    backgroundColor: theme.palette.common.white,
    boxShadow: theme.shadows[2],
    "&:hover": {
      backgroundColor: theme.palette.grey[100],
    },
  },
}))

export default RouteMap
