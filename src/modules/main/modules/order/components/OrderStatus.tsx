import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';

interface OrderStatusProps {
  status: string;
}

const OrderStatus: React.FC<OrderStatusProps> = ({status}) => {
  const styles = useOrderStyles();

  switch (status) {
    case 'Shipped':
    case 'Updated':
      return (
        <Box className={`${styles.container} ${styles.shipped}`}>
          <Typography variant="labelLarge" className={styles.createdLabel}>
            {status}
          </Typography>
        </Box>
      );

    case 'Delivered':
    case 'Active':
    case 'Completed':
      return (
        <Box className={`${styles.container} ${styles.completed}`}>
          <Typography variant="labelLarge" className={styles.completedLabel}>
            {status}
          </Typography>
        </Box>
      );

    case 'Returned':
    case 'Cancelled':
      return (
        <Box className={`${styles.container} ${styles.cancelled}`}>
          <Typography variant="labelLarge" className={styles.cancelledLabel}>
            {status}
          </Typography>
        </Box>
      );

    case 'Draft':
      return (
        <Box className={`${styles.container} ${styles.cancelled}`}>
          <Typography variant="labelLarge" className={styles.cancelledLabel}>
            Verifying Payment
          </Typography>
        </Box>
      );

    case 'Failed':
      return (
        <Box className={`${styles.container} ${styles.cancelled}`}>
          <Typography variant="labelLarge" className={styles.cancelledLabel}>
            Failed
          </Typography>
        </Box>
      );

    default:
      return (
        <Box className={`${styles.container} ${styles.created}`}>
          <Typography variant="labelLarge" className={styles.createdLabel}>
            {status}
          </Typography>
        </Box>
      );
  }
};

const useOrderStyles = makeStyles<any>((theme) => ({
  container: {
    padding: '4px 12px',
    borderRadius: 21,
  },
  created: {
    backgroundColor: theme.palette.primary50,
  },
  shipped: {
    backgroundColor: theme.palette.primary50,
  },
  completed: {
    backgroundColor: theme.palette.success50,
  },
  cancelled: {
    backgroundColor: theme.palette.error50,
  },
  createdLabel: {
    color: theme.palette.primary,
  },
  cancelledLabel: {
    color: theme.palette.error600,
  },
  completedLabel: {
    color: theme.palette.success600,
  },
}));

export default OrderStatus;
