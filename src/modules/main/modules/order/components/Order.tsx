import moment from 'moment';
import React, {useCallback, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import {useTheme} from "@mui/material/styles";
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRightRounded';
import OrderStatus from './OrderStatus';
import {CURRENCY_SYMBOLS, FB_DOMAIN} from '../../../../../utils/constants';
import {isItemCustomization} from '../../../../../utils/utils';
import VegNonVegTag from '../../../components/products/VegNonVegTag';
import useFormatDate from '../../../hooks/useFormatDate';
import useFormatNumber from '../../../hooks/useFormatNumber';
import Image from '../../../components/image/Image';


interface Order {
  order: any;
}

/**
 * Component to render single card on orders screen
 * @param order: single order object
 * @constructor
 * @returns {JSX.Element}
 */
const Order: React.FC<Order> = ({order}) => {
  const {formatNumber} = useFormatNumber();
  const {formatDate} = useFormatDate();
  const theme = useTheme();
  const styles = useOrderStyles();
  const navigate = useNavigate();

  const navigateToDetails = useCallback(() => {
    navigate(`/order?id=${order._id}`);
  }, [navigate]);

  order.state = order.confirmedItems?.every(
    (item: any) => item.cancellation_status === 'Cancelled',
  )
    ? 'Cancelled'
    : order.state;

  const mergedItems = useMemo(() => {
    return order?.confirmedItems?.reduce((acc: any, current: any) => {
      // Check if product already exists in the accumulator
      const existingProduct = acc.find(
        (item: any) => item.product.id === current.product.id,
      );

      if (existingProduct) {
        // If product exists, add the quantity
        existingProduct.quantity.count += current.quantity.count;
      } else {
        // If product does not exist, add it to the accumulator
        acc.push({...current});
      }

      return acc;
    }, []);
  }, [order.confirmedItems]);

  return (
    <Button onClick={navigateToDetails} className={styles.container}>
      <Box className={styles.header}>
        <Image
          source={{uri: order?.provider?.descriptor?.symbol}}
          imageStyle={styles.providerImage}
        />
        <Box className={styles.providerNameContainer}>
          <Typography variant='bodyLarge' className={styles.providerName}>
            {order?.provider?.descriptor?.name}
          </Typography>
          <br/>
          <Typography variant='labelMedium' className={styles.providerAddress}>
            {order?.fulfillments[0]?.start?.location?.address?.locality}{' '}
            {order?.fulfillments[0]?.start?.location?.address?.city}
          </Typography>
        </Box>
        <Box>{order.state && <OrderStatus status={order.state}/>}</Box>
      </Box>
      <Box className={styles.orderDetails}>
        {mergedItems?.map((item: any, index: number) => {
          const isCustomization = isItemCustomization(item?.tags);
          if (isCustomization || item?.quantity?.count === 0) {
            return <Box key={`${item?.id}${item.fulfillment_id}${index}`}/>
          }
          return <Box className={styles.itemBox}>
            {order.domain === FB_DOMAIN && (
              <Box className={styles.iconContainer}>
                <VegNonVegTag tags={item?.product?.tags} size={'small'}/>
              </Box>
            )}
            <Typography
              key={`${item?.id}${item.fulfillment_id}`}
              variant={'labelMedium'}
              className={styles.item}>
              {item?.quantity?.count} x {item?.product?.descriptor?.name}
            </Typography>
          </Box>
        })}
        <Divider sx={{marginTop: '10px'}}/>
        <Box className={styles.paymentDetails}>
          <Typography className={styles.date} variant={'labelSmall'}>
            {formatDate(moment(order?.createdAt), 'DD MMM YYYY hh:mm a')}
          </Typography>
          <Box className={styles.amountContainer}>
            <Typography className={styles.amount} variant="labelLarge">
              {CURRENCY_SYMBOLS[order?.payment?.params?.currency]}
              {formatNumber(
                Number(
                  order?.charges?.quote?.totalOrderValueAfterSubsidy ??
                  order?.payment?.params?.amount,
                ).toFixed(2),
              )}
            </Typography>
            <KeyboardArrowRightIcon sx={{fontSize: 16, color: theme.palette.neutral300}}/>
          </Box>
        </Box>
      </Box>
    </Button>
  );
};

export default Order;

const useOrderStyles = makeStyles<any>((theme) => ({
  container: {
    marginBottom: '15px',
    display: 'flex ',
    flexDirection: 'column',
    width: '100%',
    textTransform: 'capitalize',
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.palette.primary50,
    padding: '8px 12px',
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    width: '-webkit-fill-available',
  },
  providerImage: {
    width: '32px',
    height: '32px',
    borderRadius: '4px',
  },
  providerName: {
    color: theme.palette.neutral400,
  },
  providerAddress: {
    color: theme.palette.neutral300,
  },
  providerNameContainer: {
    textAlign: 'left',
    flex: 1,
    paddingLeft: '8px',
    paddingRight: '8px',
  },
  itemContainer: {
    display: 'flex',
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 8,
  },
  itemBox: {
    textAlign: 'left'
  },
  item: {
    color: theme.palette.neutral300,
  },
  row: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  orderDetails: {
    width: '-webkit-fill-available',
    border: `1px solid ${theme.palette.neutral100}`,
    padding: '12px',
    borderLeftWidth: 1,
    borderRightWidth: 1,
    borderBottomLeftRadius: '12px',
    borderBottomRightRadius: '12px',
  },
  paymentDetails: {
    display: 'flex',
    borderTopWidth: 1,
    borderColor: theme.palette.neutral100,
    paddingTop: 12,
    justifyContent: 'space-between',
    alignItems: 'center',
    flexDirection: 'row',
  },
  date: {
    color: theme.palette.neutral400,
  },
  amount: {
    color: theme.palette.neutral400,
    alignItems: 'center',
    marginRight: 8,
  },
  amountContainer: {
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
  },
}));
