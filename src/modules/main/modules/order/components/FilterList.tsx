import React, {useEffect, useState} from 'react';
import Skeleton from '@mui/material/Skeleton';
import {useTranslation} from 'react-i18next';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import Typography from '@mui/material/Typography';
import {useFilterListStyles} from '../../../components/products/FilterList';
import {useSearchParams, useNavigate} from 'react-router-dom';

interface FilterList {
  selectedFilter: any;
  setSelectedFilter: (values: any) => void;
  close: () => void;
}

const ValuesSkeleton = () => {
  const styles = useFilterListStyles();
  return (
    <Box className={styles.valueRow}>
      <Skeleton variant="circular" width={24} height={24} className={styles.checkboxSkeleton}/>
      <Skeleton variant="text" width="80%" height={20} className={styles.textSkeleton}/>
    </Box>
  );
};

const attributes = [
  {
    code: 'orderType',
    name: 'Order Type',
    values: [
      {name: 'Created,Accepted,In-progress', value: 'Ongoing'},
      {name: 'Completed', value: 'Completed'},
      {name: 'Cancelled', value: 'Cancelled'},
      {name: 'Processing', value: 'Processing'},
      {name: 'Draft', value: 'Draft'},
      {name: 'Failed', value: 'Failed'},
    ],
  },
  {
    code: 'orderDate',
    name: 'Order Date',
    values: [
      {name: 'Last 7 Days', value: 'Last 7 Days'},
      {name: 'Last 30 Days', value: 'Last 30 Days'},
      {name: 'Last 60 Days', value: 'Last 60 Days'},
      {name: 'Last 90 Days', value: 'Last 90 Days'},
      {name: 'All Time', value: 'All Time'},

    ],
  },
];

const FilterList: React.FC<FilterList> = ({
                                            selectedFilter,
                                            setSelectedFilter,
                                            close,
                                          }) => {
  const {t} = useTranslation();
  const styles = useFilterListStyles();
  const [currentAttribute, setCurrentAttribute] = useState<string>('orderType');
  const [currentValues, setCurrentValues] = useState<any[]>([]);
  const [selectedValue, setSelectedValue] = useState<any>('');
  const navigation = useNavigate();
  const [searchParams] = useSearchParams();
  const tabParam = searchParams.get('tab') ?? '';

  const updateFilters = () => {
    navigation(`/dashboard?tab=${tabParam}&filter=${currentAttribute}&filterValue=${selectedValue}`);
    setSelectedFilter({
      currentAttribute,
      selectedValue,
    });
    close();
  };

  const removeValue = () => {
    setSelectedValue('');
  };

  const addValue = (name: string) => {
    setSelectedValue(name);
  };

  const clearAll = () => setSelectedValue('');

  useEffect(() => {
    setCurrentAttribute(selectedFilter.currentAttribute);
    setSelectedValue(selectedFilter.selectedValue);
  }, [selectedFilter]);

  useEffect(() => {
    const attribute = attributes.find(one => one.code === currentAttribute);
    setCurrentValues(attribute?.values ?? []);
  }, [currentAttribute, attributes])

  return (
    <Box className={styles.container}>
      <Box className={styles.sheetHeader}>
        <Typography variant="titleLarge" className={styles.title}>
          {t('Orders.Filter List.Filters')}
        </Typography>
        <Button onClick={clearAll}>
          <Typography variant="labelSmall" className={styles.clearButton}>
            {t('Orders.Filter List.Clear all')}
          </Typography>
        </Button>
      </Box>
      <Box className={styles.filterContainer}>
        <Box className={styles.attributes}>
          {attributes.map(attribute => {
            const selected = currentAttribute === attribute.code;
            return (
              <Button
                key={attribute.code}
                className={styles.attribute}
                onClick={() => setCurrentAttribute(attribute.code)}>
                <Box className={selected ? styles.selected : styles.normal}/>
                <Typography
                  variant="labelSmall"
                  className={`${styles.attributeText} ${selected ? styles.selectedText : styles.normalText}`}
                >
                  {attribute.name}
                </Typography>
              </Button>
            );
          })}
        </Box>
        <Box className={styles.attributeValues}>
          {currentValues.length === 0 ? (
            <>
              <ValuesSkeleton/>
              <ValuesSkeleton/>
              <ValuesSkeleton/>
              <ValuesSkeleton/>
            </>
          ) : (
            currentValues.map(value => {
              const isSelected = selectedValue === value.name;
              return (
                <Button
                  key={value.name}
                  className={styles.valueRow}
                  onClick={() =>
                    isSelected ? removeValue() : addValue(value.name)
                  }>
                  <Checkbox checked={isSelected}/>
                  <Typography variant={'labelMedium'} className={styles.valueLabel}>
                    {value.value}
                  </Typography>
                </Button>
              );
            })
          )}
        </Box>
      </Box>
      <Box className={styles.footer}>
        <Box className={styles.buttonContainer}>
          <Button
            className={`${styles.button} ${styles.closeButton}`}
            onClick={close}>
            <Typography
              variant={'body3'}
              className={`${styles.buttonLabel} ${styles.closeLabel}`}>
              {t('Orders.Filter List.Close')}
            </Typography>
          </Button>
        </Box>
        <Box className={styles.separator}/>
        <Box className={styles.buttonContainer}>
          <Button
            className={`${styles.button} ${styles.applyButton}`}
            onClick={updateFilters}>
            <Typography
              variant={'body3'}
              className={`${styles.buttonLabel} ${styles.applyLabel}`}>
              {t('Orders.Filter List.Apply')}
            </Typography>
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default FilterList;
