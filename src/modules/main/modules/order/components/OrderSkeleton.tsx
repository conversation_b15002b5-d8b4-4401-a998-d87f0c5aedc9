import React from 'react';
import Skeleton from '@mui/material/Skeleton';
import Box from '@mui/material/Box';
import {makeStyles} from '@mui/styles';

/**
 * Component to show skeleton of order card
 * @returns {JSX.Element}
 */
const OrderSkeleton: React.FC<any> = () => {
  const styles = useOrderStyles();

  return (
    <Box className={styles.card}>
      <Box className={styles.header}>
        <Box className={styles.meta}>
          <Skeleton variant="circular" width={32} height={32} className={styles.image}/>
          <Box>
            <Box className={styles.marginBottom}>
              <Skeleton variant="text" width={150} height={18} className={styles.name}/>
            </Box>
            <Skeleton variant="text" width={150} height={18} className={styles.name}/>
          </Box>
        </Box>
        <Skeleton variant="rectangular" width={100} height={25} className={styles.tag}/>
      </Box>
      <Skeleton variant="rectangular" width="100%" height={20} className={styles.item}/>
      <Skeleton variant="rectangular" width="100%" height={20} className={styles.item}/>
    </Box>
  );
};

export default OrderSkeleton;

const useOrderStyles = makeStyles<any>((theme) => ({
  card: {
    paddingHorizontal: 16,
    borderRadius: 12,
    borderColor: theme.palette.neutral100,
    borderWidth: 1,
    marginBottom: 12,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    justifyContent: 'space-between',
  },
  meta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  name: {height: 18, width: 150},
  tag: {height: 25, width: 100},
  image: {
    width: 32,
    height: 32,
  },
  item: {
    width: '100%',
    height: 20,
    marginBottom: 12,
  },
  marginBottom: {
    marginBottom: 3,
  },
}));
