import {useEffect, useRef, useState} from "react";
import {useTranslation} from "react-i18next";
import axios from "axios";
import {useDispatch, useSelector} from "react-redux";
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import List from '@mui/material/List';
import {makeStyles} from "@mui/styles";

import StoreWishlist from "./components/StoreWishlist";
import Header from "../../components/header/HeaderWithActions";
import SafeAreaPage from "../../components/page/SafeAreaPage";
import {API_BASE_URL, WISHLIST} from "../../../../utils/apiActions";
import useNetworkErrorHandling from "../../../../hooks/useNetworkErrorHandling";
import useNetworkHandling from "../../../../hooks/useNetworkHandling";
import EmptyCart from "./components/EmptyCart";
import {updateWishlistItems} from "../../../../toolkit/reducer/wishlist";

const CancelToken = axios.CancelToken;

const WishList = () => {
  const {t} = useTranslation();
  const styles = useStyles();
  const source = useRef<any>(null);
  const {handleApiError} = useNetworkErrorHandling();
  const {getDataWithAuth} = useNetworkHandling();
  const {uid} = useSelector((state: any) => state.auth);
  const {wishlistItems} = useSelector((state: any) => state.wishlist);
  const dispatch = useDispatch();

  const [apiInProgress, setApiInProgress] = useState<boolean>(true);

  const getWishList = async () => {
    try {
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${WISHLIST}/${uid}/all`,
        source.current.token
      );

      if (data?.length > 0) {
        const sortedStores = data.sort(
          (a: any, b: any) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );
        dispatch(updateWishlistItems(sortedStores));
      } else {
        dispatch(updateWishlistItems(data));
      }
    } catch (error: any) {
      if (error.response && error.response.status !== 404) {
        handleApiError(error);
      } else {
        handleApiError(error);
      }
    }
  };

  useEffect(() => {
    if (uid) {
      getWishList()
        .then(() => setApiInProgress(false))
        .catch(() => setApiInProgress(false));
    }
  }, [uid]);

  return (
    <SafeAreaPage>
      <Box className={styles.container}>
        <Header label={t("Wishlist")} cart={true}/>
        {apiInProgress ? (
          <Box className={styles.loadingContainer}>
            <CircularProgress size={50}/>
          </Box>
        ) : (
          <List className={styles.subContainer}>
            {wishlistItems.length > 0 ? (
              wishlistItems.map((item: any) => (
                <StoreWishlist key={item.id} item={item}/>
              ))
            ) : (
              <EmptyCart/>
            )}
          </List>
        )}
      </Box>
    </SafeAreaPage>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    backgroundColor: theme.palette.background.default,
  },

  subContainer: {
    padding: '16px !important',
    backgroundColor: theme.palette.background.paper,
    height: 'calc(100dvh - 54px)',
    overflow: 'scroll',
  },

  loadingContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    minHeight: "60dvh",
    width: "100%",
  }
}));

export default WishList;
