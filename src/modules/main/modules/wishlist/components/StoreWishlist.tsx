import React, {useCallback, useEffect, useMemo, useRef, useState} from "react"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import Dialog from "@mui/material/Dialog"
import DialogActions from "@mui/material/DialogActions"
import DialogContent from "@mui/material/DialogContent"
import DialogTitle from "@mui/material/DialogTitle"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import {makeStyles} from "@mui/styles"
import {useTranslation} from "react-i18next"
import CloseIcon from "@mui/icons-material/Close"
import axios from "axios"
import {useDispatch, useSelector} from "react-redux"
import DeleteWishlistIcon from "../../../../../assets/check-circle.svg"
import WishlistItem from "./WishlistItem"
import {API_BASE_URL, WISHLIST} from "../../../../../utils/apiActions"
import {removeStoreFromWishlist} from "../../../../../toolkit/reducer/wishlist"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import useNetworkErrorHandling from "../../../../../hooks/useNetworkErrorHandling"
import {emptyAlertCallback, getNextStartTime, isCurrentTimeInRange,} from "../../../../../utils/utils"
import CartImage from "../../cart/all/components/CartImage"
import OpensAtPopup from "../../../components/provider/components/OpensAtPopup"
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined"
import {theme} from "../../../../../utils/theme"

interface StoreWishlistProps {
  item: any
}

const NoImageAvailable = require("../../../../../assets/noImage.png")
const CancelToken = axios.CancelToken

const StoreWishlist: React.FC<StoreWishlistProps> = ({item}) => {
  const dispatch = useDispatch()
  const {t} = useTranslation()
  const {uid} = useSelector(({auth}: any) => auth)
  const styles = useStyles()
  const source = useRef<any>(null)
  const {deleteDataWithAuth} = useNetworkHandling()
  const {handleApiError} = useNetworkErrorHandling()
  const [apiInProgress, setApiInProgress] = useState<boolean>(false)
  const [deleteStoreModalVisible, setDeleteStoreModalVisible] =
    useState<boolean>(false)
  const [providerImageSource, setProviderImageSource] =
    useState<any>(NoImageAvailable)

  const closeDeleteStoreConfirmationModal = useCallback(() => {
    setDeleteStoreModalVisible(false)
  }, [])

  const openDeleteStoreConfirmationModal = useCallback(() => {
    setDeleteStoreModalVisible(true)
  }, [])

  const renderProviderImage = () => (
    <CartImage
      imageSource={providerImageSource}
      className={
        isOpen
          ? styles.headerImage
          : `${styles.headerImage} ${styles.grayscaleImage}`
      }
    />
  )

  const deleteStore = useCallback(async () => {
    try {
      setApiInProgress(true)
      source.current = CancelToken.source()
      await deleteDataWithAuth(
        `${API_BASE_URL}${WISHLIST}/${uid}/${item?._id}/clear`,
        source.current.token
      )
      dispatch(removeStoreFromWishlist(item?._id))
    } catch (error: any) {
      handleApiError(error)
    } finally {
      setApiInProgress(false)
    }
  }, [item?._id])

  const {isOpen, timeFrom} = useMemo(() => {
    if (item) {
      const open = isCurrentTimeInRange(item?.location?.location_availabilities)
      return {
        isOpen: open,
        timeFrom: getNextStartTime(item?.location?.location_availabilities),
      }
    }
    return {
      isOpen: false,
      timeFrom: getNextStartTime(item?.location?.location_availabilities),
    }
  }, [item])

  useEffect(() => {
    if (item) {
      if (item.items.length === 0) {
        deleteStore().then(emptyAlertCallback).catch(emptyAlertCallback)
      }
      let imageSource = NoImageAvailable
      if (item?.location?.provider_descriptor?.symbol) {
        imageSource = {uri: item?.location?.provider_descriptor?.symbol}
      } else if (item?.location?.provider_descriptor?.images?.length > 0) {
        imageSource = {uri: item?.location?.provider_descriptor?.images[0]}
      }
      setProviderImageSource(imageSource)
    }
  }, [item])

  return (
    <Box>
      {!isOpen && <OpensAtPopup timeFrom={timeFrom}/>}
      <Box className={styles.container}>
        {/* Header */}
        <Box className={styles.itemHeader}>
          {renderProviderImage()}
          <Box className={styles.headerText}>
            <Box className={styles.titleView}>
              <Typography
                variant="titleLarge"
                component="div"
                className={styles.title}
              >
                {item?.location?.provider_descriptor?.name}
              </Typography>
              <IconButton
                size="small"
                className={styles.closeButton}
                onClick={openDeleteStoreConfirmationModal}
              >
                <CancelOutlinedIcon
                  sx={{color: theme.palette.neutral.main}}
                />
              </IconButton>
            </Box>
            <Typography
              variant="labelSmall"
              className={styles.providerLocality}
            >
              {item?.location?.address?.locality ?? ""}
            </Typography>
          </Box>
        </Box>

        <Box className={styles.line}/>

        {item?.items?.map((one: any) => (
          <WishlistItem
            key={one._id}
            wishlistItem={one}
            wishlistId={item._id}
            isOpen={isOpen}
          />
        ))}
      </Box>

      {/* Delete Confirmation Modal */}
      <Dialog
        open={deleteStoreModalVisible}
        onClose={closeDeleteStoreConfirmationModal}
      >
        <DialogTitle sx={{display: "flex", justifyContent: "flex-end"}}>
          <IconButton
            className={styles.closeButton}
            onClick={closeDeleteStoreConfirmationModal}
          >
            <CloseIcon/>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" alignItems="center">
            <img src={DeleteWishlistIcon} width={100} height={100}/>
            <Typography variant="headlineMedium" textAlign="center">
              {t("WishList.Remove Wishlist")}
            </Typography>
            <Typography
              variant="bodySmall"
              textAlign="center"
              sx={{width: "80%", marginTop: 1}}
            >
              {t(
                "WishList.Are you sure you would like to remove the store from wishlist?"
              )}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            variant="outlined"
            fullWidth
            onClick={closeDeleteStoreConfirmationModal}
            disabled={apiInProgress}
            sx={{borderRadius: 1}}
          >
            {t("WishList.No")}
          </Button>
          <Button
            variant="contained"
            fullWidth
            onClick={deleteStore}
            disabled={apiInProgress}
            sx={{borderRadius: 1}}
          >
            {apiInProgress ? <CircularProgress size={20}/> : t("WishList.Yes")}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

const useStyles = makeStyles((theme: any) => ({
  title: {
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    minWidth: 0,
  },
  container: {
    borderRadius: 16,
    border: `1px solid ${theme.palette.divider}`,
    padding: 12,
    marginBottom: 16,
  },
  itemHeader: {
    display: "flex",
    alignItems: "center",
    gap: 12,
  },
  grayscaleImage: {
    filter: "grayscale(100%)",
  },
  headerImage: {
    height: 48,
    width: 48,
    borderRadius: 8,
  },
  headerText: {
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    minWidth: 0,
    flex: 1,
  },
  line: {
    height: 1,
    backgroundColor: theme.palette.divider,
    marginTop: 5,
    marginBottom: 5,
  },
  titleView: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  closeButton: {
    marginLeft: "auto",
  },
  providerLocality: {
    color: "#9e9e9e",
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    minWidth: 0,
  },
}))

export default StoreWishlist
