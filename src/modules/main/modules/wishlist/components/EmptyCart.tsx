import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";
import {useTranslation} from "react-i18next";
import {useNavigate} from "react-router-dom";
import WishlistIcon from "../../../../../assets/wishlist.svg";

const EmptyCart = () => {
  const {t} = useTranslation();
  const styles = useStyles();
  const navigate = useNavigate();

  const goBack = () => {
    navigate("/dashboard");
  };

  return (
    <Box className={styles.emptyCart}>
      <Box className={styles.emptyCartDetails}>
        <img alt="Wishlist" src={WishlistIcon} width={128} height={128}/>
        <Typography variant="headlineSmall" component="div" className={styles.title}>
          {t("WishList.Your Wishlist is empty")}
        </Typography>
        <Typography variant="labelSmall" component="div" className={styles.emptyDescription}>
          {t(
            "WishList.Save your favorites in your wishlist. Review them anytime and easily add them to your cart."
          )}
        </Typography>
      </Box>
      <Button
        variant="outlined"
        className={styles.shopNowButton}
        onClick={goBack}
      >
        <Typography variant="body1" className={styles.shopNowText}>
          Shop Now
        </Typography>
      </Button>
    </Box>
  );
};

const useStyles = makeStyles((theme: any) => ({
  emptyCart: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    height: "100dvh",
  },
  emptyCartDetails: {
    textAlign: "center",
    maxWidth: 260,
  },
  title: {
    color: theme.palette.text.primary,
    marginTop: 15,
  },
  emptyDescription: {
    margin: "8px 0",
    color: theme.palette.text.primary,
  },
  shopNowButton: {
    marginTop: 32,
    borderColor: theme.palette.primary.main,
    borderRadius: 8,
    height: 44,
    width: 156,
  },
  shopNowText: {
    color: theme.palette.primary.main,
  },
}));

export default EmptyCart;
