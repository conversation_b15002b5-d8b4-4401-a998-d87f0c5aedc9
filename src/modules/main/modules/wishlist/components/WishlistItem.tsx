import axios from "axios";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import AddShoppingCartIcon from '@mui/icons-material/AddShoppingCart';
import CloseIcon from '@mui/icons-material/Close';
import {useCallback, useEffect, useMemo, useRef, useState} from "react";
import React from "react";
import {FontAwesomeIcon} from "@fortawesome/react-fontawesome";
import {faTrashCan} from "@fortawesome/free-solid-svg-icons";
import {useDispatch, useSelector} from "react-redux";
import {useTranslation} from "react-i18next";
import {makeStyles} from "@mui/styles";
import {setProduct} from "../../../../../toolkit/reducer/product";
import {API_BASE_URL, WISHLIST} from "../../../../../utils/apiActions";
import useNetworkHandling from "../../../../../hooks/useNetworkHandling";
import {removeProductFromWishlist} from "../../../../../toolkit/reducer/wishlist";
import {CURRENCY_SYMBOLS} from "../../../../../utils/constants";
import ProductAmount from "../../../components/products/productAmount/ProductAmount";
import NoImageAvailable from "../../../../../assets/noImage.png";
import CartImage from "../../cart/all/components/CartImage";
import ModalProductDetails from "../../product/details/ModalProductDetails";
import {theme} from "../../../../../utils/theme";

const CancelToken = axios.CancelToken;

const WishlistItem = ({
                        wishlistItem,
                        wishlistId,
                        isOpen,
                      }: {
  wishlistItem: any;
  wishlistId: string;
  isOpen: boolean;
}) => {
  const dispatch = useDispatch();
  const source = useRef<any>(null);
  const {uid} = useSelector((state: any) => state.auth);
  const {t} = useTranslation();
  const styles = useStyles();
  const {deleteDataWithAuth} = useNetworkHandling();
  const [productImageSource, setProductImageSource] = useState(NoImageAvailable);
  const [deleteItemModalVisible, setDeleteItemModalVisible] = useState(false);
  const [apiInProgress, setApiInProgress] = useState(false);
  const [productModalDetails, setProductModalDetails] = useState<any>(null);

  const showProductDetails = () => {
    dispatch(setProduct(wishlistItem));
    setProductModalDetails({
      inStock: Number(wishlistItem?.item_details?.quantity?.available?.count) >= 1,
      qty: 1,
      isOpen,
      wishlistProductId: wishlistItem?._id,
      wishlistId: wishlistId,
    });
  };

  const deleteWishlistItem = async () => {
    try {
      setApiInProgress(true);
      source.current = CancelToken.source();
      await deleteDataWithAuth(`${API_BASE_URL}${WISHLIST}/${uid}/${wishlistItem?._id}`, source.current.token);
      dispatch(removeProductFromWishlist({wishlistId, productId: wishlistItem?._id}));
    } catch (error) {
      console.error(error);
    } finally {
      setApiInProgress(false);
      closeDeleteItemConfirmationModal();
    }
  };

  const closeDeleteItemConfirmationModal = useCallback(() => {
    setDeleteItemModalVisible(false);
  }, []);

  const openDeleteItemConfirmationModal = useCallback(() => {
    setDeleteItemModalVisible(true);
  }, []);

  useEffect(() => {
    let imageSource: any = NoImageAvailable;
    if (wishlistItem?.item_details?.descriptor?.symbol) {
      imageSource = {uri: wishlistItem?.item_details?.descriptor?.symbol};
    } else if (wishlistItem?.item_details?.descriptor?.images?.length > 0) {
      imageSource = {uri: wishlistItem?.item_details?.descriptor?.images[0]};
    }
    setProductImageSource(imageSource);
  }, [wishlistItem]);

  const currency = useMemo(() => {
    const {price} = wishlistItem?.item_details;
    return CURRENCY_SYMBOLS[price?.currency];
  }, [wishlistItem?.item_details]);

  const renderItemImage = () => (
    <Button onClick={showProductDetails}>
      <CartImage imageSource={productImageSource} className={styles.itemImage}/>
    </Button>
  );

  const outOfStock = Number(wishlistItem?.item_details?.quantity?.available?.count) === 0;

  return (
    <>
      <Box key={wishlistItem._id} className={styles.itemSubView}>
        <Box className={styles.itemView}>{renderItemImage()}</Box>
        <Box className={styles.itemTextView}>
          <Typography variant="body1" className={styles.neutral400} noWrap>
            {wishlistItem?.item_details?.descriptor?.name}
          </Typography>
          <ProductAmount price={wishlistItem?.item_details?.price} currency={currency}/>
          {outOfStock && (
            <Box className={styles.outOfStock}>
              <Typography variant="labelSmall" className={styles.outOfStockLabel}>
                {t("Cart.FBProduct.Out of stock")}
              </Typography>
            </Box>
          )}
        </Box>

        <Box className={styles.iconView}>
          {/* Delete Button */}
          <IconButton size="small" className={styles.deleteButton} onClick={openDeleteItemConfirmationModal}>
            <FontAwesomeIcon icon={faTrashCan} color={theme.palette.error.main}/>
          </IconButton>

          {/* Add to Cart Button */}
          <IconButton size="small" onClick={showProductDetails} disabled={outOfStock || !isOpen}>
            <AddShoppingCartIcon color={outOfStock || !isOpen ? "disabled" : "primary"}/>
          </IconButton>
        </Box>
      </Box>

      {/* Delete Confirmation Modal */}
      <Dialog open={deleteItemModalVisible} onClose={closeDeleteItemConfirmationModal}>
        <DialogTitle>
          {t("WishList.Delete Item")}
          <IconButton onClick={closeDeleteItemConfirmationModal} style={{position: "absolute", right: 8, top: 8}}>
            <CloseIcon/>
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Typography>{t("WishList.Are you sure you want to remove this item from wishlist?")}</Typography>
        </DialogContent>
        <DialogActions sx={{p: 2}}>
          <Button
            variant="outlined"
            fullWidth
            onClick={closeDeleteItemConfirmationModal}
            disabled={apiInProgress}
            sx={{borderRadius: 1}}
          >
            {t("WishList.No")}
          </Button>
          <Button
            variant="contained"
            fullWidth
            onClick={deleteWishlistItem}
            disabled={apiInProgress}
            sx={{borderRadius: 1}}
          >
            {apiInProgress ? <CircularProgress size={20}/> : t("WishList.Yes")}
          </Button>
        </DialogActions>
      </Dialog>
      {productModalDetails && (
        <ModalProductDetails closeModal={() => setProductModalDetails(null)} wishlistProductId={productModalDetails.wishlistProductId} wishlistId={productModalDetails.wishlistId}
                             qty={productModalDetails.qty} isOpen={productModalDetails.isOpen}
                             inStock={productModalDetails.inStock}/>
      )}
    </>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  itemSubView: {
    flex: 1,
    display: 'flex',
    flexDirection: 'row',
    gap: 12
  },
  itemView: {
    display: 'flex',
    flexDirection: 'row',
    gap: 12,
    alignItems: 'center',
  },
  itemTextView: {
    flex: 1,
    gap: 6,
    overflow: "hidden",
    textOverflow: "ellipsis",
    whiteSpace: "nowrap",
    minWidth: 0,
  },
  itemImage: {
    height: 68,
    width: 68,
    borderRadius: 8,
  },
  neutral400: {
    color: theme.palette.neutral400,
    wordBreak: "break-word",
    whiteSpace: "normal"
  },
  iconView: {
    display: 'flex',
    flexDirection: 'row',
    gap: 4,
    alignItems: 'center',
  },
  deleteButton: {
    width: 24,
    height: 24,
  },
  modalContainer: {
    flex: 1,
    display: 'flex',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    alignItems: 'center',
    justifyContent: 'center',
    paddingRight: 16,
    paddingLeft: 16,
  },
  modalSubContainerItem: {
    width: '100%',
    backgroundColor: theme.palette.white,
    borderRadius: 16,
    paddingRight: 16,
    paddingLeft: 16,
    paddingTop: 20,
    paddingBottom: 20,
  },
  headerViewItem: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bottomViewItem: {
    display: 'flex',
    flexDirection: 'row',
    gap: 15,
    marginTop: 28,
  },
  noButtonView: {
    display: 'flex',
    height: 44,
    flex: 1,
    borderWidth: 1,
    borderRadius: 8,
    borderColor: theme.palette.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noText: {
    color: theme.palette.primary,
  },
  yesButtonView: {
    display: 'flex',
    height: 44,
    flex: 1,
    borderRadius: 8,
    backgroundColor: theme.palette.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  yesText: {
    color: theme.palette.white,
  },
  descriptionText: {
    marginTop: 8,
    color: theme.palette.neutral400,
  },
  outOfStock: {
    paddingRight: 8,
    paddingLeft: 8,
    borderRadius: 22,
    backgroundColor: theme.palette.neutral100,
    width: 82,
  },
  outOfStockLabel: {
    color: theme.palette.neutral300,
  },
}));

export default WishlistItem;
