import React, {memo, useEffect, useRef, useState} from 'react';
import {useSelector} from 'react-redux';
import axios from 'axios';
import StoreNearMe from './components/StoreNearMe';
import useNetworkHandling from '../../../../hooks/useNetworkHandling';
import {API_BASE_URL, SERVICEABLE_LOCATIONS,} from '../../../../utils/apiActions';
import useNetworkErrorHandling from '../../../../hooks/useNetworkErrorHandling';
import useCalculateTimeToShip from '../../hooks/useCalculateTimeToShip';
import Header from '../../components/header/Header';
import {emptyAlertCallback, skeletonList} from '../../../../utils/utils';
import StoreSkeleton from './components/StoreSkeleton';
import SafeAreaPage from '../../components/page/SafeAreaPage';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import {makeStyles} from "@mui/styles";

interface StoresNearMe {
  domain?: string;
}

const CancelToken = axios.CancelToken;

const MemoizedStore = memo(StoreNearMe, (prevProps, nextProps) => {
  return prevProps?.store?.id === nextProps?.store?.id;
});

const ListFooterComponent = ({
                               moreListRequested,
                             }: {
  moreListRequested: boolean;
}) => {
  const styles = useStyles();

  if (moreListRequested) {
    return (
      <>
        {skeletonList.map((one) => (
          <Grid item xs={4} key={one.id}>
            <StoreSkeleton key={one.id}/>
          </Grid>
        ))}
      </>
    );
  }

  return <></>;
};

const renderItem = ({item}: { item: any }) => <MemoizedStore store={item}/>;

const StoresNearMe: React.FC<StoresNearMe> = ({route}: any) => {
  const styles = useStyles();
  const {calculateTimeToShip} = useCalculateTimeToShip();
  const {address} = useSelector((state: any) => state?.address);
  const source = useRef<any>(null);
  const totalLocations = useRef<number>(0);
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const [locations, setLocations] = useState<any[]>([]);
  const [moreListRequested, setMoreListRequested] = useState<boolean>(true);
  const [page, setPage] = useState<number>(0);
  const loadMoreRef = useRef(null);
  const scrollContainerRef = useRef(null);

  useEffect(() => {
    getAllLocations(0).then(emptyAlertCallback).catch(emptyAlertCallback);
  }, []);

  const loadMoreList = () => {
    if (totalLocations.current > locations?.length && !moreListRequested) {
      getAllLocations(page).then(emptyAlertCallback).catch(emptyAlertCallback);
    }
  };

  const getAllLocations = async (pageNumber: number) => {
    setMoreListRequested(true);
    try {
      source.current = CancelToken.source();
      const queryParams = [
        `page=${pageNumber}`,
        'limit=21',
        `latitude=${address.address?.lat}`,
        `longitude=${address.address?.lng}`,
        `pincode=${address.address.areaCode}`,
        'radius=100',
      ];

      if (route?.params?.domain) {
        queryParams.push(`domain=${route.params.domain}`);
      }
      if (route?.params?.tagName) {
        queryParams.push(`searchTag=${route.params.tagName}`);
      }
      if (route?.params?.tts) {
        queryParams.push(`tts=${route.params.tts}`);
      }
      if (route?.params?.serviceability) {
        queryParams.push(`serviceability=${route.params.serviceability}`);
      }
      const url = `${API_BASE_URL}${SERVICEABLE_LOCATIONS}?${queryParams.join(
        '&',
      )}`;
      const {data} = await getDataWithAuth(url, source.current.token);
      const distanceData = calculateTimeToShip(data.data);
      setPage(pageNumber + 1);
      const list =
        pageNumber === 0 ? distanceData : [...locations, ...distanceData];
      setLocations(list);
      totalLocations.current = data.data.length > 0 ? data.count : list.length;
    } catch (error) {
      handleApiError(error);
    } finally {
      setMoreListRequested(false);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          loadMoreList();
        }
      },
      {
        root: scrollContainerRef.current ?? null, // Use scroll container
        threshold: 0.5
      }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [locations]);

  return (
    <SafeAreaPage>
      <Header label={route?.params?.title}/>
      <Grid 
        container spacing={2} className={styles.subContainer}
        ref={scrollContainerRef}
      >
        {locations.map((locationItem: any) => (
          <Grid item xs={4} key={locationItem.id}>
            {renderItem({item: locationItem})}
          </Grid>
        ))}
        <div ref={loadMoreRef} className={styles.loadMoreTrigger}/>
        <ListFooterComponent moreListRequested={moreListRequested}/>
      </Grid>
    </SafeAreaPage>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  subContainer: {paddingRight: 16, paddingLeft: 16, marginTop: 20, paddingBottom: 116, overflow: 'auto', height: '100vh'},
  columnWrapper: {
    gap: 11,
  },
  skeletonContainer: {
    display: 'flex',
    marginTop: 12,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 11,
  },
  loadMoreTrigger: {
    height: '10px'
  },
}));

export default StoresNearMe;
