import React from "react";
import Icon from "@mui/icons-material/Clear";
import { makeStyles } from "@mui/styles";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import { useLocation, useNavigate } from "react-router-dom";
import SubCategory from "../../components/page/components/subCategories/SubCategory";
import Modal from "@mui/material/Modal";
import Slide from "@mui/material/Slide";
import IconButton from "@mui/material/IconButton";
import ClearIcon from "@mui/icons-material/ClearRounded";
import {useTheme} from "@mui/material/styles";

const imageStyle = { height: 82, width: 82 };

interface SubCategoriesProps {
  allSubCategories: any[]; // Replace `any` with actual type if known
  categoryName: string;
  defaultCategoryImage: string;
  domain: string;
  imageStyle?: React.CSSProperties; // or string if it's a class
  numberOfColumns: number;
  numberOfRows: number;
  tagName: string;
  tagImage: string;
  tts: any; // Replace with actual type
  serviceability: any; // Replace with actual type
  redirectTo: string;
  open: boolean;
  handleClose: () => void;
}

const SubCategories: React.FC<SubCategoriesProps> = ({
  allSubCategories,
  categoryName,
  defaultCategoryImage,
  domain,
  imageStyle,
  numberOfColumns,
  numberOfRows,
  tagName,
  tagImage,
  tts,
  serviceability,
  redirectTo,
  open,
  handleClose,
}) => {
  const navigate = useNavigate();
  const { state } = useLocation();
  const styles = useStyles();
  const theme = useTheme();

  const goBack = () => navigate(-1);

  return (
    <Box>
      <Modal
        open={open}
        // onClose={handleClose}
        sx={{
          display: "flex",
          alignItems: "flex-end",
          justifyContent: "center",
        }}
      >
        <Slide direction="up" in={open} mountOnEnter unmountOnExit>
          <Box
            sx={{
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              boxShadow: 24,
              maxHeight: '85vh',
              // overflow: 'auto',
            }}
          >
            <Box display="flex" justifyContent="center" mb={2}>
              <IconButton
                onClick={handleClose}
                sx={{
                  width: 36,
                  height: 36,
                  bgcolor: theme.palette.grey[900],
                  color: theme.palette.common.white,
                  '&:hover': {
                    bgcolor: theme.palette.grey[500],
                  },
                }}
              >
                <ClearIcon/>
              </IconButton>
            </Box>
            <Box display="flex" justifyContent="center">
                <Box 
                  sx={{
                    bgcolor: '#fff',
                    borderTopLeftRadius: 16,
                    borderTopRightRadius: 16,
                    width: '100%',
                    overflow: "hidden"
                  }} 
                  display="flex" justifyContent="center"
                >
                  <Grid
                    container
                    spacing={2}
                    justifyContent="center"
                    className={styles.listContainer}
                  >
                    {allSubCategories?.map((item: any) => (
                      <Grid item xs={3} sm={3} md={3} lg={2} xl={2} key={item.code}>
                        <Box className={styles.subCategory}>
                          <SubCategory
                            isFirst={false}
                            categoryDomain={domain}
                            tagName={tagName}
                            tagImage={tagImage}
                            tts={tts}
                            serviceability={serviceability}
                            redirectTo={redirectTo}
                            categoryName={categoryName}
                            subCategory={item}
                            imageStyle={imageStyle}
                            defaultCategoryImage={defaultCategoryImage}
                          />
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
            </Box>

          </Box>
          {/* <Box className={styles.container}>
            <Box className={styles.topSafeArea}>
              <Box className={styles.sheetContainer}>
                <Grid
                  container
                  spacing={5}
                  justifyContent="center"
                  className={styles.listContainer}
                >
                  {allSubCategories?.map((item: any) => (
                    <Grid item xs={6} sm={4} md={3} key={item.code}>
                      <Box className={styles.subCategory}>
                        <SubCategory
                          isFirst={false}
                          categoryDomain={domain}
                          tagName={tagName}
                          tagImage={tagImage}
                          tts={tts}
                          serviceability={serviceability}
                          redirectTo={redirectTo}
                          categoryName={categoryName}
                          subCategory={item}
                          imageStyle={imageStyle}
                          defaultCategoryImage={defaultCategoryImage}
                        />
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </Box>

            <Box className={styles.bottomSafeArea} />
          </Box> */}
        </Slide>
      </Modal>
    </Box>
  );
};
const useStyles = makeStyles<any>((theme) => ({
  container: {
    width: "100%",
    maxWidth: 600,
    backgroundColor: theme.palette.white,
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    padding: 16,
    boxShadow: "0 -2px 20px rgba(0,0,0,0.2)",
  },
  topSafeArea: {
    flex: 1,
    display: "flex",
    // justifyContent: "flex-end",
  },
  bottomSafeArea: {
    flex: 0,
    backgroundColor: theme.palette.white,
  },
  closeButtonContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  closeButton: {
    display: "flex",
    width: 36,
    height: 36,
    backgroundColor: theme.palette.neutral400,
    borderRadius: 32,
    alignItems: "center",
    justifyContent: "center",
  },
  duration: {
    color: theme.palette.neutral400,
  },
  sheetContainer: {
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    backgroundColor: theme.palette.white,
    padding: 24, // Use valid CSS
  },

  listContainer: {
    padding: "16px 16px", // Fix from paddingHorizontal

  },

  subCategory: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    paddingRight: 20,
    marginBottom: 12,
    width: "100%", // Let Grid handle size
  },
}));

export default SubCategories;
