import React from 'react';
import {makeStyles} from "@mui/styles";
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';

const StoreSkeleton = () => {
  const styles = useStyles();
  return (
    <Box className={styles.brand}>
      <Skeleton className={styles.image} />
      <Skeleton className={styles.name} />
      <Skeleton className={styles.description} />
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  brand: {
    width: '100%',
    marginBottom: 15,
  },
  image: {
    width: '100%',
    height: '64px !important',
  },
  name: {
    width: '100%',
    height: 14,
    marginTop: 8,
    marginBottom: 4,
  },
  description: {
    width: '100%',
    height: 14,
  },
}));

export default StoreSkeleton;
