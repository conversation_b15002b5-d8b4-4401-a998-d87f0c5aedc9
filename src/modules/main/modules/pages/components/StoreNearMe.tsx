import React, {useCallback, useMemo, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import useMinutesToString from '../../../hooks/useMinutesToString';
import StoreIcon from '../../../../../assets/no_store_icon.svg';
import NoImageAvailable from '../../../../../assets/no_store.png';

interface StoreImage {
  source: any;
}

const StoreImage: React.FC<StoreImage> = ({source}) => {
  const [imageSource, setImageSource] = useState(source);
  const [imageLoadFailed, setImageLoadFailed] = useState<boolean>(false);

  const styles = useStyles();

  const onError = () => {
    setImageLoadFailed(true);
    setImageSource(NoImageAvailable);
  };

  if (source) {
    return (
      <img
        src={imageLoadFailed ? NoImageAvailable : imageSource.uri}
        onError={onError}
        alt="store"
        style={{objectFit: imageLoadFailed ? 'cover' : 'contain'}}
        className={styles.brandImage}
      />
    );
  } else {
    return (
      <Box className={`${styles.brandImage} ${styles.brandImageEmpty}`}>
        <img src={StoreIcon} width={48} height={48}/>
      </Box>
    );
  }
};

const StoreNearMe = ({store}: { store: any }) => {
  const {convertMinutesToHumanReadable, translateMinutesToHumanReadable} =
    useMinutesToString();
  const navigate = useNavigate();
  const styles = useStyles();

  const navigateToDetails = useCallback(() => {
    navigate(`/store?provider_id=${store.provider}&locationId=${store.id}`);
    // state: {
    //   brandId: store.provider,
    //   outletId: store.id,
    // },
  }, [store, navigate]);

  const {timeToShip, imageSource} = useMemo(() => {
    let source = null;

    if (store?.provider_descriptor?.symbol) {
      source = {uri: store?.provider_descriptor?.symbol};
    } else if (store?.provider_descriptor?.images?.length > 0) {
      source = {uri: store?.provider_descriptor?.images[0]};
    }

    return {
      timeToShip: convertMinutesToHumanReadable(store?.minDaysWithTTS / 60),
      imageSource: source,
    };
  }, [store]);

  return (
    <Box className={styles.brand} onClick={navigateToDetails}>
      <StoreImage source={imageSource}/>
      <Typography variant="subtitle2" className={styles.name}>
        {store?.provider_descriptor?.name}
      </Typography>
      {!!timeToShip && (
        <Typography className={styles.details}>
          {translateMinutesToHumanReadable(timeToShip.type, timeToShip.time)}
        </Typography>
      )}
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  brand: {
    marginBottom: 15,

  },
  brandImage: {
    display: 'flex',
    borderRadius: 8,
    width: '100%',
    height: 64,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  brandImageEmpty: {
    backgroundColor: theme.palette.neutral200,
  },
  name: {
    color: theme.palette.neutral400,
    flex: 1,
  },
  details: {
    fontWeight: '400',
    color: theme.palette.neutral300,
  },
}));

export default StoreNearMe;
