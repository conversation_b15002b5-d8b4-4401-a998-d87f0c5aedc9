import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';

import {useCallback} from 'react';
import {useSelector} from 'react-redux';
import NoImageAvailable from '../../../../../assets/noImage.png';
import {DOMAIN_NAME_MAPPING} from '../../../../../utils/categories';
import {makeStyles} from '@mui/styles';

const Category = ({
                    domain,
                    setSelectedDomain,
                    selected,
                  }: {
  domain: string;
  setSelectedDomain: (value: string) => void;
  selected: boolean;
}) => {
  const styles = useStyles();
  const {domainImages} = useSelector((state: any) => state.categories);

  const updateCategory = useCallback(() => {
    setSelectedDomain(domain);
  }, [domain]);

  const imageSrc = domainImages[domain] || NoImageAvailable;

  if (selected) {
    return (
      <Box>
        <Box className={`${styles.imageContainer} ${styles.active}`}>
          <img
            src={imageSrc}
            alt={DOMAIN_NAME_MAPPING[domain]}
            className={styles.image}
          />
        </Box>
        <Typography className={`${styles.categoryName} ${styles.selectedCategoryName}`}>
          {DOMAIN_NAME_MAPPING[domain]}
        </Typography>
      </Box>
    );
  } else {
    return (
      <Box>
        <Box className={styles.imageContainer} onClick={updateCategory}>
          <img
            src={imageSrc}
            alt={DOMAIN_NAME_MAPPING[domain]}
            className={styles.image}
          />
        </Box>
        <Typography className={styles.categoryName}>
          {DOMAIN_NAME_MAPPING[domain]}
        </Typography>
      </Box>
    );
  }
};

const useStyles = makeStyles<any>((theme) => ({
  imageContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E2F6FF',
    backgroundColor: '#E2F6FF',
    height: 56,
  },
  image: {
    width: 53,
    height: 48,
  },
  active: {
    borderColor: theme.palette.neutral400,
    borderStyle: 'solid',
    borderWidth: 1,
  },
  categoryName: {
    fontSize: '8px !important',
    marginTop: 6,
    textAlign: 'center',
    color: theme.palette.primary.main,
    fontWeight: '600',
  },
  selectedCategoryName: {
    color: '#02406B',
  },
}));

export default Category;
