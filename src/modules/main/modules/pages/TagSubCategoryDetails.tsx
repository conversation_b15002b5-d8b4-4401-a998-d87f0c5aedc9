import Box from "@mui/material/Box"
import { makeStyles } from "@mui/styles"
import { useEffect, useState } from "react"
import { useLocation, useSearchParams } from "react-router-dom"
import AnimationPage from "../../components/category/AnimationPage"
import HeaderWithSearchParams from "../../components/header/HeaderWithSearchParams"
import SafeAreaPage from "../../components/page/SafeAreaPage"
import GroceryProducts from "../../components/products/GroceryProducts"
import useCategories from "../../hooks/useCategories"
import useTagSubCategories from "../../hooks/useTagSubCategories"
import PageLoader from "../dashboard/components/pageLoader/PageLoader"
import SubCategories from "../subCategory/components/SubCategories"
import Category from "./components/Category"

const TagSubCategoryDetails = () => {
  const styles = useStyles()
  const [searchParams] = useSearchParams()
  const location = useLocation()
  const tagName = searchParams.get("tagName") ?? ""
  const tts = searchParams.get("tts") ?? ""
  const serviceability = searchParams.get("serviceability") ?? ""
  const subCategory = (searchParams.get("subCategory") ?? "").replace(
    /\|/g,
    "&"
  )

  const categoryDomain = searchParams.get("categoryDomain") ?? ""
  const tagImage = searchParams.get("tagImage") ?? ""
  const { getDomainImages } = useCategories()
  const { domains, domainRequested } = useTagSubCategories(
    tagName,
    tts,
    serviceability
  )
  const [currentSubCategory, setCurrentSubCategory] = useState(subCategory)
  const [selectedDomain, setSelectedDomain] = useState<string>(categoryDomain)

  useEffect(() => {
    setCurrentSubCategory(subCategory)
  }, [subCategory])

  useEffect(() => {
    if (selectedDomain) {
      if (selectedDomain === categoryDomain) {
        setCurrentSubCategory(subCategory)
      } else {
        const subCategories = domains[selectedDomain]
        if (subCategories && subCategories.length > 0) {
          setCurrentSubCategory(subCategories[0].code ?? "")
        }
      }
    }
    getDomainImages()
  }, [selectedDomain, domains])

  if (domainRequested) {
    return <PageLoader />
  }

  return (
    <SafeAreaPage>
      <Box className={styles.container}>
        <HeaderWithSearchParams
          label={currentSubCategory}
          categoryDomain={selectedDomain}
          subCategory={currentSubCategory}
          tagId={tagName}
        />
        <AnimationPage
          list={
            <SubCategories
              currentSubCategory={currentSubCategory}
              subCategories={domains[selectedDomain] ?? []}
              setCurrentSubCategory={setCurrentSubCategory}
            />
          }
          message={
            <Box className={styles.messageContainer}>
              {tagImage ? (
                <Box
                  component="img"
                  src={tagImage}
                  alt="tag"
                  className={styles.tagImage}
                />
              ) : null}
            </Box>
          }
        >
          <>
            <Box className={styles.categoryContainer}>
              <Box
                sx={{
                  display: "flex",
                  overflowX: "auto",
                  gap: 1,
                  paddingBottom: "8px",
                }}
                className={styles.categoryScrollView}
              >
                {Object.keys(domains).map((domain) => (
                  <Category
                    domain={domain}
                    setSelectedDomain={setSelectedDomain}
                    selected={domain === selectedDomain}
                    key={domain}
                  />
                ))}
              </Box>
            </Box>

            {!!selectedDomain && (
              <GroceryProducts
                tagId={tagName}
                serviceability={serviceability}
                tts={tts}
                categoryDomain={selectedDomain}
                currentSubCategory={currentSubCategory}
                searchText=""
                provider={null}
                isOpen
                isSearch={false}
                fbDomain={false}
                grocery
              >
                <></>
              </GroceryProducts>
            )}
          </>
        </AnimationPage>
      </Box>
    </SafeAreaPage>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
    backgroundColor: "white",
    paddingBottom: 16,
  },
  categoryContainer: {
    paddingLeft: 16,
    paddingRight: 16,
    marginBottom: 10,
    marginTop: 6,
  },
  categoryScrollView: {
    gap: 8,
  },
  messageContainer: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    marginTop: 8,
  },
  message: {
    color: theme.palette.primary.main,
    lineHeight: 13,
  },
  tagImage: {
    width: 72,
    height: 60,
  },
}))

export default TagSubCategoryDetails
