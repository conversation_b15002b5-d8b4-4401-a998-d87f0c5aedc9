import React, {useEffect} from 'react';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import useCurrentPage from '../../hooks/useCurrentPage';
import PageLoader from '../dashboard/components/pageLoader/PageLoader';
import PageDetails from '../../components/page/components/PageDetails';
import useBackHandler from '../../hooks/useBackHandler';
import {makeStyles} from "@mui/styles";
import {useSearchParams} from 'react-router-dom';

const DynamicPage = () => {
  const [searchParams] = useSearchParams();
  const tag = searchParams.get('tag') ?? '';
  const {goBack} = useBackHandler();
  const classes = useStyles();
  const {pageRequested, currentPage, updateCurrentPageName} = useCurrentPage(tag);

  useEffect(() => {
    if (tag) {
      updateCurrentPageName(tag);
    }
  }, [tag]);

  if (pageRequested) {
    return <PageLoader/>;
  }

  return (
    <Box className={classes.container}>
      <Box className={classes.topSafeArea}/>
      <Box className={classes.bottomSafeArea}>
        <Box className={classes.header}>
          <IconButton onClick={goBack} className={classes.closeButton}>
            <ArrowBackIcon sx={{color: '#fff'}}/>
          </IconButton>
          <Typography variant="titleLarge" className={classes.title}>
            {currentPage?.pageTitle}
          </Typography>
        </Box>
        <PageDetails
          pageName={currentPage?.name}
          sections={currentPage?.sections}
          pageBackground={currentPage?.pageBackground}
        />
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
  },
  topSafeArea: {
    flex: 0,
    backgroundColor: theme.palette.primary.main,
  },
  bottomSafeArea: {
    flex: 1,
    backgroundColor: theme.palette.white,
  },
  header: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: '14px 16px',
    gap: 20,
    backgroundColor: theme.palette.primary.main,
  },
  title: {
    color: theme.palette.white,
  },
  closeButton: {
    width: 24,
    height: 24,
  },
}));

export default DynamicPage;
