import React, { useEffect } from 'react';
import {useSearchParams, useNavigate} from "react-router-dom";
import PageLoader from '../dashboard/components/pageLoader/PageLoader';
import { DOMAINS } from '../../../../utils/domains';
import { setStoredData } from '../../../../utils/storage';

const CategoryPage: React.FC = () => {

    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
  
    // const queryParams = new URLSearchParams(location.search);
    const paramDomain = searchParams.get('domain') ?? '';

    useEffect(() => {
        if(paramDomain){
            const findDomain = DOMAINS?.find((domainItem: any) => domainItem?.domain === paramDomain);
            if(findDomain?.name){
                setStoredData('home_page_tab', findDomain?.name);
                navigate("/dashboard", {replace: true});
            }
            
        }
    }, [paramDomain]);

    return <PageLoader/>;
};

export default CategoryPage;
