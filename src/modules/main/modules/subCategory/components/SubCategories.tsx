import Box from "@mui/material/Box"
import List from "@mui/material/List"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import React, { useEffect, useRef } from "react"
import Image from "../../../components/image/Image"
import "./SubCategories.css"

interface SubCategoriesProps {
  currentSubCategory: string
  subCategories: any[]
  setCurrentSubCategory: (newSubCategory: string) => void
}

const SubCategories: React.FC<SubCategoriesProps> = ({
  currentSubCategory,
  subCategories,
  setCurrentSubCategory,
}) => {
  const theme = useTheme()
  const listRef = useRef<any>(null)
  const boxRef = useRef(null)
  const itemRefs = useRef<Record<string, HTMLDivElement | null>>({})

  useEffect(() => {
    const node = itemRefs.current[currentSubCategory]
    if (node && boxRef.current) {
      try {
        const container = boxRef.current
        const itemTop = node.offsetTop
        const containerTop = container.offsetTop
        const scrollPosition = itemTop - containerTop - 20 // 20px offset from top

        container.scrollTo({
          top: scrollPosition,
          behavior: "smooth",
        })
      } catch (error) {}
      // Calculate the position relative to the scrollable container
    }
  }, [currentSubCategory, subCategories?.length])

  const updateSubCategory = (subCategory: any) => {
    if (subCategory.code !== currentSubCategory) {
      setCurrentSubCategory(subCategory.code)
    }
  }

  const renderItem = (item: any) => {
    const isSelected = item?.code === currentSubCategory

    return (
      <Box
        onClick={() => updateSubCategory(item)}
        sx={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          ml: 1,
          py: 1,
        }}
        ref={(el) => {
          if (itemRefs.current) {
            itemRefs.current[item.code] = el
          }
        }}
      >
        <Box
          sx={{
            width: 52,
            height: 52,
            mb: 1,
            backgroundColor: "#E2F6FF",
            borderRadius: "50%",
            border: `1px solid ${
              isSelected ? "#02406B" : theme.palette.divider
            }`,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <Image source={item.url} imageStyle="image" />
        </Box>
        <Typography
          variant={isSelected ? "labelLarge" : "labelSmall"}
          sx={{ color: theme.palette.text.primary, textAlign: "center" }}
        >
          {item.label}
        </Typography>
      </Box>
    )
  }

  useEffect(() => {
    if (subCategories?.length > 0 && currentSubCategory) {
      // const index = subCategories?.findIndex((one) => one.code === currentSubCategory);
    }
  }, [subCategories, currentSubCategory])

  return (
    <Box
      sx={{
        marginTop: 2,
        paddingBottom: 12,
        overflowY: "auto",
        height: "100dvh",
      }}
      ref={boxRef}
    >
      <List ref={listRef} style={{ paddingBottom: "160px" }}>
        {subCategories?.map((item) => renderItem(item))}
      </List>
    </Box>
  )
}

export default SubCategories
