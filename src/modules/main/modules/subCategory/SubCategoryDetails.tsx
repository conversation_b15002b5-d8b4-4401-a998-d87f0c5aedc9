import React, { useState } from "react"
import { useSearchParams } from "react-router-dom"
import AnimationPage from "../../components/category/AnimationPage"
import Header from "../../components/header/HeaderWithActions"
import SafeAreaPage from "../../components/page/SafeAreaPage"
import ListingPage from "../../components/productsLists/ListingPage"
import useTagSubCategories from "../../hooks/useTagSubCategories"
import PageLoader from "../dashboard/components/pageLoader/PageLoader"
import SubCategories from "./components/SubCategories"

const SubCategoryDetails: React.FC = () => {
  const [searchParams] = useSearchParams()

  // const queryParams = new URLSearchParams(location.search);
  const domain =
    searchParams.get("categoryDomain") || searchParams.get("domain") || ""
  const [currentSubCategory, setCurrentSubCategory] = useState<string>(
    (
      searchParams.get("subCategory") ||
      searchParams.get("sub_category") ||
      ""
    ).replace(/\|/g, "&")
  )
  const { domains, domainRequested } = useTagSubCategories("", "", "")

  if (domainRequested) {
    return <PageLoader />
  }

  return (
    <SafeAreaPage>
      <>
        <Header label={currentSubCategory} search wishlist cart />
        <AnimationPage
          list={
            <SubCategories
              currentSubCategory={currentSubCategory}
              subCategories={domains[domain] ?? []}
              setCurrentSubCategory={setCurrentSubCategory}
            />
          }
        >
          <ListingPage
            searchQuery=""
            subCategories={currentSubCategory}
            categoryDomain={domain}
          />
        </AnimationPage>
      </>
    </SafeAreaPage>
  )
}

export default SubCategoryDetails
