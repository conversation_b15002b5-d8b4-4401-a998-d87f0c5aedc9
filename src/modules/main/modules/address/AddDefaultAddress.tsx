import React, {useRef, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import axios from "axios";
import {useTranslation} from "react-i18next";
import {useLocation, useNavigate} from "react-router-dom";
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import AddressForm from "./components/AddressForm";
import Header from "../../components/header/Header";
import "./AddDefaultAddress.css";
import {API_BASE_URL, DELIVERY_ADDRESS} from "../../../../utils/apiActions";
import {setStoredData} from "../../../../utils/storage";
import useNetworkErrorHandling from "../../../../hooks/useNetworkErrorHandling";
import useNetworkHandling from "../../../../hooks/useNetworkHandling";
// import useLogAppsFlyerEvent from "../../../../hooks/useLogAppsFlyerEvent";
import {setAddress} from "../../../../toolkit/reducer/address";
import {useToast} from "../../../../hooks/toastProvider";

const CancelToken = axios.CancelToken;

const AddDefaultAddress: React.FC = () => {
  const dispatch = useDispatch();
  const {t} = useTranslation();
  const source = useRef<any>(null);
  const navigate = useNavigate();
  const location = useLocation();
  const {showToast} = useToast();
  const {name, emailId, phoneNumber} = useSelector((state: any) => state.auth);
  const {uid} = useSelector((state: any) => state.auth);
  const {postDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const [apiInProgress, setApiInProgress] = useState<boolean>(false);

  const saveToServer = async (values: any): Promise<void> => {
    const payload = {
      descriptor: {
        name: values.name,
        email: values.email,
        phone: values.number,
      },
      defaultAddress: true,
      address: {
        areaCode: values.areaCode,
        building: values.building,
        city: values.city,
        country: "IND",
        state: values.state,
        street: values.street,
        tag: values.tag,
        lat: values.lat,
        lng: values.lng,
        completeAddress: values.completeAddress,
      },
    };

    try {
      setApiInProgress(true);
      source.current = CancelToken.source();
      const {data} = await postDataWithAuth(
        `${API_BASE_URL}${DELIVERY_ADDRESS}`,
        payload,
        source.current.token
      );
      // logEvent(APPS_FLYER_EVENTS.ADDRESS_ADDED, {
      //   user_id: uid,
      //   timestamp: getCurrentDate(),
      // });
      showToast(
        t("Address Form.Your delivery address has been added successfully"), 'info'
      );
      setStoredData("address", JSON.stringify(data));
      dispatch(setAddress(data));
      location.state?.fromDashboard ? navigate("/dashboard") : navigate(-1);
    } catch (error) {
      handleApiError(error);
    } finally {
      setApiInProgress(false);
    }
  };

  return (
    <Box className="add-address-container">
      <Header label={t("Address Form.Add Address")}/>
      {apiInProgress && <CircularProgress className="loader"/>}
      <AddressForm
        name={name || ""}
        email={emailId || ""}
        phone={phoneNumber ? String(phoneNumber) : ""}
        address={null}
        apiInProgress={apiInProgress}
        saveAddress={saveToServer}
      />
    </Box>
  );
};

export default AddDefaultAddress;
