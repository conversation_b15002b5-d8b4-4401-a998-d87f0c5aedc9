import React, {useEffect, useRef, useState} from "react";
import axios from "axios";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {useLocation, useNavigate} from "react-router-dom";
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Snackbar from '@mui/material/Snackbar';
import Box from "@mui/material/Box";
import useNetworkErrorHandling from "../../../../hooks/useNetworkErrorHandling";
import useNetworkHandling from '../../../../hooks/useNetworkHandling';
import {API_BASE_URL, UPDATE_DELIVERY_ADDRESS} from "../../../../utils/apiActions";
import AddressForm from "./components/AddressForm";
import {updateExistingAddress} from "../../../../toolkit/reducer/address";
import Header from "../../components/header/Header";
import "./UpdateAddress.css";

const CancelToken = axios.CancelToken;

const UpdateAddress: React.FC = () => {
  const dispatch = useDispatch();
  const {t} = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const addressData = location.state?.address || {};

  const source = useRef<any>(null);
  const {postDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const [apiInProgress, setApiInProgress] = useState<boolean>(false);
  const [toastMessage, setToastMessage] = useState<string | null>(null);

  const saveAddress = async (values: any): Promise<void> => {
    const payload = {
      descriptor: {
        name: values.name,
        email: values.email,
        phone: values.number,
      },
      defaultAddress: values.defaultAddress,
      address: {
        areaCode: values.areaCode,
        building: values.building,
        city: values.city,
        country: "IND",
        door: values.building,
        state: values.state,
        street: values.street,
        tag: values.tag,
        lat: values.lat,
        lng: values.lng,
        completeAddress: values.completeAddress,
      },
    };

    try {
      setApiInProgress(true);
      source.current = CancelToken.source();
      const {data} = await postDataWithAuth(
        `${API_BASE_URL}${UPDATE_DELIVERY_ADDRESS}${addressData.id}`,
        payload,
        source.current.token
      );
      dispatch(updateExistingAddress({address: data, id: addressData.id}));
      setToastMessage(t("Address Form.Your delivery address has been updated successfully"));
      navigate(-1);
    } catch (error) {
      handleApiError(error);
    } finally {
      setApiInProgress(false);
    }
  };

  useEffect(() => {
    document.title = t("Address Form.Update Delivery Address");
  }, [t]);

  return (
    <Box className="update-address-container">
      <Header label={t("Address Form.Update Delivery Address")}/>
      <AddressForm
        name={addressData?.descriptor?.name}
        email={addressData?.descriptor?.email}
        phone={addressData?.descriptor?.phone}
        address={addressData?.address}
        apiInProgress={apiInProgress}
        saveAddress={saveAddress}
      />
      {apiInProgress && <CircularProgress className="loading-spinner"/>}
      <Button variant="contained" color="primary" onClick={() => navigate(-1)}>
        Cancel
      </Button>
      <Snackbar
        open={Boolean(toastMessage)}
        autoHideDuration={3000}
        onClose={() => setToastMessage(null)}
        message={toastMessage}
      />
    </Box>
  );
};

export default UpdateAddress;
