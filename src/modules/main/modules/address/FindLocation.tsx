import React, {useEffect, useRef} from "react";
import styles from "./FindLocation.module.css";
import Typography from '@mui/material/Typography';
import <PERSON><PERSON> from "react-lottie";
import axios from "axios";
import {useDispatch} from "react-redux";
import {getDistance} from "geolib";
import {useNavigate} from "react-router-dom";

import locationTrackingAnimation from "../../../../assets/locationTracking.json";
import {setAddress, setAddressListAvailable} from "../../../../toolkit/reducer/address";
import {setStoredData} from "../../../../utils/storage";
import {NEAREST_DISTANCE} from "../../../../utils/constants";
import {MAPPLS_BASE_URL} from "../../../../utils/apiActions";
import {emptyAlertCallback} from "../../../../utils/utils";
import Box from "@mui/material/Box";
import useGetAddressesOfUser from "../../hooks/useGetAddressesOfUser";
import useCurrentLocation from "../../hooks/useCurrentLocation";

const CancelToken = axios.CancelToken;

const FindLocation = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const source = useRef<any>(null);
  const getAddresses = useGetAddressesOfUser();
  const getUserLocation = useCurrentLocation();

  const getAddressList = async (location: { latitude: number; longitude: number }) => {
    try {
      const data = await getAddresses();
      if (data.length > 0) {
        dispatch(setAddressListAvailable(true));

        let addressesWithDistance = data.map((address: any) => {
          if (address?.address) {
            return {
              ...address,
              distance: getDistance(location, {
                latitude: Number(address.address.lat),
                longitude: Number(address.address.lng),
              }),
            };
          }
          return {...address, distance: 99999};
        });

        addressesWithDistance.sort((a: any, b: any) => a.distance - b.distance);
        const firstAddress = addressesWithDistance[0];

        if (firstAddress.distance <= NEAREST_DISTANCE) {
          setStoredData("address", JSON.stringify(firstAddress));
          dispatch(setAddress(firstAddress));
          setTimeout(() => navigate("/dashboard"), 100);
        } else {
          await getAddressFromLatLong(location);
        }
      } else {
        dispatch(setAddressListAvailable(false));
        await getAddressFromLatLong(location);
      }
    } catch (error) {
      console.error("Error fetching address list:", error);
      await getAddressFromLatLong(location);
    }
  };

  const getAddressFromLatLong = async (location: { latitude: number; longitude: number }) => {
    try {
      source.current = CancelToken.source();
      const apiKey = process.env.REACT_APP_MMI_API_KEY;
      const response = await axios.get(
        `${MAPPLS_BASE_URL}/${apiKey}/rev_geocode?lat=${location.latitude}&lng=${location.longitude}`,
        {cancelToken: source.current.token}
      );

      if (response.data?.results?.length > 0) {
        let address = response.data.results[0];
        address = {...address, areaCode: address.pincode};

        dispatch(setAddress({address}));
        navigate("/dashboard");
      } else {
        navigate("/select-location");
      }
    } catch (error) {
      console.error("Error getting address from lat/long:", error);
      navigate("/select-location");
    }
  };

  const captureUsersLocation = async () => {
    try {
      const location = await getUserLocation();
      await getAddressList(location);
    } catch (error) {
      console.error("Location permission denied:", error);
      navigate("/select-location");
    }
  };

  useEffect(() => {
    captureUsersLocation().finally(emptyAlertCallback);

    return () => {
      if (source.current) {
        source.current.cancel();
      }
    };
  }, []);

  return (
    <Box className={styles.container}>
      <Box className={styles.lottieWrapper}>
        <Lottie options={{animationData: locationTrackingAnimation, loop: true, autoplay: true}}/>
      </Box>
      <Typography variant="body1" className={styles.message}>
        Finding Your Location
      </Typography>
    </Box>
  );
};

export default FindLocation;
