import React, {useCallback} from "react";
import {useSelector} from "react-redux";
import {useLocation, useNavigate} from "react-router-dom";
import Box from '@mui/material/Box';
import AddressList from "./components/AddressList";
import CloseIcon from "@mui/icons-material/Close";
import "./AddressSheet.css";
import Typography from "@mui/material/Typography";
import {useTranslation} from "react-i18next";

const AddressSheet: React.FC = () => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const {address} = useSelector((state: any) => state.address);

  const addressSelectionCompleted = useCallback(() => {
    if (location.state?.navigateToDashboard) {
      navigate("/dashboard", {replace: true});
    } else {
      navigate(-1);
    }
  }, [navigate, location]);

  return (
    <Box sx={{overflow: 'hidden', height: '100dvh'}}>
      <Box
        sx={{display: 'flex', flexDirection: 'row', justifyContent: 'space-between', padding: 2, alignItems: 'center'}}>
        <Typography variant="headlineSmall">
          {t('Address List.Select a Delivery Address')}
        </Typography>
        <Box className="close-icon" onClick={() => navigate(-1)}>
          <CloseIcon/>
        </Box>
      </Box>
      <AddressList
        deliveryAddress={address}
        addressSelectionCompleted={addressSelectionCompleted}
        navigateToDashboard={!!location.state?.navigateToDashboard}
      />
    </Box>
  );
};

export default AddressSheet;
