import React, {useCallback, useEffect, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useNavigate} from 'react-router-dom';
import {useSelector} from 'react-redux';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import LocationHeader from './components/LocationHeader';
import './SelectLocation.css';

const SelectLocation: React.FC = () => {
  const navigate = useNavigate();
  const {t} = useTranslation();
  const {addressListPresent} = useSelector((state: any) => state.address);
  const [loader, setShowLoader] = useState<boolean>(true);

  const navigateToAddAddress = useCallback(() => {
    navigate('/add-default-address');
  }, [navigate]);

  const navigateToAddressList = useCallback(() => {
    navigate('/address-sheet', {state: {navigateToDashboard: true}});
  }, [navigate]);

  useEffect(() => {
    setShowLoader(false);
  }, []);

  return (
    <Container className="page-container">
      <LocationHeader onPress={navigateToAddAddress}/>
      {loader ? (
        <Box className="loader-container">
          <CircularProgress color="primary"/>
        </Box>
      ) : (
        <Box className="content-container">
          <Box className="location-icon-box">
            <LocationOnIcon className="select-location-icon"/>
          </Box>
          <Box className="text-container">
            <Typography variant="bodyLarge" className="text-message">
              {t('Select Location.Uh-oh! Cannot proceed without your location')}
            </Typography>
            <Typography variant="labelSmall" className='continue-text'>
              {addressListPresent
                ? t('Select Location.Please select an address to continue')
                : t('Select Location.Please select delivery location to continue')}
            </Typography>
          </Box>
          {addressListPresent ? (
            <Button variant="contained" className="address-button" onClick={navigateToAddressList}>
              {t('Select Location.Select Address')}
            </Button>
          ) : (
            <Button variant="contained" className="address-button" onClick={navigateToAddAddress}>
              {t('Select Location.Select Delivery Location')}
            </Button>
          )}
        </Box>
      )}
    </Container>
  );
};

export default SelectLocation;
