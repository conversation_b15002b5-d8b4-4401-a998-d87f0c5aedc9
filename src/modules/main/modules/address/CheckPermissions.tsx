import React, {useEffect, useRef} from "react";
import {useTranslation} from "react-i18next";
import Typography from '@mui/material/Typography';
import LocationPickup from "../../../../assets/globe.svg";
import LocationPrompt from "../../../../components/locationPrompt/LocationPrompt";
import "./CheckPermissions.css";
import Box from "@mui/material/Box";

const CheckPermissions: React.FC = () => {
  const {t} = useTranslation();
  const locationPromptRef = useRef<any>(null);
  const navigatingToSettings = useRef<boolean>(false);

  const updateNavigatingToSettings = () => {
    navigatingToSettings.current = true;
  };

  useEffect(() => {
    setTimeout(() => {
      locationPromptRef?.current?.checkPermissions();
    }, 500);

    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && navigatingToSettings.current) {
        locationPromptRef?.current?.checkPermissions();
        navigatingToSettings.current = false;
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, []);

  return (
    <Box className="permission-container">
      <img src={LocationPickup} alt="Location Pickup" className="location-icon"/>
      <Typography variant="headlineMedium" className="title">
        {t("Find Location.Fetching Details")}
      </Typography>
      <Typography variant="labelSmall" className="message">
        {t("Find Location.Please give us a few seconds to grab your details")}
      </Typography>
      <LocationPrompt ref={locationPromptRef} updateNavigatingToSettings={updateNavigatingToSettings}/>
    </Box>
  );
};

export default CheckPermissions;
