import React from "react";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import ExpandMoreIcon from "@mui/icons-material/ExpandMoreOutlined";
import "./LocationHeader.css";

import {ReactComponent as Logo} from "../../../../../assets/dashboard/app.svg";

interface LocationHeaderProps {
  onPress?: () => void;
}

const LocationHeader: React.FC<LocationHeaderProps> = ({onPress}) => {
  return (
    <div className="location-address-container" onClick={onPress}>
      <Logo height={32} width={32}/>
      <Typography variant="body1" className="deliver-to">
        Select Delivery Location
      </Typography>
      <IconButton className="icon-button">
        <ExpandMoreIcon fontSize="medium"/>
      </IconButton>
    </div>
  );
};

export default LocationHeader;
