.address-header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 16px;
    background-color: white;
}

.shipping-address {
    margin-top: 16px;
    margin-bottom: 12px;
    padding-left: 16px;
    font-weight: bold;
}

.button {
    margin: 20px 16px;
    background-color: #0074AA !important;
    border-radius: 8px;
    padding: 13px;
    text-align: center;
    color: white;
    font-weight: bold;
    cursor: pointer;
    border: none;
}

.button:hover {
    background-color: #0074AA !important;
}

.content-container-style {
    padding-bottom: 16px;
    padding-left: 16px;
}

.empty-list-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    margin-top: 50px;
}

.empty-list-message {
    margin-top: 18px !important;
    font-size: 16px;
}

.main-container {
    height: calc(100dvh - 74px);
    display: flex;
    justify-content: space-between;
    flex-direction: column;
}

.address-list-container {
    padding-left: 16px;
    padding-right: 16px;
    height: calc(100dvh - 132px);
    overflow: scroll;
}

.add-button {
    margin-top: 20px !important;
    background-color: #0074AA !important;
    width: 100%;
}
