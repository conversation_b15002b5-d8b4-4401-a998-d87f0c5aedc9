.address-card {
    display: flex;
    /* align-items: center; */
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 8px 16px;
    background-color: white;
    margin-bottom: 16px;
    cursor: pointer;
    transition: 0.3s ease-in-out;
}

.address-card:hover {
    background-color: #f9f9f9;
}

.radio-button {
    margin-right: 12px;
    font-weight: bold !important;
    margin-bottom: auto !important;
    padding-top: 0px !important;
    padding-left: 0px !important;
}

.address-details {
    flex: 1;
    padding: 0 8px;
}

.description {
    color: #686868;
    margin-top: 4px !important;
}

.edit-button {
    width: 24px;
    height: 24px;
    padding: 4px;
}
