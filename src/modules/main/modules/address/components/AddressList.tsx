import React, {useCallback, useEffect, useMemo, useState} from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import {useDispatch} from 'react-redux';
import {useTranslation} from 'react-i18next';
import {useNavigate} from 'react-router-dom';

import Address from './Address';
import useNetworkErrorHandling from '../../../../../hooks/useNetworkErrorHandling';
import {setStoredData} from '../../../../../utils/storage';
import {setAddress} from '../../../../../toolkit/reducer/address';
import useGetAddressesOfUser from '../../../hooks/useGetAddressesOfUser';
import EmptyAddress from '../../../../../assets/empty_address.svg';
import './AddressList.css';
import AddressSkeleton from "./AddressSkeleton";

interface AddressListProps {
  deliveryAddress: any;
  addressSelectionCompleted: () => void;
  navigateToDashboard: boolean;
}

const ListEmptyComponent: React.FC = () => {
  const {t} = useTranslation();

  return (
    <Box className="empty-list-container">
      <img src={EmptyAddress} alt="Empty Address" className="empty-icon"/>
      <Typography variant="body1" className="empty-list-message">
        {t("Address List.No Saved Address")}
      </Typography>
    </Box>
  );
};

const AddressList: React.FC<AddressListProps> = ({
                                                   deliveryAddress,
                                                   addressSelectionCompleted,
                                                   navigateToDashboard,
                                                 }) => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const getAddresses = useGetAddressesOfUser();
  const {handleApiError} = useNetworkErrorHandling();
  const [apiInProgress, setApiInProgress] = useState<boolean>(false);
  const [addresses, setAddresses] = useState<any[]>([]);

  const updateDeliveryAddress = useCallback(async (newAddress: any) => {
    setStoredData('address', JSON.stringify(newAddress));
    dispatch(setAddress(newAddress));
    addressSelectionCompleted();
  }, [dispatch, addressSelectionCompleted]);

  const getAddressList = async (): Promise<void> => {
    try {
      setApiInProgress(true);
      const list = await getAddresses();
      setAddresses(list);
    } catch (error: any) {
      handleApiError(error);
    } finally {
      setApiInProgress(false);
    }
  };

  const navigateToAddAddress = useCallback(() => {
    navigate('/add-default-address', {state: {setDefaultAddress: !navigateToDashboard}});
  }, [navigate, navigateToDashboard]);

  useEffect(() => {
    getAddressList();
  }, []);

  const renderItem = useCallback(
    (item: any) => (
      <Address
        key={item?.id}
        item={item}
        isCurrentAddress={deliveryAddress?.id === item?.id}
        params={{navigateToNext: true}}
        onAddressSelect={updateDeliveryAddress}
      />
    ),
    [deliveryAddress?.id, updateDeliveryAddress],
  );

  const addressLength = useMemo(() => addresses.length, [addresses]);

  return (
    <Box className="main-container">
      {apiInProgress ? (
        <AddressSkeleton/>
      ) : (
        <Box>
          <Box className="address-list-container">
            {addressLength > 0 && (
              <Typography variant="labelLarge" className="saved-address-title">
                {t('Address List.Saved Addresses')}
              </Typography>
            )}
            {addresses?.length > 0 ? (
              <List className="address-list">
                {addresses.map((item) => renderItem(item))}
              </List>
            ) : <ListEmptyComponent/>}
          </Box>
          <Box sx={{padding: 1}}>
            <Button variant="contained" color="primary" onClick={navigateToAddAddress} className="add-button">
              {t('Address List.Add Address')}
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default AddressList;
