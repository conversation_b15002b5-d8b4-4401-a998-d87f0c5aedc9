import React, {useEffect, useRef, useState} from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid2';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import Typography from '@mui/material/Typography';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {makeStyles} from '@mui/styles';
import {mappls, mappls_plugin} from "mappls-web-maps";
import Divider from "@mui/material/Divider";
import axios from "axios";
import {useTranslation} from "react-i18next";
import useMapMeta from "../../../hooks/useMapMeta";
import "./AddressForm.css";
import useCurrentLocation from "../../../hooks/useCurrentLocation";
import {validateCityAndPincode} from "../../../../../utils/utils";
import {validateLatLong} from "../../../../../utils/utils";
import {formatAddress} from "../../../../../utils/utils";
import {useToast} from "../../../../../hooks/toastProvider";

const mapplsClassObject = new mappls();
const mapplsPluginObject = new mappls_plugin();


const addressTags = [
  {label: 'Home', value: 'home'},
  {label: 'Office', value: 'office'},
  {label: 'Other', value: 'other'},
];

const auto = {
  width: "300px",
  position: "absolute",
  zIndex: 999,
  fontSize: "15px",
  padding: "10px",
  border: "1px solid #ddd",
  outline: "none !important",
  top: "5px",
  borderRadius: "10px",
  margin: "4px",
};

const loadObject = {map: true, plugins: ["search,placepicker"]};

const PlaceSearchPlugin = ({map, userLocation, getAddressFromLatLong, updateAddress}: {
  map: any;
  userLocation: { latitude: any, longitude: any };
  getAddressFromLatLong: (lat: number, lng: number) => Promise<any>,
  updateAddress: (value: any) => void
}) => {
  const placeSearchRef = useRef<any>(null);
  const placePickerRef = useRef<any>(null);
  const placeDetailsRef = useRef<any>(null);

  useEffect(() => {
    if (map && placeSearchRef.current) {
      mapplsClassObject.removeLayer({map, layer: placeSearchRef.current});
    }

    const getLocationFromPicker = (picker: any) => {
      try {
        const {data} = picker.getLocation();
        if (validateLatLong(data)) {
          updateAddress(data);
        }
      } catch (e) {
        console.log(e);
      }
    };

    const renderPlaceMap = (latitude, longitude) => {
      getAddressFromLatLong(latitude, longitude).finally(() => {
      });

      const options = {
        map: map,
        location: {lat: latitude, lng: longitude},
        search: false,
        closeBtn: false,
        tokenizeAddress: true,
        header: false,
      };

      if (placePickerRef.current) {
        placePickerRef.current.remove();
      }

      placePickerRef.current = mapplsPluginObject.placePicker(
        options,
        (event: any) => {
          map.addListener('dragend', () => {
            getLocationFromPicker(event);
          });
        }
      );
    }

    const optional_config = {
      location: [userLocation.latitude, userLocation.longitude],
      region: "IND",
      height: 300,
      tokenizeAddress: true,
    };
    placeSearchRef.current = mapplsPluginObject.search(
      document.getElementById("auto"),
      optional_config,
      (data: any) => {
        if (data) {
          if (placeDetailsRef.current) {
            placeDetailsRef.current.remove();
          }
          if (data[0].placeName === 'Current Location') {
            renderPlaceMap(data[0].latitude, data[0].longitude);
          } else {
            placeDetailsRef.current = mapplsPluginObject.getPinDetails(
              {pin: data[0].eLoc},
              (e: any) => {
                renderPlaceMap(e.data.latitude, e.data.longitude);
              }
            );
          }
        }
      }
    );

    const options = {
      map: map,
      location: {lat: userLocation.latitude, lng: userLocation.longitude},
      search: false,
      closeBtn: false,
      tokenizeAddress: true,
      header: false,
    };

    placePickerRef.current = mapplsPluginObject.placePicker(
      options,
      (event: any) => {
        map.addListener('dragend', () => {
          getLocationFromPicker(event);
        });
      }
    );

    return () => {
      if (map && placeSearchRef.current) {
        mapplsClassObject.removeLayer({map, layer: placeSearchRef.current});
      }
    };
  }, [map]);

  return <></>;
};

const AddressForm = ({
                       address,
                       apiInProgress,
                       saveAddress,
                       name = '',
                       email = '',
                       phone = '',
                     }: any) => {
  const {showToast} = useToast();
  const {t} = useTranslation();
  const classes = useStyles();
  const mapRef = useRef(null);
  const mapplsToken = useRef<any>(null);
  const userLocationRef = useRef<any>(null);
  const {getMapMeta, loading} = useMapMeta();
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [mapAddress, setMapAddress] = useState<any>(null);
  const [isOpenMap, setIsOpenMap] = useState(true);
  const getUserLocation = useCurrentLocation();

  const formik = useFormik({
    initialValues: {
      name: name || '',
      number: phone || '',
      email: email || '',
      city: address?.city || '',
      state: address?.state || '',
      areaCode: address?.areaCode || '',
      street: address?.street || '',
      building: address?.building || '',
      tag: address?.tag || '',
      completeAddress: address?.completeAddress ?? formatAddress(address),
    },
    validationSchema: Yup.object({
      name: Yup.string().required('Required'),
      number: Yup.string().required('Required'),
      email: Yup.string().email('Invalid email'),
      building: Yup.string().required('Required'),
      tag: Yup.string().required('Required'),
    }),
    onSubmit: (values) => {
      saveAddress(values);
    },
    enableReinitialize: true,
  });

  const updateAddress = (res: any) => {
    setMapAddress(res.formatted_address);
    handleUpdateAddress(res);
  }

  const handleUpdateAddress = (res: any) => {
    if (validateCityAndPincode(res) && validateLatLong(res)) {
      const values = {
        ...formik.values,
        city: res?.city ?? res.village,
        areaCode: res?.pincode,
        state: res?.state,
        street: `${res?.street}, ${res.locality}`,
        lat: Number(res?.lat)?.toFixed(6),
        lng: Number(res?.lng)?.toFixed(6),
        number: phone,
        completeAddress: res?.formatted_address ?? formatAddress(res),
        ...(address && {
          building: address.building,
          tag: address.tag,
        }),
      };
      formik.setValues(values);
    } else {
      showToast(
        t('Address Form.Please select a valid location'), 'error'
      );
    }
  }

  const initializeMap = (accessToken: string) => {
    mapplsClassObject.initialize(accessToken, loadObject, async () => {
      let location = null;
      if (address) {
        location = {
          latitude: parseFloat(address?.lat),
          longitude: parseFloat(address?.lng),
        };
        setMapAddress(formatAddress(address));
      } else {
        location = await getUserLocation();
        await getAddressFromLatLong(location.latitude, location.longitude);
      }
      userLocationRef.current = location;

      const newMap = mapplsClassObject.Map({
        id: "map",
        properties: {
          center: [location.latitude, location.longitude],
          zoom: 14,
          clickableIcons: true,
        },
      });

      newMap.on("load", async () => {
        setIsMapLoaded(true);
      });
      mapRef.current = newMap;
    });
  }

  const getAddressFromLatLong = async (lat: number, lng: number) => {
    try {
      const {data} = await axios.get(`https://apis.mappls.com/advancedmaps/v1/${mapplsToken.current}/rev_geocode?lng=${lng}&lat=${lat}`)
      const result = data?.results?.[0];
      setMapAddress(result?.formatted_address ?? '');
      handleUpdateAddress(result);
    } catch (error) {
      console.log('error', error);
    }
  }

  // 1. Generate Token on Component Mount
  useEffect(() => {
    getMapMeta().then((data: any) => {
      if (data?.access_token) {
        mapplsToken.current = data.access_token;
        setTimeout(() => {
          initializeMap(data.access_token);
        }, 300);
      }
    });
  }, []);

  if (loading) {
    return <Box className={classes.emptyContainer}>
      <CircularProgress size="32px"/>
    </Box>
  }

  return (
    <Box className={classes.container}>
      <Box
        id="map"
        className={classes.mapContainer}
      >
        <input
          style={auto}
          type="text"
          id="auto"
          name="auto"
          className="search-outer form-control as-input"
          placeholder="Search places"
          spellCheck="false"
        />

        {isMapLoaded && <PlaceSearchPlugin map={mapRef.current} userLocation={userLocationRef.current}
                                           getAddressFromLatLong={getAddressFromLatLong}
                                           updateAddress={updateAddress}/>}
        <Box
          component="div"
          className={classes.footer}
        >
          {mapAddress && (
            <>
              <Box
                component="div"
                className={classes.footerSectionOne}
              >
                <Typography component="div" variant='titleMedium' color='textDisabled'>
                  Dropped Pin
                </Typography>
                <Typography component="div" variant='bodySmall' color='textDisabled' sx={{mt: 1}}>
                  {mapAddress}
                </Typography>
              </Box>
              <Divider/>
            </>
          )}
          <Typography component="div" variant='bodySmall' color='textDisabled' className={classes.doneNoteText}>
            You may move the map to get exact location
          </Typography>
          <Button
            fullWidth
            className={classes.doneButton}
            onClick={() => setIsOpenMap(false)}
          >
            Done
          </Button>
        </Box>
      </Box>
      {!isOpenMap && (
        <Box className="address-form-container">
          <Box className={classes.topMainView}>
            <Box className={classes.iconContainer}>
              <LocationOnIcon color="action"/>
            </Box>
            <Box className={classes.addressContainer}>
              <Typography className={classes.address} variant="labelSmall">
                {formik.values.completeAddress}
              </Typography>
            </Box>
            <Button
              size="small"
              onClick={() => {
                setIsOpenMap(true);
              }}
              className={classes.changeButton}
              disabled={apiInProgress}
            >
              Change
            </Button>
          </Box>
          <form onSubmit={formik.handleSubmit} noValidate className={classes.formContainer}>
            <Grid container spacing={0}>
              <Grid size={12}>
                <Typography variant="bodySmall" component="div" className={classes.inputLabel}>
                  {t('Address Form.Name')}
                  <Typography component="span" className={classes.required}>*</Typography>
                </Typography>
                <TextField
                  className={classes.inputText}
                  label=""
                  name="name"
                  fullWidth
                  required
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && Boolean(formik.errors.name)}
                  disabled={apiInProgress}
                />
              </Grid>

              <Grid size={12}>
                <Typography variant="bodySmall" component="div" className={classes.inputLabel}>
                  {t('Address Form.Mobile Number')}
                  <Typography component="span" className={classes.required}>*</Typography>
                </Typography>
                <TextField
                  variant="outlined"
                  fullWidth
                  name="number"
                  className={classes.inputText}
                  value={formik.values.number}
                  label=""
                  error={formik.touched.number && Boolean(formik.errors.number)}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  disabled={apiInProgress}
                  slotProps={{
                    input: {
                      startAdornment: <InputAdornment position="start">
                        <Typography variant="bodySmall" sx={{color: '#1A1A1A'}}>+91 |</Typography>
                      </InputAdornment>,
                    },
                  }}
                />
              </Grid>
              <Grid size={12}>
                <Typography variant="bodySmall" component="div" className={classes.inputLabel}>
                  {t('Address Form.Email')}
                  <Typography component="span" className={classes.required}>*</Typography>
                </Typography>
                <TextField
                  className={classes.inputText}
                  label=""
                  name="email"
                  type="email"
                  fullWidth
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.email && Boolean(formik.errors.email)}
                  disabled={apiInProgress}
                />
              </Grid>

              <Grid size={12}>
                <Typography variant="bodySmall" component="div" className={classes.inputLabel}>
                  {t('Address Form.Line 1')}
                  <Typography component="span" className={classes.required}>*</Typography>
                </Typography>
                <TextField
                  className={classes.inputText}
                  label=""
                  name="building"
                  fullWidth
                  required
                  value={formik.values.building}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.building && Boolean(formik.errors.building)}
                  disabled={apiInProgress}
                />
              </Grid>

              <Grid size={12}>
                <Typography variant="bodySmall" component="div" className={classes.inputLabel}>
                  {t('Address Form.Line 2')}
                </Typography>
                <TextField
                  className={classes.inputText}
                  label=""
                  name="street"
                  fullWidth
                  value={formik.values.street}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.street && Boolean(formik.errors.street)}
                  disabled={apiInProgress}
                />
              </Grid>

              <Grid size={12}>
                <Typography variant="bodySmall" component="div" className={classes.inputLabel}>
                  {t('Address Form.Address Type')}
                </Typography>
                <ToggleButtonGroup
                  exclusive
                  fullWidth
                  value={formik.values.tag}
                  className={classes.tagContainer}
                  onChange={(e, newValue) => {
                    if (newValue !== null) {
                      formik.setFieldValue('tag', newValue);
                    }
                  }}
                >
                  {addressTags.map((tag) => (
                    <ToggleButton size="small" key={tag.value} value={tag.value} className={classes.tag}>
                      {tag.label}
                    </ToggleButton>
                  ))}
                </ToggleButtonGroup>
                {formik.touched.tag && formik.errors.tag && (
                  <Typography color="error" variant="caption">
                    {formik.touched.tag && Boolean(formik.errors.tag)}
                  </Typography>
                )}
              </Grid>
              <Grid size={12}>
                {apiInProgress ? (
                  <Box sx={{display: "flex", alignItems: "center", justifyContent: "center"}}>
                    <CircularProgress size={24}/>
                  </Box>
                ) : (
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    className={classes.button}
                    disabled={apiInProgress}
                  >
                    Save
                  </Button>
                )}
              </Grid>
            </Grid>
          </form>
        </Box>
      )}
    </Box>
  );
};

const useStyles = makeStyles((theme: any) => ({
  inputLabel: {
    color: theme.palette.neutral.main,
    marginBottom: '4px !important',
  },
  required: {
    color: theme.palette.error.main,
  },
  emptyContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100dvh',
  },
  container: {
    position: 'relative',
  },
  tagContainer: {
    gap: 12,
    width: '80% !important'
  },
  tag: {
    color: theme.palette.neutral400 + '!important',
    height: 44,
    border: '1px solid' + theme.palette.neutral200 + '!important',
    borderRadius: '12px !important',
    textTransform: 'none'
  },
  topMainView: {
    display: 'flex',
    backgroundColor: theme.palette.primary50,
    borderRadius: 12,
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  outline: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: theme.palette.neutral200,
  },
  content: {fontFamily: 'Inter-Regular', fontWeight: '400'},
  iconContainer: {
    display: 'flex',
    height: 40,
    width: 70,
    backgroundColor: theme.palette.background.paper,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addressContainer: {
    flexGrow: 1,
  },
  address: {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: '-webkit-box',
    WebkitLineClamp: 5,
    WebkitBoxOrient: 'vertical',
  },
  changeButton: {
    textTransform: "lowercase",
    display: 'flex !important',
    padding: '8px 6px !important',
    borderRadius: '12px !important',
    border: '1px solid' + theme.palette.primary.main + '!important',
    alignSelf: 'center !important',
    alignItems: 'center !important',
    justifyContent: 'center !important',
    backgroundColor: theme.palette.white + '!important',
  },
  changeText: {
    color: theme.palette.primary,
  },
  formContainer: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(4),
  },
  inputText: {
    marginBottom: theme.spacing(2) + '!important',
    height: 56,
    borderRadius: 12
  },
  button: {
    marginTop: theme.spacing(2) + '!important',
    backgroundColor: theme.palette.primary.main + '!important',
    color: theme.palette.common.white,
    height: 48,
  },
  saveButton: {},
  mapContainer: {
    width: '100%',
    height: 'calc(100dvh - 72px)',
    position: 'relative',
    borderRadius: 0,
    overflow: 'hidden',
  },
  mapFooterContainer: {
    position: 'sticky',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 999,
    background: '#fff',
    borderTopLeftRadius: '18px',
    borderTopRightRadius: '18px'
  },
  footerSectionOne: {
    padding: '8px'
  },
  footer: {
    padding: '8px',
    position: 'fixed',
    bottom: 0,
    zIndex: 999,
    backgroundColor: '#fff',
    left: 0,
    right: 0,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  doneButton: {
    borderRadius: '4px !important'
  },
  doneNoteText: {
    textAlign: 'center',
    paddingBottom: '8px',
    marginTop: '8px !important',
  },
  searchInput: {
    width: '100%',
    position: 'absolute',
    zIndex: '999',
    top: '0px',
    background: '#fff',
    borderRadius: '10px',
  }

}));

export default AddressForm;
