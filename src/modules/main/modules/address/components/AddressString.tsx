import React, {useMemo} from "react";
import Typography from "@mui/material/Typography";
import "./AddressString.css";

interface AddressProps {
  address: {
    building?: string;
    street?: string;
    landmark?: string;
    city?: string;
    state?: string;
    areaCode?: string;
    tag?: string;
    completeAddress?: string;
  };
}

const AddressString: React.FC<AddressProps> = ({address}) => {
  const {
    building = "",
    street = "",
    landmark = "",
    city = "",
    state = "",
    areaCode = "",
    tag = "",
    completeAddress = "",
  } = useMemo(() => address || {}, [address]);

  return (
    <>
      <Typography component="div" variant="bodyLarge" className="address-name" noWrap>
        {tag && tag.length > 0 ? tag : "Other"}
      </Typography>
      {completeAddress ? (
        <Typography component="div" variant="bodyMedium" className="address-description">
          {building}, {completeAddress}
        </Typography>
      ) : (
        <Typography component="div" variant="bodyMedium" className="address-description">
          {building}, {street}, {landmark ? `${landmark},` : ""} {city}, {state}, {areaCode}
        </Typography>
      )}
    </>
  );
};

export default AddressString;
