.form-box {
    background: #ffffff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
}

.submit-button {
    margin-top: 16px;
    background-color: #1976d2;
    color: white;
    font-weight: bold;
}

.submit-button:hover {
    background-color: #1565c0;
}

.phone-input {
    width: 100%;
}

.phone-input .react-tel-input {
    width: 100%;
}

.map-container {
    height: calc(100dvh - 84px);
}

.address-form-container {
    background: #ffffff;
    height: calc(100dvh - 54px);
    overflow: scroll;
    padding: 16px;
    top: 54px;
    position: fixed;
    z-index: 99999;
}

#mmiPickerBot {
    display: none !important;
}
