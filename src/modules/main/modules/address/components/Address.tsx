import React from "react";
import {useDispatch} from "react-redux";
import {useNavigate} from "react-router-dom";
import Card from '@mui/material/Card';
import IconButton from '@mui/material/IconButton';
import Radio from '@mui/material/Radio';
import Typography from '@mui/material/Typography';
import EditIcon from "@mui/icons-material/Edit";
import {useTranslation} from 'react-i18next';

import {setStoredData} from "../../../../../utils/storage";
import {setAddress} from "../../../../../toolkit/reducer/address";
import AddressString from "./AddressString";
import "./Address.css";

interface AddressProps {
  detectAddressNavigation?: () => void | null;
  item: any;
  isCurrentAddress: boolean;
  params: any;
  onAddressSelect: (item: any) => void;
}

const Address: React.FC<AddressProps> = ({
                                           detectAddressNavigation = null,
                                           item,
                                           isCurrentAddress,
                                           params,
                                           onAddressSelect,
                                         }) => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const setDefaultAddress = async () => {
    if (params?.navigateToNext) {
      onAddressSelect(item);
    } else {
      await addAddressToStore();
    }
  };

  const addAddressToStore = async () => {
    setStoredData("address", JSON.stringify(item));
    dispatch(setAddress(item));
    if (params?.navigateToDashboard) {
      navigate("/dashboard");
    } else {
      navigate(-1);
    }
  };

  const editAddress = (e: any) => {
    e.stopPropagation();
    navigate("/update-address", {state: {address: item}});
    if (detectAddressNavigation) {
      detectAddressNavigation();
    }
  };

  return (
    <Card className="address-card" onClick={setDefaultAddress}>
      <Radio
        checked={isCurrentAddress}
        onChange={setDefaultAddress}
        value={item?.descriptor?.name}
        className="radio-button"
      />
      <div className="address-details">
        {item?.descriptor?.name && <AddressString address={item?.address}/>}
        <Typography component="div" variant="labelMedium" className="description">
          {t('Address Form.Mobile Number')}: +91-{item?.descriptor?.phone}
        </Typography>
      </div>
      <IconButton className="edit-button" onClick={editAddress}>
        <EditIcon color="primary"/>
      </IconButton>
    </Card>
  );
};

export default Address;
