import React from "react";
import Skeleton from "@mui/material/Skeleton";
import Card from "@mui/material/Card";
import Box from "@mui/material/Box";
import "./AddressSkeleton.css";

/**
 * Component to show skeleton of address card
 */
const AddressSkeleton: React.FC = () => (
  <Card className="skeleton-card">
    <Box className="skeleton-container">
      <Skeleton variant="circular" width={24} height={24}/>
      <Box className="skeleton-details">
        <Skeleton variant="text" className="skeleton-text"/>
        <Skeleton variant="text" className="skeleton-text"/>
        <Skeleton variant="text" className="skeleton-text"/>
        <Skeleton variant="text" className="skeleton-text"/>
      </Box>
    </Box>
  </Card>
);

export default AddressSkeleton;
