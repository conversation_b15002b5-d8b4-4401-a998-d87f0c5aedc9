.page-container {
    height: 100dvh;
    padding: 0 !important;
}

.loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.content-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    justify-content: center;
    height: 100%;
}

.select-location-icon {
    font-size: 94px !important;
    color: #0074AA;
}

.text-container {
    margin-top: 16px;
}

.text-message {
    font-weight: bold;
    margin-bottom: 12px !important;
}

.continue-text {
    padding-top: 15px;
    padding-bottom: 20px;
}

.address-button {
    margin-top: 24px;
    background-color: #0074AA !important;
    color: #fff;
    padding: 12px 24px;
    border-radius: 8px !important;
}

.location-icon-box {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #e0e8eb;
    border-radius: 50%;
    width: 200px;
    height: 200px;
}
