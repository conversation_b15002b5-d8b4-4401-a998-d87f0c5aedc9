import BottomNavigation from "@mui/material/BottomNavigation"
import BottomNavigationAction from "@mui/material/BottomNavigationAction"
import Box from "@mui/material/Box"
import { useTheme } from "@mui/material/styles"
import { makeStyles } from "@mui/styles"
import { useCallback, useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import { useLocation, useNavigate } from "react-router-dom"
import { ReactComponent as HomeIcon } from "../../../../assets/dashboard/tabs/home.svg"
import { ReactComponent as OrdersIcon } from "../../../../assets/dashboard/tabs/orders.svg"
import { ReactComponent as ProfileIcon } from "../../../../assets/dashboard/tabs/profile.svg"
import { setDomain } from "../../../../toolkit/reducer/homeDomain"
import { Domain } from "../../../../utils/domains"
import { getStoredData, removeData } from "../../../../utils/storage"
import {
  emptyAlertCallback,
  getMobilePageFromDomain,
} from "../../../../utils/utils"
import useMasthead from "../../../authentication/hooks/useMasthead"
import useCartItems from "../../hooks/useCartItems"
import useCategories from "../../hooks/useCategories"
import useLoadDefaultPages from "../../hooks/useLoadDefaultPages"
import useWishlistItems from "../../hooks/useWishlistItems"
import PageLoader from "./components/pageLoader/PageLoader"
import Home from "./components/tabs/Home"
import Orders from "./components/tabs/Orders"
import Profile from "./components/tabs/Profile"

const tabNames = ["home", "orders", "profile"]

const Dashboard = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const styles = useStyles()
  const dispatch = useDispatch()
  const domains = useSelector((state: any) => state.categories.domains)
  const [checkingPreselectedDomain, setCheckingPreselectedDomain] =
    useState(true)
  const { getCartItems } = useCartItems()
  const { getWishlistItems } = useWishlistItems()
  const hideConfirmationPopup = () => {}
  const { getMastheads } = useMasthead(hideConfirmationPopup)
  const { pageRequested } = useLoadDefaultPages()
  const { getDomainImages } = useCategories()

  const searchParams = new URLSearchParams(location.search)
  const tabParam = searchParams.get("tab") ?? "" // fallback to empty string
  const initialTab = tabNames.includes(tabParam)
    ? tabNames.indexOf(tabParam)
    : 0
  const [tab, setTab] = useState<number>(initialTab)
  const theme = useTheme()
  const { selected } = useSelector((state: any) => state.homeDomain)
  const pageDomain = getMobilePageFromDomain(selected?.domain)
  const { headerBackgroundColor } = useSelector(
    (state: any) =>
      state.masthead[pageDomain] ?? {
        headerBackgroundColor: "",
      }
  )

  const handleChangeTab = (event: any, newValue: number) => {
    setTab(newValue)
    const newTab = tabNames[newValue]
    navigate(`?tab=${newTab}`, { replace: true })
  }

  const checkDomainSelected = () => {
    const tab = getStoredData("home_page_tab") ?? "All"
    const domain = domains.find((domain: Domain) => domain.name === tab)
    if (domain) {
      dispatch(setDomain(domain))
    }
    setCheckingPreselectedDomain(false)
    removeData("home_page_tab")
  }

  useEffect(() => {
    checkDomainSelected()
    getCartItems().then(emptyAlertCallback).catch(emptyAlertCallback)
    getWishlistItems().then(emptyAlertCallback).catch(emptyAlertCallback)
    getDomainImages().then(emptyAlertCallback).catch(emptyAlertCallback)
  }, [])

  useEffect(() => {
    if (!headerBackgroundColor) {
      getMastheads().then(emptyAlertCallback).catch(emptyAlertCallback)
    }
  }, [headerBackgroundColor])

  const renderContent = useCallback(() => {
    switch (tab) {
      case 0:
        return <Home />
      case 1:
        return <Orders />
      case 2:
        return <Profile />
    }
  }, [tab])

  if (pageRequested || checkingPreselectedDomain) {
    return <PageLoader />
  }

  return (
    <Box className={styles.container}>
      <Box
        className={styles.topSafeArea}
        style={
          headerBackgroundColor
            ? { backgroundColor: headerBackgroundColor }
            : {}
        }
      />
      <Box className={styles.bottomSafeArea}>
        <Box className={styles.container}>{renderContent()}</Box>
        <Box className={styles.bottomNavContainer}>
          <Box className={styles.bottomNavigation}>
            <BottomNavigation
              value={tab}
              onChange={handleChangeTab}
              showLabels
              sx={{ height: 38 }}
            >
              <BottomNavigationAction
                label="Home"
                icon={
                  <HomeIcon style={{ color: tab === 0 ? "#fff" : "#686868" }} />
                }
                className={tab === 0 ? styles.selectedTab : styles.tab}
                sx={{
                  backgroundColor:
                    tab === 0
                      ? theme.palette.primary.main
                      : theme.palette.common.white,
                  flexDirection: "row",
                  gap: 0.5,
                  height: 36,
                }}
              />
              <BottomNavigationAction
                label="Orders"
                icon={
                  <OrdersIcon
                    style={{ color: tab === 1 ? "#fff" : "#686868" }}
                  />
                }
                className={tab === 1 ? styles.selectedTab : styles.tab}
                sx={{
                  backgroundColor:
                    tab === 1
                      ? theme.palette.primary.main
                      : theme.palette.common.white,
                  flexDirection: "row",
                  gap: 0.5,
                  height: 36,
                }}
              />
              <BottomNavigationAction
                label="Profile"
                icon={
                  <ProfileIcon
                    style={{ color: tab === 2 ? "#fff" : "#686868" }}
                  />
                }
                className={tab === 2 ? styles.selectedTab : styles.tab}
                sx={{
                  backgroundColor:
                    tab === 2
                      ? theme.palette.primary.main
                      : theme.palette.common.white,
                  flexDirection: "row",
                  gap: 0.5,
                  height: 36,
                }}
              />
            </BottomNavigation>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  bottomNavContainer: {
    width: "100%",
    maxWidth: "480px",
    position: "fixed",
    bottom: 0,
    backgroundColor: theme.palette.background.paper,
  },
  bottomNavigation: {
    padding: "10px",
    boxShadow: "0px -2px 10px rgba(0, 0, 0, 0.1)",
    borderTopLeftRadius: "10px",
    borderTopRightRadius: "10px",
  },
  selectedTab: {
    backgroundColor: `${theme.palette.primary.main} !important`,
    color: `${theme.palette.common.white} !important`,
    borderRadius: "10px !important",
    padding: "8px 16px",
    width: "120px",
  },
  tab: {
    flexDirection: "row",
    padding: "8px 16px",
    minWidth: "auto",
    gap: "8px",
  },
  icon: {
    fontSize: 28,
  },
  container: {
    flex: 1,
    width: "100%",
    overflowX: "hidden",
  },
  topSafeArea: {
    flex: 0,
  },
  bottomSafeArea: {
    flex: 1,
  },
}))

export default Dashboard
