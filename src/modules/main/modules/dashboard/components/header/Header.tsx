import SearchIcon from "@mui/icons-material/SearchRounded"
import Box from "@mui/material/Box"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useCallback, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import {
  emptyAlertCallback,
  getMobilePageFromDomain,
} from "../../../../../../utils/utils"
import useHandleWidgetCta from "../../../../hooks/useHandleWidgetCta"
import AddressTag from "../address/AddressTag"
import Domains from "../home/<USER>"
import AudioRecorder from "./AudioRecorder"
import CartAction from "./CartAction"
import WishListAction from "./WishListAction"

type HeaderProps = {
  onPress?: () => void
  selectedDomain: any
}

const Header: React.FC<HeaderProps> = ({ onPress, selectedDomain }) => {
  const videoPlayer = useRef<any>(null)
  const { t } = useTranslation()
  const theme = useTheme()
  const navigate = useNavigate()
  const { navigateToPage } = useHandleWidgetCta()
  const [showMasthead, setShowMasthead] = useState<boolean>(true)
  const pageDomain = getMobilePageFromDomain(selectedDomain?.domain)

  const { headerBackgroundColor, mastheadMedia, logoImage, mastheadMediaType } =
    useSelector(
      (state: any) =>
        state.masthead[pageDomain] ?? {
          headerBackgroundColor: "",
        }
    )
  const currentMasthead = useSelector(
    (state: any) => state.masthead[pageDomain]
  )

  const styles = useHeaderStyles()

  const headerBackgroundStyle = useMemo(
    () => ({ background: `${headerBackgroundColor} !important` }),
    [headerBackgroundColor]
  )

  const categoryDomain = useMemo(
    () => (selectedDomain.domain === "All" ? "" : selectedDomain.domain),
    [selectedDomain.domain]
  )

  const navigateToSearchProducts = useCallback(() => {
    const searchParams = new URLSearchParams()
    if (categoryDomain) {
      searchParams.append("domain", categoryDomain)
    }
    navigate(`/search?${searchParams.toString()}`)
  }, [navigate, categoryDomain])

  const onAudioSearchComplete = useCallback(
    (query: string) => {
      if (query.length > 0) {
        const searchParams = new URLSearchParams({
          domain: categoryDomain || "",
          query,
        }).toString()

        navigate(`/search?${searchParams}`)
      }
    },
    [navigate, categoryDomain]
  )

  const handleEnd = useCallback(() => {
    videoPlayer.current?.seek(0)
  }, [])
  const handleMastheadPress = () => {
    navigateToPage(currentMasthead)
  }
  return (
    <Box className={styles.headerWrapper} sx={headerBackgroundStyle}>
      <Box className={styles.headerContainer}>
        <Box className={styles.topRow}>
          <AddressTag onPress={onPress} logoImage={logoImage} />
          <Box className={styles.actionButtonContainer}>
            <WishListAction />
            <CartAction />
          </Box>
        </Box>
        <Box className={styles.searchContainer}>
          <Box
            className={styles.searchButton}
            onClick={navigateToSearchProducts}
          >
            <Box className={styles.searchInput}>
              <SearchIcon
                sx={{ fontSize: 24, color: theme.palette.primary.main }}
              />
              <Typography variant="bodyMedium" className={styles.searchText}>
                {t("Home.Search")}
              </Typography>
            </Box>
          </Box>
          <Box className={styles.qrContainer}>
            <AudioRecorder
              color={theme.palette.common.white}
              setSearchQuery={emptyAlertCallback}
              onSearchComplete={onAudioSearchComplete}
            />
            {/*<QRButton color={theme.palette.common.white}/>*/}
          </Box>
        </Box>
      </Box>
      <Domains selectedDomain={selectedDomain} />
      {mastheadMedia &&
        showMasthead &&
        (mastheadMediaType.toLowerCase().includes("gif") ? (
          <Box onClick={handleMastheadPress}>
            <img
              src={mastheadMedia}
              className={styles.masthead}
              onError={() => setShowMasthead(false)}
              alt="masthead"
            />
          </Box>
        ) : (
          <Box onClick={handleMastheadPress}>
            <video
              ref={videoPlayer}
              src={mastheadMedia}
              className={styles.masthead}
              onError={() => setShowMasthead(false)}
              onEnded={handleEnd}
              autoPlay
              muted
              loop
              playsInline
              controls={false}
            />
          </Box>
        ))}
    </Box>
  )
}

const useHeaderStyles = makeStyles<any>((theme) => ({
  headerWrapper: {
    width: "100%",
    backgroundColor: theme.palette.primary.main,
    background: `linear-gradient(to bottom, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
  },
  headerContainer: {
    padding: "14px 16px 0",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },
  topRow: {
    width: "100%",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
  actionButtonContainer: {
    display: "flex",
    gap: "20px",
  },
  searchContainer: {
    display: "flex",
    alignItems: "center",
    width: "100%",
    gap: 12,
    marginTop: 8,
  },
  qrContainer: {
    display: "flex",
    gap: "20px",
  },
  searchButton: {
    flexGrow: 1,
    width: "80%",
  },
  searchInput: {
    display: "flex",
    alignItems: "center",
    gap: 10,
    backgroundColor: theme.palette.common.white,
    padding: "10px 16px",
    borderRadius: 30,
  },
  searchText: {
    color: theme.palette.neutral300,
  },
  masthead: {
    width: "100%",
    height: 230,
    objectFit: "cover",
  },
}))

export default React.memo(Header)
