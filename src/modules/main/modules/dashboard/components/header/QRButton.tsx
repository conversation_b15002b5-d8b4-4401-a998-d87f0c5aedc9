import QrCodeScannerIcon from '@mui/icons-material/QrCodeScanner';
import React from 'react';
import {useNavigate} from 'react-router-dom';
import IconButton from "@mui/material/IconButton";

const QRButton = ({color}: { color: string }) => {
  const navigate = useNavigate();

  const showQRScanner = () => {
    navigate('/SellerQRCode');
  };

  return (
    <IconButton sx={{width: 24, height: 24}} onClick={showQRScanner}>
      <QrCodeScannerIcon sx={{fontSize: 24, color}}/>
    </IconButton>
  );
};

export default QRButton;
