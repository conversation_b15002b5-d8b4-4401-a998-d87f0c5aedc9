import React, {useEffect, useRef, useState} from 'react';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import {useTranslation} from 'react-i18next';
import MicWave from "./MicWave";
import {theme} from "../../../../../../utils/theme";

type MicrProps = {
  modalVisible: boolean;
  onSearchComplete: (value: string) => void;
  closeModal: () => void;
};

const SpeechRecognition =
  (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

const SearchModal: React.FC<MicrProps> = ({
                                            modalVisible,
                                            onSearchComplete,
                                            closeModal,
                                          }) => {
  const {t} = useTranslation();
  const [recognizedText, setRecognizedText] = useState('');
  const recognitionRef = useRef<any>(null);

  useEffect(() => {
    if (!SpeechRecognition) {
      alert('Your browser does not support speech recognition.');
      return;
    }

    const recognition = new SpeechRecognition();
    recognition.lang = navigator.language || 'en-US';
    recognition.interimResults = true;
    recognition.continuous = false;

    recognition.onresult = (event: any) => {
      const transcript = Array.from(event.results)
        .map((result: any) => result[0].transcript)
        .join('');
      setRecognizedText(transcript);

      if (event.results[0].isFinal) {
        onSearchComplete(transcript);
        closeModal();
      }
    };

    recognition.onerror = (event: any) => {
      console.error('Speech recognition error:', event.error);
    };

    recognitionRef.current = recognition;
    recognition.start();

    return () => {
      recognition.stop();
    };
  }, []);

  return (
    <Dialog open={modalVisible} onClose={closeModal} PaperProps={{
      sx: {
        borderRadius: 5,
      },
    }}>
      <DialogContent
        sx={{
          minWidth: 250,
          height: 300,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          position: 'relative',
        }}>
        <IconButton
          aria-label="close"
          onClick={closeModal}
          sx={{position: 'absolute', top: 8, left: 8}}>
          <CloseIcon sx={{color: theme.palette.neutral.main}}/>
        </IconButton>

        <MicWave/>
        <Typography variant="body1" align="center" sx={{mt: 10}}>
          {recognizedText.length > 0
            ? recognizedText
            : t('Home.Try Saying Something, We are Listening')}
        </Typography>
      </DialogContent>
    </Dialog>
  );
};

export default SearchModal;
