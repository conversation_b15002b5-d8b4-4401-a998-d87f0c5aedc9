import {useCallback, useMemo} from 'react';
import {useNavigate} from 'react-router-dom';
import ShoppingCartOutlinedIcon from '@mui/icons-material/ShoppingCartOutlined';
import {useSelector} from 'react-redux';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {makeStyles} from "@mui/styles";
import IconButton from "@mui/material/IconButton";

interface RootState {
  cart: {
    cartItems: any[];
  };
}

const CartAction = ({color = '#fff'}) => {
  const styles = useCartActionStyles();
  const navigate = useNavigate();

  const cartItems = useSelector((state: RootState) => state.cart.cartItems);

  const showCart = useCallback(() => {
    navigate('/cart');
  }, [navigate]);

  const itemsCount = useMemo(() => {
    return cartItems.length;
  }, [cartItems]);

  return (
    <Box>
      <IconButton sx={{width: 24, height: 24}} onClick={showCart}>
        <ShoppingCartOutlinedIcon sx={{color, fontSize: 24}}/>
      </IconButton>
      {itemsCount > 0 && (
        <Box className={styles.badge}>
          <Typography className={styles.badgeText}>{itemsCount}</Typography>
        </Box>
      )}
    </Box>
  );
};

const useCartActionStyles = makeStyles<any>((theme) => ({
  badge: {
    position: 'absolute',
    width: 14,
    height: 14,
    backgroundColor: theme.palette.primary.light,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -28,
    marginLeft: 15,
    borderRadius: 34,
    display: 'flex'
  },
  badgeText: {
    color: theme.palette.white,
    fontSize: '10px !important',
  },
}));

export default CartAction;
