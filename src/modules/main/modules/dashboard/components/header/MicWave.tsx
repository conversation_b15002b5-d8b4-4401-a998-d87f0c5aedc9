import React from 'react';
import Box from '@mui/material/Box';
import {motion} from 'framer-motion';
import MicNoneIcon from '@mui/icons-material/MicNone';
import {theme} from "../../../../../../utils/theme";

const MicWave = () => {
  return (
    <Box
      sx={{
        position: 'relative',
        width: 100,
        height: 100,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 7,
      }}
    >
      {/* Animated expanding and fading circle */}
      <Box
        component={motion.div}
        initial={{width: 100, height: 100, opacity: 1}}
        animate={{
          width: [100, 200],
          height: [100, 200],
          opacity: [1, 0],
        }}
        transition={{
          duration: 3,
          ease: "easeOut",
          repeat: Infinity,
        }}
        sx={{
          position: 'absolute',
          backgroundColor: 'rgba(0, 142, 204, 0.4)',
          borderRadius: '50%',
        }}
      />

      {/* Static Mic Icon in center */}
      <Box sx={{
        width: 64,
        height: 64,
        borderRadius: 32,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: theme.palette.primary.main
      }}>
        <MicNoneIcon sx={{fontSize: 40, color: '#fff'}}/>
      </Box>
    </Box>
  );
};

export default MicWave;
