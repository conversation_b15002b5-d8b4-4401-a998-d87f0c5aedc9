import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import ArrowBackIcon from '@mui/icons-material/ArrowBackRounded';
import ClearIcon from '@mui/icons-material/ClearRounded';
import {useNavigate} from 'react-router-dom';
import {useTheme} from "@mui/material/styles";
import WishListAction from './WishListAction';
import CartAction from './CartAction';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import TextField from '@mui/material/TextField';
import {makeStyles} from '@mui/styles';

type SearchHeaderProps = {
  onSearch: (query: string) => void;
  defaultQuery: string;
  fetchSuggestions: (query: string) => void;
};

const SearchHeader = forwardRef(({onSearch, defaultQuery, fetchSuggestions}: SearchHeaderProps, ref) => {
  const theme = useTheme();
  const styles = useHeaderStyles(theme);
  const searchSource = useRef<HTMLInputElement | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>(defaultQuery);
  const navigate = useNavigate();

  useImperativeHandle(ref, () => ({
    updateQuery: (updatedQuery: string) => setSearchQuery(updatedQuery),
  }));

  useEffect(() => {
    setTimeout(() => searchSource?.current?.focus(), 0);
  }, []);

  const onChangeSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value;
    setSearchQuery(query);
    fetchSuggestions(query);
  };

  const onSearchComplete = () => {
    onSearch(searchQuery);
  };

  const onClearSearch = () => setSearchQuery('');
  const backIconPress = () => navigate('/dashboard');

  return (
    <Box className={styles.headerContainer}>
      {/* Search Bar */}
      <Box className={styles.searchBar}>
        <ArrowBackIcon className={styles.searchIcon} onClick={backIconPress}/>
        <TextField
          inputRef={searchSource}
          variant="standard"
          fullWidth
          placeholder="Search..."
          value={searchQuery}
          onChange={onChangeSearch}
          onBlur={onSearchComplete}
          onKeyDown={(event) => {
            if (event.key === 'Enter') {
              event.preventDefault();
              onSearchComplete();
            }
          }}
          InputProps={{disableUnderline: true, className: styles.searchInput}}
        />
        {searchQuery.length > 0 ? (
          <IconButton sx={{width: 24, height: 24}} onClick={onClearSearch}>
            <ClearIcon className={styles.clearIcon}/>
          </IconButton>
        ) : (
          <Box sx={{width: 24}}/>
        )}
      </Box>
      <Box className={styles.iconRow}>
        <WishListAction color={theme.palette.primary.main}/>
        <CartAction color={theme.palette.primary.main}/>
      </Box>
    </Box>
  );
});

const useHeaderStyles = makeStyles<any, any>((theme) => ({
  headerContainer: {
    padding: '16px 14px',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
    display: 'flex',
  },
  topRow: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  locationBox: {
    display: 'flex',
    alignItems: 'center',
    gap: 6,
    color: 'white',
  },
  locationText: {
    fontSize: 14,
    fontWeight: 500,
    color: 'white',
  },
  iconRow: {
    display: 'flex',
    alignItems: 'center',
    gap: 20,
    marginLeft: 20,
  },
  searchBar: {
    display: 'flex',
    alignItems: 'center',
    backgroundColor: 'white',
    padding: '8px 14px',
    borderRadius: 30,
    borderWidth: 1,
    borderColor: 'rgba(25, 106, 171, 0.19)',
    borderStyle: 'solid',
    gap: 10,
    flex: 1,
  },
  searchIcon: {
    color: theme.palette.primary.main,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
  },
  actionIcon: {
    color: theme.palette.primary.main,
  },
  clearIcon: {
    color: '#666',
  },
  whiteIcon: {
    color: 'white',
  },
}));

export default SearchHeader;
