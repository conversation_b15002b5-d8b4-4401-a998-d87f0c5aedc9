import React, {useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';
import IconButton from "@mui/material/IconButton";

const WishListAction = ({color = '#fff'}) => {
  const navigate = useNavigate();

  const showWishList = useCallback(() => {
    navigate('/wishList');
  }, [navigate]);

  return (
    <IconButton sx={{width: 24, height: 24}} onClick={showWishList}>
      <FavoriteBorderIcon sx={{fontSize: 24, color}}/>
    </IconButton>
  );
};

export default WishListAction;
