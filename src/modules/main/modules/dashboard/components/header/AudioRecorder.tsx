import MicNoneIcon from '@mui/icons-material/MicNone';
import React, {useState} from 'react';
import IconButton from "@mui/material/IconButton";
import SearchModal from './SearchModal';

const AudioRecorder = ({
                         color,
                         onSearchComplete,
                       }: {
  color: string;
  setSearchQuery: (value: string) => void;
  onSearchComplete: (value: string) => void;
}) => {
  const [showVoiceModal, setShowVoiceModal] = useState<boolean>(false);

  const openVoiceModal = () => {
    setShowVoiceModal(true);
  };

  const closeVoiceModal = () => {
    setShowVoiceModal(false);
  };

  return (
    <>
      <IconButton sx={{width: 24, height: 24}} onClick={openVoiceModal}>
        <MicNoneIcon sx={{color, fontSize: 24}}/>
      </IconButton>
      {showVoiceModal && (
        <SearchModal
          modalVisible={showVoiceModal}
          onSearchComplete={onSearchComplete}
          closeModal={closeVoiceModal}
        />
      )}
    </>
  );
};

export default AudioRecorder;
