import React, {useEffect, useRef} from 'react';
import SearchIcon from '@mui/icons-material/SearchRounded';
import {useTheme} from "@mui/material/styles";
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';

type Suggestions = { title: string; data: string[] };

type SearchHeaderProps = {
  onSearch: (query: string) => void;
  clearSuggestions: (query: string) => void;
  suggestions: Suggestions[];
};

const SearchSuggestions: React.FC<SearchHeaderProps> = ({
                                                          suggestions,
                                                          onSearch,
                                                          clearSuggestions,
                                                        }) => {
  const theme = useTheme();
  const styles = useSearchSuggestionsStyles();
  const searchSource = useRef<any>(null);

  const cancelSearchSource = () => {
    if (searchSource.current) {
      searchSource.current.cancel();
    }
  };

  const onSuggestionSelected = (selected: string) => {
    onSearch(selected);
    clearSuggestions(selected);
  };

  useEffect(() => {
    return () => {
      cancelSearchSource();
    };
  }, []);

  if (suggestions.length === 0) return null;

  return (
    <Box className={styles.modalContainer}>
      {suggestions.map((section, sectionIndex) => (
        <Box key={section.title + sectionIndex}>
          <Box className={styles.header}>
            <Typography variant='labelMedium' className={styles.headerText}>
              {section.title}
            </Typography>
          </Box>
          {section.data.map((item, itemIndex) => (
            <Button
              key={item + itemIndex}
              className={styles.searchItem}
              onClick={() => onSuggestionSelected(item)}
            >
              <SearchIcon sx={{fontSize: 16, color: theme.palette.neutral200}}/>
              <Typography variant='labelMedium' className={styles.searchText}>
                {item}
              </Typography>
            </Button>
          ))}
        </Box>
      ))}
    </Box>
  );
};

const useSearchSuggestionsStyles = makeStyles<any>((theme) => ({
  modalContainer: {
    margin: '0 16px',
    padding: '4px 0',
    backgroundColor: theme.palette.white,
    borderRadius: 12,
  },
  header: {
    padding: '4px 12px',
    backgroundColor: theme.palette.neutral50,
    margin: '12px 0',
  },
  searchItem: {
    display: 'flex',
    flexDirection: 'row',
    gap: 8,
    alignItems: 'center',
    padding: '6px 12px',
    width: '100%',
    textAlign: 'left',
  },
  searchText: {
    color: theme.palette.neutral400,
    flex: 1,
  },
  headerText: {
    color: theme.palette.neutral200,
  },
}));

export default SearchSuggestions;
