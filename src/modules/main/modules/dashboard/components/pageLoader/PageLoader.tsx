import React from 'react';
import {useSelector} from 'react-redux';
import <PERSON><PERSON> from 'lottie-react';
import defaultAnimation from '../../../../../../assets/dashboard/homeLoader.json';
import Box from '@mui/material/Box';
import {makeStyles} from '@mui/styles';

const PageLoader: React.FC = () => {
  const styles = usePageLoaderStyles();
  const {loaderJson} = useSelector((state: any) => state.masthead);

  return (
    <Box className={styles.container}>
      <Lottie
        animationData={loaderJson ? {animationData: loaderJson} : defaultAnimation}
        loop
        className={styles.animation}
      />
    </Box>
  );
};

const usePageLoaderStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100dvh',
    width: '100%'
  },
  animation: {
    width: 280,
    height: 280,
  },
}));

export default PageLoader;
