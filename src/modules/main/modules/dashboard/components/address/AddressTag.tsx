import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDownRounded"
import React, { useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"

import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import Logo from "../../../../../../assets/dashboard/app.svg"

interface AddressTag {
  onPress?: () => void
  logoImage: any
}

const AddressTag: React.FC<AddressTag> = ({ onPress, logoImage }) => {
  const { t } = useTranslation()
  const theme = useTheme()
  const styles = useAddressTagStyles()
  const { address } = useSelector((state: any) => state.address)

  const [showLogo, setShowLogo] = useState<boolean>(true)

  const addressText = useMemo(() => {
    const { tag, areaCode, city } = address?.address || {}
    return `${tag && tag.length > 0 ? tag : "Other"} - ${areaCode || city}`
  }, [address, t])

  if (address) {
    return (
      <Button className={styles.addressContainer} onClick={onPress}>
        {logoImage && showLogo ? (
          <img
            src={logoImage}
            className={styles.logo}
            style={{ objectFit: "contain" }}
            onError={() => setShowLogo(false)}
            alt="logo"
          />
        ) : (
          <img src={Logo} alt="Icon" width={32} height={32} />
        )}
        <Typography variant="bodyMedium" className={styles.deliverTo}>
          {addressText}
        </Typography>
        <KeyboardArrowDownIcon
          sx={{ fontSize: 24 }}
          className={styles.downKey}
        />
      </Button>
    )
  } else {
    return <CircularProgress size={24} />
  }
}

const useAddressTagStyles = makeStyles<any>((theme) => ({
  addressContainer: {
    display: "flex",
    flexDirection: "row",
    width: "80%",
    justifyContent: "flex-start !important",
  },
  deliverTo: {
    marginLeft: "20px !important",
    marginRight: "6px !important",
    color: theme.palette.neutral.contrastText,
  },
  downKey: {
    color: theme.palette.neutral.contrastText,
  },
  logo: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
}))

export default AddressTag
