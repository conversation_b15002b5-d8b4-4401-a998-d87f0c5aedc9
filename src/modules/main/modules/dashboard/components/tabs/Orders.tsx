import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import {useTranslation} from 'react-i18next';
import {useLocation, useSearchParams} from 'react-router-dom';
import ClearIcon from '@mui/icons-material/ClearRounded';
import SearchIcon from '@mui/icons-material/SearchRounded';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import {useTheme} from "@mui/material/styles";
import useNetworkErrorHandling from '../../../../../../hooks/useNetworkErrorHandling';
import {skeletonList} from '../../../../../../utils/utils';
import ListFooter from '../../../order/components/ListFooter';
import OrderSkeleton from '../../../order/components/OrderSkeleton';
import OrderComponent from '../../../order/components/Order';
import useNetworkHandling from '../../../../../../hooks/useNetworkHandling';
import {API_BASE_URL, ORDERS} from '../../../../../../utils/apiActions';
import FiltersIcon from '../../../../../../assets/filter.svg';
import FilterList from '../../../order/components/FilterList';

const allOrdersFilterState = 'Created,Accepted,In-progress,Completed,Cancelled,Processing,Draft,Failed';

interface OrderItem {
  id: string;
  name: string;
  status: string;
  createdAt: string;
}

interface OrdersApiResponse {
  totalCount: number;
  orders: OrderItem[];
}

const Orders: React.FC = () => {
  const {t} = useTranslation();
  const theme = useTheme();
  const styles = useOrdersStyles();
  const location = useLocation();
  const source = useRef<AbortController | null>(null);
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();

  const totalOrders = useRef<number>(0);
  const pageNumber = useRef<number>(1);
  const searchQuery = useRef<string>('');
  const [orders, setOrders] = useState<OrderItem[]>([]);
  const [searchParams] = useSearchParams();
  const [selectedFilter, setSelectedFilter] = useState<any>({
    currentAttribute: searchParams.get('filter') ?? 'orderType',
    selectedValue: searchParams.get('filterValue') ?? '',
  });
  const [moreListRequested, setMoreListRequested] = useState<boolean>(false);
  const [refreshInProgress, setRefreshInProgress] = useState<boolean>(false);
  const [apiInProgress, setApiInProgress] = useState<boolean>(false);
  const [query, setQuery] = useState<string>('');
  const [filterSheetOpen, setFilterSheetOpen] = useState<boolean>(false);

  // Handle search input changes
  const onChangeSearch = (value: string) => {
    searchQuery.current = value;
    setQuery(value);
  };

  // Execute search
  const onSearchComplete = async () => {
    try {
      setApiInProgress(true);
      await getOrderList(1, selectedFilter);
    } finally {
      setApiInProgress(false);
    }
  };

  // Clear search input
  const onClearIconPress = async () => {
    searchQuery.current = '';
    setQuery('');
    try {
      setApiInProgress(true);
      await getOrderList(1, selectedFilter);
    } finally {
      setApiInProgress(false);
    }
  };
  const getFilter = (filter: any) => {
    const {currentAttribute, selectedValue} = filter;
    if (currentAttribute === 'orderType') {
      return `&state=${selectedValue === '' ? allOrdersFilterState : selectedValue
      }`;
    } else {
      const endDate = Date.now();
      switch (selectedValue) {
        case 'Last 7 Days':
          const startDate7Days = endDate - 7 * 24 * 60 * 60 * 1000;
          return `&start=${startDate7Days}&end=${endDate}`;
        case 'Last 30 Days':
          const startDate30Days = endDate - 30 * 24 * 60 * 60 * 1000;
          return `&start=${startDate30Days}&end=${endDate}`;
        case 'Last 60 Days':
          const startDate60Days = endDate - 60 * 24 * 60 * 60 * 1000;
          return `&start=${startDate60Days}&end=${endDate}`;
        case 'Last 90 Days':
          const startDate90Days = endDate - 90 * 24 * 60 * 60 * 1000;
          return `&start=${startDate90Days}&end=${endDate}`;
        default:
          return '';
      }
    }
  };
  // Fetch orders
  const getOrderList = async (currentPage: number, filter: string): Promise<void> => {
    try {
      source.current = new AbortController();

      const filterString = getFilter(filter);
      const url = new URL(`${API_BASE_URL}${ORDERS}`);
      const params = new URLSearchParams();
      params.append('pageNumber', String(currentPage));
      params.append('limit', '10');

      if (searchQuery.current.length > 0) {
        params.append('name', searchQuery.current);
      }

      const fullUrl = `${url.toString()}?${params.toString()}${filterString}`;

      const response = await getDataWithAuth(fullUrl, {
        signal: source.current.signal,
      });

      const data = response.data as OrdersApiResponse;

      totalOrders.current = data.totalCount;
      setOrders(currentPage === 1 ? data.orders : [...orders, ...data.orders]);
      pageNumber.current += 1;

    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404 && orders.length === 0) {
          setOrders([]);
        } else {
          handleApiError(error);
        }
      } else {
        handleApiError(error);
      }
    }
  };

  // Load more orders on scroll
  const loadMoreList = () => {
    if (totalOrders.current > orders.length && !moreListRequested) {
      setMoreListRequested(true);
      getOrderList(pageNumber.current, selectedFilter)
        .finally(() => setMoreListRequested(false));
    }
  };

  // Refresh orders manually
  const onRefreshHandler = () => {
    pageNumber.current = 1;
    setRefreshInProgress(true);
    getOrderList(1, selectedFilter).finally(() => setRefreshInProgress(false));
  };

  // Fetch orders when page loads or filter changes
  useEffect(() => {
    setApiInProgress(true);
    pageNumber.current = 1;
    getOrderList(pageNumber.current, selectedFilter).finally(() => setApiInProgress(false));
  }, [location, selectedFilter]);

  useEffect(() => {
    document.title = t('Profile.Order History');
  }, [t]);

  // Render individual order
  const renderItem = useCallback(
    (item: OrderItem) => <OrderComponent order={item}/>,
    [],
  );

  // Open and close filter modal
  const openFilterSheet = useCallback(() => setFilterSheetOpen(true), []);
  const closeFilterSheet = useCallback(() => setFilterSheetOpen(false), []);

  return (
    <>
      <Box className={styles.pageContainer}>
        <Box className={styles.header}>
          <Typography variant="titleLarge" className={styles.pageTitle}>
            {t('Profile.Order History')}
          </Typography>
        </Box>
        <Box className={styles.searchHeader}>
          <TextField
            variant="outlined"
            size="small"
            fullWidth
            className={styles.search}
            placeholder="Search"
            value={query}
            onChange={(e) => onChangeSearch(e.target.value)}
            onBlur={onSearchComplete}
            onKeyDown={(event) => {
              if (event.key === 'Enter') {
                event.preventDefault();
                onSearchComplete();
              }
            }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon style={{color: theme.palette.primary.main}}/>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  {query.length > 0 && (
                    <IconButton onClick={onClearIconPress}>
                      <ClearIcon/>
                    </IconButton>
                  )}
                </InputAdornment>
              ),
            }}
          />
          <Button variant='outlined' className={styles.filterButton} onClick={openFilterSheet}>
            <img src={FiltersIcon} alt="Icon" width={14} height={14}/>
            <Typography variant="bodyMedium" className={styles.filterButtonText}>
              {t('Orders.Filter')}
            </Typography>
          </Button>
        </Box>

        {/* Loading Skeleton */}
        {apiInProgress ? (
          skeletonList.map((_, index) => <OrderSkeleton key={index}/>)
        ) : orders.length > 0 ? (
          <>
            {orders.map(renderItem)}
            <ListFooter moreRequested={moreListRequested}/>
          </>
        ) : (
          <Box className={styles.noDataFound}>
            <Typography component="span">{t('Orders.No data found')}</Typography>
          </Box>
        )}
      </Box>

      {/* Filter Modal */}
      <Dialog open={filterSheetOpen} PaperProps={{
        sx: {
          marginTop: 'auto',
          width: '100% !important',
          marginLeft: '0px !important',
          marginRight: '0px !important',
          marginBottom: '0px !important',
          borderTopLeftRadius: '8px !important',
          borderTopRightRadius: '8px !important',
        },
      }} onClose={closeFilterSheet} maxWidth="sm" fullWidth className={styles.filterDialog}>
        <FilterList selectedFilter={selectedFilter} setSelectedFilter={setSelectedFilter} close={closeFilterSheet}/>
      </Dialog>
    </>
  );
};

const useOrdersStyles = makeStyles<any>((theme) => ({
  noDataFound: {
    height: '90%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  container: {
    flex: 1,
  },
  pageContainer: {
    flex: 1,
    backgroundColor: theme.palette.common.white,
    paddingBottom: '90px',
    padding: 8,
    height: 'calc(100dvh - 100px)'
  },
  header: {
    padding: '10px 16px',
    marginBottom: 8,
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20,
    backgroundColor: theme.palette.common.white,
  },
  pageTitle: {
    color: theme.palette.neutral.main,
  },
  searchHeader: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    paddingLeft: '8px',
    paddingRight: '8px',
    marginBottom: '16px',
  },
  filterButton: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    borderRadius: 8,
    border: `1px solid ${theme.palette.primary.main}`,
    padding: 8,
    height: 44,
  },
  filterButtonText: {
    color: theme.palette.primary.main,
  },
  contentContainerStyle: {padding: '10px 16px'},
  emptyContainer: {justifyContent: 'center', alignItems: 'center'},
  searchInput: {
    padding: '10px 16px',
    minHeight: 40,
    flex: 1,
    borderRadius: '58px',

  },
  search: {
    flex: 1,
    height: '44px',
    backgroundColor: theme.palette.common.white,
    borderWidth: 1,
    borderColor: theme.palette.neutral.main,
    '& .MuiOutlinedInput-root': {
      borderRadius: '48px',
    },
  },
  rbSheet: {borderTopLeftRadius: 15, borderTopRightRadius: 15},
}));

export default memo(Orders);
