import React, {useCallback, useEffect} from 'react';
import {useSelector} from 'react-redux';
import Box from "@mui/material/Box";
import {useNavigate} from "react-router-dom";
import {makeStyles} from "@mui/styles";

import Header from '../header/Header';
import {MOBILE_PAGES} from '../../../../../../utils/constants';
import useCurrentPage from '../../../../hooks/useCurrentPage';
import PageLoader from '../pageLoader/PageLoader';
import PageDetails from '../../../../components/page/components/PageDetails';


const Home = () => {
  const navigation = useNavigate();
  const styles = useStyles();
  const {selected} = useSelector((state: any) => state.homeDomain);

  const {pageRequested, currentPage, updateCurrentPageName} = useCurrentPage(
    MOBILE_PAGES.ALL,
  );

  const openAddressList = useCallback(() => {
    navigation('/address-sheet');
  }, [navigation]);

  useEffect(() => {
    switch (selected.domain) {
      case 'ONDC:RET11':
        updateCurrentPageName(MOBILE_PAGES.FOOD);
        break;

      case 'ONDC:RET10':
        updateCurrentPageName(MOBILE_PAGES.GROCERY);
        break;

      case 'ONDC:RET13':
        updateCurrentPageName(MOBILE_PAGES.BEAUTY);
        break;

      case 'ONDC:RET14':
        updateCurrentPageName(MOBILE_PAGES.ELECTRONICS);
        break;

      default:
        updateCurrentPageName(MOBILE_PAGES.ALL);
        break;
    }
  }, [selected]);

  if (pageRequested) {
    return <PageLoader/>;
  }

  return (
    <Box className={styles.container}>
      <Header onPress={openAddressList} selectedDomain={selected}/>
      <PageDetails
        key={currentPage?.id}
        pageName={currentPage?.name}
        sections={currentPage?.sections}
        pageBackground={currentPage?.pageBackground}
      />
    </Box>
  );
};

const useStyles = makeStyles(() => ({
  container: {
    flex: 1,
  },
}));

export default Home;
