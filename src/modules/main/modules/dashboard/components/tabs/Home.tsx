import React, { useCallback, useEffect } from "react"
import { useSelector } from "react-redux"
import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import { useNavigate } from "react-router-dom"
import { makeStyles } from "@mui/styles"

import Header from "../header/Header"
import { MOBILE_PAGES } from "../../../../../../utils/constants"
import useCurrentPage from "../../../../hooks/useCurrentPage"
import PageLoader from "../pageLoader/PageLoader"
import PageDetails from "../../../../components/page/components/PageDetails"
import RouteMap from "../../../order/components/RouteMap"

const Home = () => {
  const navigation = useNavigate()
  const styles = useStyles()
  const { selected } = useSelector((state: any) => state.homeDomain)
  const { pageRequested, currentPage, updateCurrentPageName } = useCurrentPage(
    MOBILE_PAGES.ALL
  )

  const openAddressList = useCallback(() => {
    navigation("/address-sheet")
  }, [navigation])

  useEffect(() => {
    switch (selected.domain) {
      case "ONDC:RET11":
        updateCurrentPageName(MOBILE_PAGES.FOOD)
        break

      case "ONDC:RET10":
        updateCurrentPageName(MOBILE_PAGES.GROCERY)
        break

      case "ONDC:RET13":
        updateCurrentPageName(MOBILE_PAGES.BEAUTY)
        break

      case "ONDC:RET14":
        updateCurrentPageName(MOBILE_PAGES.ELECTRONICS)
        break

      default:
        updateCurrentPageName(MOBILE_PAGES.ALL)
        break
    }
  }, [selected])

  if (pageRequested) {
    return <PageLoader />
  }

  return (
    <Box className={styles.container}>
      <Header onPress={openAddressList} selectedDomain={selected} />

      {/* Route Map Section */}
      <Box className={styles.routeMapSection}>
        <Box className={styles.routeMapHeader}>
          <Typography variant="h6" className={styles.sectionTitle}>
            Route Navigation
          </Typography>
        </Box>

        <Box className={styles.mapWrapper}>
          <RouteMap
            fromLocation={{
              lat: 28.6139,
              lng: 77.209,
              name: "New Delhi Railway Station",
            }}
            toLocation={{
              lat: 28.5355,
              lng: 77.391,
              name: "Noida Sector 18",
            }}
            height="400px"
            showCloseButton={false}
          />
        </Box>
      </Box>

      <PageDetails
        key={currentPage?.id}
        pageName={currentPage?.name}
        sections={currentPage?.sections}
        pageBackground={currentPage?.pageBackground}
      />
    </Box>
  )
}

const useStyles = makeStyles<any>((theme) => ({
  container: {
    flex: 1,
  },
  routeMapSection: {
    padding: "16px",
    margin: "16px",
    backgroundColor: theme.palette.background.paper,
    borderRadius: "8px",
    boxShadow: theme.shadows[2],
  },
  routeMapHeader: {
    display: "flex",
    justifyContent: "flex-start",
    alignItems: "center",
    marginBottom: "16px",
  },
  sectionTitle: {
    fontWeight: 600,
    color: theme.palette.text.primary,
  },
  mapWrapper: {
    marginTop: "16px",
    borderRadius: "8px",
    overflow: "hidden",
  },
}))

export default Home
