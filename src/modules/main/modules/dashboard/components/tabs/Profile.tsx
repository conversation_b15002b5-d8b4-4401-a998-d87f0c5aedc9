import { useCallback, useState } from "react"
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRightRounded"
import { useNavigate } from "react-router-dom"
import { useTranslation } from "react-i18next"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Divider from "@mui/material/Divider"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"

import useLogoutUser from "../../../../hooks/useLogoutUser"
import Info from "../../../profile/components/Info"
import ProfileIcon from "../../../../../../assets/profile/profile.svg"
import ComplaintsIcon from "../../../../../../assets/profile/complaint.svg"
import AccountSettingIcon from "../../../../../../assets/profile/account_setting.svg"
import AboutIcon from "../../../../../../assets/profile/about.svg"
import LegalIcon from "../../../../../../assets/profile/legal.svg"
import LogoutIcon from "../../../../../../assets/profile/logout.svg"
import ConfirmationDialog from "../../../../../../components/alert/ConfirmationDialog"
import UpdateProfile from "../../../profile/UpdateProfile"

const Profile = () => {
  const { t } = useTranslation()
  const styles = useProfileStyles()
  const { clearDataAndLogout } = useLogoutUser()
  const navigate = useNavigate()
  const [logoutAlertOpen, setLogoutAlertOpen] = useState(false)
  const [openProfile, setOpenProfile] = useState(false)
  const [showNameForm, setShowNameForm] = useState(true)

  const handleCloseProfileModal = () => setOpenProfile(false)

  const confirmLogout = () => setLogoutAlertOpen(true)

  const closeLogoutAlert = () => setLogoutAlertOpen(false)

  const navigateToProfile = useCallback(() => {
    setShowNameForm(false)
    setOpenProfile(true)
  }, [])

  const navigateToNameEditor = useCallback(() => {
    setShowNameForm(true)
    setOpenProfile(true)
  }, [])

  const navigateToComplaints = useCallback(
    () => navigate("/complaints"),
    [navigate]
  )

  const navigateToAccountSettings = useCallback(
    () => navigate("/account-setting"),
    [navigate]
  )

  const navigateToAbout = useCallback(() => navigate("/About"), [navigate])

  const navigateToLegal = useCallback(
    () => navigate("/LegalPolicies"),
    [navigate]
  )

  const menu = [
    {
      icon: <img src={ProfileIcon} alt="Profile" width={36} height={36} />,
      title: t("Profile.My Profile"),
      action: navigateToProfile,
    },
    {
      icon: (
        <img src={ComplaintsIcon} alt="Complaints" width={36} height={36} />
      ),
      title: t("Profile.Complaints"),
      action: navigateToComplaints,
    },
    {
      icon: (
        <img
          src={AccountSettingIcon}
          alt="Account Settings"
          width={36}
          height={36}
        />
      ),
      title: t("Profile.Account Settings"),
      action: navigateToAccountSettings,
    },
    {
      icon: <img src={AboutIcon} alt="About" width={36} height={36} />,
      title: t("Profile.About"),
      action: navigateToAbout,
    },
    {
      icon: (
        <img src={LegalIcon} alt="Legal and Policies" width={36} height={36} />
      ),
      title: t("Profile.Legal and Policies"),
      action: navigateToLegal,
    },
    {
      icon: <img src={LogoutIcon} alt="Logout" width={36} height={36} />,
      title: t("Profile.Logout"),
      action: confirmLogout,
    },
  ]

  return (
    <>
      <Box className={styles.pageContainer}>
        <Box className={styles.header}>
          <Typography variant="titleLarge" className={styles.pageTitle}>
            {t("Profile.Profile")}
          </Typography>
        </Box>
        <Box className={styles.container}>
          <Info navigateToNameEditor={navigateToNameEditor} />
          <Box className={styles.optionsContainer}>
            <Box className={styles.listContainer}>
              {menu.map(({ icon, title, action }) => (
                <Box key={title} className={styles.menuContainer}>
                  <Button className={styles.menuOption} onClick={action}>
                    <Box className={styles.menuNameContainer}>
                      {icon}
                      <Typography
                        variant="titleMedium"
                        className={styles.menuName}
                      >
                        {title}
                      </Typography>
                    </Box>
                    <KeyboardArrowRightIcon
                      className={styles.arrowKey}
                      sx={{ fontSize: 24 }}
                    />
                  </Button>
                  <Divider className={styles.dividerLine} />
                </Box>
              ))}
            </Box>
          </Box>
        </Box>
      </Box>
      <Box className={styles.supportContainer}>
        <Typography variant="labelSmall" className={styles.supportTitle}>
          {t("Profile.Support Email ID")}
        </Typography>
        <Typography
          variant="labelLarge"
          className={styles.supportMessage}
          component="a"
          href={`mailto:${process.env.REACT_APP_HELP_EMAIL}`}
          sx={{
            textDecoration: "none",
            color: "primary.main",
            "&:hover": {
              textDecoration: "underline",
            },
          }}
        >
          {process.env.REACT_APP_HELP_EMAIL}
        </Typography>
      </Box>
      <ConfirmationDialog
        open={logoutAlertOpen}
        title="Logout"
        message="Are you sure you want to logout?"
        positiveText="Cancel"
        negativeText="Logout"
        positiveAction={closeLogoutAlert}
        negativeAction={clearDataAndLogout}
      />
      <UpdateProfile
        updateName={showNameForm}
        open={openProfile}
        handleClose={handleCloseProfileModal}
      />
    </>
  )
}

const useProfileStyles = makeStyles<any>((theme) => ({
  pageContainer: {
    backgroundColor: theme.palette.common.white,
    height: "calc(100dvh - 148px)",
    overflow: "scroll",
  },
  container: {
    backgroundColor: theme.palette.common.white,
  },
  header: {
    marginBottom: 8,
    padding: "10px 16px",
  },
  pageTitle: {
    color: theme.palette.neutral.main,
  },
  listContainer: {
    display: "flex",
    flexDirection: "column",
    paddingRight: "16px",
    paddingLeft: "16px",
    alignItems: "flex-start",
  },
  menuContainer: {
    width: "100%",
    paddingTop: "10px",
  },
  menuOption: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    paddingTop: "16px",
    paddingBottom: "16px",
    width: "100%",
  },
  menuNameContainer: {
    gap: 8,
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    width: "100%",
  },
  menuName: {
    color: theme.palette.neutral.main,
  },
  divider: {
    width: "100%",
    height: 1,
    backgroundColor: theme.palette.neutral.main,
  },
  optionsContainer: {
    marginTop: 14,
  },
  supportContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    paddingBottom: "16px",
    flexDirection: "column",
    bottom: 80,
    paddingTop: "16px",
    backgroundColor: theme.palette.common.white,
  },
  supportTitle: {
    color: theme.palette.neutral.main,
  },
  supportMessage: {
    marginTop: "4px",
    color: theme.palette.neutral.main,
  },
  arrowKey: {
    color: theme.palette.neutral.main,
  },
  dividerLine: {
    width: "100%",
    paddingTop: "10px",
  },
}))

export default Profile
