import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import { useTheme } from "@mui/material/styles"
import Typography from "@mui/material/Typography"
import useMediaQuery from "@mui/material/useMediaQuery"
import { makeStyles } from "@mui/styles"
import React, { useCallback } from "react"
import { useDispatch, useSelector } from "react-redux"
import { setDomain } from "../../../../../../toolkit/reducer/homeDomain"
import { Domain } from "../../../../../../utils/domains"
import { setStoredData } from "../../../../../../utils/storage"
import { getMobilePageFromDomain } from "../../../../../../utils/utils"

interface RootState {
  categories: {
    domains: Domain[]
  }
  masthead: {
    domainListBackgroundColor: string
  }
}

interface DomainsProps {
  selectedDomain: Domain
}

const Domains: React.FC<DomainsProps> = ({ selectedDomain }) => {
  const dispatch = useDispatch()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"))
  const styles = useDomainsStyles({ isMobile })

  const domains = useSelector((state: RootState) => state.categories.domains)
  const pageDomain = getMobilePageFromDomain(selectedDomain?.domain)

  const domainListBackgroundColor = useSelector(
    (state: RootState) => state.masthead[pageDomain]?.domainListBackgroundColor
  )

  const navigateToCategory = useCallback(
    (domain: Domain) => {
      dispatch(setDomain(domain))
    },
    [dispatch]
  )

  const renderItem = (domain: Domain) => {
    const name = domain.name
    const activeIndex = selectedDomain.domain === domain.domain

    if (activeIndex) {
      setStoredData("home_page_tab", name)
    } else {
    }

    return (
      <Button
        key={domain.name}
        className={styles.category}
        onClick={() => navigateToCategory(domain)}
      >
        <Box className={styles.imageContainer}>
          <img src={domain.Icon} alt="Icon" width={24} height={24} />
        </Box>
        <Typography variant="labelMedium" className={styles.categoryText}>
          {name}
        </Typography>
        <Box
          className={`${styles.borderBottom} ${
            activeIndex ? styles.activeBorder : ""
          }`}
        />
      </Button>
    )
  }

  return (
    <Box
      className={styles.container}
      sx={
        domainListBackgroundColor
          ? { backgroundColor: domainListBackgroundColor }
          : {}
      }
    >
      {domains.map(renderItem)}
    </Box>
  )
}

const useDomainsStyles = makeStyles<any, { isMobile: boolean }>((theme) => ({
  container: ({ isMobile }) => ({
    display: "flex",
    paddingTop: 24,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  }),
  categoryText: {
    textAlign: "center",
    letterSpacing: -0.5,
    color: theme.palette.common.white,
  },
  category: {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    gap: 6,
    borderBottomColor: theme.palette.primary,
    paddingBottom: "1px !important",
  },
  imageContainer: {
    height: 24,
    width: 24,
    justifyContent: "center",
    alignItems: "center",
  },
  borderBottom: {
    height: 3,
    width: "100%",
    backgroundColor: "transparent",
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  activeBorder: {
    backgroundColor: theme.palette.common.white,
  },
}))

export default Domains
