import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import {makeStyles, styled} from "@mui/styles"
import {useTranslation} from "react-i18next"

import {APP_VERSION} from "../../../../utils/constants"
import Header from "../../components/header/Header"
import SafeAreaPage from "../../components/page/SafeAreaPage"
import {Divider, IconButton} from "@mui/material/node"
import {useCallback} from "react"
import {useNavigate} from "react-router-dom"
import KeyboardArrowRight from "@mui/material/internal/svg-icons/KeyboardArrowRight"

const MenuOption = styled("div")({
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  padding: "12px 16px",
  cursor: "pointer",
  "&:hover": {
    backgroundColor: "rgba(0, 0, 0, 0.04)", // subtle hover effect
  },
})

const MenuName = styled(Typography)(({theme}) => ({
  color: theme.palette.text.primary,
  fontWeight: 500,
}))
const About = () => {
  const navigate = useNavigate()
  const styles = useStyles()
  const {t} = useTranslation()
  const navigateToOpenSourceLibraries = useCallback(
    () => navigate("/OpenSourceLibraries"),
    [navigate]
  )
  return (
    <SafeAreaPage>
      <Header label={t("Profile.About")}/>
      <Box className={styles.container}>
        <Box className={styles.appVersion}>
          <Typography variant="labelSmall" className={styles.appVersionText}>
            {t("Profile.App version")}
          </Typography>
          <br/>
          <Typography variant="labelSmall" className={styles.appVersionText}>
            v{APP_VERSION}
          </Typography>
        </Box>
        <Divider sx={{my: 2}}/>
        <MenuOption onClick={navigateToOpenSourceLibraries}>
          <MenuName variant="h6">{t("Profile.Open Source Libraries")}</MenuName>
          <IconButton edge="end" size="small" color="inherit">
            <KeyboardArrowRight/>
          </IconButton>
        </MenuOption>
      </Box>
    </SafeAreaPage>
  )
}

const useStyles = makeStyles((theme) => ({
  container: {
    flex: 1,
    padding: 16,
    height: "100dvh",
  },
  appVersion: {
    paddingVertical: 20,
  },
  appVersionText: {},
  listContainer: {
    paddingHorizontal: 16,
  },
  menuOption: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 20,
  },
  menuName: {
    // color: colors.neutral400,
  },
  divider: {
    width: "100%",
    height: 1,
    // backgroundColor: colors.neutral100,
  },
}))

export default About
