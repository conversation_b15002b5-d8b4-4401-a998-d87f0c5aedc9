import ClearOutlinedIcon from "@mui/icons-material/ClearOutlined"
import FavoriteIcon from "@mui/icons-material/Favorite"
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorder"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import IconButton from "@mui/material/IconButton"
import Modal from "@mui/material/Modal"
import Slide from "@mui/material/Slide"
import useTheme from "@mui/material/styles/useTheme"
import Typography from "@mui/material/Typography"
import axios from "axios"
import React, { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import useNetworkErrorHandling from "../../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import { setProduct } from "../../../../../toolkit/reducer/product"
import { API_BASE_URL, ITEM_DETAILS } from "../../../../../utils/apiActions"
import {
  FASHION_DOMAIN,
  FB_DOMAIN,
  GROCERY_DOMAIN,
} from "../../../../../utils/constants"
import {
  emptyAlertCallback,
  parseBrandOutletId,
} from "../../../../../utils/utils"
import VariationsRenderer from "../../../components/products/VariationsRenderer"
import VegNonVegTag from "../../../components/products/VegNonVegTag"
import CustomizationFooterButtons from "../../../components/provider/components/CustomizationFooterButtons"
import FBProductCustomization from "../../../components/provider/components/FBProductCustomization"
import useAddToCart from "../../../hooks/useAddToCart"
import useCustomizationStateHelper from "../../../hooks/useCustomizationStateHelper"
import useProductWishlist from "../../../hooks/useProductWishlist"
import AboutProduct from "./components/AboutProduct"
import ProductAmount from "./components/ProductAmount"
import ProductImages from "./components/ProductImages"
import ProductSkeleton from "./components/ProductSkeleton"

type ModalProductDetailsProps = {
  inStock: boolean // Or whatever type it should be
  qty: number
  isOpen?: boolean
  wishlistProductId?: string
  wishlistId?: string
  closeModal: () => void
}

const CancelToken = axios.CancelToken

const ModalProductDetails: React.FC<ModalProductDetailsProps> = ({
  inStock,
  qty,
  isOpen = true,
  wishlistProductId = "",
  wishlistId = "",
  closeModal,
}) => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const theme = useTheme()
  const styles = makeStyles(theme)
  const { handleApiError } = useNetworkErrorHandling()
  const { currentProduct } = useSelector((state: any) => state.product)
  const productSource = useRef<any>(null)
  const { customizationState, setCustomizationState, customizationPrices } =
    useCustomizationStateHelper()
  const { getDataWithAuth } = useNetworkHandling()
  const {
    presentInWishlist,
    wishlistApiInProgress,
    addItemToWishlist,
    deleteItemFromWishlist,
    setProductId,
  } = useProductWishlist()
  const navigate = useNavigate()
  const { addDetailsToCart, addRequested } = useAddToCart()
  const [itemOutOfStock, setItemOutOfStock] = useState<boolean>(false)
  const [variationState, setVariationState] = useState<any[]>([])
  const [itemQty, setItemQty] = useState<number>(1)
  const [apiInProgress, setApiInProgress] = useState<boolean>(false)

  const initializeProductId = (product: any) => {
    let firstProductId = product.id
    if (!product.is_first) {
      const firstProduct = product.related_items.find(
        (item: any) => item.is_first
      )
      if (firstProduct) {
        firstProductId = firstProduct.id
      }
    }
    setProductId(firstProductId)
  }

  const addItemDetailsToCart = async () => {
    await addDetailsToCart(
      currentProduct,
      customizationState,
      customizationPrices,
      itemQty,
      setCustomizationState,
      emptyAlertCallback,
      closeModal,
      wishlistProductId,
      wishlistId
    )
  }

  const getProductDetails = async (productId: string) => {
    try {
      setApiInProgress(true)
      productSource.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${ITEM_DETAILS}?id=${productId}`,
        productSource.current.token
      )
      dispatch(setProduct(data))
      initializeProductId(data)
    } catch (error) {
      handleApiError(error)
    } finally {
      setApiInProgress(false)
    }
  }

  const openBrandDetails = () => {
    const result = parseBrandOutletId(currentProduct.provider_details.id)
    navigate(
      `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}`
    )
  }

  const renderFooter = () => {
    if (!inStock) {
      return (
        <Box sx={styles.outOfStockContainer}>
          <Box sx={styles.outOfStockSheetButton}>
            <Typography
              variant="bodyLarge"
              sx={styles.outOfStockSheetButtonText}
            >
              {t("Cart.FBProduct.Out of stock")}
            </Typography>
          </Box>
        </Box>
      )
    }

    if (!isOpen) {
      return (
        <Box sx={styles.outOfStockContainer}>
          <Box sx={styles.outOfStockSheetButton}>
            <Typography
              variant="bodyLarge"
              sx={styles.outOfStockSheetButtonText}
            >
              {t("Cart.FBProduct.Item is unavailable")}
            </Typography>
          </Box>
        </Box>
      )
    }

    return (
      <CustomizationFooterButtons
        productLoading={addRequested}
        itemQty={itemQty}
        setItemQty={setItemQty}
        itemOutOfStock={itemOutOfStock}
        addDetailsToCart={addItemDetailsToCart}
        product={currentProduct}
        customizationPrices={customizationPrices}
        isOpen={isOpen}
      />
    )
  }

  useEffect(() => {
    setItemQty(qty)
  }, [qty])

  useEffect(() => {
    if (currentProduct) {
      initializeProductId(currentProduct)
    }
  }, [currentProduct])

  return (
    <Modal open={true}>
      <Slide direction="up" in={true} mountOnEnter unmountOnExit>
        <Box
          sx={{
            position: "fixed",
            bottom: 0,
            left: 0,
            right: 0,
            boxShadow: 24,
            maxHeight: "90dvh",
            overflow: "auto",
          }}
        >
          {apiInProgress ? (
            <Box sx={styles.sheetContainer}>
              <ProductSkeleton />
            </Box>
          ) : (
            <Box sx={styles.sheetContainer}>
              <Box sx={styles.closeSheet}>
                <IconButton
                  sx={{
                    width: 32,
                    height: 32,
                    backgroundColor: theme.palette.common.black,
                  }}
                  onClick={closeModal}
                >
                  <ClearOutlinedIcon sx={{ fontSize: 24, color: "#fff" }} />
                </IconButton>
              </Box>
              <Box sx={{ backgroundColor: "#fff" }}>
                <Box sx={styles.productDetails}>
                  <>
                    <Box sx={styles.header}>
                      <Box sx={styles.actionContainer}>
                        {(currentProduct?.context?.domain === GROCERY_DOMAIN ||
                          currentProduct?.context.domain === FB_DOMAIN) && (
                          <Box sx={styles.vegNonVegContainer}>
                            <VegNonVegTag
                              tags={currentProduct?.item_details?.tags}
                            />
                          </Box>
                        )}
                        <Box sx={styles.wishlistContainer}>
                          {wishlistApiInProgress ? (
                            <Box sx={styles.wishlistButton}>
                              <CircularProgress size="small" />
                            </Box>
                          ) : presentInWishlist ? (
                            <IconButton
                              sx={styles.wishlistButton}
                              onClick={deleteItemFromWishlist}
                            >
                              <FavoriteIcon color="error" />
                            </IconButton>
                          ) : (
                            <IconButton
                              sx={styles.wishlistButton}
                              onClick={() => addItemToWishlist(currentProduct)}
                            >
                              <FavoriteBorderIcon color="error" />
                            </IconButton>
                          )}
                        </Box>
                      </Box>
                      <Box sx={styles.imagesContainer}>
                        {!inStock && <Box sx={styles.disableImages} />}
                        <ProductImages
                          fbProduct
                          roundedCorner
                          images={
                            currentProduct?.item_details?.descriptor?.images
                          }
                          symbol={
                            currentProduct?.item_details?.descriptor?.symbol
                          }
                          disabled={!inStock || !isOpen}
                        />
                      </Box>
                      <Box sx={styles.detailsContainer}>
                        <Button sx={{ padding: 0 }} onClick={openBrandDetails}>
                          <Typography variant="bodyLarge" sx={styles.brandName}>
                            {currentProduct?.provider_details?.descriptor?.name}
                          </Typography>
                        </Button>
                        <Typography
                          variant="titleLarge"
                          component="div"
                          sx={styles.title}
                        >
                          {currentProduct?.item_details?.descriptor?.name}
                        </Typography>
                        <ProductAmount
                          price={currentProduct?.item_details?.price}
                          inStock={inStock}
                        />
                        <Typography
                          variant="labelSmall"
                          component="div"
                          sx={[
                            styles.description,
                            inStock ? {} : styles.disabledText,
                          ]}
                        >
                          {currentProduct?.item_details?.descriptor?.short_desc}
                        </Typography>
                      </Box>

                      <Box sx={styles.variations}>
                        <VariationsRenderer
                          product={currentProduct}
                          variationState={variationState}
                          setVariationState={setVariationState}
                          isFashion={
                            currentProduct?.context?.domain === FASHION_DOMAIN
                          }
                          handleVariationChange={getProductDetails}
                          disabled={!isOpen || !inStock}
                        />
                        <Box sx={styles.customizationContainer}>
                          <FBProductCustomization
                            hideProductDetails
                            product={currentProduct}
                            customizationState={customizationState}
                            setCustomizationState={setCustomizationState}
                            setItemOutOfStock={setItemOutOfStock}
                            disabled={!inStock || !isOpen}
                          />
                        </Box>
                        <AboutProduct
                          product={currentProduct}
                          inStock={inStock}
                        />
                      </Box>
                    </Box>
                  </>
                </Box>
                {renderFooter()}
              </Box>
            </Box>
          )}
        </Box>
      </Slide>
    </Modal>
  )
}

const makeStyles = (theme: any) => ({
  closeSheet: {
    alignItems: "center",
    display: "flex",
    justifyContent: "center",
    marginBottom: 1,
  },
  sheetContainer: {
    overflow: "hidden",
  },
  variations: {
    padding: 2,
  },
  productDetails: {
    height: "calc(90dvh - 120px)",
    overflowX: "hidden",
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  detailsContainer: {
    paddingLeft: 2,
    paddingRight: 2,
  },
  title: {
    color: theme.palette.common.neutral400,
    marginTop: 1,
    marginBottom: 1,
  },
  price: {
    color: theme.palette.common.neutral400,
  },
  disabledText: {
    color: theme.palette.common.neutral300,
  },
  header: {
    paddingHorizontal: 16,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    backgroundColor: theme.palette.common.white,
  },
  actionContainer: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginLeft: "16px",
    marginRight: "16px",
    marginTop: "16px",
  },
  imagesContainer: {
    borderRadius: 8,
  },
  vegNonVegContainer: {},
  wishlistContainer: {
    width: 32,
    height: 32,
    backgroundColor: theme.palette.common.white,
    borderRadius: 32,
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
  },
  disableImages: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    zIndex: 1000,
    height: 220,
    width: "100%",
    position: "absolute",
    // top: 0,
    // left: 0,
    // right: 0,
    // bottom: 0,
    borderRadius: 8,
  },
  disableTags: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    width: 18,
    height: 18,
    marginTop: -18,
  },
  brandName: {
    color: theme.palette.common.primary,
  },
  description: {
    color: theme.palette.common.neutral400,
    marginTop: 1,
  },
  customizationContainer: {
    backgroundColor: theme.palette.common.neutral50,
  },
  outOfStockContainer: {
    flexDirection: "row",
    justifyContent: "center",
    paddingBottom: 10,
  },
  outOfStockSheetButton: {
    borderRadius: 8,
    backgroundColor: theme.palette.common.neutral200,
    paddingVertical: 13,
    alignItems: "center",
    marginHorizontal: 24,
    width: "100%",
  },
  outOfStockSheetButtonText: {
    color: theme.palette.common.white,
  },
  wishlistButton: {
    width: 24,
    height: 24,
    alignItems: "center",
    justifyContent: "center",
  },
})

export default ModalProductDetails
