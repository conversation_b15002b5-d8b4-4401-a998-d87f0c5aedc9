import Box from "@mui/material/Box"
import Typography from "@mui/material/Typography"
import { useTheme } from "@mui/styles"
import axios from "axios"
import { useEffect, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate, useSearchParams } from "react-router-dom"
import { useToast } from "../../../../../hooks/toastProvider"
import useNetworkErrorHandling from "../../../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../../../hooks/useNetworkHandling"
import { updateCartItems } from "../../../../../toolkit/reducer/cart"
import {
  API_BASE_URL,
  CART,
  ITEM_DETAILS,
} from "../../../../../utils/apiActions"
import {
  CURRENCY_SYMBOLS,
  FASHION_DOMAIN,
  FB_DOMAIN,
} from "../../../../../utils/constants"
import {
  areCustomisationsSame,
  emptyAlertCallback,
  isCurrentTimeInRange,
} from "../../../../../utils/utils"
import HeaderWithActions from "../../../components/header/HeaderWithActions"
import Page from "../../../components/page/Page"
import VariationsRenderer from "../../../components/products/VariationsRenderer"
import FBProductCustomization from "../../../components/provider/components/FBProductCustomization"
import OutletCoupons from "../../../components/provider/components/OutletCoupons"
import useFormatNumber from "../../../hooks/useFormatNumber"
import useNavigateToProduct from "../../../hooks/useNavigateToProduct"
import useProductWishlist from "../../../hooks/useProductWishlist"
import userUpdateCartItem from "../../../hooks/userUpdateCartItem"
import AboutProduct from "./components/AboutProduct"
import AddToCartButton from "./components/AddToCartButton"
import FoodType from "./components/FoodType"
import ProductAmount from "./components/ProductAmount"
import ProductImages from "./components/ProductImages"
import ProductSkeleton from "./components/ProductSkeleton"
import QuantityControl from "./components/QuantityControl"
import WishlistUI from "./components/WishlistUI"

const CancelToken = axios.CancelToken

const ProductDetails = () => {
  const { showToast } = useToast()
  const navigation = useNavigate()
  const [searchParams] = useSearchParams()
  const domain = searchParams.get("domain") || ""

  const providerId = searchParams.get("provider_id") || ""
  const bppId = searchParams.get("bpp_id") || ""
  const itemId = searchParams.get("item_id") || ""
  const currentProductId = `${bppId}_${domain}_${providerId}_${itemId}`
  const { formatNumber } = useFormatNumber()
  const { t } = useTranslation()
  const firstTime = useRef<boolean>(true)
  const { uid } = useSelector((state: any) => state.auth)
  const source = useRef<any>(null)
  const dispatch = useDispatch()
  const theme = useTheme()
  const styles = useStyles(theme)
  const currentCartItem = useRef<any>(null)
  const navigateToProduct = useNavigateToProduct()
  const { cartItems } = useSelector((state: any) => state.cart)
  // const logEvent = useLogAppsFlyerEvent();
  const [product, setProduct] = useState<any>(null)
  const [apiRequested, setApiRequested] = useState<boolean>(true)
  const [itemOutOfStock, setItemOutOfStock] = useState<boolean>(false)
  const [addToCartLoading, setAddToCartLoading] = useState<boolean>(false)
  const [customizationPrices, setCustomizationPrices] = useState<number>(0)
  const [itemAvailableInCart, setItemAvailableInCart] = useState<any>(null)
  const [isItemAvailableInCart, setIsItemAvailableInCart] =
    useState<boolean>(false)
  const [storeOpen, setStoreOpen] = useState<boolean>(true)
  const [priceRange, setPriceRange] = useState<any>(null)

  const [variationState, setVariationState] = useState<any[]>([])
  const [customizationState, setCustomizationState] = useState<any>({})
  const { deleteDataWithAuth, getDataWithAuth, postDataWithAuth } =
    useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const {
    presentInWishlist,
    wishlistApiInProgress,
    addItemToWishlist,
    deleteItemFromWishlist,
    setProductId,
  } = useProductWishlist()
  const { updateCartItem } = userUpdateCartItem()

  useEffect(() => {
    if (domain === FB_DOMAIN) {
      navigation(
        `/store?domain=${providerId}&provider_id=${bppId}_${domain}_${providerId}&bpp_id=${domain}&locationId=${bppId}_${domain}_${providerId}_L:${providerId}&itemId=${currentProductId}`
      )
    }
  }, [])

  const getProductDetails = async () => {
    try {
      if (firstTime.current) {
        setApiRequested(true)
      }
      firstTime.current = false
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${ITEM_DETAILS}?id=${currentProductId}`,
        source.current.token
      )
      const isOpen = isCurrentTimeInRange(data.location_availabilities)

      let rangePriceTag = null
      if (data?.item_details?.price?.tags) {
        const findRangePriceTag = data?.item_details?.price?.tags.find(
          (item: any) => item.code === "range"
        )
        if (findRangePriceTag) {
          const findLowerPriceObj = findRangePriceTag.list.find(
            (item: any) => item.code === "lower"
          )
          const findUpperPriceObj = findRangePriceTag.list.find(
            (item: any) => item.code === "upper"
          )
          rangePriceTag = {
            maxPrice: findUpperPriceObj.value,
            minPrice: findLowerPriceObj.value,
          }
        }
      }
      setPriceRange(rangePriceTag)
      setProduct(data)
      setStoreOpen(isOpen)
      await getCartItems(data.id)
    } catch (error) {
      handleApiError(error)
    } finally {
      setApiRequested(false)
    }
  }

  const calculateSubtotal = (groupId: any, newState: any) => {
    let group = newState[groupId]
    if (!group) {
      return
    }

    let prices = group.selected.map((s: any) => s.price)
    setCustomizationPrices((prevState) => {
      return prevState + prices.reduce((a: any, b: any) => a + b, 0)
    })

    group?.childs?.map((child: any) => {
      calculateSubtotal(child, newState)
    })
  }

  const getCustomization = (groupId: any, selectedCustomizationIds: any[]) => {
    let group = customizationState[groupId]
    if (!group) {
      return selectedCustomizationIds
    }

    group.selected.forEach((selectedGroup: any) =>
      selectedCustomizationIds.push(selectedGroup.id)
    )
    group?.childs?.forEach((child: any) => {
      getCustomization(child, selectedCustomizationIds)
    })
    return selectedCustomizationIds
  }

  const getCustomizations = () => {
    const { customisation_items } = product

    if (!customisation_items?.length) {
      return null
    }
    const customizations = []

    const firstGroupId = customizationState.firstGroup?.id

    if (!firstGroupId) {
      return
    }
    let selectedCustomizationIds = getCustomization(firstGroupId, [])

    for (const cId of selectedCustomizationIds) {
      let c = customisation_items.find((item: any) => item.local_id === cId)
      if (c) {
        c = {
          ...c,
          quantity: {
            count: 1,
          },
        }
        customizations.push(c)
      }
    }

    return customizations
  }

  const handleItemCountChange = (productId: any, data: any) => {
    let itemIndex: any = 0
    if (productId) {
      let isItemAvailable = false
      let findItem
      data?.forEach((res: any, index: number) => {
        const check = res.items.find((item: any) => item.item.id === productId)
        if (check) {
          itemIndex = index
          findItem = check
        }
      })
      if (findItem) {
        isItemAvailable = true
        setItemAvailableInCart(findItem)
        currentCartItem.current = findItem
      }
      setIsItemAvailableInCart(isItemAvailable)
    } else {
      currentCartItem.current = null
      setItemAvailableInCart(null)
      setIsItemAvailableInCart(false)
    }

    return itemIndex
  }

  const getCartItems = async (productId = null) => {
    try {
      source.current = CancelToken.source()
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/all`,
        source.current.token
      )
      const index = handleItemCountChange(productId, data)
      dispatch(updateCartItems(data))
      return data[index].items
    } catch (error: any) {
      // crashlytics().recordError(error);
      return []
    }
  }

  const deleteCartItem = async (itemId: any) => {
    source.current = CancelToken.source()
    await deleteDataWithAuth(
      `${API_BASE_URL}${CART}/${uid}/${itemId}`,
      source.current.token
    )
    let list = cartItems.map((cart: any) => ({
      ...cart,
      items: cart.items.filter((item: any) => item._id !== itemId),
    }))
    const cartToDelete = list.find((cart: any) => cart?.items?.length === 0)
    if (cartToDelete) {
      await deleteDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/${cartToDelete._id}/clear`,
        source.current.token
      )
    }
    await getCartItems()
  }

  const getCartPayload = (subtotal: number, customisations: any) => {
    return {
      id: product.id,
      local_id: product.local_id,
      bpp_id: product.bpp_details.bpp_id,
      bpp_uri: product.context.bpp_uri,
      domain: product.context.domain,
      tags: product.item_details.tags,
      customisationState: customizationState,
      contextCity: product.context.city,
      quantity: {
        count: 1,
      },
      provider: {
        id: product.bpp_details.bpp_id,
        locations: product.locations,
        ...product.provider_details,
      },
      location_details: product.location_details,
      product: {
        id: product.id,
        subtotal,
        ...product.item_details,
      },
      customisations,
      hasCustomisations: !!customisations,
    }
  }

  const addProductToCart = async (payload: any) => {
    source.current = CancelToken.source()
    await postDataWithAuth(
      `${API_BASE_URL}${CART}/${uid}`,
      payload,
      source.current.token
    )
    // logEvent(APPS_FLYER_EVENTS.ITEM_ADDED_TO_CART, {
    //   user_id: uid,
    //   af_quantity: payload.quantity.count,
    //   timestamp: getCurrentDate(),
    //   provider_id: payload.provider.local_id,
    //   domain: payload.domain,
    // });
    await getCartItems(product.id)
    showToast(t("Product Summary.Item added to cart successfully"), "info")
    setAddToCartLoading(false)
  }

  const updateProductInCart = async (
    items: any[],
    isIncrement: boolean,
    id: string
  ) => {
    const data = await updateCartItem(items, isIncrement, id)
    setItemAvailableInCart(data)
    await getCartItems(product.id)
    showToast(t("Product Summary.Item quantity updated in your cart"), "info")
    setAddToCartLoading(false)
  }

  const addToCart = async (isIncrement = true) => {
    try {
      setAddToCartLoading(true)
      source.current = CancelToken.source()
      let subtotal = product?.item_details?.price?.value

      const customisations = getCustomizations() ?? null

      if (customisations) {
        calculateSubtotal(customizationState.firstGroup?.id, customizationState)
        subtotal += customizationPrices
      }

      const payload: any = getCartPayload(subtotal, customisations)

      const cartItems: any[] = await getCartItems(product.id)

      let cartItem = cartItems?.filter((ci) => {
        return ci.item.id === payload.id
      })

      if (customisations) {
        cartItem = cartItem.filter((ci) => {
          return ci.item.customisations != null
        })
      }

      if (cartItem?.length > 0 && customisations) {
        cartItem = cartItem.filter((ci) => {
          return ci.item.customisations?.length === customisations?.length
        })
      }

      if (cartItem?.length === 0) {
        await addProductToCart(payload)
      } else {
        const currentCount = Number(cartItem[0].item.quantity.count)
        const maxCount = Number(cartItem[0].item.product.quantity.maximum.count)

        if (currentCount < maxCount || !isIncrement) {
          if (!customisations) {
            await updateProductInCart(cartItems, isIncrement, cartItem[0]._id)
          } else {
            const currentIds = customisations.map((item) => item.id)
            let matchingCustomisation = null

            for (let i = 0; i < cartItem?.length; i++) {
              let existingIds = cartItem[i].item.customisations.map(
                (item: any) => item.id
              )
              const areSame = areCustomisationsSame(existingIds, currentIds)
              if (areSame) {
                matchingCustomisation = cartItem[i]
              }
            }

            if (matchingCustomisation) {
              await updateProductInCart(
                cartItems,
                isIncrement,
                matchingCustomisation._id
              )
            } else {
              await addProductToCart(payload)
            }
          }
        } else {
          showToast(
            t(
              "Product Summary.The maximum available quantity for item is already in your cart"
            ),
            "error"
          )
          setAddToCartLoading(false)
        }
      }
    } catch (error: any) {
      // crashlytics().recordError(error);
    }
  }

  const currency = useMemo(
    () => CURRENCY_SYMBOLS[product?.item_details?.price?.currency],
    [product, CURRENCY_SYMBOLS]
  )

  const initialiseProductId = () => {
    let firstProductId = product.id
    if (!product.is_first) {
      const firstProduct = product.related_items.find(
        (item: any) => item.is_first
      )
      if (firstProduct) {
        firstProductId = firstProduct.id
      }
    }
    setProductId(firstProductId)
  }

  useEffect(() => {
    if (product) {
      initialiseProductId()
    }
  }, [product])

  useEffect(() => {
    getProductDetails().finally(emptyAlertCallback)

    return () => {
      if (source.current) {
        source.current.cancel()
      }
    }
  }, [])

  // useEffect(() => {
  //   // if (isFocused) {
  //     handleItemCountChange(params?.productId, cartItems);
  //   // }
  // }, []);

  if (apiRequested || !product) {
    return <ProductSkeleton />
  }

  const shouldDisableActionButtons = () => {
    const availableCount = Number(
      product?.item_details?.quantity?.available?.count
    )
    return (
      availableCount < 1 || itemOutOfStock || addToCartLoading || !storeOpen
    )
  }

  const disableActionButtons = shouldDisableActionButtons()

  return (
    <Page outletId={product?.location_details?.id}>
      <HeaderWithActions
        label={product?.item_details?.descriptor?.name}
        wishlist
        cart
      />
      <Box sx={styles.container}>
        <ProductImages
          images={product?.item_details?.descriptor?.images}
          symbol={product?.item_details?.descriptor?.symbol}
          disabled={disableActionButtons}
        />
        <Box sx={styles.detailsContainer}>
          <Box sx={styles.details}>
            <FoodType
              tags={product?.item_details?.tags}
              domain={product?.context?.domain}
            />
            <Typography
              variant="titleLarge"
              component="div"
              sx={styles.brandName}
            >
              {product?.provider_details?.descriptor?.name}
            </Typography>
            <Typography
              variant="headlineSmall"
              component="div"
              sx={styles.title}
            >
              {product?.item_details?.descriptor?.name}
            </Typography>
            {priceRange ? (
              <Box sx={styles.priceContainer}>
                <Typography variant="titleLarge" sx={styles.price}>
                  {`${currency}${formatNumber(
                    Number(priceRange?.minPrice).toFixed(2)
                  )} - ${currency}${formatNumber(
                    Number(priceRange?.maxPrice).toFixed(2)
                  )}`}
                </Typography>
              </Box>
            ) : (
              <ProductAmount
                price={product?.item_details?.price}
                inStock={true}
              />
            )}
            <OutletCoupons
              bppId={product?.context?.bpp_id}
              domain={product?.context?.domain}
              providerId={product?.provider_details?.id}
            />
          </Box>
          <Box sx={styles.details}>
            <VariationsRenderer
              product={product}
              variationState={variationState}
              setVariationState={setVariationState}
              isFashion={
                product?.context?.domain === FASHION_DOMAIN &&
                !!product?.attributes?.size_chart
              }
              handleVariationChange={navigateToProduct}
              disabled={!storeOpen}
            />
            {product?.context?.domain === FB_DOMAIN && (
              <FBProductCustomization
                product={product}
                customizationState={customizationState}
                setCustomizationState={setCustomizationState}
                isEditFlow={false}
                setItemOutOfStock={setItemOutOfStock}
                disabled={!storeOpen}
              />
            )}
            <Box sx={styles.addToCartContainer}>
              <Box sx={styles.buttonRow}>
                <Box sx={styles.addToWishlistButton}>
                  <WishlistUI
                    wishlistApiInProgress={wishlistApiInProgress}
                    presentInWishlist={presentInWishlist}
                    deleteItemFromWishlist={deleteItemFromWishlist}
                    addItemToWishlist={addItemToWishlist}
                    product={product}
                  />
                </Box>
                {product?.context?.domain !== FB_DOMAIN &&
                isItemAvailableInCart &&
                itemAvailableInCart ? (
                  <QuantityControl
                    loading={addToCartLoading}
                    item={itemAvailableInCart.item}
                    onDecrement={() => {
                      if (itemAvailableInCart.item.quantity.count === 1) {
                        deleteCartItem(itemAvailableInCart._id).then(
                          emptyAlertCallback
                        )
                      } else {
                        addToCart(false).then(emptyAlertCallback)
                      }
                    }}
                    onIncrement={() => addToCart(true)}
                  />
                ) : (
                  <AddToCartButton
                    onPress={() => addToCart(true)}
                    loading={addToCartLoading}
                    disabled={disableActionButtons}
                    label={
                      itemOutOfStock || !storeOpen
                        ? t("Cart.FBProduct.Item is unavailable")
                        : t("Cart.Add to cart")
                    }
                  />
                )}
              </Box>
              <AboutProduct product={product} inStock />
            </Box>
          </Box>
        </Box>
      </Box>
    </Page>
  )
}

const useStyles = (theme: any) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    backgroundColor: theme.palette.common.white,
    height: "100dvh",
    overflow: "scroll",
  },
  header: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    padding: "12px 16px",
  },
  detailsContainer: {
    paddingTop: "16px",
    paddingBottom: "16px",
  },
  details: {
    paddingLeft: "16px",
    paddingRight: "16px",
  },
  brandName: {
    color: theme.palette.primary.main,
    marginBottom: "12px",
  },
  title: {
    color: theme.palette.text.primary,
    marginBottom: "8px",
  },
  price: {
    color: theme.palette.neutral?.[400] ?? theme.palette.text.secondary,
  },
  priceContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  addToCartContainer: {
    marginTop: "28px",
  },
  addToWishlistButton: {
    flex: 1,
    borderRadius: "8px",
    border: `1px solid ${theme.palette.text.primary}`,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: "44px",
  },
  quantity: {
    color: theme.palette.primary.main,
    marginLeft: "20px",
    marginRight: "20px",
  },
  buttonRow: {
    display: "flex",
    flexDirection: "row",
    columnGap: "15px",
  },
})

export default ProductDetails
