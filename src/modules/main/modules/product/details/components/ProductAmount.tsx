import React from 'react';
import {useTranslation} from 'react-i18next';
import {useTheme} from "@mui/styles";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import {CURRENCY_SYMBOLS} from '../../../../../../utils/constants';
import useFormatNumber from '../../../../hooks/useFormatNumber';

const ProductAmount = ({price, inStock}: { price: any; inStock: boolean }) => {
  const {t} = useTranslation();
  const {formatNumber} = useFormatNumber();
  const theme = useTheme();
  const styles = makeStyles(theme);

  if (!price) {
    return <></>;
  } else {
    let discount = 0;
    const discountedValue = Number(price?.value);
    const maxValue = Number(price?.maximum_value);
    if (maxValue !== 0 && maxValue !== discountedValue) {
      discount = 100 - Math.floor((100 * discountedValue) / maxValue);
    }
    const currency = CURRENCY_SYMBOLS[price?.currency ?? CURRENCY_SYMBOLS.INR];

    return (
      <Box sx={styles.priceContainer}>
        <Typography
          variant="bodyLarge"
          sx={[styles.price, inStock ? {} : styles.disabledText]}>
          {currency}
          {formatNumber(Number(price?.value).toFixed(2))}
        </Typography>
        {discount !== 0 && (
          <Box sx={styles.amountContainer}>
            <Typography variant="labelMedium" sx={styles.maximumAmount}>
              {currency}
              {formatNumber(Number(price?.maximum_value).toFixed(2))}
            </Typography>
            <Typography variant="labelLarge" sx={styles.discount}>
              {t('Product.discount', {discount})}
            </Typography>
          </Box>
        )}
      </Box>
    );
  }
};

const makeStyles = (theme: any) => ({
  price: {
    color: theme.palette.text.primary,
  },
  disabledText: {
    color: theme.palette.text.disabled,
  },
  priceContainer: {
    display: 'flex',
    flexDirection: 'row' as const,
    alignItems: 'center',
    gap: 4,
  },
  maximumAmount: {
    color: theme.palette.text.disabled,
    textDecoration: 'line-through',
  },
  amountContainer: {
    display: 'flex',
    flexDirection: 'row' as const,
    gap: 4,
  },
  discount: {
    color: theme.palette.success.main,
  },
});

export default ProductAmount;
