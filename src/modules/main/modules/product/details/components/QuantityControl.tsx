import React from 'react';
import Box from "@mui/material/Box";
import IconButton from "@mui/material/IconButton";
import RemoveOutlinedIcon from '@mui/icons-material/RemoveOutlined';
import AddOutlinedIcon from '@mui/icons-material/AddOutlined';
import Typography from "@mui/material/Typography";
import CircularProgress from "@mui/material/CircularProgress";
import {useTheme} from "@mui/material/styles";
import useFormatNumber from '../../../../hooks/useFormatNumber';

const QuantityControl = ({
                           item,
                           onIncrement,
                           onDecrement,
                           loading,
                         }: {
  item: any;
  onIncrement: () => void;
  onDecrement: () => void;
  loading: boolean;
}) => {
  const theme = useTheme();
  const {formatNumber} = useFormatNumber();

  return (
    <Box sx={{
      backgroundColor: '#ECF3F8',
      borderColor: '#008ECC',
      borderStyle: 'solid',
      flexDirection: 'row',
      alignItems: 'center',
      display: 'flex',
      borderWidth: 1,
      borderRadius: 2,
      height: 44,
      justifyContent: 'center',
      flex: 1,
    }}>
      <IconButton onClick={onDecrement}>
        <RemoveOutlinedIcon color="primary"/>
      </IconButton>
      <Typography variant="bodyLarge" component="div"
                  sx={{color: theme.palette.primary.main, textAlign: 'center', mx: 2, flex: 1}}>
        {loading ? (
          <CircularProgress size={16}/>
        ) : (
          formatNumber(item.quantity.count)
        )}
      </Typography>
      <IconButton onClick={onIncrement}>
        <AddOutlinedIcon color="primary"/>
      </IconButton>
    </Box>
  );
};

export default QuantityControl;
