import React from 'react';
import Box from "@mui/material/Box";
import {FB_DOMAIN, GROCERY_DOMAIN} from '../../../../../../utils/constants';
import VegNonVegTag from '../../../../components/products/VegNonVegTag';

const FoodType = ({domain, tags}: { tags: any; domain: string | undefined }) => {
  switch (domain) {
    case FB_DOMAIN:
    case GROCERY_DOMAIN:
      return (
        <Box sx={{flexDirection: 'row', alignItems: 'center', marginBottom: '10px'}}>
          <VegNonVegTag tags={tags} showLabel/>
        </Box>
      );

    default:
      return <></>;
  }
};

export default FoodType;
