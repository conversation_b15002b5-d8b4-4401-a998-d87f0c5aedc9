import ExpandMoreIcon from "@mui/icons-material/ExpandMore"
import Accordion from "@mui/material/Accordion"
import AccordionDetails from "@mui/material/AccordionDetails"
import AccordionSummary from "@mui/material/AccordionSummary"
import Box from "@mui/material/Box"
import Grid from "@mui/material/Grid2"
import Typography from "@mui/material/Typography"
import { useTheme } from "@mui/styles"
import "moment-duration-format"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import useMinutesToString from "../../../../hooks/useMinutesToString"

const AboutProduct = ({
  product,
  inStock,
}: {
  product: any
  inStock?: boolean
}) => {
  const { t } = useTranslation()
  const { convertDurationToHumanReadable, translateMinutesToHumanReadable } =
    useMinutesToString()
  const theme = useTheme()
  const styles = makeStyles(theme)

  const attributes = useMemo(() => {
    if (product) {
      let returnWindowValue: string = "0"
      if (product.item_details?.["@ondc/org/return_window"]) {
        const time = convertDurationToHumanReadable(
          product.item_details?.["@ondc/org/return_window"]
        )
        returnWindowValue = translateMinutesToHumanReadable(
          time.type,
          time.time
        )
      }

      const returnable =
        product.item_details?.["@ondc/org/returnable"]?.toString() === "true"

      let data: any = {
        "Available on COD":
          product.item_details?.["@ondc/org/available_on_cod"]?.toString() ===
          "true"
            ? "Yes"
            : "No",
        Cancellable:
          product.item_details?.["@ondc/org/cancellable"]?.toString() === "true"
            ? "Yes"
            : "No",
        Returnable: returnable ? "Yes" : "No",
        "Customer care":
          product.item_details?.["@ondc/org/contact_details_consumer_care"],
        "Manufacturer name":
          product.item_details?.[
            "@ondc/org/statutory_reqs_packaged_commodities"
          ]?.manufacturer_or_packer_name,
        "Manufacturer address":
          product.item_details?.[
            "@ondc/org/statutory_reqs_packaged_commodities"
          ]?.manufacturer_or_packer_address,
        Description: product.item_details?.descriptor?.long_desc ?? "",
      }

      if (returnable) {
        data["Returnable in"] = returnWindowValue
      }

      data = Object.assign({}, data, product?.attributes)

      return data
    }

    return {}
  }, [product])

  return (
    <Box sx={styles.container}>
      <Accordion sx={styles.listHeader}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography
            variant="titleLarge"
            sx={{
              ...styles.about,
              ...(inStock ? {} : styles.disabledText),
            }}
          >
            {t("Cart.Product Details")}
          </Typography>
        </AccordionSummary>
        <AccordionDetails sx={styles.details}>
          {Object.keys(attributes).map((key) => (
            <Grid container key={key} spacing={2} sx={{ mb: 2 }}>
              <Grid size={4}>
                <Typography sx={styles.aboutTitle} variant="bodySmall">
                  {key}
                </Typography>
              </Grid>
              <Grid size={8}>
                <Typography variant="bodySmall" sx={styles.aboutDetails}>
                  {attributes[key]}
                </Typography>
              </Grid>
            </Grid>
          ))}
        </AccordionDetails>
      </Accordion>
    </Box>
  )
}

const makeStyles = (theme: any) => ({
  container: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  about: {
    color: theme.palette.text.primary,
  },
  disabledText: {
    color: theme.palette.text.disabled,
  },
  aboutTitle: {
    color: theme.palette.text.secondary,
    textTransform: "capitalize",
  },
  aboutDetails: {
    color: theme.palette.text.primary,
    whiteSpace: "normal",
    wordBreak: "break-word",
    overflowWrap: "break-word",
  },
  details: {
    padding: 2,
    backgroundColor: theme.palette.background.paper,
  },
  detailsContainer: {
    width: "calc(100vw - 64px)",
  },
  listHeader: {
    backgroundColor: theme.palette.background.paper,
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: "0px !important",
    borderTopLeftRadius: "8px !important",
    borderTopRightRadius: "8px !important",
    borderBottomLeftRadius: "0px !important",
    borderBottomRightRadius: "0px !important",
    boxShadow: "none",
  },
})

export default AboutProduct
