import React from 'react';
import Box from '@mui/material/Box';
import Skeleton from '@mui/material/Skeleton';
import Stack from '@mui/material/Stack';

const ProductSkeleton: React.FC = () => {
  return (
    <Box sx={{width: '100%', px: 2, py: 3}}>
      <Stack spacing={4}>
        {/* Image Placeholder */}
        <Skeleton variant="rectangular" width="100%" height={300}/>

        {/* Title */}
        <Skeleton variant="text" width={100} height={20}/>

        {/* Button / input */}
        <Skeleton variant="rectangular" width="100%" height={48}/>

        {/* Two rows of small text placeholders */}
        {[...Array(2)].map((_, rowIndex) => (
          <Box
            key={rowIndex}
            sx={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: 2,
            }}
          >
            <Skeleton variant="text" width={100} height={20}/>
            <Skeleton variant="text" width={100} height={20}/>
          </Box>
        ))}
      </Stack>
    </Box>
  );
};

export default ProductSkeleton;
