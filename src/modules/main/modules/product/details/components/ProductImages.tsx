import React, {useState} from 'react';
import Slider from 'react-slick';
import Box from '@mui/material/Box';
import {useTheme} from "@mui/material/styles";

interface ProductImagesProps {
  images: string[];
  symbol?: string;
  roundedCorner?: boolean;
  fbProduct?: boolean;
  disabled?: boolean;
}

const ProductImages: React.FC<ProductImagesProps> = ({
                                                       images,
                                                       symbol,
                                                       roundedCorner = false,
                                                       fbProduct = false,
                                                       disabled = false,
                                                     }) => {
  const theme = useTheme();
  const [selectedMediaPosition, setSelectedMediaPosition] = useState(0);

  if (images.length === 0) {
    return <></>;
  }

  let productImages = [...images];
  if (symbol) {
    productImages.push(symbol);
  }

  const sliderSettings = {
    dots: false,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    afterChange: (index: number) => setSelectedMediaPosition(index),
  };

  return (
    <Box sx={{backgroundColor: 'white', pb: 2}}>
      <Slider {...sliderSettings}>
        {productImages.map((uri, index) => (
          <Box
            key={`${index}Image`}
            sx={{
              position: 'relative',
              display: 'flex',
              justifyContent: 'center',
              filter: disabled ? 'grayscale(100%)' : 'none',
              borderRadius: roundedCorner ? 2 : 0,
              height: fbProduct ? 220 : 400,
              overflow: 'hidden',
            }}
          >
            <Box
              component="img"
              src={uri}
              alt={`product-${index}`}
              sx={{
                width: '100%',
                height: fbProduct ? 220 : 400,
                objectFit: 'contain',
              }}
            />
          </Box>
        ))}
      </Slider>

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          mt: 2,
        }}
      >
        {productImages.map((_, index) => (
          <Box
            key={`${index}dot`}
            sx={{
              width: index === selectedMediaPosition ? 24 : 8,
              height: 8,
              borderRadius: 4,
              backgroundColor:
                index === selectedMediaPosition
                  ? theme.palette.primary.main
                  : theme.palette.grey[300],
              mr: 1,
              transition: 'all 0.3s ease-in-out',
            }}
          />
        ))}
      </Box>
    </Box>
  );
};

export default ProductImages;
