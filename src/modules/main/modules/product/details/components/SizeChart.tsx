import React, {useCallback, useState} from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import CloseIcon from '@mui/icons-material/Close';
import {useTranslation} from 'react-i18next';
import {Document, Page} from 'react-pdf';

interface SizeChartProps {
  sizeChart: string;
  closeSizeChart: () => void;
}

const SizeChart = ({sizeChart, closeSizeChart}: SizeChartProps) => {
  const {t} = useTranslation();
  const [imageLoadFailed, setImageLoadFailed] = useState<boolean>(false);

  const onImageLoadFailed = useCallback(() => {
    setImageLoadFailed(true);
  }, []);

  return (
    <Dialog open onClose={closeSizeChart} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="bodyLarge">{t('Variations.Size Guide')}</Typography>
          <IconButton edge="end" color="inherit" onClick={closeSizeChart}>
            <CloseIcon/>
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent>
        {imageLoadFailed ? (
          <Box display="flex" justifyContent="center" alignItems="center" width="100%">
            <Document file={sizeChart}>
              <Page pageNumber={1}/>
            </Document>
          </Box>
        ) : (
          <img
            src={sizeChart}
            alt="Size Chart"
            style={{width: '100%', objectFit: 'contain'}}
            onError={onImageLoadFailed}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default SizeChart;
