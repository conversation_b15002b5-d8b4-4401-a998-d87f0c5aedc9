import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import Typography from "@mui/material/Typography"
import { useTheme } from "@mui/styles"

const AddToCartButton = ({
  onPress,
  loading,
  disabled,
  label,
}: {
  onPress: () => void
  loading: boolean
  disabled: boolean
  label: string
}) => {
  const theme = useTheme()
  const styles = makeStyles(theme)

  return (
    <Button
      variant="outlined"
      onClick={onPress}
      disabled={disabled}
      sx={styles.addToCartButton}
    >
      {loading ? (
        <CircularProgress size={20} />
      ) : (
        <Typography variant="bodyLarge" style={styles.addToCartLabel}>
          {label}
        </Typography>
      )}
    </Button>
  )
}

const makeStyles = (theme: any) => ({
  addToCartButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 2,
    borderColor: theme.palette.primary.main,
    alignItems: "center",
    justifyContent: "center",
    height: 44,
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.common.white,
    display: "flex",
    textTransform: "none", // Optional: to match RN's default
  },
  addToCartLabel: {
    color: theme.palette.white,
  },
})

export default AddToCartButton
