import FavoriteIcon from "@mui/icons-material/Favorite"
import FavoriteBorderIcon from "@mui/icons-material/FavoriteBorder"
import Box from "@mui/material/Box"
import CircularProgress from "@mui/material/CircularProgress"
import IconButton from "@mui/material/IconButton"
import Typography from "@mui/material/Typography"
import { useTranslation } from "react-i18next"

interface WishlistUIProps {
  wishlistApiInProgress: boolean
  presentInWishlist: boolean
  deleteItemFromWishlist: () => void
  addItemToWishlist: (value: any) => void
  product: any
}

const WishlistUI = ({
  wishlistApiInProgress,
  presentInWishlist,
  deleteItemFromWishlist,
  addItemToWishlist,
  product,
}: WishlistUIProps) => {
  const { t } = useTranslation()

  if (wishlistApiInProgress) {
    return <CircularProgress size={24} color="primary" />
  } else if (presentInWishlist) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center">
        <IconButton onClick={deleteItemFromWishlist} color="error">
          <FavoriteIcon />
        </IconButton>
        <Typography variant="bodyLarge" color="textPrimary">
          {t("WishList.Wishlisted")}
        </Typography>
      </Box>
    )
  } else {
    return (
      <Box display="flex" alignItems="center" justifyContent="center">
        <IconButton onClick={() => addItemToWishlist(product)} color="error">
          <FavoriteBorderIcon />
        </IconButton>
        <Typography variant="bodyLarge" color="textPrimary">
          {t("WishList.Wishlist")}
        </Typography>
      </Box>
    )
  }
}

export default WishlistUI
