import React, {useRef, useState} from 'react';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import CloseIcon from '@mui/icons-material/Close';
import SettingsIcon from '@mui/icons-material/Settings';
import {QrReader} from 'react-qr-reader';
import {useNavigate} from 'react-router-dom';

const SellerQRCode = () => {
  const [hasPermission, setHasPermission] = useState(true); // Assume true, since browsers prompt
  const [modalOpen, setModalOpen] = useState(false);
  const navigatingToSettings = useRef(false);
  const navigate = useNavigate();

  const handleScan = (data: string | null) => {
    if (data) {
      // Simulate deep link handling
      navigate(`/handle-qr?data=${encodeURIComponent(data)}`);
    }
  };

  const handleError = (err: any) => {
    console.error(err);
    setHasPermission(false);
    setModalOpen(true);
  };

  const goToSettings = () => {
    navigatingToSettings.current = true;
    setModalOpen(false);
    window.open('about:preferences#privacy', '_blank'); // Browser settings fallback
  };

  return (
    <Box display="flex" flexDirection="column" alignItems="center" p={2}>
      {hasPermission ? (
        <QrReader
          delay={300}
          onError={handleError}
          onScan={handleScan}
          style={{width: '100%', maxWidth: 400}}
        />
      ) : (
        <Typography color="error">Camera permission denied.</Typography>
      )}

      <Modal open={modalOpen} onClose={() => setModalOpen(false)}>
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: 24,
            p: 4,
            width: 300,
          }}
        >
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <Typography variant="headlineSmall">Camera Permission</Typography>
            <IconButton onClick={() => setModalOpen(false)}>
              <CloseIcon/>
            </IconButton>
          </Box>
          <Typography variant="labelSmall" mt={2}>
            You have denied the camera permission. Please allow it from your browser settings.
          </Typography>
          <Box mt={3} display="flex" justifyContent="space-between" gap={2}>
            <Button variant="outlined" fullWidth onClick={() => navigate(-1)}>
              Cancel
            </Button>
            <Button
              variant="contained"
              fullWidth
              onClick={goToSettings}
              startIcon={<SettingsIcon/>}
            >
              Settings
            </Button>
          </Box>
        </Box>
      </Modal>
    </Box>
  );
};

export default SellerQRCode;
