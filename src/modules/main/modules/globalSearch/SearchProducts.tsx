import Box from "@mui/material/Box"
import Menu from "@mui/material/Menu"
import { makeStyles } from "@mui/styles"
import React, { useEffect, useMemo, useRef, useState } from "react"
import { useNavigate, useSearchParams } from "react-router-dom"

import { emptyAlertCallback } from "../../../../utils/utils"
import SafeAreaPage from "../../components/page/SafeAreaPage"
import ListingPage from "../../components/productsLists/ListingPage"
import useCartItems from "../../hooks/useCartItems"
import SearchHeader from "../dashboard/components/header/SearchHeader"
import SearchSuggestions from "../dashboard/components/header/SearchSuggestions"

type Suggestions = { title: string; data: string[] }

const SearchProducts = () => {
  const productSearchSource = useRef<AbortController | null>(null)
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()

  const tagId = searchParams.get("tag") || ""
  const categoryDomain = searchParams.get("domain") || ""
  const subCategory = searchParams.get("subCategory") || ""
  const serviceability = searchParams.get("serviceability") || ""
  const tts = searchParams.get("tts") || ""
  const tempHideStores =
    searchParams.get("hide_stores") || searchParams.get("hideStores") || false

  const hideStores = tempHideStores === "true"
  const bppId = searchParams.get("bpp_id") || ""
  const styles = useStyles()
  const searchHeaderRef = useRef<any>(null)
  const currentSearchQuery = useRef<string>("")
  const [suggestions, setSuggestions] = useState<Suggestions[]>([])
  const [searchQuery, setSearchQuery] = useState<string | "">(
    searchParams.get("query") ?? ""
  )
  const [getDataOnInitialRender, setGetDataOnInitialRender] =
    React.useState(true)
  const { getCartItems } = useCartItems()

  const getCartList = async () => {
    await getCartItems()
    setGetDataOnInitialRender(false)
  }

  useEffect(() => {
    if (getDataOnInitialRender) {
      getCartList().finally(emptyAlertCallback)
    }
  }, [getDataOnInitialRender])

  const onSearch = (query: string) => {
    setSearchQuery(query)
    currentSearchQuery.current = query
    cancelCurrentApi()
    setSuggestions([])

    const params = new URLSearchParams(searchParams)
    if (query) {
      params.set("query", query)
    }
    navigate(`/search?${params.toString()}`)
  }

  const clearSuggestions = (selectedText: string) => {
    searchHeaderRef?.current.updateQuery(selectedText)
    setSuggestions([])
  }

  const closeMenu = () => {
    setSuggestions([])
  }

  const cancelCurrentApi = () => {
    if (productSearchSource.current) {
      productSearchSource.current.abort()
      productSearchSource.current = null
    }
  }

  const fetchSuggestions = async (searchText: string) => {}

  const modalVisible = useMemo(() => {
    return suggestions.length > 0
  }, [suggestions])

  return (
    <SafeAreaPage>
      <Box className={styles.container}>
        <Menu
          open={modalVisible}
          onClose={closeMenu}
          className={styles.menuStyle}
          anchorEl={null}
        >
          <SearchSuggestions
            suggestions={suggestions}
            onSearch={onSearch}
            clearSuggestions={clearSuggestions}
          />
        </Menu>
        <SearchHeader
          ref={searchHeaderRef}
          onSearch={onSearch}
          defaultQuery={searchParams.get("query") ?? ""}
          fetchSuggestions={fetchSuggestions}
        />
        {(searchQuery.length > 0 || tagId || categoryDomain || subCategory) && (
          <Box className={styles.listContainer}>
            <ListingPage
              searchQuery={searchQuery.replace(/’/g, "'")}
              subCategories={subCategory ?? ""}
              isSearch
              tagId={tagId ?? ""}
              bppId={bppId ?? ""}
              serviceability={serviceability ?? ""}
              tts={tts ?? ""}
              categoryDomain={categoryDomain ?? ""}
              hideStores={hideStores}
            />
          </Box>
        )}
      </Box>
    </SafeAreaPage>
  )
}

const useStyles = makeStyles((theme: any) => ({
  container: {
    display: "flex",
    flexDirection: "column",
    minHeight: "100dvh",
    backgroundColor: "#fff",
  },
  listContainer: {
    overflow: "scroll",
    height: "100dvh",
  },
  menuStyle: {
    width: "calc(100% - 16px)",
    marginTop: 60,
  },
  switchRow: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    margin: "20px 0",
  },
  switchContainer: {
    borderRadius: 24,
    backgroundColor: "#E3F2FD",
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  button: {
    padding: "5px 10px",
    width: 100,
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  activeButton: {
    backgroundColor: "#2196F3",
    borderRadius: 24,
  },
  activeButtonText: {
    color: "#fff",
  },
  buttonText: {
    color: "#9E9E9E",
  },
}))

export default SearchProducts
