import React, {memo, useCallback, useEffect, useRef, useState} from 'react';
import axios from 'axios';
import {useTranslation} from 'react-i18next';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import List from '@mui/material/List';
import {makeStyles} from '@mui/styles';

import useNetworkErrorHandling from '../../../../../hooks/useNetworkErrorHandling';
import {keyExtractor, skeletonList} from '../../../../../utils/utils';
import useNetworkHandling from '../../../../../hooks/useNetworkHandling';
import {API_BASE_URL, GET_ISSUES} from '../../../../../utils/apiActions';
import ComplaintSkeleton from '../components/ComplaintSkeleton';
import ListFooter from '../../order/components/ListFooter';
import Complaint from '../components/Complaint';
import SafeAreaPage from '../../../components/page/SafeAreaPage';
import Header from '../../../components/header/Header';


const CancelToken = axios.CancelToken;

const ListEmptyComponent = () => {
  const {t} = useTranslation();
  return (
    <Typography variant="body1" align="center">
      {t('Profile.No data found')}
    </Typography>
  );
};

const Complaints: React.FC = () => {
  const {t} = useTranslation();
  const styles = useStyles();
  const source = useRef<any>(null);
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();

  const totalComplaints = useRef<number>(0);
  const pageNumber = useRef<number>(1);

  const [complaints, setComplaints] = useState<any[]>([]);
  const [moreListRequested, setMoreListRequested] = useState<boolean>(false);
  const [refreshInProgress, setRefreshInProgress] = useState<boolean>(false);
  const [apiInProgress, setApiInProgress] = useState<boolean>(true);

  const getComplaints = useCallback(async (currentPage: number) => {
    try {
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${GET_ISSUES}?pageNumber=${currentPage}&limit=10`,
        source.current.token
      );
      totalComplaints.current = data.totalCount;
      setComplaints(currentPage === 1 ? data.issues : prev => [...prev, ...data.issues]);
      pageNumber.current = currentPage + 1;
    } catch (error: any) {
      if (error.response?.status === 404 && complaints.length === 0) {
        setComplaints([]);
      } else {
        handleApiError(error);
      }
    }
  }, [getDataWithAuth, handleApiError, complaints.length]);

  const loadMoreList = () => {
    if (totalComplaints.current > complaints.length && !moreListRequested) {
      setMoreListRequested(true);
      getComplaints(pageNumber.current).finally(() => {
        setMoreListRequested(false);
      });
    }
  };

  const onRefreshHandler = () => {
    pageNumber.current = 1;
    setRefreshInProgress(true);
    getComplaints(1).finally(() => {
      setRefreshInProgress(false);
    });
  };

  useEffect(() => {
    pageNumber.current = 1;
    getComplaints(1).finally(() => setApiInProgress(false));
  }, []);

  return (
    <SafeAreaPage>
      <Header label={t('Profile.Complaints')}/>
      <Box className={styles.pageContainer}>
        {apiInProgress ? (
          <List>
            {skeletonList.map((_, index) => (
              <ComplaintSkeleton key={index}/>
            ))}
          </List>
        ) : (
          <List className={styles.listContainer}>
            {complaints.map((item) => (
              <Complaint key={keyExtractor(item)} complaint={item}/>
            ))}
            {complaints.length === 0 && <ListEmptyComponent/>}
            <ListFooter moreRequested={moreListRequested}/>
          </List>
        )}
      </Box>
    </SafeAreaPage>
  );
};

const useStyles = makeStyles((theme: any) => ({
  pageContainer: {
    backgroundColor: theme.palette.neutral50,
    padding: 16,
    overflow: 'scroll',
    height: 'calc(100dvh - 72px)',
  },
  listContainer: {
    padding: '20px 0',
  },
  emptyContainer: {
    textAlign: 'center',
    marginTop: 24,
  },
}));

export default memo(Complaints);
