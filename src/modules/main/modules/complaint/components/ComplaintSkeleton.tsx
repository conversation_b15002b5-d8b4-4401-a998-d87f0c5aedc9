import React from 'react';
import Box from '@mui/material/Box';
import {makeStyles} from '@mui/styles';
import Skeleton from '@mui/material/Skeleton';

/**
 * Component to show skeleton of complaint
 * @returns {JSX.Element}
 */
const ComplaintSkeleton: React.FC = () => {
  const styles = useStyles();

  return (
    <Box className={styles.container}>
      <Skeleton variant="rectangular" className={styles.image}/>
      <Skeleton variant="rectangular" className={styles.details}/>
    </Box>
  );
};

const useStyles = makeStyles<any>(() => ({
  container: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: '0 16px',
  },
  image: {
    width: 52,
    height: 52,
    marginRight: 8,
  },
  details: {
    width: 200,
    height: 100,
  },
}));

export default ComplaintSkeleton;
