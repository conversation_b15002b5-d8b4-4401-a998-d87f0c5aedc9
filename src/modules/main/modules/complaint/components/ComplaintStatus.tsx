import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';

interface ComplaintStatusProps {
  status: string;
}

const ComplaintStatus: React.FC<ComplaintStatusProps> = ({status}) => {
  const styles = useStyles();

  if (status === 'Close') {
    return (
      <Box className={`${styles.statusChip} ${styles.statusCloseChip}`}>
        <Typography variant="labelLarge" className={styles.statusClose}>
          {status}
        </Typography>
      </Box>
    );
  } else {
    return (
      <Box className={`${styles.statusChip} ${styles.statusOpenChip}`}>
        <Typography variant="labelLarge" className={styles.statusOpen}>
          {status}
        </Typography>
      </Box>
    );
  }
};

const useStyles = makeStyles<any>((theme) => ({
  statusChip: {
    borderRadius: 21,
    fontWeight: 'bold',
    padding: '4px 12px !important',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusOpenChip: {
    backgroundColor: theme.palette.error50,
    width: 43
  },
  statusCloseChip: {
    backgroundColor: theme.palette.success50,
  },
  statusOpen: {
    color: theme.palette.error600,
  },
  statusClose: {
    color: theme.palette.success600,
  },
}));

export default ComplaintStatus;
