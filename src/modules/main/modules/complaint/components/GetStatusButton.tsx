import React, {useEffect, useRef, useState} from 'react';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';
import {makeStyles} from '@mui/styles';
import {useSelector} from 'react-redux';
import axios from 'axios';
import {useTranslation} from 'react-i18next';

import useNetworkHandling from '../../../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../../../hooks/useNetworkErrorHandling';
import {API_BASE_URL, ISSUE, ISSUE_STATUS, ON_ISSUE_STATUS,} from '../../../../../utils/apiActions';
import {emptyAlertCallback, errorCallback,} from '../../../../../utils/utils';
import {SSE_TIMEOUT} from '../../../../../utils/constants';
import {EventSourcePolyfill} from 'event-source-polyfill';
import {useToast} from "../../../../../hooks/toastProvider";

const CancelToken = axios.CancelToken;

interface GetStatusButtonProps {
  transactionId: string;
  bppId: string;
  issueId: string;
  domain: string;
  complainantActions: any;
  mergeIssueActions: (value: any) => void;
}

const GetStatusButton: React.FC<GetStatusButtonProps> = ({
                                                           transactionId,
                                                           bppId,
                                                           issueId,
                                                           domain,
                                                           complainantActions,
                                                           mergeIssueActions,
                                                         }) => {
  const {showToast} = useToast();
  const {t} = useTranslation();
  const {token} = useSelector(({auth}: any) => auth);
  const styles = useStyles();
  const source = useRef<any>(null);
  const {getDataWithAuth, postDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const cancelPartialEventSourceResponseRef = useRef<any[]>([]);
  const eventTimeOutRef = useRef<any>(null);
  const responseRef = useRef<any[]>([]);
  const [statusLoading, setStatusLoading] = useState<boolean>(false);

  const clearTimer = () => {
    if (eventTimeOutRef.current) {
      eventTimeOutRef.current?.eventSource?.close();
      clearTimeout(eventTimeOutRef.current?.timer);
      eventTimeOutRef.current = null;
    }
  };

  const getIssueStatusDetails = async (messageId: string) => {
    try {
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${ON_ISSUE_STATUS}${messageId}`,
        source.current.token
      );
      cancelPartialEventSourceResponseRef.current = [
        ...cancelPartialEventSourceResponseRef.current,
        data,
      ];
      setStatusLoading(false);
      if (data?.message) {
        mergeIssueActions({
          respondent_actions: data.message.issue?.issue_actions.respondent_actions,
          complainant_actions: complainantActions,
        });
        showToast(
          t('Complaint Details.Complaint status updated successfully'), 'info'
        );
      } else {
        showToast(
          t('Complaint Details.Something went wrong, issue status cannot be fetched'), 'error'
        );
      }
    } catch (err: any) {
      setStatusLoading(false);
      showToast(err?.message, 'error');
    } finally {
      clearTimer();
    }
  };

  const fetchIssueStatusThroughEvents = (messageId: string) => {
    const eventSource = new EventSourcePolyfill(`${API_BASE_URL}${ISSUE}${messageId}`, {
      headers: {Authorization: `Bearer ${token}`},
    });

    eventSource.addEventListener('on_issue_status', (event: any) => {
      const data = JSON.parse(event.data);
      getIssueStatusDetails(data.messageId)
        .then(emptyAlertCallback)
        .catch(errorCallback);
    });

    const timer = setTimeout(() => {
      clearTimer();
      if (responseRef.current.length <= 0) {
        setStatusLoading(false);
        return;
      }
    }, SSE_TIMEOUT);

    eventTimeOutRef.current = {
      eventSource,
      timer,
    };
  };

  const checkIssueStatus = async () => {
    cancelPartialEventSourceResponseRef.current = [];
    setStatusLoading(true);
    try {
      source.current = CancelToken.source();
      const {data} = await postDataWithAuth(
        `${API_BASE_URL}${ISSUE_STATUS}`,
        {
          context: {
            transaction_id: transactionId,
            bpp_id: bppId,
            domain,
          },
          message: {
            issue_id: issueId,
          },
        },
        source.current.token
      );
      // Error handling workflow e.g., NACK
      if (data.message && data.message.ack.status === 'NACK') {
        setStatusLoading(false);
        showToast('Something went wrong', 'error');
      } else {
        fetchIssueStatusThroughEvents(data.context?.message_id);
      }
    } catch (err: any) {
      setStatusLoading(false);
      showToast(err?.message, 'error');
      handleApiError(err);
    }
  };

  useEffect(() => {
    return () => {
      clearTimer();
    };
  }, []);

  return (
    <Button
      disabled={statusLoading}
      className={`${styles.actionButton} ${statusLoading ? styles.disabledButton : ''}`}
      onClick={checkIssueStatus}
    >
      {statusLoading ? (
        <CircularProgress size={16} color="primary"/>
      ) : (
        <Typography variant='body2' className={styles.actionButtonLabel}>
          {t('Profile.Get Status')}
        </Typography>
      )}
    </Button>
  );
};

const useStyles = makeStyles(() => ({
  actionButton: {
    borderRadius: 8,
    border: '1px solid',
    padding: '6px 12px',
    minWidth: 100,
  },
  disabledButton: {
    opacity: 0.6,
    cursor: 'not-allowed',
  },
  actionButtonLabel: {
    color: 'inherit',
  },
}));

export default GetStatusButton;
