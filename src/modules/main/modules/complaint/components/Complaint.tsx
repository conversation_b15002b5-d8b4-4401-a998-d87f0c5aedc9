import React, {useMemo} from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {useDispatch} from 'react-redux';
import moment from 'moment';
import {useTranslation} from 'react-i18next';
import {useNavigate} from 'react-router-dom';
import ComplaintStatus from './ComplaintStatus';
import {updateComplaint} from '../../../../../toolkit/reducer/complaint';
import {issueCategories} from '../../../../../utils/issueTypes';
import {makeStyles} from '@mui/styles';

interface ComplaintProps {
  complaint: any;
}

const Complaint: React.FC<ComplaintProps> = ({complaint}) => {
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const styles = useStyles();
  const {issue_status, order_details} = complaint;
  const address = order_details?.fulfillments[0]?.end?.location?.address;

  const navigateToDetails = () => {
    dispatch(updateComplaint(complaint));
    navigate('/complaint-details');
  };

  const categoryName = useMemo(() => {
    return (
      issueCategories.find(one => one.enums === complaint?.sub_category)
        ?.value ?? 'NA'
    );
  }, [complaint]);

  return (
    <Box className={styles.container}>
      <img
        src={order_details?.items[0]?.product?.descriptor?.symbol}
        alt="Product"
        className={styles.image}
      />
      <Box className={styles.details}>
        <Box className={styles.header}>
          <Typography variant="bodyLarge" component="div" className={styles.title} noWrap>
            {order_details?.items[0]?.product?.descriptor?.name}
          </Typography>
          <Box className={styles.status}>
            <ComplaintStatus status={issue_status}/>
          </Box>
        </Box>
        <Typography variant="labelSmall" className={styles.address}>
          {address?.building}, {address?.locality}, {address?.city}, {address?.state}, {address?.country} - {address?.area_code}
        </Typography>
        <Box className={styles.row}>
          <Typography variant="caption" className={styles.label}>
            {complaint?.category}:
          </Typography>
          <Typography variant="labelSmall" className={styles.value}>
            {categoryName}
          </Typography>
        </Box>
        <Box className={styles.row}>
          <Typography variant="caption" className={styles.label}>
            {t('Complaint.Issue Id')}:
          </Typography>
          <Typography variant="labelSmall" className={styles.value}>
            {complaint?.issueId}
          </Typography>
        </Box>
        <Box className={styles.row}>
          <Typography variant="caption" className={styles.label}>
            {t('Complaint.Issue Raised On')}:
          </Typography>
          <Typography variant="labelSmall" className={styles.value}>
            {moment(complaint?.created_at).locale('en').format('DD MMM YYYY hh:mma')}
          </Typography>
        </Box>
        <Box className={styles.action}>
          <Button variant="outlined" className={styles.summaryButton} onClick={navigateToDetails}>
            {t('Complaint.View Summary')}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    display: 'flex',
    alignItems: 'flex-start',
    padding: 16,
    borderRadius: 8,
    border: '1px solid #ddd',
    backgroundColor: '#fff',
    marginBottom: 16,
  },
  image: {
    width: 52,
    height: 52,
    borderRadius: 12,
    marginRight: 12,
  },
  details: {
    flex: 1,
    overflow: 'hidden',
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    flex: 1,
    color: '#555',
    fontWeight: 'bold',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  },
  address: {
    color: '#888',
    fontWeight: 'bold'
  },
  row: {
    display: 'flex',
    marginTop: 8,
    alignItems: 'center',
    gap: 4,
    flexWrap: 'wrap',
  },
  label: {
    color: '#777',
  },
  value: {
    color: '#555',
    flexGrow: 1,
    flexShrink: 1,
    fontWeight: 'bold !important'
  },
  action: {
    display: 'flex',
    justifyContent: 'flex-end',
    marginTop: 12,
  },
  status: {
    width: 66,
  },
  summaryButton: {
    color: theme.palette.primary.main + '!important',
    borderRadius: '8px !important'
  }
}));

export default Complaint;
