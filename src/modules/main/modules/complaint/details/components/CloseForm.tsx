import ClearIcon from "@mui/icons-material/Clear"
import ThumbDownOutlinedIcon from "@mui/icons-material/ThumbDownOutlined"
import ThumbUpOutlinedIcon from "@mui/icons-material/ThumbUpOutlined"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import IconButton from "@mui/material/IconButton"
import { Divider } from "@mui/material/node"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import axios from "axios"
import { useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useSelector } from "react-redux"
import { useToast } from "../../../../../../hooks/toastProvider"
import useNetworkHandling from "../../../../../../hooks/useNetworkHandling"
import { API_BASE_URL, RAISE_ISSUE } from "../../../../../../utils/apiActions"

const CancelToken = axios.CancelToken
const CloseForm = ({
  hideModal,
  onSuccess,
}: {
  hideModal: () => void
  onSuccess: (data: any[]) => void
}) => {
  const { showToast } = useToast()
  const { t } = useTranslation()
  const source = useRef<any>(null)
  const [rating, setRating] = useState<string>("")
  const [apiInProgress, setApiInProgress] = useState<boolean>(false)
  const { complaintDetails } = useSelector((state: any) => state.complaint)
  const { postDataWithAuth } = useNetworkHandling()
  const styles = useStyles()

  const escalateComplaint = async () => {
    try {
      setApiInProgress(true)
      const formData = {
        context: {
          action: "issue",
          domain: complaintDetails?.domain,
          timestamp: new Date(),
          transaction_id: complaintDetails?.transaction_id,
        },
        message: {
          issue: {
            id: complaintDetails.issue_id,
            status: "CLOSED",
            rating: rating,
            updated_at: new Date(),
            created_at: complaintDetails.created_at,
            issue_actions: {
              complainant_actions: [
                ...complaintDetails.issue_actions.complainant_actions,
                {
                  complainant_action: "CLOSE",
                  short_desc: "Complaint closed",
                  updated_at: new Date(),
                  updated_by:
                    complaintDetails.issue_actions.complainant_actions[0]
                      .updated_by,
                },
              ],
            },
          },
        },
      }

      source.current = CancelToken.source()
      const { data } = await postDataWithAuth(
        `${API_BASE_URL}${RAISE_ISSUE}`,
        formData,
        source.current.token
      )
      //Error handling workflow eg, NACK
      if (data.message && data.message.ack.status === "NACK") {
        showToast(
          t("Global.Something went wrong, please try again after some time"),
          "error"
        )
        setApiInProgress(false)
      } else {
        onSuccess([
          {
            respondent_action: "CLOSE",
            short_desc: "Complaint closed",
            updated_at: new Date(),
            updated_by:
              complaintDetails.issue_actions.complainant_actions[0].updated_by,
          },
        ])
        hideModal()
      }
    } catch (err: any) {
      showToast(err?.message, "error")
      setApiInProgress(false)
    } finally {
      setApiInProgress(false)
    }
  }

  return (
    <Box className={styles.modal}>
      <Box className={styles.modalHeader}>
        <Typography variant="titleLarge" className={styles.title}>
          {t("Close Form.Close")}
        </Typography>
        <IconButton sx={{ padding: 0 }} onClick={hideModal}>
          <ClearIcon />
        </IconButton>
      </Box>
      <Divider />
      <Box className={styles.modalContainer}>
        <Typography variant="body1" className={styles.message}>
          {t("Close Form.Choose Rating")}*
        </Typography>
        <Box className={styles.ratingContainer}>
          <Box
            className={`${styles.ratingButton} ${
              rating === "THUMBS-UP" ? styles.selectedRating : ""
            }`}
            onClick={() => setRating("THUMBS-UP")}
          >
            <ThumbUpOutlinedIcon />
          </Box>
          <Box
            className={`${styles.ratingButton} ${
              rating === "THUMBS-DOWN" ? styles.selectedRating : ""
            }`}
            onClick={() => setRating("THUMBS-DOWN")}
          >
            <ThumbDownOutlinedIcon />
          </Box>
        </Box>
        <Box className={styles.buttonContainer}>
          <Button
            variant="outlined"
            onClick={hideModal}
            className={styles.button}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={escalateComplaint}
            className={styles.button}
            disabled={rating.length === 0 || apiInProgress}
          >
            {apiInProgress ? <CircularProgress size={20} /> : "Confirm"}
          </Button>
        </Box>
      </Box>
    </Box>
  )
}

const useStyles = makeStyles((theme: any) => ({
  modal: {
    backgroundColor: theme.palette.white,
    borderRadius: 16,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.palette.neutral100,
    display: "flex",
    margin: "10px 20px",
  },
  title: {
    color: theme.palette.neutral400,
    flex: 1,
  },
  modalContainer: {
    padding: 16,
  },
  message: {
    color: theme.palette.neutral400,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
    display: "flex",
  },
  ratingButton: {
    width: 44,
    height: 44,
    marginRight: 12,

    borderRadius: 8,
    border: `1px solid ${theme.palette.neutral200}`,
    alignItems: "center",
    justifyContent: "center",
    display: "flex",
  },
  selectedRating: {
    backgroundColor: theme.palette.primary.main,
  },
  buttonContainer: {
    marginTop: 28,
    flexDirection: "row",
    alignItems: "center",
    display: "flex",
    gap: 15,
  },
  button: {
    flex: 1,
    borderRadius: 8,
    borderColor: theme.palette.primary,
  },
  buttonContent: {
    height: 44,
  },
}))

export default CloseForm
