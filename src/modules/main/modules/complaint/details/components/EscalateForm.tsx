import React, {useEffect, useRef, useState} from 'react';
import {useSelector} from 'react-redux';
import axios from 'axios';
import {useTranslation} from 'react-i18next';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import CloseIcon from '@mui/icons-material/Close';
import {makeStyles} from '@mui/styles';

import {API_BASE_URL, ISSUE, ON_ISSUE, RAISE_ISSUE,} from '../../../../../../utils/apiActions';
import useNetworkHandling from '../../../../../../hooks/useNetworkHandling';
import {emptyAlertCallback, errorCallback,} from '../../../../../../utils/utils';
import {SSE_TIMEOUT} from '../../../../../../utils/constants';
import InputField from '../../../../components/input/InputField';
import {useToast} from "../../../../../../hooks/toastProvider";
import { EventSourcePolyfill } from 'event-source-polyfill';

const CancelToken = axios.CancelToken;
const EscalateForm = ({
                        hideEscalateModalVisible,
                        onSuccess,
                      }: {
  hideEscalateModalVisible: () => void;
  onSuccess: (data: any[]) => void;
}) => {
  const {showToast} = useToast();
  const {t} = useTranslation();
  const source = useRef<any>(null);
  const eventTimeOutRef = useRef<any>(null);
  const responseRef = useRef<any[]>([]);
  const [remarks, setRemarks] = useState<string>('');
  const [apiInProgress, setApiInProgress] = useState<boolean>(false);
  const {complaintDetails} = useSelector((state: any) => state.complaint);
  const {token} = useSelector((state: any) => state.auth);
  const {getDataWithAuth, postDataWithAuth} = useNetworkHandling();
  const styles = useStyles();

  const clearTimer = () => {
    if (eventTimeOutRef.current) {
      eventTimeOutRef.current.eventSource.close();
      clearTimeout(eventTimeOutRef.current.timer);
      eventTimeOutRef.current = null;
    }
  };

  // on Issue api
  const getPartialCancelOrderDetails = async (
    message_id: any,
    issueActions: any,
  ) => {
    try {
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${ON_ISSUE}${message_id}`,
        source.current.token,
      );
      let successData = [
        {
          respondent_action: 'ESCALATE',
          short_desc: remarks,
          updated_at: new Date(),
          updated_by: issueActions.complainant_actions[0].updated_by,
        },
      ];
      responseRef.current = [...responseRef.current, data];
      if (data?.message) {
        let respondentArray =
          data.message?.issue?.issue_actions?.respondent_actions;
        let processObj = respondentArray[respondentArray.length - 1];
        onSuccess([...successData, processObj]);
      } else {
        onSuccess(successData);
      }
    } catch (err: any) {
      showToast(err?.message, 'error');
      clearTimer();
    } finally {
      setApiInProgress(false);
    }
  };

  const onEscalateIssue = (messageId: any, issueActions: any) => {
    const eventSource = new EventSourcePolyfill(
      `${API_BASE_URL}${ISSUE}${messageId}`, {
        headers: {Authorization: `Bearer ${token}`},
      }
    );

    eventSource.addEventListener('on_issue', (event: any) => {
      const data = JSON.parse(event.data);
      getPartialCancelOrderDetails(data.messageId, issueActions)
        .then(emptyAlertCallback)
        .catch(errorCallback);
    });

    const timer = setTimeout(() => {
      clearTimer();
      if (responseRef.current.length <= 0) {
        setApiInProgress(false);
        showToast(
          t('Global.Unable to fetch details. Please try again'), 'error'
        );
        return;
      }
    }, SSE_TIMEOUT);

    eventTimeOutRef.current = {
      eventSource,
      timer,
    };
  };

  const escalateComplaint = async () => {
    try {
      if (complaintDetails) {
        setApiInProgress(true);
        const formData = {
          context: {
            action: 'issue',
            timestamp: new Date(),
            transaction_id: complaintDetails.transaction_id,
            domain: complaintDetails.domain,
          },
          message: {
            issue: {
              id: complaintDetails.issueId,
              status: 'OPEN',
              issue_type: 'GRIEVANCE',
              updated_at: new Date(),
              created_at: complaintDetails.created_at,
              issue_actions: {
                complainant_actions: [
                  ...complaintDetails.issue_actions.complainant_actions,
                  {
                    complainant_action: 'ESCALATE',
                    short_desc: remarks,
                    updated_at: new Date(),
                    updated_by:
                    complaintDetails.issue_actions.complainant_actions[0]
                      .updated_by,
                  },
                ],
              },
            },
          },
        };

        source.current = CancelToken.source();
        const {data} = await postDataWithAuth(
          `${API_BASE_URL}${RAISE_ISSUE}`,
          formData,
          source.current.token,
        );
        //Error handling workflow eg, NACK
        if (data.message && data.message.ack.status === 'NACK') {
          showToast(
            t('Global.Something went wrong, please try again after some time'), 'error'
          );
          setApiInProgress(false);
        } else {
          onEscalateIssue(
            data.context?.message_id,
            complaintDetails.issue_actions,
          );
        }
      }
    } catch (err: any) {
      showToast(err?.message, 'error');
      setApiInProgress(false);
    }
  };

  useEffect(() => {
    return () => clearTimer();
  }, []);

  const confirmDisabled = remarks.length === 0 || apiInProgress;

  return (
    <Box className={styles.modal}>
      <Box className={styles.modalHeader}>
        <Typography variant="titleLarge" className={styles.title}>
          {t('Escalate Form.Escalate')}
        </Typography>
        <IconButton disabled={apiInProgress} onClick={hideEscalateModalVisible}>
          <CloseIcon style={{color: '#999'}}/>
        </IconButton>
      </Box>
      <Box className={styles.modalContainer}>
        <InputField
          inputLabel="Remarks"
          dense
          required
          value={remarks}
          mode="outlined"
          placeholder="Enter the remarks"
          onChange={(e: any) => {
            setRemarks(e?.target?.value)
          }}
        />
        <Box className={styles.buttonContainer}>
          <Button
            variant="outlined"
            className={`${styles.button} ${styles.cancelButton} ${apiInProgress ? styles.disabledCancelButton : ''}`}
            disabled={apiInProgress}
            onClick={hideEscalateModalVisible}
          >
            <Typography variant="bodyLarge" className={styles.cancelButtonLabel}>
              {t('Profile.Cancel')}
            </Typography>
          </Button>
          <Button
            variant="contained"
            className={`${styles.button} ${styles.confirmButton} ${confirmDisabled ? styles.disabledConfirmButton : ''
            }`}
            onClick={escalateComplaint}
            disabled={confirmDisabled}
          >
            {apiInProgress ? (
              <CircularProgress size={18} color="inherit"/>
            ) : (
              t('Return Items.Confirm')
            )}
          </Button>
        </Box>
      </Box>
    </Box>
  );

};

const useStyles = makeStyles((theme: any) => ({
  modal: {
    backgroundColor: theme.palette.white,
    borderRadius: 16,
    margin: 20,
  },
  modalHeader: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '16px 14px',
    borderBottom: '1px solid' + theme.palette.neutral100,
  },
  title: {
    color: theme.palette.neutral400,
  },
  modalContainer: {
    padding: 16,
  },
  buttonContainer: {
    display: 'flex',
    marginTop: 28,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  cancelButton: {
    borderWidth: 1,
  },
  confirmButton: {
    backgroundColor: theme.palette.primary,
  },
  confirmButtonLabel: {
    color: theme.palette.white,
  },
  cancelButtonLabel: {
    color: theme.palette.primary,
  },
  button: {
    display: 'flex',
    flex: 1,
    borderRadius: 8,
    borderColor: theme.palette.primary,
    paddingVertical: 12,
    alignItems: 'center',
    minHeight: 36,
  },
  disabledCancelButton: {
    borderColor: theme.palette.surfaceDisabled,
  },
  disabledConfirmButton: {
    borderColor: theme.palette.surfaceDisabled,
    backgroundColor: theme.palette.surfaceDisabled,
  },
}));

export default EscalateForm;
