import ExpandMoreIcon from "@mui/icons-material/ExpandMore"
import Accordion from "@mui/material/Accordion"
import AccordionDetails from "@mui/material/AccordionDetails"
import AccordionSummary from "@mui/material/AccordionSummary"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import Dialog from "@mui/material/Dialog"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import moment from "moment"
import { useCallback, useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { updateComplaint } from "../../../../../toolkit/reducer/complaint"
import { CURRENCY_SYMBOLS } from "../../../../../utils/constants"
import { ISSUE_TYPES } from "../../../../../utils/issueTypes"
import Header from "../../../components/header/Header"
import SafeAreaPage from "../../../components/page/SafeAreaPage"
import useFormatDate from "../../../hooks/useFormatDate"
import useFormatNumber from "../../../hooks/useFormatNumber"
import ComplaintStatus from "../components/ComplaintStatus"
import GetStatusButton from "../components/GetStatusButton"
import CloseForm from "./components/CloseForm"
import EscalateForm from "./components/EscalateForm"

const categories = ISSUE_TYPES.map((item) => {
  return item.subCategory.map((subcategoryItem) => {
    return {
      ...subcategoryItem,
      category: item.value,
      label: subcategoryItem.value,
    }
  })
}).flat()

const ComplaintDetails = () => {
  const { formatNumber } = useFormatNumber()
  const { formatDate } = useFormatDate()
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const styles = useStyles()
  const { complaintDetails } = useSelector((state: any) => state.complaint)
  const [actions, setActions] = useState<any[]>([])
  const [escalateModalVisible, setEscalateModalVisible] =
    useState<boolean>(false)
  const [takeAction, setTakeAction] = useState<boolean>(false)
  const [closeModalVisible, setCloseModalVisible] = useState<boolean>(false)

  const hideEscalateModalVisible = useCallback(
    () => setEscalateModalVisible(false),
    []
  )
  const showEscalateModalVisible = useCallback(
    () => setEscalateModalVisible(true),
    []
  )

  const hideCloseModal = useCallback(() => setCloseModalVisible(false), [])
  const showCloseModal = useCallback(() => setCloseModalVisible(true), [])

  const escalationSuccess = (list: any) => {
    setActions([...actions, ...list])
    hideEscalateModalVisible()
  }

  const closeSuccess = (list: any) => {
    setActions([...actions, ...list])
    dispatch(
      updateComplaint(
        Object.assign({}, complaintDetails, {
          issue_status: "Close",
        })
      )
    )
  }

  const mergeIssueActions = (list: any) => {
    let resActions = list.respondent_actions,
      comActions = list.complainant_actions.map((item: any) => {
        return { ...item, respondent_action: item.complainant_action }
      }),
      mergedActions = [...comActions, ...resActions]

    mergedActions.sort(
      (a, b) =>
        new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime()
    )
    let showTakeAction: boolean
    const lastAction =
      mergedActions[mergedActions.length - 1]?.respondent_action
    if (
      lastAction === "PROCESSING" ||
      lastAction === "OPEN" ||
      lastAction === "ESCALATE"
    ) {
      showTakeAction = false
    } else {
      showTakeAction = lastAction === "RESOLVED"
    }

    setTakeAction(showTakeAction)
    setActions(mergedActions)
  }

  useEffect(() => {
    if (complaintDetails) {
      mergeIssueActions(complaintDetails?.issue_actions)
    }
  }, [complaintDetails])

  return (
    <SafeAreaPage>
      <Header label={t("Complaint Details.Complaint Details")} />
      <Box className={styles.container}>
        <Box className={styles.pageContainer}>
          <Box className={styles.accordionContainer}>
            <Accordion className={styles.accordion} sx={{ boxShadow: "none" }}>
              <AccordionSummary
                className={styles.accordionSummary}
                expandIcon={<ExpandMoreIcon />}
              >
                <Typography
                  variant="headlineSmall"
                  className={styles.accordionTitle}
                >
                  {t("Complaint Details.Complaint Details")}
                </Typography>
              </AccordionSummary>
              <AccordionDetails className={styles.accordionDetails}>
                {actions.map((action: any, actionIndex: number) => (
                  <Box
                    className={styles.process}
                    key={action?.respondent_action}
                  >
                    <Box className={styles.dotContainer}>
                      <Box className={styles.dot}>
                        <Box className={styles.innerDot} />
                      </Box>
                      {actionIndex !== actions.length - 1 && (
                        <Box className={styles.dottedLine} />
                      )}
                    </Box>
                    <Box className={styles.processDetails}>
                      <Box className={styles.processHeader}>
                        <Typography
                          variant="body1"
                          className={styles.actionTitle}
                        >
                          {action?.respondent_action} (Issue)
                        </Typography>
                        <Typography
                          variant="labelSmall"
                          className={styles.date}
                        >
                          {formatDate(
                            moment(action?.updated_at),
                            "DD MMM YYYY hh:mma"
                          )}
                        </Typography>
                      </Box>
                      <Typography
                        variant="body1"
                        className={styles.shortDescription}
                      >
                        {action?.short_desc}
                      </Typography>
                      {!!action?.updated_by && (
                        <Typography
                          variant="labelSmall"
                          className={styles.updateByText}
                        >
                          {t("Complaint Details.Updated by")}:{" "}
                          {action?.updated_by?.person?.name}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                ))}
              </AccordionDetails>
            </Accordion>
          </Box>
          <Box className={styles.card}>
            <Box className={styles.orderIdRow}>
              <Box className={styles.orderId}>
                <Typography variant="bodySmall" className={styles.textLabel}>
                  {t("Complaint Details.Issue Id")}:{" "}
                </Typography>
                <Typography variant="bodyLarge" className={styles.text}>
                  {complaintDetails?.issueId}
                </Typography>
              </Box>
              <ComplaintStatus status={complaintDetails?.issue_status} />
            </Box>
            <Box className={styles.row}>
              <Typography variant="bodySmall" className={styles.textLabel}>
                {t("Complaint Details.Level")}:{" "}
              </Typography>
              <Typography variant="bodyLarge" className={styles.text}>
                {t("Complaint Details.Issue")}
              </Typography>
            </Box>
            <Box className={styles.row}>
              <Typography variant="bodySmall" className={styles.textLabel}>
                {t("Complaint Details.Order Id")}:{" "}
              </Typography>
              <Typography variant="bodyLarge" className={styles.text}>
                {complaintDetails?.order_details?.id}
              </Typography>
            </Box>
            <Box className={styles.row}>
              <Typography variant="labelLarge" className={styles.issueRaisedOn}>
                {t("Complaint Details.Issue Raised On")}:{" "}
                {formatDate(
                  moment(complaintDetails?.created_at),
                  "DD MMM YYYY hh:mma"
                )}{" "}
                | Fulfillment :{" "}
                {categories.find(
                  (one) => one.enums === complaintDetails?.sub_category
                )?.value ?? "NA"}
              </Typography>
            </Box>
            {complaintDetails?.order_details?.items?.map((item: any) => (
              <Box
                key={`${item.id}${item.fulfillment_id}`}
                className={styles.itemRow}
              >
                <Typography variant="bodyLarge" className={styles.itemTitle}>
                  {item?.product?.descriptor?.name}
                </Typography>
                <Box className={styles.itemContainer}>
                  <Typography variant="bodySmall" className={styles.qty}>
                    {t("Complaint Details.QTY")}: {item?.quantity?.count} X{" "}
                    {CURRENCY_SYMBOLS[item?.product?.price?.currency]}
                    {formatNumber(item?.product?.price?.value)}
                  </Typography>
                  <Typography
                    variant="bodyLarge"
                    className={styles.itemQuantity}
                  >
                    {CURRENCY_SYMBOLS[item?.product?.price?.currency]}
                    {formatNumber(
                      item?.quantity?.count * item?.product?.price?.value
                    )}
                  </Typography>
                </Box>
              </Box>
            ))}
            <Box className={styles.bottomItemRow}>
              <Typography variant="bodyLarge" className={styles.itemTitle}>
                {complaintDetails?.description?.short_desc}
              </Typography>
              <Typography
                variant="bodySmall"
                className={styles.itemDescription}
              >
                {complaintDetails?.description?.long_desc}
              </Typography>
            </Box>
            <Box className={styles.bottomItemRow}>
              <Typography variant="bodyLarge" className={styles.itemTitle}>
                {t("Complaint Details.Expected Response Time")}
              </Typography>
              <Typography
                variant="bodySmall"
                className={styles.itemDescription}
              >
                {formatDate(
                  moment(complaintDetails?.created_at).add(
                    moment.duration("PT1H").asMilliseconds(),
                    "milliseconds"
                  ),
                  "hh:mm a, MMMM Do, YYYY"
                )}
              </Typography>
            </Box>
            <Box className={styles.bottomItemRow}>
              <Typography variant="bodyLarge" className={styles.itemTitle}>
                {t("Complaint Details.Expected Resolution Time")}
              </Typography>
              <Typography
                variant="bodySmall"
                className={styles.itemDescription}
              >
                {formatDate(
                  moment(complaintDetails?.created_at).add(
                    moment.duration("P1D").asMilliseconds(),
                    "milliseconds"
                  ),
                  "hh:mm a, MMMM Do, YYYY"
                )}
              </Typography>
            </Box>
            <Box className={styles.actionButtonContainer}>
              {!takeAction ? (
                <GetStatusButton
                  mergeIssueActions={mergeIssueActions}
                  complainantActions={
                    complaintDetails?.issue_actions?.complainant_actions
                  }
                  transactionId={complaintDetails?.transaction_id}
                  bppId={complaintDetails?.bppId}
                  issueId={complaintDetails?.issueId}
                  domain={complaintDetails?.domain}
                />
              ) : (
                <Box className={styles.actionButtonContainer}>
                  <Button
                    className={styles.actionButton}
                    onClick={showEscalateModalVisible}
                  >
                    <Typography
                      variant="bodyLarge"
                      className={styles.actionButtonLabel}
                    >
                      {t("Complaint Details.Escalate")}
                    </Typography>
                  </Button>

                  {complaintDetails?.issue_status !== "Close" && (
                    <Button
                      onClick={showCloseModal}
                      className={styles.cancelButton}
                    >
                      <Typography
                        variant="bodyLarge"
                        className={styles.cancelButtonLabel}
                      >
                        {t("Complaint Details.Close")}
                      </Typography>
                    </Button>
                  )}
                </Box>
              )}
            </Box>
          </Box>
          <Box className={styles.card}>
            <Box className={styles.titleBox}>
              <Typography variant="headlineSmall" className={styles.title}>
                {t("Complaint Details.Respondent Details")}
              </Typography>
            </Box>
            <Box className={styles.bottomItemRow}>
              <Typography variant="bodyLarge" className={styles.itemTitle}>
                {t("Complaint Details.Phone")}
              </Typography>
              <Typography
                variant="bodySmall"
                className={styles.itemDescription}
              >
                {complaintDetails?.issue_actions?.respondent_actions[
                  complaintDetails?.issue_actions.respondent_actions.length - 1
                ]?.updated_by?.contact?.phone ?? "N/A"}
              </Typography>
            </Box>
            <Box className={styles.bottomItemRow}>
              <Typography variant="bodyLarge" className={styles.itemTitle}>
                {t("Complaint Details.Email")}
              </Typography>
              <Typography
                variant="bodySmall"
                className={styles.itemDescription}
              >
                {complaintDetails?.issue_actions?.respondent_actions[
                  complaintDetails?.issue_actions.respondent_actions.length - 1
                ]?.updated_by?.contact?.email ?? "N/A"}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
      <Dialog
        open={escalateModalVisible}
        onClose={hideEscalateModalVisible}
        fullWidth
        maxWidth="sm"
      >
        <EscalateForm
          hideEscalateModalVisible={hideEscalateModalVisible}
          onSuccess={escalationSuccess}
        />
      </Dialog>
      <Dialog
        open={closeModalVisible}
        onClose={hideCloseModal}
        fullWidth
        maxWidth="sm"
      >
        <CloseForm hideModal={hideCloseModal} onSuccess={closeSuccess} />
      </Dialog>
    </SafeAreaPage>
  )
}

const useStyles = makeStyles((theme: any) => ({
  container: {
    flex: 1,
    backgroundColor: theme.palette.neutral50,
    overflow: "scroll",
    height: "calc(100dvh - 54px)",
  },
  pageContainer: {
    padding: 16,
  },
  card: {
    border: "1px solid" + theme.palette.neutral100,
    borderRadius: 12,
    padding: 16,
    backgroundColor: theme.palette.white,
    marginBottom: 12,
  },
  accordionContainer: {
    marginBottom: 12,
    boxShadow: "none",
  },
  accordion: {
    borderRadius: 12,
    backgroundColor: theme.palette.white,
  },
  accordionTitle: {
    color: theme.palette.neutral400,
  },
  accordionDetails: {
    padding: 16,
    backgroundColor: theme.palette.white,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  accordionSummary: {
    border: "1px solid" + theme.palette.neutral100,
    borderRadius: 8,
  },
  titleBox: {
    marginBottom: 16,
  },
  title: {
    fontWeight: "bold !important",
  },
  process: {
    display: "flex",
    flexDirection: "row",
  },
  dot: {
    display: "flex",
    width: 20,
    height: 20,
    borderRadius: 20,
    backgroundColor: theme.palette.primary.main,
    alignItems: "center",
    justifyContent: "center",
  },
  innerDot: {
    width: 10,
    height: 10,
    borderRadius: 20,
    backgroundColor: theme.palette.white,
  },
  dotContainer: {
    marginRight: 8,
  },
  dottedLine: {
    borderLeft: "1px solid" + theme.palette.primary.main,
    height: 60,
    marginLeft: 9,
  },
  processDetails: {
    flex: 1,
  },
  processHeader: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  actionTitle: {
    color: theme.palette.neutral400,
    fontWeight: "bold",
  },
  date: {
    color: theme.palette.neutral300,
  },
  shortDescription: {
    marginBottom: 4,
    color: theme.palette.neutral400,
    fontWeight: "bold",
  },
  orderIdRow: {
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  orderId: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    flexWrap: "wrap",
    flex: 1,
    marginRight: 16,
    gap: 5,
  },
  text: {
    color: theme.palette.neutral400,
    fontWeight: "bold",
  },
  textLabel: {
    color: theme.palette.neutral400,
  },
  issueRaisedOn: {
    color: theme.palette.neutral300,
  },
  row: {
    display: "flex",
    marginBottom: 8,
    flexDirection: "row",
    flexWrap: "wrap",
    alignItems: "center",
  },
  itemTitle: {
    color: theme.palette.neutral400,
    fontWeight: "bold",
  },
  itemRow: {
    marginTop: 8,
  },
  bottomItemRow: {
    display: "flex",
    flexDirection: "column",
  },
  itemContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "flex-end",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  itemDescription: {
    marginBottom: 16,
    color: theme.palette.neutral300,
  },
  qty: {
    color: theme.palette.neutral300,
  },
  itemQuantity: {
    color: theme.palette.neutral400,
  },
  actionButton: {
    textTransform: "none",
    borderRadius: 8,
    border: "1px solid" + theme.palette.primary.main,
    padding: "6px 12px",
  },
  actionButtonLabel: {
    color: theme.palette.primary.main,
  },
  cancelButton: {
    borderRadius: 8,
    border: "1px solid" + theme.palette.error.main,
    padding: "6px 12px",
  },
  cancelButtonLabel: {
    color: theme.palette.error.main,
  },
  updateBy: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
  },
  updateByText: {
    color: theme.palette.neutral400,
  },
  actionButtonContainer: {
    display: "flex",
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
}))

export default ComplaintDetails
