import {useTheme} from "@mui/material/styles"
import {useCallback} from "react"
import {useTranslation} from "react-i18next"

import {KeyboardArrowRight} from "@mui/icons-material"
import {Box, Divider, List, ListItem, ListItemButton, ListItemText, Typography,} from "@mui/material"

import {PRIVACY_POLICY, TERMS_CONDITION} from "../../../../utils/constants"
import Header from "../../components/header/Header"
import SafeAreaPage from "../../components/page/SafeAreaPage"

const LegalPolicies = () => {
  const theme = useTheme()
  const {t} = useTranslation()

  const navigateToPrivacyPolicy = useCallback(() => {
    window.open(TERMS_CONDITION, "_blank")
  }, [])

  const navigateToTermsCondition = useCallback(() => {
    window.open(PRIVACY_POLICY, "_blank")
  }, [])

  return (
    <SafeAreaPage>
      <Header label={t("Profile.Legal and Policies")}/>
      <Box
        sx={{
          flex: 1,
          backgroundColor: theme.palette.grey[50],
          px: 2,
          py: 2,
          height: "100dvh",
        }}
      >
        <List sx={{width: "100%"}}>
          <ListItem disablePadding>
            <ListItemButton onClick={navigateToPrivacyPolicy}>
              <ListItemText
                primary={
                  <Typography variant="titleMedium" color="black">
                    {t("Profile.Privacy Policy")}
                  </Typography>
                }
              />
              <KeyboardArrowRight color="action"/>
            </ListItemButton>
          </ListItem>
          <Divider sx={{my: 2}}/>
          <ListItem disablePadding>
            <ListItemButton onClick={navigateToTermsCondition}>
              <ListItemText
                primary={
                  <Typography variant="titleMedium" color="black">
                    {t("Profile.Terms & Condition")}
                  </Typography>
                }
              />
              <KeyboardArrowRight color="action"/>
            </ListItemButton>
          </ListItem>
        </List>
      </Box>
    </SafeAreaPage>
  )
}

export default LegalPolicies
