import React, {useCallback} from "react"
import {useTranslation} from "react-i18next"
import {Box, List, ListItem, Paper, styled, Typography, useTheme,} from "@mui/material"

import SafeAreaPage from "../../components/page/SafeAreaPage"
import Header from "../../components/header/Header"
import {APACHE_LICENSE, licenses, MIT_LICENSE,} from "../../../../utils/licenses"

const LicenseContainer = styled(Paper)(({theme}) => ({
  padding: theme.spacing(1.5),
  border: `1px solid ${theme.palette.divider}`,
  marginBottom: theme.spacing(3.5),
  width: "100%",
}))

const LicenseContent = styled(Box)(({theme}) => ({
  marginTop: theme.spacing(1),
}))

const OpenSourceLibraries = () => {
  const theme = useTheme()
  const {t} = useTranslation()

  const renderItem = useCallback((item: any) => {
    return (
      <ListItem key={item.package} sx={{padding: 0, flexDirection: "column"}}>
        <div style={{display: "flex", alignSelf: "flex-start"}}>
          <Typography
            variant="titleLarge"
            gutterBottom
            sx={{alignItems: "left"}}
          >
            {item.package}
          </Typography>
        </div>
        <LicenseContainer elevation={0}>
          {item.license === "MIT License" ? (
            <div>
              {item.header.length > 0 && (
                <Typography
                  variant="labelMedium"
                  sx={{color: theme.palette.neutral300}}
                >
                  {item.header.split("\n").map((line, index) => (
                    <div key={index}>{line}</div>
                  ))}
                </Typography>
              )}
              <LicenseContent>
                <Typography
                  variant="labelMedium"
                  sx={{color: theme.palette.neutral300}}
                >
                  {MIT_LICENSE.split("\n").map((line, index) => (
                    <div
                      key={index}
                      style={{marginBottom: line.length === 0 ? "15px" : 0}}
                    >
                      {line}
                    </div>
                  ))}
                </Typography>
              </LicenseContent>
            </div>
          ) : (
            <LicenseContent>
              <Typography
                variant="labelMedium"
                sx={{color: theme.palette.neutral300}}
              >
                {APACHE_LICENSE.split("\n").map((line, index) => (
                  <div
                    key={index}
                    style={{marginBottom: line.length === 0 ? "15px" : 0}}
                  >
                    {line}
                  </div>
                ))}
              </Typography>
            </LicenseContent>
          )}
        </LicenseContainer>
      </ListItem>
    )
  }, [])

  return (
    <SafeAreaPage>
      <Header label={t("Profile.Open Source Libraries")}/>

      <Box
        sx={{
          // flex: 1,
          backgroundColor: theme.palette.background.paper,
          paddingTop: theme.spacing(2.75),
          paddingLeft: theme.spacing(2),
          paddingRight: theme.spacing(2),
          overflow: "scroll",
          height: "calc(100dvh - 56px)",
        }}
      >
        <List>{licenses.map((item) => renderItem(item))}</List>
      </Box>
    </SafeAreaPage>
  )
}

export default OpenSourceLibraries
