import {useEffect, useRef, useState} from 'react';
import axios from 'axios';
import {useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';
import {EventSourcePolyfill} from "event-source-polyfill";
import {useNavigate} from "react-router-dom";


import {getStoredData, setStoredData} from '../../../utils/storage';
import {constructQuoteObject, emptyAlertCallback, errorCallback, removeNullValues,} from '../../../utils/utils';
import {ORDER_PAYMENT_METHODS, SSE_TIMEOUT,} from '../../../utils/constants';
import {API_BASE_URL, APPLY_COUPON, EVENTS, INITIALIZE_ORDER, ON_INITIALIZE,} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import useBackHandler from './useBackHandler';
import {useToast} from "../../../hooks/toastProvider";

const CancelToken = axios.CancelToken;
const useInitItems = (
  deliveryAddress: any,
  billingAddress: any,
  activePaymentMethod = ORDER_PAYMENT_METHODS.PREPAID,
) => {
  const {showToast} = useToast();
  const navigation = useNavigate();
  const {t} = useTranslation();
  const {goBack} = useBackHandler();
  const {token, transaction_id, uid} = useSelector((state: any) => state.auth);
  const {getDataWithAuth, postDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const appliedCoupon = useRef<any>(null);
  const updatedCartItems = useRef<any[]>([]);
  const itemsTotal = useRef<number>(0);
  const itemCount = useRef<number>(0);
  const responseRef = useRef<any[]>([]);
  const requestCount = useRef(0);
  const eventTimeOutRef = useRef<any[]>([]);
  const source = useRef<any>(null);
  const [initializeOrderLoading, setInitializeOrderLoading] = useState(false);
  const [eventData, setEventData] = useState<any[]>([]);

  const backTriggered = useRef<boolean>(false);
  const navigationCompleted = useRef<boolean>(false);
  const cartId = useRef<string>('');
  const cartItems = useRef<any[]>([]);
  const fulfilmentList = useRef<any[]>([]);
  const responseReceivedIds = useRef<any[]>([]);
  const selectedFulfillmentList = useRef<any[]>([]);
  const updatedCartItemsData = useRef<any[]>([]);
  const totalOrderValueAfterSubsidy = useRef<number>(0);

  const handleSuccess = async () => {
    let checkout: any = {
      successOrderIds: [],
      productQuotes: [],
    };
    responseRef.current.forEach(item => {
      const {message} = item;
      checkout = {
        productQuotes: [...checkout.productQuotes, message?.order?.quote],
        successOrderIds: [
          ...checkout.successOrderIds,
          message?.order?.provider?.id.toString(),
        ],
      };
    });
    setStoredData('checkout_details', JSON.stringify(checkout));
    eventTimeOutRef.current.forEach(eventTimeout => {
      eventTimeout.eventSource.close();
      clearTimeout(eventTimeout.timer);
    });
    navigation('/order-processing', {
      replace: true,
      state: {
        activePaymentMethod,
        cartItems: cartItems.current,
        updatedCartItemsData: updatedCartItemsData.current,
        cartId: cartId.current,
        itemsTotal: itemsTotal.current,
        itemCount: itemCount.current,
        orderTotal: totalOrderValueAfterSubsidy.current,
        deliveryAddress,
      }
    });
    navigationCompleted.current = true;
  };

  const onInitializeOrder = async (messageId: any) => {
    setInitializeOrderLoading(true);
    try {
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${ON_INITIALIZE}${messageId}`,
        source.current.token,
      );
      responseRef.current = [...responseRef.current, data[0]];
      setEventData([...eventData, data[0]]);

      let oldData = updatedCartItems.current;
      oldData[0].message.quote.quote = data[0].message.order.quote;
      oldData[0].message.quote.payment = data[0].message.order.payment;
      await handleSuccess();
    } catch (err: any) {
      handleApiError(err);
      setInitializeOrderLoading(false);
      goBack();
    }
  };

  const clearEventTimeout = (eventTimeout: any) => {
    eventTimeout.eventSource.close();
    clearTimeout(eventTimeout.timer);
  };

  // use this function to initialize the order
  const onInit = (messageIds: any[]) => {
    eventTimeOutRef.current = messageIds.map(messageId => {
      const eventSource = new EventSourcePolyfill(
        `${API_BASE_URL}${EVENTS}${messageId}`,
        {
          headers: {Authorization: `Bearer ${token}`},
        },
      );
      eventSource.addEventListener('on_init', (event: any) => {
        const data = JSON.parse(event.data);
        onInitializeOrder(data.messageId)
          .then(emptyAlertCallback)
          .catch(errorCallback);
      });

      const timer = setTimeout(() => {
        eventTimeOutRef.current.forEach(clearEventTimeout);
        if (responseRef.current.length <= 0) {
          setInitializeOrderLoading(false);
          showToast(
            t('Global.Unable to fetch details. Please try again'), 'error'
          );
          goBack();
        }
        if (requestCount.current !== responseRef.current.length) {
          showToast(
            t('Global.Unable to fetch details. Please try again'), 'error'
          );
          goBack();
        }
      }, SSE_TIMEOUT);

      return {
        eventSource,
        timer,
      };
    });
  };

  const processItemData = (itemData: any) => {
    if (itemData?.product?.fulfillment_id) {
      delete itemData.product.fulfillment_id;
    }
    if (updatedCartItems.current) {
      let findItemFromQuote =
        updatedCartItems.current[0].message.quote.items.find(
          (one: any) => one.id === itemData.local_id,
        );
      if (findItemFromQuote) {
        itemData.parent_item_id = findItemFromQuote.parent_item_id;
        itemData.fulfillment_id = findItemFromQuote.fulfillment_id;
      }
    }
    return itemData;
  };

  const generatePayload = async (itemsList: any[]) => {
    const items = JSON.parse(JSON.stringify(Object.assign([], itemsList)));
    const contextCity = await getStoredData('contextCity');
    return items.map((item: any) => {
      let itemsData = Object.assign([], JSON.parse(JSON.stringify(item)));
      itemsData = itemsData?.map(processItemData);

      return {
        context: {
          transaction_id,
          city: contextCity || deliveryAddress.address.city,
          state: deliveryAddress.address.state,
          pincode: deliveryAddress.address.areaCode,
          domain: item[0].domain,
        },
        message: {
          items: itemsData,
          fulfillments: fulfilmentList.current.filter(
            (fulfillment: { id: any }) =>
              selectedFulfillmentList.current.includes(fulfillment.id),
          ),
          billing_info: {
            address: removeNullValues(billingAddress?.address),
            phone: billingAddress?.descriptor?.phone || billingAddress?.phone,
            name: billingAddress?.descriptor?.name || billingAddress?.name,
            email: billingAddress?.descriptor?.email || billingAddress?.email,
          },
          delivery_info: {
            type: 'Delivery',
            name: deliveryAddress?.descriptor?.name,
            email: deliveryAddress?.descriptor?.email,
            phone: deliveryAddress?.descriptor?.phone,
            location: {
              gps: `${deliveryAddress?.address?.lat},${deliveryAddress?.address?.lng}`,
              address: deliveryAddress?.address,
            },
          },
          payment: {
            type:
              activePaymentMethod === ORDER_PAYMENT_METHODS.COD
                ? 'ON-FULFILLMENT'
                : 'ON-ORDER',
          },
        },
      };
    });
  };

  const applyCoupon = async () => {
    if (!appliedCoupon.current.hasOwnProperty('id')) {
      return true;
    }
    const applyResponse = await postDataWithAuth(
      `${API_BASE_URL}${APPLY_COUPON}`,
      {
        offerId: appliedCoupon.current.id,
        userId: uid,
      },
      source.current.token,
    );
    if (!applyResponse.data) {
      showToast(t('Coupon.Applied coupon not available'), 'error');
      setInitializeOrderLoading(false);
    }
    return applyResponse.data;
  };

  const initializeOrder = async (itemsList: any[]) => {
    responseRef.current = [];
    setInitializeOrderLoading(true);
    try {
      source.current = CancelToken.source();
      const couponApplied = await applyCoupon();
      if (couponApplied) {
        const payload = await generatePayload(itemsList);
        // logEvent(APPS_FLYER_EVENTS.LAND_ON_CHECKOUT_PAGE, {
        //   user_id: uid,
        //   timestamp: getCurrentDate(),
        //   transaction_id: transaction_id,
        //   domain: payload[0].context.domain,
        //   provider_id: payload[0].message.items[0].provider.id,
        // });
        const {data} = await postDataWithAuth(
          `${API_BASE_URL}${INITIALIZE_ORDER}`,
          payload,
          source.current.token,
        );

        //Error handling workflow eg, NACK
        const isNACK = data.find(
          (item: any) => item.error || item?.message?.ack?.status === 'NACK',
        );
        if (isNACK) {
          showToast(
            data[0]?.error?.code
              ? t(`Fulfillment.Errors.${data[0]?.error?.code}`)
              : '', 'error'
          );
          goBack();
        } else {
          const parentTransactionIdMap = new Map();
          data.forEach((one: any) => {
            const provider_id = one?.context?.provider_id;
            parentTransactionIdMap.set(provider_id, {
              parent_order_id: one?.context?.parent_order_id,
              transaction_id: one?.context?.transaction_id,
            });
          });
          await setStoredData(
            'parent_order_id',
            data[0]?.context?.parent_order_id,
          );
          await setStoredData(
            'parent_and_transaction_id_map',
            JSON.stringify(Array.from(parentTransactionIdMap.entries())),
          );
          requestCount.current = data.length;
          onInit(data?.map((txn: any) => txn?.context?.message_id));
        }
      }
    } catch (err: any) {
      setInitializeOrderLoading(false);
      handleApiError(err);
      goBack();
    }
  };

  const handleInitializeOrder = () => {
    setInitializeOrderLoading(true);
    let items = cartItems.current.map((item: any) => item.item);
    const requestObjects = constructQuoteObject(
      items.filter(({provider}: { provider: any }) =>
        responseReceivedIds.current.includes(provider.local_id.toString()),
      ),
    );

    initializeOrder(requestObjects).then(emptyAlertCallback);
  };

  const setPreInitInfo = (
    id: string,
    items: any[],
    selectedItems: any[],
    list: any[],
    orderValueAfterSubsidy: number,
    offer: any,
    itemCost: number,
    totalItems: number,
  ) => {
    appliedCoupon.current = offer;
    updatedCartItems.current = selectedItems;
    itemsTotal.current = itemCost;
    itemCount.current = totalItems;
    totalOrderValueAfterSubsidy.current = orderValueAfterSubsidy;
    cartId.current = id;
    cartItems.current = items;
    updatedCartItemsData.current = selectedItems;
    selectedFulfillmentList.current = list;
    fulfilmentList.current = selectedItems[0]?.message?.quote?.fulfillments;
    responseReceivedIds.current = selectedItems.map(item =>
      item?.message?.quote?.provider?.id.toString(),
    );
  };

  useEffect(() => {
    return () => {
      backTriggered.current = true;
      eventTimeOutRef.current.forEach(eventTimeout => {
        eventTimeout.eventSource.close();
        clearTimeout(eventTimeout.timer);
      });
      if (source.current) {
        source.current.cancel();
        source.current = null;
      }
    };
  }, []);

  return {setPreInitInfo, handleInitializeOrder, initializeOrderLoading};
};

export default useInitItems;
