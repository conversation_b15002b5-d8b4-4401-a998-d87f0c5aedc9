import {useRef} from 'react';
import {useDispatch} from 'react-redux';

import {API_BASE_URL, PAGE} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import useAddressParams from './useAddressParams';
import {setPage} from '../../../toolkit/reducer/pages';
import {Section} from '../components/page/components/type';
import {sortSectionWidgets} from "../../../utils/utils";

interface Page {
  sections: Section[];
}

interface ApiResponse {
  pages: Page[];
}

const usePages = () => {
  const source = useRef<AbortController | null>(null);
  const {getDataWithAuth} = useNetworkHandling();
  const dispatch = useDispatch();
  const {handleApiError} = useNetworkErrorHandling();
  const {getAddressParams} = useAddressParams();

  const getPage = async (name: string) => {
    try {
      const params = getAddressParams();
      params.append('name', name);
      const url = `${API_BASE_URL}${PAGE}?${params.toString()}`;

      source.current = new AbortController();
      const response = await getDataWithAuth(url, {signal: source.current.signal});
      const responseData = response.data as ApiResponse; // Extract only the `.data` field

      if (responseData.pages && responseData.pages.length > 0) {
        const page = responseData.pages[0];
        page.sections = page.sections.filter((section: Section) => section.active);
        sortSectionWidgets(page.sections);
        dispatch(setPage({pageName: name, page}));
      }
    } catch (error: any) {
      if (source.current) {
        source.current.abort();
      } else {
        handleApiError(error);
      }
    }
  };

  return {
    getPage,
  };
};

export default usePages;
