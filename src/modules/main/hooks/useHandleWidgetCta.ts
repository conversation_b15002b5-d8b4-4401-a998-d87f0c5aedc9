import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import useDeepLink from "../../../hooks/useDeepLink"
import { setDomain } from "../../../toolkit/reducer/homeDomain"
import { CATEGORIES } from "../../../utils/categories"
import { FB_DOMAIN, WIDGET_CTA } from "../../../utils/constants"
import { DOMAINS } from "../../../utils/domains"
import { emptyAlertCallback, parseBrandOutletId } from "../../../utils/utils"

const useHandleWidgetCta = () => {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const { handleDeepLink } = useDeepLink()
  const { token } = useSelector(({ auth }: any) => auth)
  const { address } = useSelector((state: any) => state.address)

  const navigateToSubCategoryPage = (item: any) => {
    const category = CATEGORIES.find(
      (cat: any) => cat.domain === item.params?.ctaDomain
    )
    if (category && item.params) {
      navigate(
        `/sub-category?sub_category=${item.params.subcategory}&category=${category.shortName}&domain=${category.domain}`
      )
    }
  }

  const navigateToSearchPage = (item: any) => {
    if (item.params) {
      const searchParams = new URLSearchParams({
        serviceability: item.params?.tag?.serviceability || "",
        tts: item.params?.tag?.tts || "",
        tag: item.params?.ctaTag || "",
        domain: item.params.ctaDomain || "",
        subCategory: item.params.subcategory || "",
        hideStores: item?.params?.hideStores ?? false,
        query: item.params.searchQuery || "",
      }).toString()
      navigate(`/search?${searchParams}`)
    }
  }

  const navigateToStorePage = (item: any) => {
    if (item.params) {
      if (item.params.locationId) {
        const result = parseBrandOutletId(item.params.locationId)
        navigate(
          `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${item.params.locationId}`
        )
      } else {
        const result = parseBrandOutletId(item.params.providerId)

        navigate(
          `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}`
        )
      }
    }
  }
  const navigateToHyperLocalLanding = (item: any) => {
    const firstTag = item.params?.tag?.[0]
    navigate(
      `/hyper-local-landing?subCategory=${item.params?.subcategory}&category=&categoryDomain=${item.params?.ctaDomain}&tagName=${item.params?.ctaTag}&tagImage=${firstTag?.image}&tts=${firstTag?.tts}&serviceability=${firstTag?.serviceability}`
    )
  }
  const navigateToCategoryPage = (item: any) => {
    const category = DOMAINS.find(
      (cat: any) => cat.domain === item.params?.ctaDomain
    )
    if (category) {
      dispatch(setDomain(category))
    }
  }

  const navigateToDynamicPage = (item: any) => {
    if (item.params) {
      navigate(`/page?tag=${item.params.ctaPage}`)
    }
  }

  const navigateToProduct = (item: any) => {
    if (item.params) {
      const itemParams = item.params
      const productId = `${itemParams.providerId}_${itemParams.itemId}`
      if (item.params?.ctaDomain === FB_DOMAIN) {
        // navigation.navigate('BrandDetails', {
        //   brandId: item.params?.providerId || '',
        //   outletId: item.params?.locationId || '',
        //   analytics: item,
        //   productId: productId,
        //   showProductDetails: true,
        // });
        if (item.params) {
          if (item.params.locationId) {
            const result = parseBrandOutletId(item.params.locationId)
            navigate(
              `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&locationId=${item.params.locationId}`
            )
          } else {
            const result = parseBrandOutletId(item.params.providerId)

            navigate(
              `/store?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}`
            )
          }
        }
      } else {
        // const productId = `${itemParams.providerId}_${itemParams.itemId}`;
        // navigation.navigate('ProductDetails', {productId: productId});
        if (item.params?.providerId) {
          const result = parseBrandOutletId(item.params?.providerId)
          navigate(
            `/product?domain=${result?.domain}&provider_id=${result?.providerId}&bpp_id=${result?.bppId}&item_id=${itemParams.itemId}`
          )
        }
      }
    }
  }

  // Main function
  const navigateToPage = (item: any) => {
    console.log("🚀 ~ navigateToPage ~ item:", item)

    if (!item.allowRedirection) {
      return
    }
    switch (item.cta) {
      case WIDGET_CTA.SUBCATEGORY_PAGE:
        navigateToSubCategoryPage(item)
        break
      case WIDGET_CTA.ITEM_SEARCH:
      case WIDGET_CTA.GLOBAL_SEARCH:
        navigateToSearchPage(item)
        break
      case WIDGET_CTA.STORE_PAGE:
        navigateToStorePage(item)
        break
      case WIDGET_CTA.HYPERLOCAL_LANDING:
        navigateToHyperLocalLanding(item)
        break
      case WIDGET_CTA.CATEGORY_PAGE:
        navigateToCategoryPage(item)
        break
      case WIDGET_CTA.PAGE:
        navigateToDynamicPage(item)
      case WIDGET_CTA.ITEM:
      case WIDGET_CTA.PRODUCT:
        navigateToProduct(item)
        break
      case WIDGET_CTA.CUSTOM_LINK:
        if (item.params.link.startsWith("https://digihaat.in")) {
          handleDeepLink(item.params.link, token, address, navigate, item)
        } else {
          try {
            if (item.params.link) {
              window.open(item.params.link, "_blank")
            }
          } catch (error) {
            console.error("Failed to open URL:", error)
          } finally {
            emptyAlertCallback()
          }
        }
        break
    }
  }

  return { navigateToPage }
}

export default useHandleWidgetCta
