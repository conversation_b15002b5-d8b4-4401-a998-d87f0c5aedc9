import {useEffect, useState} from 'react';
import usePages from './usePages';
import {MOBILE_PAGES} from '../../../utils/constants';
import {emptyAlertCallback} from '../../../utils/utils';

const useLoadDefaultPages = () => {
  const [pageRequested, setPageRequested] = useState<boolean>(true);
  const {getPage} = usePages();

  const getPageDetails = async () => {
    setPageRequested(true);
    const promises = Object.values(MOBILE_PAGES).map(category =>
      getPage(category).then(emptyAlertCallback).catch(emptyAlertCallback),
    );
    await Promise.allSettled(promises);
    setPageRequested(false);
  };

  useEffect(() => {
    getPageDetails().then(emptyAlertCallback);
  }, []);

  return {pageRequested};
};

export default useLoadDefaultPages;
