import {useSelector} from 'react-redux';
import {useEffect, useRef, useState} from 'react';
import usePages from './usePages';

const useCurrentPage = (page: string) => {
  const pageName = useRef<string>(page);
  const {pages} = useSelector((state: any) => state.pages);
  const [pageRequested, setPageRequested] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<any>(null);
  const {getPage} = usePages();

  const getPageDetails = () => {
    if (!pages.hasOwnProperty(pageName.current)) {
      setCurrentPage(null);
      setPageRequested(true);
      getPage(pageName.current)
        .then(() => {
          setPageRequested(false);
        })
        .catch(() => {
          setPageRequested(false);
        });
    } else {
      setPageRequested(false);
      setCurrentPage(pages[pageName.current]);
    }
  };

  const updateCurrentPageName = (newPage: string) => {
    pageName.current = newPage;
    getPageDetails();
  };

  useEffect(() => {
    getPageDetails();
  }, []);

  useEffect(() => {
    if (pages.hasOwnProperty(pageName.current)) {
      setCurrentPage(pages[pageName.current]);
    }
  }, [pages]);

  return {pageRequested, currentPage, updateCurrentPageName};
};

export default useCurrentPage;
