import {useRef} from 'react';
import axios from 'axios';
import {useSelector} from 'react-redux';

import {API_BASE_URL, COUPONS} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';

const CancelToken = axios.CancelToken;
const useCoupons = () => {
  const source = useRef<any>(null);
  const {getDataWithWithoutEncode} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const {address} = useSelector((state: any) => state.address);

  const getCoupons = async (
    bppId = '',
    domain = '',
    providerId = '',
    subcategory = '',
    isHidden = false,
    query = '',
    applySubsidy = false,
    sellerDiscount = '',
    mov = '',
    deliveryCharge = '',
    bestOffer = 'false',
  ) => {
    try {
      source.current = CancelToken.source();
      const urlParams = new URLSearchParams();
      urlParams.append('currentTime', String(new Date().getTime()));
      urlParams.append('pincodes', address.address.areaCode);
      urlParams.append('isHidden', String(isHidden));
      urlParams.append('ondcFa', sellerDiscount);
      urlParams.append('mov', mov);
      urlParams.append('deliveryCharge', deliveryCharge);
      if (bppId) {
        urlParams.append('snpId', bppId);
      }
      if (providerId) {
        urlParams.append('provider', providerId);
      }
      if (subcategory) {
        urlParams.append('DhSubcategory', subcategory);
      }
      if (query) {
        urlParams.append('offerId', query.toUpperCase());
      }
      if (bestOffer === 'true') {
        urlParams.append('bestoffer', 'true');
      }
      const domainQuery = domain ? `&categoryDomain=${domain}` : '';
      const {data} = await getDataWithWithoutEncode(
        `${API_BASE_URL}${COUPONS}?${urlParams.toString()}${domainQuery}`,
        source.current.token,
      );
      if (applySubsidy) {
        return data.filter((one: any) => !one.additive);
      } else {
        return data;
      }
    } catch (error: any) {
      handleApiError(error);
      return [];
    }
  };

  return {getCoupons};
};

export default useCoupons;
