import {useDispatch, useSelector} from 'react-redux';
import {API_BASE_URL, WISHLIST} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import {updateWishlistItems} from '../../../toolkit/reducer/wishlist';

interface RootState {
  auth: {
    uid: string;
  };
}

const useWishlist = () => {
  const {uid} = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();

  const getWishlistItems = async () => {
    const controller = new AbortController();
    const signal = controller.signal;

    try {
      const response = await getDataWithAuth(
        `${API_BASE_URL}${WISHLIST}/${uid}/all`,
        {signal}
      );

      if (response?.data) {
        dispatch(updateWishlistItems(response.data));
        return response.data;
      }

      return [];
    } catch (error) {
      if (signal.aborted) {
        console.warn('Wishlist request was aborted.');
      } else {
        handleApiError(error);
      }
      return [];
    }
  };

  return {getWishlistItems};
};

export default useWishlist;
