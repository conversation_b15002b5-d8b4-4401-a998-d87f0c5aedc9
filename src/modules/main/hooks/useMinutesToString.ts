import moment from 'moment';
import {useTranslation} from 'react-i18next';

const useTimeConversion = () => {
  const {t} = useTranslation();

  const convertToRange = (value: number) => {
    const lower = Math.floor(value);
    const higher = lower + 1;
    return {lower, higher};
  };

  const convertMinutesToHumanReadable = (minutes: number) => {
    const duration = moment.duration(minutes, 'minutes');

    if (minutes >= 1440) {
      return {type: 'days', time: Math.floor(duration.asDays())};
    } else if (minutes >= 60) {
      return {type: 'hours', time: Math.floor(duration.asHours())};
    } else {
      return {type: 'minutes', time: minutes};
    }
  };

  const convertDurationToHumanReadable = (value: string) => {
    const duration = moment.duration(value);
    const days = Math.floor(duration.asDays());

    if (days >= 1) {
      return {type: 'days', time: days};
    } else {
      const hours = Math.floor(duration.asHours());
      if (hours >= 1) {
        return {type: 'hours', time: hours};
      } else {
        return {type: 'minutes', time: duration.minutes()};
      }
    }
  };

  const translateMinutesToHumanReadable = (type: string, time: number) => {
    const {lower, higher} = convertToRange(time);
    switch (type) {
      case 'days':
        if (lower >= 7) {
          return t('Store.more than 7 days');
        }
        return t('Store.days', {time: `${lower}-${higher}`});

      case 'hours':
        return t('Store.hours', {time: `${lower}-${higher}`});

      default:
        return t('Store.minutes', {time: lower});
    }
  };

  return {
    convertDurationToHumanReadable,
    convertMinutesToHumanReadable,
    translateMinutesToHumanReadable,
  };
};

export default useTimeConversion;
