import {useRef, useState} from 'react';
import {useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import {API_BASE_URL, CART} from '../../../utils/apiActions';
import {useToast} from "../../../hooks/toastProvider";

interface RootState {
  auth: {
    uid: string;
  };
}

interface CartItem {
  _id: string;
  item: {
    product?: {
      quantity?: {
        maximum?: { count: number };
      };
    };
    quantity: {
      count: number;
    };
  };
}

interface CartProvider {
  location_id: string;
  items: CartItem[];
}

export default () => {
  const {showToast} = useToast();
  const {t} = useTranslation();
  const [updatingCartItem, setUpdatingCartItem] = useState<string | null>(null);
  const {putDataWithAuth} = useNetworkHandling();
  const {uid} = useSelector((state: RootState) => state.auth);
  const controllerRef = useRef<AbortController | null>(null);

  const cancelPreviousRequest = () => {
    if (controllerRef.current) {
      controllerRef.current.abort();
    }
    controllerRef.current = new AbortController();
  };

  const updateSpecificCartItem = async (
    locationId: string,
    itemId: string,
    increment: boolean,
    uniqueId: string,
    cartItems: CartProvider[],
    setCartItemsData: (items: CartProvider[]) => void
  ) => {
    try {
      setUpdatingCartItem(uniqueId);
      cancelPreviousRequest();

      if (!controllerRef.current) return;
      const {signal} = controllerRef.current;

      const newCartItems = cartItems.map(cart => ({
        ...cart,
        items: cart.items ? [...cart.items] : [],
      }));

      let providerCart = newCartItems.find(cart => cart.location_id === locationId);
      if (!providerCart) return;

      const itemIndex = providerCart.items.findIndex(item => item._id === uniqueId);
      if (itemIndex === -1) return;

      const url = `${API_BASE_URL}${CART}/${uid}/${uniqueId}`;
      const newCartItem = JSON.parse(JSON.stringify(providerCart.items[itemIndex]));

      if (increment) {
        const productMaxQuantity = newCartItem.item.product?.quantity?.maximum?.count ?? Infinity;
        if (newCartItem.item.quantity.count < productMaxQuantity) {
          newCartItem.item.quantity.count += 1;
        } else {
          showToast(
            t('Cart.Maximum allowed quantity is', {
              count: newCartItem.item.quantity.count,
            }), 'error'
          );
          return;
        }
      } else {
        if (newCartItem.item.quantity.count > 1) {
          newCartItem.item.quantity.count -= 1;
        } else {
          showToast(t('Cart.Minimum quantity is 1'), 'error');
          return;
        }
      }

      await putDataWithAuth(url, newCartItem.item, {signal});

      providerCart.items[itemIndex] = newCartItem;
      setCartItemsData(newCartItems);
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.warn('Request was canceled:', error.message);
      } else {
        console.error('Error updating cart item:', error);
      }
    } finally {
      setUpdatingCartItem(null);
    }
  };

  return {updatingCartItem, updateSpecificCartItem};
};
