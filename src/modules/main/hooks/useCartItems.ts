import {useDispatch, useSelector} from 'react-redux';
import {useEffect, useRef} from 'react';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import {updateCartItems} from '../../../toolkit/reducer/cart';
import {API_BASE_URL, CART} from '../../../utils/apiActions';

interface RootState {
  auth: {
    uid: string;
  };
}

export default () => {
  const {uid} = useSelector((state: RootState) => state.auth);
  const dispatch = useDispatch();
  const source = useRef<AbortController | null>(null);
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();

  const getCartItems = async () => {
    try {
      source.current = new AbortController();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/all`,
        {signal: source.current.signal}
      );
      dispatch(updateCartItems(data));
      return data;
    } catch (error: any) {
      handleApiError(error);
      return [];
    }
  };

  useEffect(() => {
    return () => {
      // if (source.current) {
      //     source.current.abort();
      // }
    };
  }, []);

  return {getCartItems};
};
