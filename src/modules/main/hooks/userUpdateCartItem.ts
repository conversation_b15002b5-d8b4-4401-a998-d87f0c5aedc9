import {useDispatch, useSelector} from 'react-redux';
import {useRef} from 'react';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import {API_BASE_URL, CART} from '../../../utils/apiActions';
import {updateCartItemQuantity} from '../../../toolkit/reducer/cart';
import {useToast} from "../../../hooks/toastProvider";

interface RootState {
  auth: {
    uid: string;
  };
}

const useCartUpdater = () => {
  const {showToast} = useToast();
  const controllerRef = useRef<AbortController | null>(null);
  const {uid} = useSelector((state: RootState) => state.auth);
  const {putDataWithAuth} = useNetworkHandling();
  const dispatch = useDispatch();

  const cancelPreviousRequest = () => {
    if (controllerRef.current) {
      controllerRef.current.abort();
    }
    controllerRef.current = new AbortController();
  };

  const updateCartItem = async (
    cartItems: any[],
    increment: boolean,
    uniqueId: string
  ) => {
    try {
      const itemIndex = cartItems.findIndex((item) => item._id === uniqueId);
      if (itemIndex === -1) return;
      const url = `${API_BASE_URL}${CART}/${uid}/${uniqueId}`;
      cancelPreviousRequest();

      if (!controllerRef.current) return;
      const {signal} = controllerRef.current;

      const item = {...cartItems[itemIndex]};
      const quantity = {...item.item.quantity};

      if (increment) {
        const productMaxQuantity = Number(item?.item?.product?.quantity?.maximum?.count);
        if (productMaxQuantity && quantity.count < productMaxQuantity) {
          quantity.count += 1;
        } else {
          showToast(
            `Maximum allowed quantity is ${quantity.count}`, 'error'
          );
          return;
        }
      } else {
        if (quantity.count > 1) {
          quantity.count -= 1;
        } else {
          showToast('Minimum quantity is 1', 'error');
          return;
        }
      }

      await putDataWithAuth(url, {...item.item, quantity}, {signal});
      return {...item, item: {...item.item, quantity}};
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.warn('Request was canceled:', error.message);
      } else {
        console.error('Error updating cart item:', error);
      }
    }
  };

  const updateSpecificItemQuantity = async (
    cartItems: any[],
    itemQty: number,
    uniqueId: string,
    locationId: string
  ) => {
    try {
      const itemIndex = cartItems.findIndex((item) => item._id === uniqueId);
      if (itemIndex === -1) return;

      const url = `${API_BASE_URL}${CART}/${uid}/${uniqueId}`;
      cancelPreviousRequest();

      if (!controllerRef.current) return;
      const {signal} = controllerRef.current;

      const item = {...cartItems[itemIndex]};
      const quantity = {...item.item.quantity, count: itemQty};

      await putDataWithAuth(url, {...item.item, quantity}, {signal});

      dispatch(
        updateCartItemQuantity({
          locationId,
          itemId: uniqueId,
          quantity: itemQty,
        })
      );

      return {...item, item: {...item.item, quantity}};
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.warn('Request was canceled:', error.message);
      } else {
        console.error('Error updating specific cart item quantity:', error);
      }
    }
  };

  return {updateCartItem, updateSpecificItemQuantity};
};

export default useCartUpdater;
