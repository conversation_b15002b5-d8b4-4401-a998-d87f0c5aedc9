import {useDispatch} from 'react-redux';
import {useRef} from 'react';
import axios from 'axios';
import useAppRatingService from '../components/stickyCart/services/appRatingService';
import {
  setAppRatingEligibility,
  resetAppRatingEligibility,
} from '../../../toolkit/reducer/appRating';

/**
 * Custom hook for app rating functionality
 */
const useAppRating = () => {
  const dispatch = useDispatch();
  const source = useRef<any>(null);

  // Get the app rating service functions
  const {checkAppRatingEligibility, submitAppRating} = useAppRatingService();

  /**
   * Check if a user is eligible for app rating
   * @param userId The user ID to check
   */
  const checkEligibility = async (userId: string) => {
    try {
      // Create a cancel token source using axios
      source.current = axios.CancelToken.source();

      const data = await checkAppRatingEligibility(
        userId,
        source.current.token,
      );

      dispatch(setAppRatingEligibility(data));
      return data;
    } catch (error) {
      console.error('Error checking app rating eligibility:', error);
      dispatch(setAppRatingEligibility({apprating: false}));
      return {apprating: false};
    }
  };

  /**
   * Submit a rating for an order
   * @param orderId Order ID
   * @param mongoId MongoDB ID
   * @param orderData Complete order data object
   * @param orderRating Overall order rating (1-5)
   * @param storeRating Store rating (1-5)
   * @param itemRatings Item ratings object
   * @param deliveryRating Delivery rating (1-5)
   * @param comment User feedback comment
   * @param dismissed Dismissed flag (0 for normal submission)
   */
  const submitRating = async (
    orderId: string, // Order ID - DIRECT PARAMETER
    mongoId: string, // MongoDB ID - DIRECT PARAMETER
    orderData: any, // Complete order data - DIRECT PARAMETER
    orderRating: number, // Overall order rating
    storeRating: number, // Store rating
    itemRatings: Record<string, number> = {},
    deliveryRating: number = 0,
    comment: string = '',
    dismissed: number = 0,
  ) => {
    try {
      // Validate required parameters
      if (!orderId || !mongoId || !orderData) {
        throw new Error(
          'Missing required parameters: orderId, mongoId, or orderData',
        );
      }

      source.current = axios.CancelToken.source();

      // Extract store info from provided order data
      const storeProviderID = orderData?.provider?.id || '';
      const bppId = orderData?.bppId || '';
      const domain = orderData?.domain || '';

      // Validate we have required store info
      if (!storeProviderID) {
        console.warn('Missing storeProviderID from orderData');
      }

      // Format store ID correctly based on schema requirements
      const storeId =
        bppId && storeProviderID && domain
          ? `${bppId}-${storeProviderID}-${domain}`
          : `ONDC:${storeProviderID}-ProviderID-BppId`; // Fallback


      // Format item ratings for the service
      const formattedItemRatings = [];
      if (orderData?.confirmedItems) {
        for (const item of orderData.confirmedItems) {
          formattedItemRatings.push({
            id: item.id,
            item_id: item.id,
            rating: itemRatings[item.id] || orderRating, // Use orderRating as fallback
          });
        }
      }

      // Call the service with correct parameters
      const data = await submitAppRating(
        orderId, // 1st param
        mongoId, // 2nd param
        storeId, // 3rd param
        orderRating, // 4th param: order rating
        storeRating, // 5th param: store rating
        formattedItemRatings, // 6th param: item ratings
        deliveryRating, // 7th param: delivery rating
        comment, // 8th param: user experience
        dismissed, // 9th param: dismissed
        storeProviderID, // 10th param: store provider ID
        source.current.token, // 11th param: cancel token
      );

      dispatch(resetAppRatingEligibility());
      return data;
    } catch (error) {
      console.error('Error submitting rating:', error);
      throw error;
    }
  };

  /**
   * Reset app rating eligibility state
   */
  const resetEligibility = () => {
    dispatch(resetAppRatingEligibility());
  };

  /**
   * Cancel any ongoing requests when component unmounts
   */
  const cancelRequests = () => {
    if (source.current) {
      source.current.cancel('Operation canceled by the user.');
    }
  };

  return {
    checkEligibility,
    submitRating,
    resetEligibility,
    cancelRequests,
  };
};

export default useAppRating;
