import axios from "axios"
import { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDispatch, useSelector } from "react-redux"
import { useNavigate } from "react-router-dom"
import { v4 as uuidv4 } from "uuid"

import { EventSourcePolyfill } from "event-source-polyfill"
import { useToast } from "../../../hooks/toastProvider"
import useNetworkErrorHandling from "../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../hooks/useNetworkHandling"
import { setTransactionId } from "../../../toolkit/reducer/auth"
import {
  API_BASE_URL,
  EVENTS,
  ON_SELECT,
  SELECT,
} from "../../../utils/apiActions"
import { CART_LOADER_TIMER, SSE_TIMEOUT } from "../../../utils/constants"
import { setStoredData } from "../../../utils/storage"
import {
  constructQuoteObject,
  emptyAlertCallback,
  errorCallback,
} from "../../../utils/utils"
import { useFulfillmentErrorContext } from "../components/providers/FulfillmentErrorProvider"

const CancelToken = axios.CancelToken

const useSelect = () => {
  const { showToast } = useToast()
  const { t } = useTranslation()
  const navigation = useNavigate()
  const { handleApiError } = useNetworkErrorHandling()
  const { getDataWithAuth, postDataWithAuth } = useNetworkHandling()
  const { showFulfillmentError } = useFulfillmentErrorContext()
  const dispatch = useDispatch()
  const source = useRef<any>(null)
  const address = useRef<any>(null)
  const cartId = useRef<any>(null)
  const loaderTimer = useRef<any>(null)
  const { token } = useSelector(({ auth }: any) => auth)
  const navigationCompleted = useRef<boolean>(false)
  const backTriggered = useRef<boolean>(false)
  const closeLoaderOnComplete = useRef<boolean>(false)
  const responseRef = useRef<any[]>([])
  const eventTimeOutRef = useRef<any[]>([])
  const updatedCartItems = useRef<any[]>([])
  const [selectedItems, setSelectedItems] = useState<any[]>([])
  const [selectedItemsForInit, setSelectedItemsForInit] = useState<any[]>([])
  const [eventData, setEventData] = useState<any[]>([])
  const [cartItems, setCartItems] = useState<any>([])
  const [checkoutLoading, setCheckoutLoading] = useState(false)
  const [haveDistinctProviders, setHaveDistinctProviders] =
    useState<boolean>(false)
  const [
    isProductAvailableQuantityIsZero,
    setIsProductAvailableQuantityIsZero,
  ] = useState(false)
  const [isProductCategoryIsDifferent, setIsProductCategoryIsDifferent] =
    useState(false)

  const checkDistinctProviders = (items: any[]) => {
    if (items.length < 2) {
      setHaveDistinctProviders(false)
    } else {
      const firstProvider = items[0].item.provider.id
      const everyEnvHasSameValue = items?.every(
        (item: any) => item.item.provider.id === firstProvider
      )
      setHaveDistinctProviders(!everyEnvHasSameValue)
    }
  }

  const checkAvailableQuantity = (items: any[]) => {
    let quantityIsZero = false
    items.forEach((item: any) => {
      const availableQuantity = item?.item?.product?.quantity?.available
      if (availableQuantity && availableQuantity?.count === 0) {
        quantityIsZero = true
      }
    })
    setIsProductAvailableQuantityIsZero(quantityIsZero)
  }

  const checkDifferentCategory = (items: any[]) => {
    const everyEnvHasSameValue = items.every(
      (item: any) => item.item.domain === items[0].item.domain
    )
    setIsProductCategoryIsDifferent(!everyEnvHasSameValue)
  }

  const checkCartItemStatus = (items: any[]) => {
    checkDistinctProviders(items)
    checkAvailableQuantity(items)
    checkDifferentCategory(items)
  }

  const getProviderIds = (qoute: any[]) => {
    let providers: any[] = []
    qoute.forEach((item) => {
      providers.push(item.provider.local_id)
    })
    const ids = Array.from(new Set(providers))
    setStoredData("providerIds", JSON.stringify(ids))
    return ids
  }

  const getUpdateItemsDomain = (items: any[]) => {
    let domain: string = ""
    let contextCity: string = ""
    const updatedItems: any[] = items.map((item) => {
      const newItem = Object.assign({}, item)
      domain = newItem.domain
      contextCity = newItem.contextCity
      delete newItem.context
      delete newItem.contextCity
      return newItem
    })
    if (contextCity === "") {
      contextCity = address.current.address.city
    }

    return { contextCity, domain, updatedItems }
  }

  const generatePayload = (
    domain: string,
    contextCity: string,
    updatedItems: any[]
  ) => {
    const transactionId: any = uuidv4()
    dispatch(setTransactionId(transactionId))
    return {
      context: {
        transaction_id: transactionId,
        domain,
        pincode: address.current.address.areaCode,
        city: contextCity || address.current.address.city,
        state: address.current.address.state,
      },
      message: {
        cart: {
          items: updatedItems,
        },
        fulfillments: [
          {
            end: {
              location: {
                gps: `${address.current?.address?.lat},${address.current?.address?.lng}`,
                address: {
                  area_code: `${address.current?.address?.areaCode}`,
                },
              },
            },
          },
        ],
      },
    }
  }

  const requestSelect = async (selectPayload: any, contextCity: string) => {
    try {
      source.current = CancelToken.source()
      const { data } = await postDataWithAuth(
        `${API_BASE_URL}${SELECT}`,
        [selectPayload],
        source.current.token
      )

      if (data[0]?.error?.code) {
        stopLoader()
        showFulfillmentError(data[0]?.error?.code, cartId.current)
        return
      }
      setStoredData("contextCity", contextCity)
      //Error handling workflow eg, NACK
      const isNACK = data.find(
        (item: any) => item.error || item?.message?.ack?.status === "NACK"
      )
      if (isNACK) {
        stopLoader()
      } else {
        // fetch through events
        onFetchQuote(
          data?.map((txn: any) => {
            const { context } = txn
            return context?.message_id
          })
        )
      }
    } catch (error: any) {
      handleApiError(error)
      stopLoader()
    }
  }

  const getQuote = async (items: any[]) => {
    responseRef.current = []
    if (address.current) {
      const { contextCity, domain, updatedItems } = getUpdateItemsDomain(items)
      const selectPayload = generatePayload(domain, contextCity, updatedItems)
      await requestSelect(selectPayload, contextCity)
    } else {
      showToast(t("Global.Please select address"), "error")
      stopLoader()
    }
  }

  const updateSelectedItemsForInit = () => {
    const newItems = selectedItems[0]?.message?.quote.items
    updatedCartItems.current.forEach((one) => {
      const updatedItem = newItems.find(
        (newItem: any) => newItem.id === one.item.local_id
      )
      one.item.fulfillment_id = updatedItem?.fulfillment_id
    })
  }

  const navigateToDashboard = async () => {
    stopLoader()
    showToast(t("Global.Unable to fetch details. Please try again"), "error")
  }

  const clearEventTimout = (eventTimeout: any) => {
    eventTimeout.eventSource.close()
    clearTimeout(eventTimeout.timer)
  }

  const onFetchQuote = (messageIds: any[]) => {
    const request_object = constructQuoteObject(cartItems)

    eventTimeOutRef.current = messageIds.map((messageId) => {
      const eventSource = new EventSourcePolyfill(
        `${API_BASE_URL}${EVENTS}${messageId}`,
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      )
      eventSource.addEventListener("on_select", (event: any) => {
        if (!backTriggered.current) {
          const data = JSON.parse(event.data)
          onGetQuote(data.messageId, cartItems)
            .then(emptyAlertCallback)
            .catch(errorCallback)
        } else {
          eventTimeOutRef.current.forEach((eventTimeout) => {
            eventTimeout.eventSource.close()
            clearTimeout(eventTimeout.timer)
          })
        }
      })

      const timer = setTimeout(() => {
        eventTimeOutRef.current.forEach(clearEventTimout)
        if (responseRef.current.length <= 0) {
          navigateToDashboard()
            .then(emptyAlertCallback)
            .catch(emptyAlertCallback)
        }
        if (responseRef.current.length !== request_object.length) {
          showToast(
            t("Global.Unable to fetch details. Please try again"),
            "error"
          )
        }
      }, SSE_TIMEOUT)

      return {
        eventSource,
        timer,
      }
    })
  }

  const stopLoader = () => {
    if (!loaderTimer.current) {
      setCheckoutLoading(false)
    } else {
      closeLoaderOnComplete.current = true
    }
  }

  const onGetQuote = async (
    messageId: string,
    itemsInCart: any,
    fulfilmentId?: any,
    coupon?: any
  ) => {
    try {
      source.current = CancelToken.source()
      setCheckoutLoading(true)
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${ON_SELECT}${messageId}${
          fulfilmentId ? `&fulfillmentId=${fulfilmentId}` : ""
        }${coupon ? `&coupon=${coupon.id}` : ""}`,
        source.current.token
      )
      if (data[0]?.error?.code) {
        stopLoader()
        showFulfillmentError(data[0]?.error?.code, cartId.current)
        eventTimeOutRef.current.forEach((eventTimeout) => {
          eventTimeout.eventSource.close()
          clearTimeout(eventTimeout.timer)
        })
        return
      } else {
        const selectResponse = Object.assign({}, data[0])
        selectResponse.message.quote.fulfillments =
          selectResponse.message.quote.fulfillments.filter(
            (one: any) => one.type !== "Self-Pickup"
          )
        responseRef.current = [...responseRef.current, selectResponse]

        setEventData([...eventData, selectResponse])

        selectResponse.message.quote.items.forEach((item: any) => {
          const findItemIndexFromCart = updatedCartItems.current.findIndex(
            (prod: any) => prod.item.product.id === item.id
          )
          if (findItemIndexFromCart > -1) {
            updatedCartItems.current[
              findItemIndexFromCart
            ].item.product.fulfillment_id = item.fulfillment_id
            updatedCartItems.current[
              findItemIndexFromCart
            ].item.product.fulfillments =
              selectResponse.message.quote.fulfillments
          }
        })
        setSelectedItemsForInit(updatedCartItems.current.concat([]))
        const newList = responseRef.current.concat([])
        setSelectedItems(newList)
        eventTimeOutRef.current.forEach((eventTimeout) => {
          eventTimeout.eventSource.close()
          clearTimeout(eventTimeout.timer)
        })

        if (!!coupon) {
          navigation(-1)
        }

        setTimeout(
          () =>
            navigation("/fulfillment", {
              replace: !!coupon,
              state: {
                cartId: cartId.current,
                items: itemsInCart,
                cartItems: newList,
                deliveryAddress: address.current,
                appliedCoupon: coupon,
              },
            }),
          200
        )
        responseRef.current = []
      }
      stopLoader()
      navigationCompleted.current = true
    } catch (err: any) {
      handleApiError(err)
      stopLoader()
    }
  }
  const onGetQuoteForCouponList = async (
    messageId: string,
    itemsInCart: any,
    fulfilmentId?: any,
    coupon?: any
  ) => {
    try {
      source.current = CancelToken.source()
      setCheckoutLoading(true)
      const { data } = await getDataWithAuth(
        `${API_BASE_URL}${ON_SELECT}${messageId}${
          fulfilmentId ? `&fulfillmentId=${fulfilmentId}` : ""
        }${coupon ? `&coupon=${coupon.id}` : ""}`,
        source.current.token
      )

      const selectResponse = Object.assign({}, data[0])
      selectResponse.message.quote.fulfillments =
        selectResponse.message.quote.fulfillments.filter(
          (one: any) => one.type !== "Self-Pickup"
        )
      responseRef.current = [...responseRef.current, selectResponse]

      setEventData([...eventData, selectResponse])

      selectResponse.message.quote.items.forEach((item: any) => {
        const findItemIndexFromCart = updatedCartItems.current.findIndex(
          (prod: any) => prod.item.product.id === item.id
        )
        if (findItemIndexFromCart > -1) {
          updatedCartItems.current[
            findItemIndexFromCart
          ].item.product.fulfillment_id = item.fulfillment_id
          updatedCartItems.current[
            findItemIndexFromCart
          ].item.product.fulfillments =
            selectResponse.message.quote.fulfillments
        }
      })
      setSelectedItemsForInit(updatedCartItems.current.concat([]))
      const newList = responseRef.current.concat([])
      setSelectedItems(newList)
      eventTimeOutRef.current.forEach((eventTimeout) => {
        eventTimeout.eventSource.close()
        clearTimeout(eventTimeout.timer)
      })

      // if (
      //     route.name === 'ProviderCart' ||
      //     route.name === 'CouponList' ||
      //     route.name === 'Fulfillment'
      // ) {
      stopLoader()
      return {
        cartId: cartId.current,
        items: itemsInCart,
        cartItems: newList,
        deliveryAddress: address.current,
        appliedCoupon: coupon,
      }
      // }
      // navigationCompleted.current = true;
    } catch (err: any) {
      handleApiError(err)
      stopLoader()
    }
  }
  const setAddressAndCartId = (currentAddress: any, currentCartId: string) => {
    address.current = currentAddress
    cartId.current = currentCartId
  }

  const onCheckoutFromCart = async (
    currentAddress: any,
    currentCartId: string
  ) => {
    setCheckoutLoading(true)
    closeLoaderOnComplete.current = false
    loaderTimer.current = setTimeout(() => {
      loaderTimer.current = null
      if (closeLoaderOnComplete.current) {
        setCheckoutLoading(false)
      }
    }, CART_LOADER_TIMER)
    setAddressAndCartId(currentAddress, currentCartId)
    if (cartItems.length > 0) {
      let items = cartItems.map((item: any) => item.item)
      const quote = constructQuoteObject(items)
      await getQuote(quote[0])
      getProviderIds(quote[0])
    }
  }

  useEffect(() => {
    checkCartItemStatus(cartItems)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [cartItems])

  useEffect(() => {
    return () => {
      source?.current?.cancel()
      eventTimeOutRef.current.forEach((eventTimeout) => {
        eventTimeout.eventSource.close()
        clearTimeout(eventTimeout.timer)
      })
      clearTimeout(loaderTimer.current)
      loaderTimer.current = null
    }
  }, [])

  return {
    cartItems,
    checkoutLoading,
    onCheckoutFromCart,
    haveDistinctProviders,
    isProductAvailableQuantityIsZero,
    isProductCategoryIsDifferent,
    setCartItems,
    selectedItems,
    setSelectedItems,
    selectedItemsForInit,
    setSelectedItemsForInit,
    updateSelectedItemsForInit,
    onGetQuote,
    setAddressAndCartId,
    onGetQuoteForCouponList,
  }
}

export default useSelect
