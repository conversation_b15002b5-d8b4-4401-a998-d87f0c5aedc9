import {useRef} from 'react';
import axios from 'axios';
import {v4 as uuidv4} from 'uuid';
import {useDispatch, useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';

import {getStoredData, removeData, setStoredData} from '../../../utils/storage';
import {ORDER_PAYMENT_METHODS,} from '../../../utils/constants';
import {API_BASE_URL, CART, CREATE_PAYMENT, RAZORPAY_KEYS, VERIFY_PAYMENT,} from '../../../utils/apiActions';
import {constructQuoteObject, emptyAlertCallback,} from '../../../utils/utils';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import {setTransactionId} from '../../../toolkit/reducer/auth';
import {clearSpecificCart} from '../../../toolkit/reducer/cart';
import useNavigateToOrder from './useNavigateToOrder';
import useBackHandler from './useBackHandler';
import {alertWithOneButton} from '../../../utils/alerts';
import {useToast} from "../../../hooks/toastProvider";

const CancelToken = axios.CancelToken;
const useConfirmItems = (updateMessage: () => void) => {
  // const logEvent = useLogAppsFlyerEvent();
  const {showToast} = useToast();
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const responseRef = useRef<any[]>([]);
  const source = useRef<any>(null);
  const {uid} = useSelector((state: any) => state.auth);
  const {deleteDataWithAuth, getDataWithAuth, postDataWithAuth} =
    useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const confirmCartItems = useRef<any[]>([]);
  const currentCartId = useRef<any>(null);
  const currentAddress = useRef<any>(null);
  const {navigateToOrder} = useNavigateToOrder();
  const {goBack} = useBackHandler();

  const handleError = () => {
    createTransaction().then(emptyAlertCallback);
    goBack();
  };

  const getItemProviderId = async (item: any) => {
    const providersString = await getStoredData('providerIds');
    const providers = providersString ? JSON.parse(providersString) : [];
    let provider: any = {};
    if (providers.includes(item.provider.local_id)) {
      provider = {
        id: item.provider.local_id,
        locations: item.provider.locations.map(
          (location: any) => location.local_id,
        ),
      };
    }
    return provider;
  };

  const createTransactionAndClear = async () => {
    const transactionId: any = uuidv4();
    dispatch(setTransactionId(transactionId));
    setStoredData('transaction_id', transactionId);
    removeData('parent_order_id');
    removeData('checkout_details');
    removeData('parent_and_transaction_id_map');
    await deleteCart();
    dispatch(clearSpecificCart(currentCartId.current));
  };

  const createTransaction = async () => {
    const transactionId: any = uuidv4();
    dispatch(setTransactionId(transactionId));
    setStoredData('transaction_id', transactionId);
    removeData('parent_order_id');
    removeData('checkout_details');
    removeData('parent_and_transaction_id_map');
  };

  const getConfirmParams = async (
    contextCity: any,
    item: any,
    parentOrderIDMap: any,
    payment: any,
    productQuotesForCheckout: any,
    method: string,
  ) => {
    return [
      {
        context: {
          domain: item.domain,
          city: contextCity || currentAddress.current.address.city,
          state: currentAddress.current.address.state,
          pincode: currentAddress.current.address.areaCode,
          parent_order_id: parentOrderIDMap.get(item?.provider?.id)
            .parent_order_id,
          transaction_id: parentOrderIDMap.get(item?.provider?.id)
            .transaction_id,
        },
        message: {
          payment: {
            ...payment,
            paid_amount: Number(productQuotesForCheckout[0]?.price?.value),
            type:
              method === ORDER_PAYMENT_METHODS.COD
                ? 'ON-FULFILLMENT'
                : 'ON-ORDER',
            transaction_id: parentOrderIDMap.get(item?.provider?.id)
              .transaction_id,
            paymentGatewayEnabled: false,
          },
          quote: {
            ...productQuotesForCheckout[0],
            price: {
              currency: productQuotesForCheckout[0].price.currency,
              value: String(productQuotesForCheckout[0].price.value),
            },
          },
          providers: await getItemProviderId(item),
        },
      },
    ];
  };

  const getRazorpayKeys = async () => {
    try {
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${RAZORPAY_KEYS}`,
        source.current.token,
      );
      return data.keyId;
    } catch (error) {
      return null;
    }
  };

  const handlePaymentSuccess = async (razorpayResponse: any) => {
    const {data} = await postDataWithAuth(
      `${API_BASE_URL}${VERIFY_PAYMENT}`,
      {
        razorPayRequest: razorpayResponse,
        confirm: false,
      },
      source.current.token,
    );
    if (data.hasOwnProperty('status')) {
      if (
        (data.orderStatus === 'created' || data.orderStatus === 'attempted') &&
        data.payment
      ) {
        if (
          data.payment.status === 'captured' ||
          data.payment.status === 'paid' ||
          data.payment.status === 'authorized'
        ) {
          alertWithOneButton(
            t('Orders.Processing'),
            t('Orders.Processing Payment Message'),
            t('Cart.Okay'),
            () => handlePaymentProcessing(data?.orderId),
          );
        } else {
          await handlePaymentProcessing(data?.orderId);
        }
      } else {
        await handlePaymentProcessing(data?.orderId);
      }
    } else {
      // logEvent(APPS_FLYER_EVENTS.TRANSACTION_COMPLETED, {
      //   user_id: uid,
      //   af_order_id: data[0].orderId,
      //   transaction_id: transactionId,
      //   domain,
      //   af_price: itemsTotal,
      //   af_quantity: itemCount,
      //   af_revenue: amount,
      // });
      await createTransactionAndClear();
      navigateToOrder(data[0]?.orderId);
    }
  };

  const makePayment = async (
    createPaymentResponse: any,
    amount: number,
    razorpayKey: string,
    providerId: string,
    domain: string,
    transactionId: string,
  ) => {
    const razorpayOrderId = createPaymentResponse.data.data.orderDetail.id;
    try {
      const {email, phone, name} = currentAddress.current.descriptor;
      const options: any = {
        description: 'Payment towards order',
        currency: 'INR',
        name: 'Test',
        order_id: razorpayOrderId,
        key: razorpayKey,
        handler: (response: any) => {
          // ✅ Payment was successful
          handlePaymentSuccess(response);
          // You can now send `response.razorpay_payment_id` to your backend
        },
        retry: {
          enabled: false,
        },
        amount,
        prefill: {
          email: email,
          contact: phone,
          name: name,
        },
      };

      if (createPaymentResponse.data.data.customerId) {
        options.customer_id = createPaymentResponse.data.data.customerId;
        options.remember_customer = true;
      }

      // logEvent(APPS_FLYER_EVENTS.LAND_ON_PG_PAGE, {
      //   user_id: uid,
      //   timestamp: getCurrentDate(),
      //   provider_id: providerId,
      //   domain,
      //   transaction_id: transactionId,
      // });
      const rzp = new (window as any).Razorpay(options);
      rzp.on('payment.failed', (response: any) => {
        handlePaymentSuccess(response);
      });
      rzp.open();
    } catch (error: any) {
      return {
        razorpay_order_id: razorpayOrderId,
        noCallback: true,
        razorpayException: error,
      };
    }
  };

  const processPrepaidPayment = async (
    parentOrderIDMap: any,
    item: any,
    queryParams: any,
    orderTotal: number,
    providerId: string,
    domain: string,
    transactionId: string,
    itemsTotal: number,
    itemCount: number,
  ) => {
    source.current = CancelToken.source();
    const createUrl = `${API_BASE_URL}${CREATE_PAYMENT}${
      parentOrderIDMap.get(item?.provider?.id).transaction_id
    }`;
    let amount = Number(orderTotal);
    const createPaymentResponse = await postDataWithAuth(
      createUrl,
      {amount, confirmRequest: queryParams},
      source.current.token,
    );
    const razorpayKey = await getRazorpayKeys();
    if (!razorpayKey) {
      throw '';
    }

    await makePayment(
      createPaymentResponse,
      amount,
      razorpayKey,
      providerId,
      domain,
      transactionId,
    );
  };

  const handlePaymentProcessing = async (orderId: string) => {
    await createTransactionAndClear();
    navigateToOrder(orderId);
  };

  const confirmOrder = async (
    items: any[],
    method: string,
    payment: any,
    orderTotal: number,
    itemsTotal: number,
    itemCount: number,
  ) => {
    responseRef.current = [];
    const parentTransactionIdMap = await getStoredData(
      'parent_and_transaction_id_map',
    );
    const parentOrderIDMap: any = new Map(
      JSON.parse(parentTransactionIdMap || '{}'),
    );
    const checkoutDetails = await getStoredData('checkout_details');
    const {productQuotes: productQuotesForCheckout} = JSON.parse(
      checkoutDetails || '{}',
    );
    try {
      const item = items[0];
      const contextCity = await getStoredData('contextCity');
      const queryParams: any = await getConfirmParams(
        contextCity,
        item,
        parentOrderIDMap,
        payment,
        productQuotesForCheckout,
        method,
      );
      source.current = CancelToken.source();

      await processPrepaidPayment(
        parentOrderIDMap,
        item,
        queryParams,
        orderTotal,
        item.provider.local_id,
        item.domain,
        queryParams[0].context.transaction_id,
        itemsTotal,
        itemCount,
      );
    } catch (err: any) {
      handleError();
    }
  };

  const handleConfirmOrder = async (
    cartId: string,
    deliveryAddress: any,
    activePaymentMethod: string,
    cartItems: any[],
    updatedCartItems: any[],
    orderTotal: number,
    itemsTotal: number,
    itemCount: number,
  ) => {
    try {
      currentCartId.current = cartId;
      currentAddress.current = deliveryAddress;
      if (activePaymentMethod) {
        confirmCartItems.current = cartItems.concat([]);
        const checkoutDetails = await getStoredData('checkout_details');
        const {successOrderIds} = JSON.parse(checkoutDetails || '{}');
        let items = cartItems.map((item: any) => item.item);
        const request_object = constructQuoteObject(
          items.filter(({provider}: { provider: any }) =>
            successOrderIds.includes(provider.local_id.toString()),
          ),
        );
        await confirmOrder(
          request_object[0],
          ORDER_PAYMENT_METHODS.PREPAID,
          updatedCartItems[0].message.quote.payment,
          orderTotal,
          itemsTotal,
          itemCount,
        );
      } else {
        showToast(t('Global.Please select payment'), 'error');
      }
    } catch (e) {
      handleError();
    }
  };

  const deleteCart = async () => {
    try {
      source.current = CancelToken.source();
      await deleteDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/${currentCartId.current}/clear`,
        source.current.token,
      );
    } catch (error: any) {
      if (error.response) {
        if (error.response.status !== 404) {
          handleApiError(error);
        }
      } else {
        handleApiError(error);
      }
    }
  };

  return {
    handleConfirmOrder,
  };
};

export default useConfirmItems;
