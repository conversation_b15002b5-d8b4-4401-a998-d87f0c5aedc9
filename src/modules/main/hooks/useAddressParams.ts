import {useSelector} from 'react-redux';

const useAddressParams = () => {
  const {address} = useSelector((state: any) => state.address);
  const getAddressParams = () => {
    const {lat, lng, areaCode} = address?.address || {};
    const params = new URLSearchParams();
    params.append('latitude', lat);
    params.append('longitude', lng);
    params.append('pincode', areaCode);

    return params;
  };

  return {getAddressParams};
};

export default useAddressParams;
