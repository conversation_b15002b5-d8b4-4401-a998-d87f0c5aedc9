import {useEffect} from 'react';
import {useTheme} from '@mui/material/styles';

type StatusBarStyle = 'light' | 'dark';

const useStatusBarColor = (barStyle: StatusBarStyle, color: string) => {
  const theme = useTheme();

  useEffect(() => {
    document.body.style.backgroundColor = color;
    document.body.style.color = barStyle === 'dark' ? theme.palette.text.primary : '#fff';
  }, [barStyle, color, theme]);
};

export default useStatusBarColor;
