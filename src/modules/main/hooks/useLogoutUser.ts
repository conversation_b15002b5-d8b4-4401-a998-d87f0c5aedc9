import {useDispatch} from 'react-redux';
import {useNavigate} from 'react-router-dom';
import {clearAll} from '../../../utils/storage';
import {logoutUser} from '../../../toolkit/reducer/auth';
import {clearAddress} from '../../../toolkit/reducer/address';
import {clearCart} from '../../../toolkit/reducer/cart';
import {clearComplaint} from '../../../toolkit/reducer/complaint';
import {clearOrder} from '../../../toolkit/reducer/order';
import {clearStoresList} from '../../../toolkit/reducer/stores';

const useLogoutUser = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const clearDataAndLogout = () => {
    clearAll();
    dispatch(logoutUser());
    dispatch(clearAddress());
    dispatch(clearCart());
    dispatch(clearComplaint());
    dispatch(clearOrder());
    dispatch(clearStoresList());

    navigate('/login', {replace: true});
  };

  return {clearDataAndLogout};
};

export default useLogoutUser;
