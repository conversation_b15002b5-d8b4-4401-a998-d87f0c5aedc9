const useQuoteSummary = () => {
  const getQuoteSummary = (quote: any) => {
    const data = quote?.breakup?.filter(
      (one: any) => one['@ondc/org/title_type'] !== 'item',
    );
    return data?.reduce((acc: any, curr: any) => {
      const title = curr.title;
      const value = parseFloat(curr.price.value);

      if (!acc[title]) {
        acc[title] = {
          title: title,
          price: {
            currency: curr.price.currency,
            value: 0,
          },
        };
      }

      acc[title].price.value += value;

      return acc;
    }, {});
  };

  return {getQuoteSummary};
};

export default useQuoteSummary;
