import useNetworkErrorHandling from "../../../hooks/useNetworkErrorHandling"
import useNetworkHandling from "../../../hooks/useNetworkHandling"
import { API_BASE_URL, SERVICEABLE_LOCATIONS } from "../../../utils/apiActions"
import useAddressParams from "./useAddressParams"

interface StoreResponse {
  data: any[]
}

const useStores = () => {
  const { getDataWithAuth } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const { getAddressParams } = useAddressParams()

  const fetchStores = async (
    maxItems: number,
    tagName: string = "",
    serviceability?: string,
    tts?: string,
    domain: string = ""
  ): Promise<any[]> => {
    const controller = new AbortController()
    const signal = controller.signal

    try {
      const params = getAddressParams()
      params.append("radius", "100")
      params.append("limit", String(maxItems))

      if (tagName) params.append("searchTag", tagName)
      if (tts) params.append("tts", tts)
      if (serviceability) params.append("serviceability", serviceability)
      // if (domain) params.append('domain', domain);

      const response = await getDataWithAuth(
        `${API_BASE_URL}${SERVICEABLE_LOCATIONS}?${params.toString()}${
          domain ? `&domain=${domain}` : ""
        }`,
        { signal }
      )

      if (
        response?.data &&
        typeof response.data === "object" &&
        response.data !== null
      ) {
        const apiResponse = response.data as StoreResponse
        return apiResponse.data ?? []
      }

      return []
    } catch (error) {
      if (signal.aborted) {
        console.warn("Request was aborted.")
      } else {
        handleApiError(error)
      }
      return []
    }
  }

  return fetchStores
}

export default useStores
