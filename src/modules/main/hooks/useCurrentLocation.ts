const useCurrentLocation = () => {
  return (): Promise<{ latitude: number; longitude: number }> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by your browser."));
      } else {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            });
          },
          (error) => {
            resolve({
              latitude: 28.55370299999999,
              longitude: 77.21492799999999,
            });
          }, {
            enableHighAccuracy: true,
            timeout: 10000, // 10 seconds
            maximumAge: 0,
          }
        );
      }
    });
  };

};

export default useCurrentLocation;
