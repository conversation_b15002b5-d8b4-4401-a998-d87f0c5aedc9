import {useEffect, useState} from 'react';

const useKeyboardHelper = () => {
  const [keyboardShown, setKeyboardShown] = useState<boolean>(false);

  useEffect(() => {
    const handleFocus = () => setKeyboardShown(true);
    const handleBlur = () => setKeyboardShown(false);

    window.addEventListener('focusin', handleFocus);
    window.addEventListener('focusout', handleBlur);

    return () => {
      window.removeEventListener('focusin', handleFocus);
      window.removeEventListener('focusout', handleBlur);
    };
  }, []);

  return {keyboardShown};
};

export default useKeyboardHelper;
