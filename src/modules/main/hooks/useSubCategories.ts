import {API_BASE_URL, CATEGORIES} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import useAddressParams from './useAddressParams';

interface ApiResponse {
  [domain: string]: any[];
}

const useSubCategories = () => {
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const {getAddressParams} = useAddressParams();

  const fetchSubCategories = async (
    domain: string,
    tagName: string = '',
    serviceability?: string,
    tts?: string
  ): Promise<any[]> => {
    const controller = new AbortController();
    const signal = controller.signal;

    try {
      const params = getAddressParams();
      params.append('searchTag', tagName);
      if (tts) {
        params.append('tts', String(tts));
      }
      if (serviceability) {
        params.append('serviceability', serviceability);
      }

      const response = await getDataWithAuth(
        `${API_BASE_URL}${CATEGORIES}?${params.toString()}&domain=${domain}`,
        {signal}
      );

      if (response?.data && typeof response.data === 'object' && response.data !== null) {
        const apiResponse = response.data as ApiResponse;
        return apiResponse[domain] ?? [];
      }

      return [];
    } catch (error) {
      if (!(error instanceof DOMException && error.name === 'AbortError')) {
        handleApiError(error);
      }
      return [];
    }
  };

  return fetchSubCategories;
};

export default useSubCategories;
