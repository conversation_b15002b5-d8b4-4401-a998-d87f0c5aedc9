import {useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import useProviders from './useProviders';
import usePageWidgets from './usePageWidgets';
import useStores from './useStores';
import useSubCategories from './useSubCategories';
import {Section} from '../components/page/components/type';
// import {upsertSectionsInDB} from '../../../utils/databaseHelper';
// import {useDatabase} from '../../../hooks/useDatabase';
import {setPage} from '../../../toolkit/reducer/pages';

const usePageDetails = () => {
  const dispatch = useDispatch();
  const [sectionsRequested, setSectionsRequested] = useState<boolean>(false);
  const [sectionData, setSectionData] = useState<any>({});
  const getProviders = useProviders();
  const getPageWidgets = usePageWidgets();
  const getStores = useStores();
  const getSubCategories = useSubCategories();
  // const db = useDatabase();
  const {pages} = useSelector((state: any) => state.pages);
  const currentPageName = useRef<string>('');

  const hideSections = () => {
    setSectionsRequested(false);
  };

  const processDependencies = (fetchedData: any, sections: any[]) => {
    const filteredData = {...fetchedData};

    sections.forEach(section => {
      if (section.dependsOn?.length > 0) {
        const areDependenciesEmpty = section.dependsOn.every(
          (dependencyId: string) =>
            !filteredData[dependencyId] ||
            !Object.keys(filteredData[dependencyId]).length,
        );

        if (areDependenciesEmpty) {
          delete filteredData[section.id];
        }
      }
    });

    return filteredData;
  };

  // Mock API calls for different section types
  const fetchDataForSectionType = async (section: Section) => {
    switch (section.typeOfWidgets) {
      case 'providers':
        return await getProviders(
          section.maxItems,
          section.tags?.name ?? '',
          section.tags?.serviceability,
          section.tags?.tts,
        );
      case 'widgets':
        return await getPageWidgets(section.id, section.maxItems);
      case 'stores':
        return await getStores(
          section.maxItems,
          section.tags?.name ?? '',
          section.tags?.serviceability,
          section.tags?.tts,
          section?.domain ?? '',
        );
      case 'subcategories':
        return await getSubCategories(
          section.domain,
          section.tags?.name ?? '',
          section.tags?.serviceability,
          section.tags?.tts,
        );
      default:
        throw new Error('Unknown section type');
    }
  };

  const processSections = (responses: any[], sections: Section[]) => {
    const formattedData = responses.reduce((acc: any, curr) => {
      acc[curr.id] = curr.data;
      return acc;
    }, {});
    const dependencyCheckedData = processDependencies(formattedData, sections);
    setSectionData(dependencyCheckedData);
  };

  const fetchSpecificSectionData = async (section: Section) => {
    try {
      const data = await fetchDataForSectionType(section);
      return {id: section.id, data};
    } catch (error) {
      return {id: section.id, data: []};
    }
  };

  const fetchSectionDataFromServer = async (
    sections: Section[],
    pageName: string,
  ) => {
    try {
      const responses = await Promise.all(
        sections.map(section => fetchSpecificSectionData(section)),
      );
      // if (db) {
      //   await upsertSectionsInDB(db, responses);
      // }
      if (currentPageName.current === pageName) {
        processSections(responses, sections);
      }
      const currentPage = {
        ...pages[pageName],
        sections: pages[pageName]?.sections?.map((section: Section) => {
          const newSection: any = {...section};
          const selected = responses.find(one => one.id === section.id);
          if (selected) {
            newSection.widgets = selected.data;
          }
          return newSection;
        }),
      };
      dispatch(setPage({pageName, page: currentPage}));
    } catch (error) {
      console.error('Error fetching section data:', error);
    } finally {
      setSectionsRequested(false);
    }
  };

  const fetchSectionData = async (sections: Section[], pageName: string) => {
    try {
      currentPageName.current = pageName;
      const sectionsWithoutWidgets = [];
      const sectionsWithWidgets = [];

      for (const section of sections) {
        if (section.hasOwnProperty('widgets')) {
          sectionsWithWidgets.push(section);
        } else {
          sectionsWithoutWidgets.push(section);
        }
      }

      if (sectionsWithWidgets.length === 0) {
        setSectionsRequested(true);
      }
      if (sectionsWithoutWidgets.length > 0) {
        await fetchSectionDataFromServer(
          sectionsWithoutWidgets.slice(0, 4),
          pageName,
        );
      }
      if (sectionsWithWidgets.length > 0) {
        const formattedData = sectionsWithWidgets.reduce((acc: any, curr: any) => {
          acc[curr.id] = curr.widgets;
          return acc;
        }, {});
        const dependencyCheckedData = processDependencies(
          formattedData,
          sections,
        );
        setSectionData(dependencyCheckedData);
      }
    } catch (error) {
      console.error('Error fetching section data:', error);
    } finally {
      setSectionsRequested(false);
    }
  };

  return {sectionsRequested, sectionData, fetchSectionData, hideSections};
};

export default usePageDetails;
