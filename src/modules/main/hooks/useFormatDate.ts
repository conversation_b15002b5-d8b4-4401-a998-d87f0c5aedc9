import {useTranslation} from 'react-i18next';
import moment from 'moment';
import 'moment/locale/hi';
import 'moment/locale/mr';
import 'moment/locale/bn';
import 'moment/locale/ta';

const useFormatDate = () => {
  const {i18n} = useTranslation();

  const formatDate = (date: moment.Moment, format: string) => {
    if (!date) return '';

    const momentDate = moment.isMoment(date) ? date : moment(date);
    const locale = moment.locales().includes(i18n.language) ? i18n.language : 'en';

    return momentDate.locale(locale).format(format);
  };

  return {formatDate};
};

export default useFormatDate;
