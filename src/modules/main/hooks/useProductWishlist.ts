import {useEffect, useRef, useState} from 'react';
import {useSelector} from 'react-redux';
import axios from 'axios';

import {API_BASE_URL, WISHLIST} from '../../../utils/apiActions';
import useWishlistItems from './useWishlistItems';
import useNetworkHandling from '../../../hooks/useNetworkHandling';

const CancelToken = axios.CancelToken;

const useProductWishlist = () => {
  const {wishlistItems} = useSelector((state: any) => state.wishlist);
  const productWishlistId = useRef<any>(null);
  const {uid} = useSelector((state: any) => state.auth);
  const source = useRef<any>(null);
  const {getWishlistItems} = useWishlistItems();
  const {deleteDataWithAuth, postDataWithAuth} = useNetworkHandling();

  const [productId, setProductId] = useState<string>('');
  const [presentInWishlist, setPresentInWishlist] = useState<boolean>(false);
  const [wishlistApiInProgress, setWishlistApiInProgress] =
    useState<boolean>(true);

  const deleteItemFromWishlist = async () => {
    setWishlistApiInProgress(true);
    try {
      source.current = CancelToken.source();
      await deleteDataWithAuth(
        `${API_BASE_URL}${WISHLIST}/${uid}/${productWishlistId.current}`,
        source.current.token,
      );
      await getWishlistItems();
    } catch (error: any) {
      // crashlytics().recordError(error);
    } finally {
      setWishlistApiInProgress(false);
    }
  };

  const addItemToWishlist = async (product: any) => {
    setWishlistApiInProgress(true);
    try {
      source.current = CancelToken.source();
      await postDataWithAuth(
        `${API_BASE_URL}${WISHLIST}/${uid}`,
        {
          id: productId,
          locationId: product.location_details.id,
        },
        source.current.token,
      );
      await getWishlistItems();
    } catch (error: any) {
      // crashlytics().recordError(error);
    } finally {
      setWishlistApiInProgress(false);
    }
  };

  useEffect(() => {
    if (productId && wishlistItems) {
      let findWishlistStatus = false;
      wishlistItems?.forEach((element: any) => {
        element?.items?.forEach((item: any) => {
          if (item.id === productId) {
            productWishlistId.current = item._id;
            findWishlistStatus = true;
          }
        });
      });
      setPresentInWishlist(findWishlistStatus);
      setWishlistApiInProgress(false);
    }
  }, [productId, wishlistItems]);

  return {
    presentInWishlist,
    wishlistApiInProgress,
    addItemToWishlist,
    deleteItemFromWishlist,
    setProductId,
  };
};

export default useProductWishlist;
