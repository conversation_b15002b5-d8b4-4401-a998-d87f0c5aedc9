import {useEffect, useRef, useState} from 'react';
import axios from 'axios';
import {API_BASE_URL, CATEGORIES} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import useAddressParams from './useAddressParams';
import {emptyAlertCallback} from '../../../utils/utils';
import {DOMAINS} from '../../../utils/domains';

const CancelToken = axios.CancelToken;
const useTagSubCategories = (
  tagName: string,
  tts: any,
  serviceability: any,
) => {
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const {getAddressParams} = useAddressParams();
  const source = useRef<any>(null);
  const [domains, setDomains] = useState<any>({});
  const [domainRequested, subDomainRequested] = useState<boolean>(true);

  const getSubCategoriesForDomain = async (domain: string) => {
    try {
      const params = getAddressParams();
      if (tagName) {
        params.append('searchTag', tagName);
      }
      if (tts) {
        params.append('tts', String(tts));
      }
      if (serviceability) {
        params.append('serviceability', serviceability);
      }
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${CATEGORIES}?${params.toString()}&domain=${domain}`,
        source.current.token,
      );
      return data;
    } catch (error) {
      handleApiError(error);
    }
  };

  const getSubCategories = async () => {
    try {
      subDomainRequested(true);
      Promise.allSettled(
        DOMAINS.map(domain => getSubCategoriesForDomain(domain.domain)),
      ).then(results => {
        const list: any = {};
        results.forEach(result => {
          if (result.status === 'fulfilled') {
            Object.entries(result.value).forEach(([key, value]) => {
              list[key] = value;
            });
          }
        });
        setDomains(list);
      });
    } catch (error) {
      handleApiError(error);
    } finally {
      subDomainRequested(false);
    }
  };

  useEffect(() => {
    getSubCategories().then(emptyAlertCallback).catch(emptyAlertCallback);

    return () => {
      if (source.current) {
        source?.current?.cancel();
      }
    };
  }, []);

  return {domains, domainRequested};
};

export default useTagSubCategories;
