import axios from 'axios';
import {useEffect, useRef} from 'react';
import {useDispatch} from 'react-redux';

import {API_BASE_URL, DELIVERY_ADDRESS} from '../../../utils/apiActions';
import {setAddressListAvailable} from '../../../toolkit/reducer/address';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';

const useGetAddressesOfUser = () => {
  const source = useRef<any>(null);
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const dispatch = useDispatch();

  const getAddresses = async () => {
    try {
      const CancelToken = axios.CancelToken;
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${DELIVERY_ADDRESS}`,
        source.current.token,
      );
      dispatch(setAddressListAvailable(data.length > 0));
      return data;
    } catch (error: any) {
      if (error?.response?.status === 404) {
      } else {
        handleApiError(error);
      }
      return [];
    }
  };

  useEffect(() => {
    return () => {
      source.current?.cancel();
    };
  }, []);

  return getAddresses;
};

export default useGetAddressesOfUser;
