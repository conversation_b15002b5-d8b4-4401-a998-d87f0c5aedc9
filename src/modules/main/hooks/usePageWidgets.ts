import {API_BASE_URL, PAGE_WIDGETS} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import useAddressParams from './useAddressParams';
import {sortSectionWidgets} from "../../../utils/utils";

interface Widget {
  id: string;
  title: string;
}

const usePageWidgets = () => {
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const {getAddressParams} = useAddressParams();

  return async (sectionId: string, maxItems: number): Promise<Widget[]> => {
    const controller = new AbortController();
    const signal = controller.signal;

    try {
      const params = getAddressParams();
      params.append('sectionId', sectionId);
      params.append('currentTime', String(Date.now()));
      params.append('limit', String(maxItems));

      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${PAGE_WIDGETS}?${params.toString()}`,
        {signal}
      );
      sortSectionWidgets(data.widgets);
      return data.widgets;
    } catch (error) {
      if (signal.aborted) {
        console.warn(`Request for section ${sectionId} was aborted.`);
      } else {
        handleApiError(error);
      }
      return [];
    }
  };
};

export default usePageWidgets;
