import {useDispatch, useSelector} from 'react-redux';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import {API_BASE_URL, CATEGORIES, DOMAIN_IMAGES} from '../../../utils/apiActions';
import {updateImages} from '../../../toolkit/reducer/categories';

const useCategories = () => {
  const dispatch = useDispatch();
  const {getDataWithAuth, getDataWithWithoutEncode} = useNetworkHandling();
  const {address} = useSelector((state: any) => state.address);

  const getDomainImages = async () => {
    const controller = new AbortController();
    const signal = controller.signal;

    try {
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${DOMAIN_IMAGES}`,
        {signal}
      );
      dispatch(updateImages(data));
    } catch (error: any) {
      if (!signal.aborted) {
        console.error(error);
      }
    }
  };

  const getMappedSubCategories = async (categoryDomain: string, subCategory: string) => {
    const controller = new AbortController();
    const signal = controller.signal;

    try {
      const {lat, lng, areaCode} = address?.address || {};
      const queryParams = [
        lat && `latitude=${lat}`,
        lng && `longitude=${lng}`,
        areaCode && `pincode=${areaCode}`,
        `category=${categoryDomain}`,
        `subcategory=${encodeURIComponent(subCategory)}`,
      ].filter(Boolean).join('&');

      const {data} = await getDataWithWithoutEncode(
        `${API_BASE_URL}${CATEGORIES}/all?${queryParams}`,
        {signal}
      );

      return data;
    } catch (error: any) {
      if (!signal.aborted) {
        console.error(error);
      }
      return [];
    }
  };

  return {getDomainImages, getMappedSubCategories};
};

export default useCategories;
