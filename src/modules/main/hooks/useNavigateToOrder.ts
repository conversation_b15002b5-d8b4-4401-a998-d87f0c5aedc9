import {useNavigate} from 'react-router-dom';

const useNavigateToOrder = () => {
  const navigate = useNavigate();

  const navigateToOrder = (orderId?: string) => {
    navigate(-3);
    if (orderId) {
      setTimeout(() => navigate(`/order?id=${orderId}`, {replace: true}), 200);
    } else {
      setTimeout(() => navigate(`/dashboard?tab=Orders`, {replace: true}), 200);
    }
  };

  return {navigateToOrder};
};

export default useNavigateToOrder;
