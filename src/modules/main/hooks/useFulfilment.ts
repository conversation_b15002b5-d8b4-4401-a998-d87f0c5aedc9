import {useRef, useState} from 'react';

const useFulfilment = () => {
  const updatedCartItems = useRef<any[]>([]);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);

  const updateSelectedItemsForInit = () => {
    const newItems = selectedItems[0]?.message?.quote.items;
    updatedCartItems.current.forEach(one => {
      const updatedItem = newItems.find(
        (newItem: any) => newItem.id === one.item.local_id,
      );
      one.item.fulfillment_id = updatedItem?.fulfillment_id;
    });
  };

  return {
    selectedItems,
    setSelectedItems,
    updateSelectedItemsForInit,
  };
};

export default useFulfilment;
