import {API_BASE_URL, GLOBAL_SEARCH_STORES} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import useNetworkErrorHandling from '../../../hooks/useNetworkErrorHandling';
import useAddressParams from './useAddressParams';

interface ProviderResponse {
  response: {
    data: any[];
  };
}

const useProviders = () => {
  const {getDataWithAuth} = useNetworkHandling();
  const {handleApiError} = useNetworkErrorHandling();
  const {getAddressParams} = useAddressParams();

  const fetchProviders = async (
    maxItems: number,
    tagName: string = '',
    serviceability?: string,
    tts?: string
  ): Promise<any[]> => {
    const controller = new AbortController();
    const signal = controller.signal;

    try {
      const params = getAddressParams();
      params.append('size', String(maxItems));
      params.append('searchTag', tagName);
      params.append('hideItems', String(true));

      if (tts) {
        params.append('tts', tts);
      }
      if (serviceability) {
        params.append('serviceability', serviceability);
      }

      const response = await getDataWithAuth(
        `${API_BASE_URL}${GLOBAL_SEARCH_STORES}?${params.toString()}`,
        {signal}
      );

      if (response?.data && typeof response.data === 'object' && response.data !== null) {
        const apiResponse = response.data as ProviderResponse;
        return apiResponse.response?.data ?? [];
      }

      return [];
    } catch (error) {
      if (!(error instanceof DOMException && error.name === 'AbortError')) {
        handleApiError(error);
      }
      return [];
    }
  };

  return fetchProviders;
};

export default useProviders;
