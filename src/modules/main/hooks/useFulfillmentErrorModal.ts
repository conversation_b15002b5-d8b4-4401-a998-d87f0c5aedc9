import { useState, useCallback } from 'react'

interface FulfillmentErrorData {
  errorCode: string
  cartLocationId: string
}

const useFulfillmentErrorModal = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [errorData, setErrorData] = useState<FulfillmentErrorData | null>(null)

  const showFulfillmentError = useCallback((errorCode: string, cartLocationId: string) => {
    setErrorData({ errorCode, cartLocationId })
    setIsOpen(true)
  }, [])

  const hideFulfillmentError = useCallback(() => {
    setIsOpen(false)
    setErrorData(null)
  }, [])

  return {
    isOpen,
    errorData,
    showFulfillmentError,
    hideFulfillmentError,
  }
}

export default useFulfillmentErrorModal
