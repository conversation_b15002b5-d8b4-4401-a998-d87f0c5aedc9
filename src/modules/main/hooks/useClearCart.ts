import { API_BASE_URL, CART } from "../../../utils/apiActions"
import { removeStoreFromCart } from "../../../toolkit/reducer/cart"
import { useRef, useState } from "react"
import axios from "axios"
import { useDispatch, useSelector } from "react-redux"
import useNetworkHandling from "../../../hooks/useNetworkHandling"
import useNetworkErrorHandling from "../../../hooks/useNetworkErrorHandling"

const useClearCart = () => {
  const source = useRef<any>(null)
  const dispatch = useDispatch()
  const { uid } = useSelector(({ auth }) => auth)
  const { deleteDataWithAuth } = useNetworkHandling()
  const { handleApiError } = useNetworkErrorHandling()
  const [deleteInProgress, setDeleteInProgress] = useState<boolean>(false)

  const deleteStoreCart = async (cart: any) => {
    try {
      let sampleCartItem = null
      let noOfItems = 0

      if (cart?.items && cart?.items?.length > 0) {
        sampleCartItem = cart.items[0]
        noOfItems = cart?.items?.reduce(
          (sum: number, item: any) => sum + (item?.item?.quantity?.count ?? 0),
          0
        )
      }

      const CancelToken = axios.CancelToken
      setDeleteInProgress(true)
      source.current = CancelToken.source()
      await deleteDataWithAuth(
        `${API_BASE_URL}${CART}/${uid}/${cart?._id}/clear`,
        source.current.token
      )
      dispatch(removeStoreFromCart(cart?._id))
    } catch (error: any) {
      if (error.response) {
        if (error.response.status !== 404) {
          handleApiError(error)
        }
      } else {
        handleApiError(error)
      }
    } finally {
      setDeleteInProgress(false)
    }
  }

  return { deleteInProgress, deleteStoreCart }
}

export default useClearCart
