import {useNavigate} from 'react-router-dom';

const useLocationPermission = () => {
  const navigate = useNavigate();

  const checkLocationPermissionAvailable = async (): Promise<PermissionState> => {
    if (!navigator.permissions) {
      return 'prompt';
    }

    try {
      const result = await navigator.permissions.query({name: 'geolocation'});
      return result.state;
    } catch (err) {
      return 'prompt';
    }
  };

  const requestLocationPermission = async (): Promise<PermissionState> => {
    try {
      return new Promise((resolve) => {
        navigator.geolocation.getCurrentPosition(
          () => resolve('granted'),
          (error) => {
            if (error.code === error.PERMISSION_DENIED) {
              resolve('denied');
            } else {
              resolve('prompt');
            }
          },
          {timeout: 10000}
        );
      });
    } catch {
      return 'denied';
    }
  };

  const navigateToSelectLocation = () => {
    navigate('/select-location', {replace: true});
  };

  const handleLocationFailure = (
    preventNavigation?: boolean,
    showLocationPopup?: () => void
  ) => {
    if (preventNavigation && typeof showLocationPopup === 'function') {
      showLocationPopup();
    } else {
      navigateToSelectLocation();
    }
  };

  const getUserLocation = async (
    preventNavigation?: boolean,
    showLocationPopup?: () => void
  ): Promise<{ latitude: number; longitude: number } | undefined> => {
    try {
      if ('geolocation' in navigator) {
        return new Promise((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(
            (position) => {
              const {latitude, longitude} = position.coords;
              resolve({latitude, longitude});
            },
            (error) => {
              handleLocationFailure(preventNavigation, showLocationPopup);
              reject(error);
            },
            {enableHighAccuracy: true, timeout: 20000, maximumAge: 1000}
          );
        });
      } else {
        handleLocationFailure(preventNavigation, showLocationPopup);
      }
    } catch (e) {
      handleLocationFailure(preventNavigation, showLocationPopup);
    }
  };

  return {
    checkLocationPermissionAvailable,
    requestLocationPermission,
    getUserLocation,
  };
};

export default useLocationPermission;
