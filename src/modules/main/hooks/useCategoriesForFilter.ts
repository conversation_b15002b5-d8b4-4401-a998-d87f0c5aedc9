import axios from "axios"
import { useEffect, useRef } from "react"
import { useSelector } from "react-redux"

import useNetworkHandling from "../../../hooks/useNetworkHandling"
import { API_BASE_URL, CATEGORIES } from "../../../utils/apiActions"

const useCategoriesForFilter = () => {
  const source = useRef<any>(null)
  const { getDataWithWithoutEncode } = useNetworkHandling()
  const { address } = useSelector((state: any) => state.address)

  const getCategoriesForSearch = async (
    categoryDomain: string,
    subCategory: string,
    locationId?: string,
    bppId?: string,
    itemIds?: string,
    providerIds?: string,
    searchText?: string
  ) => {
    const CancelToken = axios.CancelToken

    const { lat, lng, areaCode } = address?.address
    source.current = CancelToken.source()
    try {
      const queryParams = [
        `latitude=${lat}`,
        `longitude=${lng}`,
        `pincode=${areaCode}`,
        `subcategory=${encodeURIComponent(subCategory)}`,
      ].filter(Boolean) // Remove any falsy values

      const { data } = await getDataWithWithoutEncode(
        `${API_BASE_URL}${CATEGORIES}/all?${queryParams.join("&")}${
          categoryDomain ? `&category=${categoryDomain}` : ""
        }${locationId ? `&locationIds=${locationId}` : ""}${
          bppId ? `&bpp_ids=${bppId}` : ""
        }${itemIds ? `&itemIds=${itemIds}` : ""}${
          providerIds ? `&providerIds=${providerIds}` : ""
        }${searchText ? `&name=${searchText}` : ""}`,
        source.current.token
      )
      return data
    } catch (error: any) {
      return []
    }
  }

  useEffect(() => {
    return () => {
      source.current?.cancel()
    }
  }, [])

  return { getCategoriesForSearch }
}

export default useCategoriesForFilter
