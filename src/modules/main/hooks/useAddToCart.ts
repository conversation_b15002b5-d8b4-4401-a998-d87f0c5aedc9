import {useRef, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {useTranslation} from 'react-i18next';

import {API_BASE_URL, CART, WISHLIST} from '../../../utils/apiActions';
import {areCustomisationsSame, getCustomizations,} from '../../../utils/utils';
import useCartUpdater from './userUpdateCartItem';
import useCartItems from './useCartItems';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import {removeProductFromWishlist} from '../../../toolkit/reducer/wishlist';
import {useToast} from "../../../hooks/toastProvider";

interface RootState {
  auth: {
    uid: string;
  };
}

const useAddToCart = () => {
  const {showToast} = useToast();
  const {t} = useTranslation();
  const dispatch = useDispatch();
  const {uid} = useSelector((state: RootState) => state.auth);
  const {cartItems} = useSelector((state: any) => state.cart);
  const source = useRef<any>(null);
  const {getCartItems} = useCartItems();
  const {updateSpecificItemQuantity} = useCartUpdater();
  const {deleteDataWithAuth, postDataWithAuth} = useNetworkHandling();
  const [addRequested, setAddRequested] = useState<boolean>(false);

  const deleteWishlistItem = async (
    wishlistId: string,
    wishlistProductId: string,
  ) => {
    try {
      if (source.current) {
        source.current.abort();
      }

      source.current = new AbortController();
      const {signal} = source.current;

      await deleteDataWithAuth(
        `${API_BASE_URL}${WISHLIST}/${uid}/${wishlistProductId}`,
        {signal}
      );

      dispatch(
        removeProductFromWishlist({wishlistId, productId: wishlistProductId}),
      );
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.warn('Request was canceled:', error.message);
      } else {
        console.error('Error deleting wishlist item:', error);
      }
    }
  };


  const checkAndDeleteFromWishlist = async (
    wishlistId: any,
    wishlistProductId: any,
  ) => {
    if (wishlistProductId && wishlistId) {
      await deleteWishlistItem(wishlistId, wishlistProductId);
    }
  };

  const generateCartPayload = (
    productDetails: any,
    customizationState: any,
    itemQty: number,
    subtotal: number,
    customisations: any,
  ) => {
    return {
      id: productDetails.id,
      local_id: productDetails.local_id,
      bpp_id: productDetails.bpp_details.bpp_id,
      bpp_uri: productDetails.context.bpp_uri,
      contextCity: productDetails.context.city,
      domain: productDetails.context.domain,
      tags: productDetails.item_details.tags,
      customisationState: customizationState,
      quantity: {
        count: itemQty,
      },
      provider: {
        id: productDetails.bpp_details.bpp_id,
        locations: productDetails.locations,
        ...productDetails.provider_details,
      },
      location_details: productDetails.location_details,
      product: {
        id: productDetails.id,
        subtotal,
        ...productDetails.item_details,
      },
      customisations,
      hasCustomisations: true,
    };
  };

  const addItemToCart = async (
    url: string,
    payload: any,
    wishlistId: string | undefined,
    wishlistProductId: string | undefined,
    setCustomizationState: (value: any) => void,
    hideCustomization: () => void,
    hideProductDetails: () => void,
  ) => {
    try {
      if (source.current) {
        source.current.abort();
      }

      source.current = new AbortController();
      const {signal} = source.current;

      await postDataWithAuth(url, payload, {signal});
      await getCartItems();

      showToast(t('Product Summary.Item added to cart successfully'), 'info');

      await checkAndDeleteFromWishlist(wishlistId, wishlistProductId);

      setCustomizationState({});
      setAddRequested(false);
      hideCustomization();
      hideProductDetails();
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.warn('Request was canceled:', error.message);
      } else {
        console.error('Error adding item to cart:', error);
      }
    }
  };

  const updateExistingItem = async (
    items: any,
    itemQty: number,
    _id: string,
    locationId: string,
    wishlistId: string | undefined,
    wishlistProductId: string | undefined,
    setCustomizationState: (value: any) => void,
    hideCustomization: () => void,
    hideProductDetails: () => void,
  ) => {
    await updateSpecificItemQuantity(items, itemQty, _id, locationId);
    showToast(t('Product Summary.Item quantity updated in your cart'), 'info');
    await checkAndDeleteFromWishlist(wishlistId, wishlistProductId);
    setCustomizationState({});
    setAddRequested(false);
    hideCustomization();
    hideProductDetails();
  };

  const cartAlreadyPresentUpdate = async (
    providerCart: any,
    payload: any,
    customisations: any,
    url: string,
    wishlistId: string | undefined,
    wishlistProductId: string | undefined,
    setCustomizationState: (value: any) => void,
    hideCustomization: () => void,
    hideProductDetails: () => void,
    itemQty: number,
    productDetails: any
  ) => {
    let items: any[] = [];
    items = providerCart.items.filter((ci: any) => ci.item.id === payload.id);

    if (items.length > 0 && customisations && customisations.length > 0) {
      items = providerCart.items.filter(
        (ci: any) =>
          ci.item.customisations &&
          ci.item.customisations.length === customisations?.length
      );
    }

    if (source.current) {
      source.current.abort();
    }

    source.current = new AbortController();
    const {signal} = source.current;

    if (items.length === 0) {
      await addItemToCart(
        url,
        payload,
        wishlistId,
        wishlistProductId,
        setCustomizationState,
        hideCustomization,
        hideProductDetails
      );
    } else {
      const currentCount = Number(items[0].item.quantity.count);
      const maxCount = Number(items[0].item.product.quantity.maximum.count);

      if (currentCount < maxCount) {
        if (!customisations) {
          await updateExistingItem(
            providerCart.items,
            itemQty,
            items[0]._id,
            productDetails.location_details.id,
            wishlistId,
            wishlistProductId,
            setCustomizationState,
            hideCustomization,
            hideProductDetails
          );
        } else {
          const currentIds = customisations.map((item: any) => item.id);
          let matchingCustomisation = null;

          for (let i = 0; i < items.length; i++) {
            let existingIds = items[i].item.customisations.map(
              (item: any) => item.id
            );
            const areSame = areCustomisationsSame(existingIds, currentIds);
            if (areSame) {
              matchingCustomisation = items[i];
            }
          }

          if (matchingCustomisation) {
            await updateExistingItem(
              providerCart.items,
              itemQty,
              matchingCustomisation._id,
              productDetails.location_details.id,
              wishlistId,
              wishlistProductId,
              setCustomizationState,
              hideCustomization,
              hideProductDetails
            );
          } else {
            await addItemToCart(
              url,
              payload,
              wishlistId,
              wishlistProductId,
              setCustomizationState,
              hideCustomization,
              hideProductDetails
            );
          }
        }
      } else {
        showToast(
          t(
            'Product Summary.The maximum available quantity for item is already in your cart'
          ), 'error'
        );
      }
    }
  };

  const addDetailsToCart = async (
    productDetails: any,
    customizationState: any,
    customizationPrices: any,
    itemQty: number,
    setCustomizationState: (value: any) => void,
    hideCustomization: () => void,
    hideProductDetails: () => void,
    wishlistProductId?: string | undefined,
    wishlistId?: string | undefined,
  ) => {
    setAddRequested(true);
    try {
      const url = `${API_BASE_URL}${CART}/${uid}`;

      let customisations = await getCustomizations(
        productDetails,
        customizationState,
      );

      const subtotal =
        productDetails?.item_details?.price?.value + customizationPrices;

      const payload: any = generateCartPayload(
        productDetails,
        customizationState,
        itemQty,
        subtotal,
        customisations,
      );

      const newCartItems = JSON.parse(JSON.stringify(cartItems));
      let providerCart: any = newCartItems?.find(
        (cart: any) => cart.location_id === productDetails.location_details.id,
      );
      if (providerCart) {
        await cartAlreadyPresentUpdate(
          providerCart,
          payload,
          customisations,
          url,
          wishlistId,
          wishlistProductId,
          setCustomizationState,
          hideCustomization,
          hideProductDetails,
          itemQty,
          productDetails,
        );
      } else {
        await addItemToCart(
          url,
          payload,
          wishlistId,
          wishlistProductId,
          setCustomizationState,
          hideCustomization,
          hideProductDetails,
        );
      }
    } catch (error: any) {
    } finally {
      setAddRequested(false);
    }
  };

  return {addDetailsToCart, addRequested};
};

export default useAddToCart;
