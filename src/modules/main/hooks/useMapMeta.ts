import {useCallback, useRef, useState} from 'react';
import axios from 'axios';
import useNetworkHandling from '../../../hooks/useNetworkHandling';
import {API_BASE_URL, MAP_ACCESS_TOKEN} from '../../../utils/apiActions';

const CancelToken = axios.CancelToken;

const useMapMetaWeb = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [apiError, setApiError] = useState<boolean>(false);
  const source = useRef<any>(null);
  const {getDataWithAuth} = useNetworkHandling();

  const getMapMeta = useCallback(async () => {

    const fetchToken = async (): Promise<any> => {
      source.current = CancelToken.source();
      const {data} = await getDataWithAuth(
        `${API_BASE_URL}${MAP_ACCESS_TOKEN}`,
        source.current.token,
      );
      return data;
    };

    const success = await fetchToken();
    setApiError(!success);
    setLoading(false);
    return success;
  }, []);

  return {
    getMapMeta,
    loading,
    apiError,
  };
};

export default useMapMetaWeb;
