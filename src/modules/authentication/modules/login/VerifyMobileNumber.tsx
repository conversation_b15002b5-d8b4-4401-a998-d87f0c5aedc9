import { faPenToSquare } from "@fortawesome/free-solid-svg-icons"
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome"
import Box from "@mui/material/Box"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import Typography from "@mui/material/Typography"
import { makeStyles } from "@mui/styles"
import React, { useEffect, useMemo, useRef, useState } from "react"
import { useLocation } from "react-router-dom"
import { removeData } from "../../../../utils/storage"
import { theme } from "../../../../utils/theme"
import { emptyAlertCallback } from "../../../../utils/utils"
import useBackHandler from "../../../main/hooks/useBackHandler"
import PageBackground from "../../common/PageBackground"
import useSignUpUser from "../../hooks/useSignUpUser"
import useStoreUserAndNavigate from "../../hooks/useStoreUserAndNavigate"
import ResendOtp from "./components/ResendOtp"

const OTP_LENGTH = 6
const OTP_REGEX = /^\d{6}$/

const VerifyMobileNumber = () => {
  const inputRefs = useRef<Array<HTMLInputElement | null>>([])
  const styles = useStyles()
  const location = useLocation()
  const { goBack } = useBackHandler()
  const [otpValues, setOtpValues] = useState<string[]>(
    Array(OTP_LENGTH).fill("")
  )
  const [otpError, setOTPError] = useState<string>("")
  const [verificationFailed, setVerificationFailed] = useState<boolean>(false)
  const { verificationInProgress, signUpUser } = useSignUpUser()
  const { storeDetails } = useStoreUserAndNavigate()
  const otp = ""
  const { mobileNumber } = location.state || {}

  const focusNextField = (index: number) => {
    if (index < inputRefs.current.length - 1) {
      const nextField = inputRefs.current[index + 1]
      nextField?.focus()
    }
  }

  const handleTextChange = (text: string, index: number) => {
    setOTPError("")
    setVerificationFailed(false)

    // Handle pasted OTP (multiple digits)
    if (text.length > 1) {
      const digits = text.replace(/\D/g, "").slice(0, OTP_LENGTH) // Extract only digits, limit to OTP_LENGTH
      const updatedOtp = Array(OTP_LENGTH).fill("")

      // Fill the OTP array starting from current index
      for (let i = 0; i < digits.length && index + i < OTP_LENGTH; i++) {
        updatedOtp[index + i] = digits[i]
      }

      // Keep existing values before the current index
      for (let i = 0; i < index; i++) {
        updatedOtp[i] = otpValues[i]
      }

      setOtpValues(updatedOtp)

      // Update input values and focus the last filled input
      inputRefs.current.forEach((ref, refIndex) => {
        if (ref) ref.value = updatedOtp[refIndex]
      })

      // Focus the next empty field or the last field
      const nextFocusIndex = Math.min(index + digits.length, OTP_LENGTH - 1)
      inputRefs.current[nextFocusIndex]?.focus()

      // Auto-verify if complete OTP is pasted
      if (digits.length === OTP_LENGTH) {
        verifyUserAndSignUp(digits)
          .then(emptyAlertCallback)
          .catch(emptyAlertCallback)
      }
    } else {
      // Handle single digit input
      const updated = [...otpValues]
      updated[index] = text
      setOtpValues(updated)

      if (text.length === 1) {
        focusNextField(index)
      }
    }
  }

  const handleBackspace = (index: number) => {
    if (!otpValues[index] && index > 0) {
      // Clear the previous field and focus it
      const updated = [...otpValues]
      updated[index - 1] = ""
      setOtpValues(updated)
      const prevField = inputRefs.current[index - 1]
      prevField?.focus()
    }
  }

  const handleKeyPress = (
    event: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (event.key === "Backspace") {
      handleBackspace(index)
    }
  }

  const handlePaste = (
    event: React.ClipboardEvent<HTMLInputElement>,
    index: number
  ) => {
    event.preventDefault()
    const pastedData = event.clipboardData.getData("text")
    handleTextChange(pastedData, index)
  }

  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    // Select all text when input is focused for better UX
    event.target.select()
  }

  const verifyUserAndSignUp = async (code: string) => {
    try {
      setOTPError("")
      let token: string | null = null
      const { user, error } = await signUpUser(mobileNumber, code, token)
      if (error) {
        const msg =
          error.response?.data?.error?.message || "Verification failed"
        setOTPError(msg)
      } else {
        removeData("resend_timer")
        await storeDetails(user.token, user.user, false, user.isWhiteListed)
      }
    } catch {
      setOTPError("Something went wrong, please try again later.")
    }
  }

  const handleOTPSubmit = async () => {
    const code = otpValues.join("")
    if (!code) {
      setOTPError("Please enter OTP")
      return
    }
    if (!OTP_REGEX.test(code)) {
      setOTPError("Please enter a valid OTP")
      return
    }
    await verifyUserAndSignUp(code)
  }

  useEffect(() => {
    if (otp) handleTextChange(otp, 0)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [otp])

  const disabled = useMemo(
    () => otpValues.join("").length < 6 || !!otpError,
    [otpValues, otpError]
  )

  return (
    <Box className={styles.root}>
      <PageBackground />
      <Box className={styles.pageContainer}>
        <Box className={styles.formContainer}>
          <Box className={styles.titleRow}>
            <Typography variant="headlineMedium" className={styles.title}>
              OTP Verification
            </Typography>
          </Box>
          <Box className={styles.messageContainer}>
            <Typography
              variant="bodySmall"
              component="div"
              className={styles.signUpMessage}
            >
              A 6-digit code has been sent to {mobileNumber}{" "}
              <FontAwesomeIcon
                icon={faPenToSquare}
                color={theme.palette.primary.main}
                onClick={goBack}
              />
            </Typography>
          </Box>
          <Typography variant="bodySmall" className={styles.inputLabel}>
            Enter OTP
          </Typography>
          <Box className={styles.inputContainer}>
            {otpValues.map((val, i) => (
              <Box
                key={i}
                className={`${styles.input} ${
                  verificationFailed ? styles.failed : ""
                }`}
              >
                <input
                  ref={(ref: any) => (inputRefs.current[i] = ref)}
                  type="tel"
                  value={val}
                  onChange={(e) => {
                    const val = e.target.value
                    if (val === "") {
                      // Allow deletion
                      handleTextChange("", i)
                    } else if (val.length === 1 && /^\d$/.test(val)) {
                      // Allow only single digit for typing
                      handleTextChange(val, i)
                    } else if (val.length > 1) {
                      // Handle pasted content
                      handleTextChange(val, i)
                    }
                  }}
                  onKeyDown={(e) => handleKeyPress(e, i)}
                  onPaste={(e) => handlePaste(e, i)}
                  onFocus={handleFocus}
                  className={styles.otpInput}
                />
              </Box>
            ))}
          </Box>

          {!!otpError && (
            <Typography variant="labelSmall" className={styles.error}>
              {otpError}
            </Typography>
          )}

          <Button
            fullWidth
            variant="contained"
            onClick={handleOTPSubmit}
            disabled={verificationInProgress || disabled}
            className={`${disabled ? styles.disabled : styles.active} ${
              styles.button
            }`}
          >
            {verificationInProgress ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              "Verify"
            )}
          </Button>

          <ResendOtp mobileNumber={mobileNumber} />
        </Box>
      </Box>
    </Box>
  )
}

const useStyles = makeStyles((theme: any) => ({
  root: {
    height: "100dvh",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    position: "relative",
  },
  pageContainer: {
    background: theme.palette.background.paper + "!important",
    padding: 16,
    margin: 16,
    zIndex: 1,
    borderRadius: 12,
  },
  formContainer: {
    maxWidth: 400,
    margin: "auto",
  },
  titleRow: {
    color: theme.palette.primary.main,
    marginBottom: 10,
  },
  title: {
    fontWeight: "bold !important",
  },
  messageContainer: {
    marginBottom: 12,
  },
  signUpMessage: {
    alignItems: "center",
    gap: 4,
    color: theme.palette.neutral.main,
  },
  inputLabel: {
    marginTop: 24,
    marginBottom: 4,
    color: theme.palette.neutral.main,
  },
  inputContainer: {
    display: "flex",
    justifyContent: "space-between",
    gap: 8,
    padding: 0,
    marginTop: 4,
  },
  input: {
    border: "1px solid",
    borderColor: theme.palette.grey[300],
    width: 32,
    height: 32,
    borderRadius: 12,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  disabled: {
    backgroundColor: theme.palette.neutral200 + "!important",
  },
  active: {
    backgroundColor: theme.palette.primary.main + "!important",
  },
  otpInput: {
    textAlign: "center",
    outline: "none",
    width: 32,
    height: 32,
    borderRadius: 12,
    fontSize: 18,
    border: "1px solid" + theme.palette.neutral200 + "!important",
  },
  failed: {
    borderColor: theme.palette.error.main,
  },
  error: {
    color: theme.palette.error.main,
    marginTop: 8,
  },
  button: {
    marginTop: "24px !important",
    borderRadius: "12px !important",
    height: 48,
    color: theme.palette.background.paper + "!important",
  },
  editNumber: {
    color: theme.palette.primary.main + "!important",
  },
}))

export default VerifyMobileNumber
