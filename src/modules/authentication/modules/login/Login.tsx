import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import {useAuthenticationStyles} from "../../common/authenticationStyles";
import PageBackground from "../../common/PageBackground";
import LoginWithPhone from "./components/LoginWithPhone";

/**
 * Component is used to render login form
 */
const Login = () => {
  const styles = useAuthenticationStyles();

  return (
    <Box className={styles.root}>
      <Box className={styles.pageContainer}>
        <PageBackground/>
        <Box className={styles.container}>
          <Box className={styles.formContainer}>
            <Box className={styles.titleRow}>
              <Typography variant="headlineMedium" className={styles.title}>
                Welcome to DigiHaat
              </Typography>
            </Box>
            <Typography variant="bodySmall" className={styles.signUpMessage}>
              Shop Smarter, Start Here
            </Typography>
            <LoginWithPhone/>
          </Box>
        </Box>
      </Box>
    </Box>
  )
}

export default Login
