import React, {useEffect, useState} from 'react';
import {makeStyles} from '@mui/styles';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import FormHelperText from '@mui/material/FormHelperText';
import Typography from '@mui/material/Typography';
import {formatNumber} from '../../../../../utils/utils';
import {OTP_RETRY_COUNTER} from '../../../../../utils/constants';
import useRequestOtp from '../../../hooks/useRequestOtp';
import {getStoredData} from "../../../../../utils/storage";
import {setStoredData} from "../../../../../utils/storage";

const ResendOtp = ({mobileNumber}: { mobileNumber: string }) => {
  const styles = useStyles();
  const {apiInProgress, resendOtp} = useRequestOtp();
  const [timer, setTimer] = useState<number>(OTP_RETRY_COUNTER);
  const [isResendEnabled, setIsResendEnabled] = useState<boolean>(false);
  const [apiError, setApiError] = useState<string>('');

  const resend = async () => {
    try {
      await resendOtp(mobileNumber);
      const now = Math.floor(Date.now() / 1000);
      setStoredData('resend_timer', now.toString());
      setTimer(OTP_RETRY_COUNTER);
      setIsResendEnabled(false);
    } catch (error: any) {
      if (error?.response?.status === 400) {
        setApiError(error.response.data.error.message);
      } else if (error?.response?.data?.error?.message) {
        setApiError(error.response.data.error.message);
      } else {
        setApiError('Something went wrong, please try again later.');
      }
    }
  };

  useEffect(() => {
    if (timer === 0) {
      setIsResendEnabled(true);
      return;
    }

    const interval = setInterval(() => {
      setTimer((prevTimer) => prevTimer - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [timer]);

  useEffect(() => {
    // Check if a resend timestamp exists in localStorage
    const now = Math.floor(Date.now() / 1000);
    const storedTime = getStoredData('resend_timer');

    if (storedTime) {
      const elapsed = now - parseInt(storedTime, 10);
      const remaining = OTP_RETRY_COUNTER - elapsed;
      setTimer(remaining > 0 ? remaining : 0);
    } else {
      setStoredData('resend_timer', now.toString());
      setTimer(OTP_RETRY_COUNTER);
    }
  }, []);

  return (
    <>
      <Box className={styles.resendContainer}>
        <Typography variant="bodySmall" className={styles.message}>
          Didn’t receive OTP?
        </Typography>
        {isResendEnabled ? (
          apiInProgress ? (
            <CircularProgress size={20}/>
          ) : (
            <Button onClick={resend}>
              <Typography variant="bodyLarge" className={styles.timer}>
                Resend
              </Typography>
            </Button>
          )
        ) : (
          <Box className={styles.container}>
            <Typography variant="bodySmall" className={styles.resendMessage}>
              Resend Code in
            </Typography>
            <Typography variant="bodyLarge" className={styles.timer}>
              00:{formatNumber(timer)}
            </Typography>
          </Box>
        )}
      </Box>
      {!!apiError && (
        <FormHelperText error className={styles.apiError}>
          {apiError}
        </FormHelperText>
      )}
    </>
  );
};

export const useStyles = makeStyles((theme: any) => ({
  container: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  message: {
    color: theme.palette.neutral300,
  },
  resendMessage: {
    color: theme.palette.primary.main,
  },
  timer: {
    color: theme.palette.primary.main,
  },
  apiError: {
    textAlign: 'center',
    marginTop: 12,
  },
  resendContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 32,
    gap: 8,
  },
}));

export default ResendOtp;
