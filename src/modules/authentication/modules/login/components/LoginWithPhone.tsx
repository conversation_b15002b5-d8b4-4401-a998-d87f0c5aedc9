import React, {useCallback, useRef, useState} from 'react';
import {makeStyles} from '@mui/styles';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import {useNavigate} from "react-router-dom";

import MobileField from '../../../../../components/input/MobileField';
import useRequestOtp from '../../../hooks/useRequestOtp';
import {TERMS_CONDITION} from '../../../../../utils/constants';

const LoginWithPhone = () => {
  const navigation = useNavigate();
  const styles = useStyles();
  const [mobileNumber, setMobileNumber] = useState<string>('');
  const [mobileError, setMobileError] = useState<string>('');
  const notificationPermissionAvailable = useRef<boolean>(false);
  const {apiInProgress, sendOtp} = useRequestOtp();
  const [otpError, setOTPError] = useState<string>('');

  const updateMobileNumber = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const digitsOnly = value.replace(/[^0-9]/g, '');
    setMobileNumber(digitsOnly);
    setMobileError('');
  };

  const requestOtp = async () => {
    try {
      await sendOtp(mobileNumber);
      navigation('/verify-mobile-number', {
        state: {
          mobileNumber,
          notificationPermissionAvailable:
          notificationPermissionAvailable.current,
        }
      });
    } catch (error: any) {
      if (error?.response?.status === 400) {
        setOTPError(error?.response?.data?.error?.message ?? 'Not able to process the request, try again');
      } else if (error?.response?.status === 410) {
        setOTPError(error?.response?.data?.error?.message ?? 'Not able to process the request, try again');
      } else {
        setOTPError(error?.response?.data?.error?.message ?? 'Not able to process the request, try again');
      }
    }
  };

  const openTermsConditions = useCallback(() => {
    window.open(TERMS_CONDITION, '_blank');
  }, []);

  return (
    <Box>
      <Box className={styles.inputContainer}>
        <MobileField
          disabled={apiInProgress}
          value={mobileNumber}
          maxLength={10}
          required
          inputLabel="Mobile Number"
          placeholder="Enter mobile number"
          error={Boolean(mobileError || otpError)}
          helperText={mobileError || otpError || ' '}
          onChange={updateMobileNumber}
        />
      </Box>
      <Box className={styles.buttonContainer}>
        <Button
          variant="contained"
          onClick={requestOtp}
          loading={apiInProgress}
          sx={{textTransform: 'capitalize'}}
          className={styles.sendOtp}
          disabled={
            apiInProgress ||
            mobileNumber.length !== 10 ||
            mobileError.length > 0
          }>
          Request OTP
        </Button>
        <Box className={styles.footerContainer}>
          <Typography variant="labelSmall" className={styles.textCenter}>
            By Signing to DigiHaat Platform. I accept all the
          </Typography>
          <Button className={styles.tnc} onClick={openTermsConditions}>
            <Typography variant="labelLarge" className={styles.tncText}>
              Terms and Conditions
            </Typography>
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export const useStyles = makeStyles<any>((theme) => ({
  inputContainer: {
    marginTop: 20,
  },
  buttonContainer: {
    marginTop: 28,
  },
  containedButton: {
    borderRadius: 12,
  },
  footerContainer: {
    paddingBottom: 0,
    marginTop: 32,
    textAlign: 'center'
  },
  textCenter: {
    textAlign: 'center',
    color: theme.palette.neutral300,
  },
  tnc: {
    paddingTop: 8,
    color: theme.palette.primary.main,
    fontWeight: '600',
  },
  tncText: {
    textTransform: 'none',
  },
  sendOtp: {
    backgroundColor: theme.palette.primary.main,
    width: '100%',
  },
}));

export default LoginWithPhone;
