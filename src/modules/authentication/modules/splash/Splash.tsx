import React, {useCallback, useEffect} from 'react';
import {makeStyles} from "@mui/styles";
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import {useDispatch} from "react-redux";
import i18n from "i18next";
import {useNavigate} from "react-router-dom";

import {APP_STORE_URL, PLAY_STORE_URL} from '../../../../utils/constants';
import AppLogo from '../../../../assets/splash/digiHaat-logo.svg';
import Brands from '../../../../assets/splash/brands.svg';
import SplashBackground from '../../../../assets/splash/splash-background.svg';

import AndroidIcon from '../../../../assets/splash/android-icon.svg';
import AppleIcon from '../../../../assets/splash/apple-icon.svg';
import GoogleStoreIcon from '../../../../assets/splash/google-play-store.svg';
import AppleStoreIcon from '../../../../assets/splash/apple-store.svg';
import {emptyAlertCallback} from "../../../../utils/utils";
import useMasthead from "../../hooks/useMasthead";
import {getMultipleData, getStoredData} from "../../../../utils/storage";
import {setStoredData} from "../../../../utils/storage";
import {setAddress} from "../../../../toolkit/reducer/address";
import useProcessAuthPayload from "../../../../hooks/useProcessAuthPayload";

import GroceryIcon from '../../../../assets/splash/grocery.svg'
import ElectronicsIcon from '../../../../assets/splash/electronics.svg';
import FoodIcon from '../../../../assets/splash/food.svg';
import BeautyIcon from '../../../../assets/splash/beauty.svg';
import AllIcon from '../../../../assets/splash/all.svg';
import AmazingIndiaIcon from '../../../../assets/splash/amazing-india.svg';

const categories = [
  {icon: AllIcon, label: 'All', tab: 'All'},
  {icon: FoodIcon, label: 'Food', tab: 'Food'},
  {icon: ElectronicsIcon, label: 'Electronics', tab: 'Electronics'},
  {icon: GroceryIcon, label: 'Grocery', tab: 'Grocery'},
  {icon: AmazingIndiaIcon, label: 'Amazing India', tab: 'All'},
  {icon: BeautyIcon, label: 'Beauty', tab: 'Beauty'},
];

const Splash = () => {
  const dispatch = useDispatch();
  const styles = useStyles();
  const navigation = useNavigate();
  const processAuthPayload = useProcessAuthPayload();

  const hideConfirmationPopup = () => {
    checkIfUserIsLoggedIn().finally(emptyAlertCallback);
  };

  const {getMastheads} = useMasthead(hideConfirmationPopup);

  const navigateToLogin = useCallback(() => navigation('/login'), []);

  const navigateToLoginWithTab = (tab: string) => {
    setStoredData('home_page_tab', tab);
    navigation(`/login`);
  }

  const checkLanguage = async (language: any, token: string) => {
    await i18n.changeLanguage('en');
    const addressString = getStoredData('address');
    if (addressString) {
      const address = JSON.parse(addressString);
      dispatch(setAddress(address));
      navigation('/dashboard', {replace: true});
    } else {
      navigation('/check-permissions', {replace: true});
    }
  };

  const getDataFromStorage = () => {
    try {
      const data = getMultipleData([
        'token', 'uid', 'emailId', 'name', 'transaction_id',
        'language', 'phoneNumber', 'isGuest', 'isWhiteListed',
      ]);
      return data[0][1] !== null ? processAuthPayload(data) : null;
    } catch (error) {
      return null;
    }
  };

  const checkIfUserIsLoggedIn = async (): Promise<void> => {
    try {
      const payload: any = getDataFromStorage();
      if (payload) {
        await checkLanguage(payload.language, payload.token);
      }
    } catch (error) {
      console.log("Error", error)
    }
  };

  useEffect(() => {
    getMastheads().finally(emptyAlertCallback);
  }, []);

  return (
    <Box className={styles.container}>
      <Box className={styles.header}>
        <img src={AppLogo} alt="DigiHaat" className={styles.appLogo}/>
        <Box className={styles.headerRight}>
          <Typography variant="labelSmall" className={styles.getAppText}>Get App:</Typography>
          <img src={AndroidIcon} onClick={() => window.open(PLAY_STORE_URL, '_blank')} alt="Google Play"
               className={styles.storeIcon}/>
          <img src={AppleIcon} onClick={() => window.open(APP_STORE_URL, '_blank')} alt="App Store"
               className={styles.storeIcon}/>
          <Button sx={{textTransform: 'none !important'}} onClick={navigateToLogin} className={styles.signInBtn}>Sign
            In/Up</Button>
        </Box>
      </Box>
      <Box className={styles.backgroundImage}></Box>
      <Box className={styles.essentialsSection}>
        <Typography variant='headlineMedium' className={styles.essentialsHeading}>
          Shop for all your <span className={styles.blueText}>Essentials!</span>
        </Typography>
        <Box className={styles.categories}>
          {categories.map((item, index) => (
            <Box key={index} className={styles.categoryItem}>
              <Box className={styles.categoryCircle} onClick={() => navigateToLoginWithTab(item.tab)}>
                <img src={item.icon} alt={item.label} className={styles.categoryIcon}/>
                <Typography align="center" className={styles.categoryLabel}>{item.label}</Typography>
              </Box>
            </Box>
          ))}
        </Box>
      </Box>

      <Box className={styles.brands}>
        <img className={styles.scrollImage} src={Brands} alt="brands"/>
      </Box>

      <Box className={styles.footer}>
        <img src={GoogleStoreIcon} alt="Google Play" onClick={() => window.open(PLAY_STORE_URL, '_blank')}
             className={styles.downloadBtn}/>
        <img src={AppleStoreIcon} alt="App Store" onClick={() => window.open(APP_STORE_URL, '_blank')}
             className={styles.downloadBtn}/>
      </Box>
    </Box>
  );
};

const useStyles = makeStyles<any>((theme) => ({
  container: {
    backgroundColor: '#ffffff',
    display: 'flex',
    flexDirection: 'column',
    minHeight: '100dvh',
  },
  backgroundImage: {
    minHeight: '250px',
    backgroundImage: `url(${SplashBackground})`,
    backgroundSize: 'cover',
    backgroundPosition: 'top center',
    backgroundRepeat: 'no-repeat',
  },
  header: {
    padding: 16,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#0078AD'
  },
  appLogo: {
    height: 18,
  },
  headerRight: {
    display: 'flex',
    alignItems: 'center',
    gap: 8,
  },
  getAppText: {
    fontSize: 12,
    color: '#fff',
  },
  storeIcon: {
    width: 20,
    height: 20,
  },
  signInBtn: {
    height: 30,
    borderRadius: '8px !important',
    color: theme.palette.primary.main,
    backgroundColor: '#fff !important',
    marginLeft: '8px !important'
  },
  headlineSection: {
    marginTop: 32,
    textAlign: 'center',
  },
  headline: {
    fontWeight: 700,
    fontSize: 28,
  },
  essentialsSection: {
    padding: 32,
    textAlign: 'center',
  },
  essentialsHeading: {
    fontSize: 16,
    fontWeight: 500,
    marginBottom: '16px !important',
  },
  blueText: {
    color: '#0078AD',
  },
  categories: {
    display: 'grid',
    gridTemplateColumns: 'repeat(3, 1fr)',
    gap: 24,
    justifyItems: 'center',
    marginTop: '25px'
  },
  categoryItem: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    width: 80,
  },
  categoryCircle: {
    width: 80,
    height: 80,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    marginBottom: 4,
  },

  categoryIcon: {
    // width: 40,
    // height: 40,
    paddingBottom: 3
  },
  brands: {
    display: 'flex',
    backgroundColor: '#f2f2f2',
    alignItems: 'center',
    // overflowX: 'auto',
    gap: 30,
    padding: '16px',
    marginTop: 15,
    // marginLeft: 16,
    // marginRight: 16,
    height: 52,
    // scrollSnapType: 'x mandatory',

    // '&::-webkit-scrollbar': {
    //   display: 'none',
    // },

    overflow: 'hidden',
    position: 'relative',
  },
  scrollImage: {
    display: 'block',
    whiteSpace: 'nowrap',
    animation: 'scroll-left 20s linear infinite'
  },
  brandLogo: {
    height: 55,
    width: 80,
    scrollSnapAlign: 'center',
    flexShrink: 0,
  },
  footer: {
    display: 'flex',
    justifyContent: 'center',
    gap: 16,
    paddingTop: 24,
    paddingBottom: 16,
  },
  downloadBtn: {
    height: 40,
  },
  categoryLabel: {
    fontSize: '11px !important',
    fontWeight: 'bold',
    lineHeight: '15px !important',
    color: "black"
  }
}));

export default Splash;
