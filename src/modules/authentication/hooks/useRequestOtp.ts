import {useEffect, useRef, useState} from 'react';
import axios from 'axios';

import useNetworkHandling from '../../../hooks/useNetworkHandling';
import {API_BASE_URL, RESEND_OTP, SEND_OTP} from '../../../utils/apiActions';
import {emptyAlertCallback} from '../../../utils/utils';

const CancelToken = axios.CancelToken;

const useRequestOtp = () => {
  const [apiInProgress, setApiInProgress] = useState<boolean>(false);
  const {postData} = useNetworkHandling();
  const source = useRef<any>(null);

  const appHash = useRef<string>('WEB_APP_HASH');

  const sendOtp = async (mobile: string) => {
    try {
      setApiInProgress(true);
      source.current = CancelToken.source();
      await postData(
        `${API_BASE_URL}${SEND_OTP}`,
        {mobile, appHash: appHash.current},
        source.current.token
      );
    } catch (error: any) {
      throw error;
    } finally {
      setApiInProgress(false);
    }
  };

  const resendOtp = async (mobile: string) => {
    try {
      setApiInProgress(true);
      source.current = CancelToken.source();
      await postData(
        `${API_BASE_URL}${RESEND_OTP}`,
        {mobile, appHash: appHash.current},
        source.current.token
      );
    } catch (error: any) {
      throw error;
    } finally {
      setApiInProgress(false);
    }
  };

  useEffect(() => {
    try {
      appHash.current = 'WEB_APP_HASH';
    } catch (error) {
      emptyAlertCallback();
    }
  }, []);

  return {apiInProgress, resendOtp, sendOtp};
};

export default useRequestOtp;
