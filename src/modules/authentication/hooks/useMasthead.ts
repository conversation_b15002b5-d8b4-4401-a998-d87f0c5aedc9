import { useDispatch } from "react-redux"

import useNetworkHandling from "../../../hooks/useNetworkHandling"
import { clearMasthead, setMasthead } from "../../../toolkit/reducer/masthead"
import { API_BASE_URL, MASTHEAD } from "../../../utils/apiActions"

interface MastheadResponse {
  data: {
    data: any[]
  }
}

const useMasthead = (hideConfirmationPopup: () => void) => {
  const dispatch = useDispatch()
  const { getDataWithAuth } = useNetworkHandling()

  const getMastheads = async () => {
    const controller = new AbortController()
    const signal = controller.signal

    try {
      const currentTime = Date.now()

      const mastheadResponse = (await getDataWithAuth(
        `${API_BASE_URL}${MASTHEAD}?currentTime=${currentTime}`,
        { signal }
      )) as MastheadResponse

      if (mastheadResponse?.data?.data?.length > 0) {
        const transformed: any = {}
        mastheadResponse?.data?.data.forEach((item: any) => {
          if (item.page) {
            transformed[item.page.name] = item
          }
        })

        Object.entries(transformed).forEach(([page, data]) => {
          dispatch(setMasthead({ page, data }))
        })
      } else {
        dispatch(clearMasthead(null))
      }
    } catch (error: any) {
    } finally {
      hideConfirmationPopup()
    }
  }

  return { getMastheads }
}

export default useMasthead
