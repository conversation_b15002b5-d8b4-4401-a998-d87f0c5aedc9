import {useRef, useState} from 'react';
import axios from 'axios';

import {API_BASE_URL, VERIFY_OTP} from '../../../utils/apiActions';
import useNetworkHandling from '../../../hooks/useNetworkHandling';

const CancelToken = axios.CancelToken;

const useSignUpUser = () => {
  const source = useRef<any>(null);
  const {postData} = useNetworkHandling();
  const [verificationInProgress, setVerificationInProgress] = useState<boolean>(false);

  const signUpUser = async (
    mobile: string,
    otp: string,
    fcmToken: string | null,
  ) => {
    try {
      setVerificationInProgress(true);
      source.current = CancelToken.source();
      const {data} = await postData(
        `${API_BASE_URL}${VERIFY_OTP}`,
        {
          mobile,
          otp,
          fcmToken,
          deviceType: 'web',
        },
        source.current.token,
      );
      return {user: data, error: null};
    } catch (error: any) {
      return {user: null, error};
    } finally {
      setVerificationInProgress(false);
    }
  };

  return {verificationInProgress, signUpUser};
};

export default useSignUpUser;
