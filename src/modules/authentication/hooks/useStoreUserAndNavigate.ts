import {useNavigate} from 'react-router-dom';
import {useDispatch} from 'react-redux';
import {v4 as uuidv4} from 'uuid';

import {saveMultipleData} from '../../../utils/storage';
import {setLoginDetails} from '../../../toolkit/reducer/auth';
import useBackHandler from '../../main/hooks/useBackHandler';

const useStoreUserAndNavigate = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {goBack} = useBackHandler();
  const checkLanguageAndLogin = () => {
    navigate('/check-permissions', {replace: true});
  };

  const storeDetails = async (
    token: any,
    user: any,
    navigateBackToFlow: boolean,
    isWhiteListed: boolean,
  ) => {

    try {
      const photoURL = user?.photoURL ?? '';
      const transactionId: any = uuidv4();

      const storageData = [
        ['token', token],
        ['uid', user?.userId],
        ['name', user?.name ?? ''],
        ['photoURL', photoURL],
        ['transaction_id', transactionId],
      ];

      if (user.email) {
        storageData.push(['emailId', user?.email]);
      }
      if (user.mobile) {
        storageData.push(['phoneNumber', user?.mobile]);
      }
      storageData.push(['isWhiteListed', String(isWhiteListed)]);

      saveMultipleData(storageData);

      const payload = {
        token,
        emailId: user?.email,
        phoneNumber: user?.mobile,
        uid: user?.userId,
        name: user?.name,
        photoURL,
        transaction_id: transactionId,
        isWhiteListed,
      };

      dispatch(setLoginDetails(payload));
      if (navigateBackToFlow) {
        goBack();
      } else {
        checkLanguageAndLogin();
      }
    } catch (error) {
      console.error(error);
    }
  };

  return {storeDetails, checkLanguageAndLogin};
};

export default useStoreUserAndNavigate;
