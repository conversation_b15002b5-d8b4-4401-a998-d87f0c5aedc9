import {makeStyles} from "@mui/styles"

export const useAuthenticationStyles = makeStyles<any>((theme) => ({
  root: {
    height: "100dvh",
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    position: "relative",
  },
  flexContainer: {
    flex: 1,
  },
  titleRow: {
    display: "flex",
    width: "100%",
  },
  pageContainer: {
    background: `linear-gradient(45deg, ${theme.palette.primary.main}, rgba(0, 142, 204, 0.36))`,
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    overflowX: "hidden",
  },
  container: {
    position: "absolute",
    margin: "auto 16px",
  },
  title: {
    color: theme.palette.primary.main,
    fontWeight: "bold",
    fontSize: "24px",
  },
  signUpMessage: {
    paddingTop: 8,
    color: theme.palette.neutral.light,
  },
  formContainer: {
    padding: 20,
    borderRadius: 15,
    backgroundColor: theme.palette.background.paper,
  },
  footerContainer: {
    paddingBottom: 0,
  },
  textCenter: {
    textAlign: "center",
    color: theme.palette.neutral.light,
  },
}))
