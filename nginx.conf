server {
    listen 3000;
    server_name localhost;

    # Serve static files
    root /usr/share/nginx/html;
    index index.html;

    # <PERSON><PERSON> requests for the React app
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Special handling for .well-known files
    location /.well-known {
        alias /usr/share/nginx/html/.well-known;
        default_type application/json;
        add_header Content-Type application/json;
        try_files $uri $uri/ =404;
    }

    # Other static files
    location /static {
        alias /usr/share/nginx/html/static;
        expires 1y;
        add_header Cache-Control "public";
        access_log off;
    }
}
